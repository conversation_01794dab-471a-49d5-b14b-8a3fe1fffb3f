# # GeneratorAI/celeryconfig.py
# broker_url = 'redis://localhost:6379/0'
# result_backend = 'redis://localhost:6379/1'
# task_serializer = 'json'
# result_serializer = 'json'
# accept_content = ['json']
# timezone = 'UTC'
# enable_utc = True

# Redis Configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6379

# Broker and Backend Configuration
broker_url = f'redis://{REDIS_HOST}:{REDIS_PORT}/0'
result_backend = f'redis://{REDIS_HOST}:{REDIS_PORT}/1'

# Serialization
task_serializer = 'json'
result_serializer = 'json'
accept_content = ['json']

# Time settings
timezone = 'UTC'
enable_utc = True

# Queue Configuration
task_default_queue = 'default'
task_create_missing_queues = True

# Task Routing
task_routes = {
    'app.RankCollections.celery_config_collection.*': {'queue': 'collections'},
    'app.Blogging.StoreBlog.celery_config_article.*': {'queue': 'articles'},
    'app.Utils.celery_config_products.*': {'queue': 'products'},
    'app.celery_sync.celery_config_blogs.*': {'queue': 'blogs'},
    'app.celery_sync.celery_config_articles.*': {'queue': 'articles'},
    'app.celery_sync.celery_config_collections.*': {'queue': 'collections'}
}

# Concurrency and Performance Settings
worker_prefetch_multiplier = 1  # Prevents worker from prefetching too many tasks
task_acks_late = True  # Tasks are acknowledged after completion
worker_max_tasks_per_child = 200  # Restart worker after 200 tasks to prevent memory leaks

# Retry Settings
broker_connection_retry = True
broker_connection_retry_on_startup = True

# Task Result Settings
task_ignore_result = False
result_expires = 60 * 60 * 24  # Results expire after 24 hours

# Queue Settings
task_queues = {
    'collections': {
        'exchange': 'collections',
        'routing_key': 'collections',
    },
    'articles': {
        'exchange': 'articles',
        'routing_key': 'articles',
    },
    'products': {
        'exchange': 'products',
        'routing_key': 'products',
    },
    'blogs': {
        'exchange': 'blogs',
        'routing_key': 'blogs',
    }
}