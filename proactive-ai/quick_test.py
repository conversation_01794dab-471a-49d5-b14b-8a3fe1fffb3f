#!/usr/bin/env python3
"""
Quick Test Script for Slack Installation Alerts

This is a minimal script to quickly test if your Slack integration is working.
Run this first before running the full test suite.
"""

import os
import sys

# Add the slack_notifications module to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

def quick_test():
    """Quick test to verify Slack integration is working."""
    print("🔍 Quick Slack Integration Test")
    print("=" * 40)
    
    # Check if token is set
    token = os.getenv('SLACK_BOT_TOKEN')
    if not token:
        print("❌ SLACK_BOT_TOKEN not set!")
        print("\nTo fix this, run:")
        print("export SLACK_BOT_TOKEN='xoxb-your-token-here'")
        return False
    
    print(f"✅ Token found: {token[:12]}...")
    
    # Test imports
    try:
        from slack_notifications.slack_utils import SlackNotifier, send_installation_notification
        print("✅ Imports successful")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test connection
    try:
        notifier = SlackNotifier()
        if notifier.test_connection():
            print("✅ Slack connection successful")
        else:
            print("❌ Slack connection failed")
            return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False
    
    # Send a simple test message
    try:
        print("\n📤 Sending test installation alert...")
        
        success = send_installation_notification(
            project_name='test-project',
            user_info={
                'name': 'Test User',
                'email': '<EMAIL>'
            },
            status='completed',
            details={
                'installation_type': 'new_user_installation',
                'version': 'v1.0.0-test',
                'features_enabled': ['test_feature']
            }
        )
        
        if success:
            print("✅ Test message sent successfully!")
            print("\n🎉 Check your Slack channel for the test message!")
            return True
        else:
            print("❌ Failed to send test message")
            return False
            
    except Exception as e:
        print(f"❌ Error sending message: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print("\n" + "=" * 40)
        print("🎉 Quick test PASSED!")
        print("You can now run the full test:")
        print("python test_installation_alerts.py")
    else:
        print("\n" + "=" * 40)
        print("❌ Quick test FAILED!")
        print("Please check the setup guide: SLACK_SETUP_GUIDE.md")
    
    sys.exit(0 if success else 1)
