# REFER THE BOTTOM PART OF THE DOC FOR RUNNING THIS REPO

# FastAPI Project

This is a Python project built with [FastAPI](https://fastapi.tiangolo.com/), a modern web framework for building APIs with Python.

## Features
- RESTful API endpoints.
- Asynchronous request handling.
- Dependency injection and data validation using Pydantic.

## Prerequisites
- Python 3.8 or higher.
- `pip` package manager.

## Installation
1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd <repository-folder>
   ```

2. Install the dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Running the Server
Start the FastAPI server with the following command:
```bash
uvicorn main:app --reload
```
- Replace `main:app` with the actual Python file and FastAPI app instance name if different.

To start the server on a particular port, use:
```bash
uvicorn main:app --reload --port <port_number>
```
- Replace `<port_number>` with the desired port number.

The server will be available at: `http://127.0.0.1:<port_number>` (default is 8000).

## API Documentation
FastAPI provides interactive API documentation:
- Swagger UI: `http://127.0.0.1:8000/docs`
- ReDoc: `http://127.0.0.1:8000/redoc`

## Stopping the Server
To stop the server, press `Ctrl+C` in the terminal.

## License
This project is licensed under the MIT License. See the LICENSE file for more details.


# Celery start command -- start it on root. -- change the root .env value.
`celery -A celery_app worker --loglevel=info`

# redis start command
`brew services start redis`

# redis stop command
`brew services stop redis`



# STEPS TO RUN THIS PROJECT:

1. `pdm install`
2. `pdm venv activate`
   NOTE: if some package/module not installed on running below command, then enter 'pip install <pkg_name>' manually.
3. After install spacy package install its large model using 'python3 -m spacy download en_core_web_lg' or mention the venv python dir fully instead of python3
4. Move to the app folder and enter 
   `uvicorn main:app --reload --port 5050` or `pdm run uvicorn main:app --reload --port 5050`
5. start the redis server manually - differs based on the machine - for macOS homebrew --> `brew services start redis`
6. to start the queqing system - celery . change the root .env value - python path (venv path). then enter the command at root
   `celery -A celery_app worker --loglevel=info`

# celery run command
'# Terminal 1 - Collections worker
celery -A celery_app worker -Q collections -l info -n collections@%h

# Terminal 2 - Articles worker
celery -A celery_app worker -Q articles -l info -n articles@%h

# For running both workers
celery -A celery_app worker -Q collections,articles -l info



   # Method 1: Single worker for both queues
   celery -A celery_app worker -Q collections,articles,products,blogs,articles -l info

   # Method 2: Separate workers (recommended)
   celery -A celery_app worker -Q collections -l info -n collections@%h
   celery -A celery_app worker -Q articles -l info -n articles@%h
# Monitoring
   celery -A celery_app flower  # Optional: Install flower first with pip install flower


   Local Start Command:
   /Users/<USER>/Documents/projects/GeneratorAiModule/GeneratorAI/.venv/bin/python -m uvicorn main:app --reload --port 5050