stages:
  - deploy

deploy:
  stage: deploy
  script:
    - echo "Deploy production started"
    - echo "Checking tag ${CI_COMMIT_TAG:-not set}"

    # Check tag and determine the target server, directory, and PM2 task
    - |
      if [[ "$CI_COMMIT_TAG" == stage-v* ]]; then
        TARGET_IP="***************"
        DEPLOY_DIR="/proactive-ai"
        PM2_TASK="Proactive AI"
      else
        echo "Unknown or unset tag: ${CI_COMMIT_TAG:-not set}. Exiting."
        exit 1
      fi

    - echo "Deploying to $TARGET_IP"
    - echo "Using directory $DEPLOY_DIR"
    - echo "Restarting PM2 task $PM2_TASK"

    # Execute deployment on the target server
    - |
      /usr/bin/sshpass -p B1nch4@*2024 ssh -o StrictHostKeyChecking=no bincha-admin@$TARGET_IP "
        hostname &&
        pwd &&
        sudo su &&
        cd $DEPLOY_DIR &&
        pwd &&
        git config --global --add safe.directory $DEPLOY_DIR &&
        git stash &&
        git pull &&
        source ~/.bashrc &&
        mkdir test &&
        cd test &&
        pm2 list > step1Done.txt
      "

  environment:
    name: azure-shopify-runner
  only:
    - tags
