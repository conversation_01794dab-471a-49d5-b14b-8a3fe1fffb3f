#celery_config_article.py
import sys
from pathlib import Path
from typing import Dict, Any
import requests
import asyncio
from datetime import datetime
from celery import shared_task

# Add project root to Python path
project_root = str(Path(__file__).resolve().parents[3])
if project_root not in sys.path:
    sys.path.append(project_root)

from app.Utils.redis_utils import redis_client
from app.Agents.storeBlog import article_generator
from app.Models.dbHelpers import update_article_job_status
from app.Logging.logger_config import get_logger

logger = get_logger(__name__)

def run_async(coro):
    """Helper function to run async code in sync context"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop.run_until_complete(coro)

async def notify_slack(task_id: str, store_id: int, status: str, error_message: str = None):
    """
    Send notifications to Slack about article generation status
    """
    message = {
        "task_id": task_id,
        "store_id": store_id,
        "status": status,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    if error_message is not None:
        message["error"] = error_message
        
    logger.info(
        "Slack notification would be sent",
        notification=message
    )
    return message

@shared_task(
    bind=True, 
    max_retries=1,
    name='app.Blogging.StoreBlog.celery_config_article.generate_article_task',
    queue='articles'
)
def generate_article_task(self, payload: Dict[str, Any], store_id: int, request_id: str):
    """Celery task for generating articles"""
    task_id = self.request.id
    
    # Add more detailed logging
    logger.info(
        "Article generation task received",
        extra={
            'task_id': task_id,
            'request_id': request_id,
            'store_id': store_id,
            'payload': payload
        }
    )

    try:
        if redis_client:
            redis_client.set(f'article_task_{task_id}_status', 'PROCESSING')
            logger.info(f"Set Redis status to PROCESSING for task {task_id}")
        
        # # Store task ID in Redis for tracking
        # run_async(update_article_status(store_id=store_id, task_id=task_id, status='STARTED'))
        # logger.info(f"Updated article status to STARTED in database for task {task_id}")

        # Update processing status
        run_async(update_article_job_status(task_id=task_id, status='PROCESSING'))
        logger.info(f"Updated article status to PROCESSING for task {task_id}")

        # Add task_id to payload
        payload['task_id'] = task_id

        # Generate article
        logger.info(f"Starting article generation for task {task_id}")
        article_result = run_async(article_generator.create_article(
            payload=payload,
            store_id=store_id
        ))
        logger.info(f"Article generation completed for task {task_id}")

        # Update completion status
        run_async(update_article_job_status(task_id=task_id, status='COMPLETED'))
        logger.info(f"Updated article status to COMPLETED for task {task_id}")

        # Send success notification
        run_async(notify_slack(
            task_id=task_id,
            store_id=store_id,
            status="success"
        ))

        
        # Send result to callback URL
        logger.info(f"Sending results to callback URL for task {task_id}")
        response = requests.post(
            str(payload['cb_url']),
            json=article_result
        )

        if response.status_code != 200:
            logger.error(
                "Failed to send results to callback URL",
                extra={
                    'task_id': task_id,
                    'status_code': response.status_code
                }
            )
            raise Exception(f"Failed to send results to callback URL. Status code: {response.status_code}")

        logger.info(
            "Article generation task completed successfully",
            extra={
                'task_id': task_id,
                'request_id': request_id,
                'store_id': store_id
            }
        )

        return {
            "status": "success",
            "article": article_result
        }

    except Exception as e:
        logger.error(
            "Article generation task failed",
            extra={
                'task_id': task_id,
                'request_id': request_id,
                'error': str(e)
            },
            exc_info=True
        )

        if redis_client:
            redis_client.set(f'article_task_{task_id}_status', 'FAILED')
            logger.info(f"Set Redis status to FAILED for task {task_id}")
        
        # Update failure status
        run_async(update_article_job_status(task_id=task_id, status='FAILED'))
        logger.info(f"Updated article status to FAILED for task {task_id}")

        # Send failure notification
        run_async(notify_slack(
            task_id=task_id,
            store_id=store_id,
            status="failed",
            error_message=str(e)
        ))

        # Send error to callback URL
        try:
            requests.post(
                str(payload['cb_url']),
                json={
                    "status_code": 400,
                    "status": "error",
                    "error": str(e),
                    "task_id": task_id
                }
            )
        except Exception as callback_error:
            logger.error(
                "Failed to send error to callback URL",
                extra={
                    'task_id': task_id,
                    'error': str(callback_error)
                }
            )

        try:
            self.retry(exc=e, countdown=60)
        except self.MaxRetriesExceededError:
            return {
                "status": "error",
                "error": str(e)
            }
        raise e

@shared_task(name='app.Blogging.StoreBlog.celery_config_article.get_article_task_status')
def get_article_task_status(task_id: str) -> Dict[str, Any]:
    """Get the status of an article generation task from Redis"""
    if redis_client:
        status = redis_client.get(f'article_task_{task_id}_status')
        return {
            "task_id": task_id,
            "status": status if status else "UNKNOWN"
        }
    return {
        "task_id": task_id,
        "status": "UNKNOWN",
        "error": "Redis unavailable"
    }

# Redis Configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 1  # Changed to match your existing configuration