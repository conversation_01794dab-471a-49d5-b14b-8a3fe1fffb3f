LANGUAGES = {
    "ar": "Arabic",
    "zh-Hant": "Traditional Chinese",
    "zh-Hans": "Simplified Chinese",
    "cs": "Czech",
    "da": "Danish",
    "nl": "Dutch",
    "en-GB": "English (UK)",
    "en-US": "English (US)",
    "et": "Estonian",
    "fi": "Finnish",
    "fr": "French",
    "de": "German",
    "el": "Greek",
    "hu": "Hungarian",
    "id": "Indonesian",
    "it": "Italian",
    "ja": "Japanese",
    "ko": "Korean",
    "lv": "Latvian",
    "lt": "Lithuanian",
    "no": "Norwegian",
    "pl": "Polish",
    "pt": "Portuguese",
    "ru": "Russian",
    "sr": "Serbian",
    "sk": "Slovak",
    "es": "Spanish",
    "es-419": "Spanish (Latin America)",
    "sv": "Swedish",
    "tr": "Turkish",
    "th": "Thai",
    "uk": "Ukrainian",
    "vi": "Vietnamese"
}

MODELS = {
    "gpt-3.5-turbo": {
        "temperature": 0.7,
        "max_tokens": 2000,
        "top_p": 1,
        "frequency_penalty": 0.7,
        "presence_penalty": 0.7
    },
    "gpt-4": {
        "temperature": 0.7,
        "max_tokens": 2000,
        "top_p": 1,
        "frequency_penalty": 0.7,
        "presence_penalty": 0.7
    },
    "gpt-4-turbo-preview": {
        "temperature": 0.7,
        "max_tokens": 2000,
        "top_p": 1,
        "frequency_penalty": 0.7,
        "presence_penalty": 0.7
    },
    "gpt-4o": {
        "temperature": 0.7,
        "max_tokens": 2000,
        "top_p": 1,
        "frequency_penalty": 0.7,
        "presence_penalty": 0.7
    }
}

TONES = {
    "seo_optimized": "Clear and Confident (Best for SEO)",
    "casual": "Casual",
    "excited": "Excited",
    "formal": "Formal",
    "friendly": "Friendly",
    "humorous": "Humorous",
    "professional": "Professional"
}

POV = {
    "first_person_singular": "First person singular (I, me, my, mine)",
    "first_person_plural": "First person plural (we, us, our, ours)",
    "second_person": "Second person (you, your, yours)",
    "third_person": "Third person (he, she, it, they)"
}

WORDCOUNT = {
    "type-a": {
        "min_headings": 4,
        "max_headings": 5,
        "word_count": 1000,
        "display_text": "4-5 headings(500-1000 words) Best for SEO"
    },
    "type-b": {
        "min_headings": 6,
        "max_headings": 7,
        "word_count": 2000,
        "display_text": "6-7 headings(1000-2000 words)"
    },
    "type-c": {
        "min_headings": 7,
        "max_headings": 8,
        "word_count": 3000,
        "display_text": "7-8 headings(2000-3000 words)"
    },
    "type-d": {
        "min_headings": 9,
        "max_headings": 10,
        "word_count": 4000,
        "display_text": "9-10 headings(3000-4000 words)"
    },
    "type-e": {
        "min_headings": 11,
        "max_headings": 12,
        "word_count": 5000,
        "display_text": "11-12 headings(4000-5000 words)"
    }
}

ARTICLE_TYPE = {
    "In-Depth-Guide": {
        "type": "in-depth-guide",
        "description": "Comprehensive & detailed content on broad topics",
        "example_title": "The Ultimate Guide to Building Your Dream Wardrobe",
        "display_text": "In-Depth Guide"
    },
    "Product-Showcase": {
        "type": "product-showcase",
        "description": "Put your products in the spotlight",
        "example_title": "The Ultimate Guide to Home Coffee Brewing",
        "display_text": "Product Showcase"
    },
    "Industry Trends": {
        "type": "industry-trends",
        "description": "Fresh content about market trends in your niche",
        "example_title": "Upcoming Trends in Athleisure Wear for 2025",
        "display_text": "Industry Trends"
    },
    "How-To-Guide": {
        "type": "how-to-guide",
        "description": "Step-by-step solutions on one topic",
        "example_title": "How to Choose the Perfect Pair of Jeans",
        "display_text": "How-To Guide"
    }
}