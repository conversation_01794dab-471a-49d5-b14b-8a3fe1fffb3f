{"languages": {"ar": "Arabic", "zh-Hant": "Traditional Chinese", "zh-Hans": "Simplified Chinese", "cs": "Czech", "da": "Danish", "nl": "Dutch", "en-GB": "English (UK)", "en-US": "English (US)", "et": "Estonian", "fi": "Finnish", "fr": "French", "de": "German", "el": "Greek", "hu": "Hungarian", "id": "Indonesian", "it": "Italian", "ja": "Japanese", "ko": "Korean", "lv": "Latvian", "lt": "Lithuanian", "no": "Norwegian", "pl": "Polish", "pt": "Portuguese", "ru": "Russian", "sr": "Serbian", "sk": "Slovak", "es": "Spanish", "es-419": "Spanish (Latin America)", "sv": "Swedish", "tr": "Turkish", "th": "Thai", "uk": "Ukrainian", "vi": "Vietnamese"}, "models": {"gpt-3.5-turbo": {"temperature": 0.7, "max_tokens": 2000, "top_p": 1, "frequency_penalty": 0.7, "presence_penalty": 0.7}, "gpt-4": {"temperature": 0.7, "max_tokens": 2000, "top_p": 1, "frequency_penalty": 0.7, "presence_penalty": 0.7}, "gpt-4-turbo-preview": {"temperature": 0.7, "max_tokens": 2000, "top_p": 1, "frequency_penalty": 0.7, "presence_penalty": 0.7}, "gpt-4o": {"temperature": 0.7, "max_tokens": 2000, "top_p": 1, "frequency_penalty": 0.7, "presence_penalty": 0.7}}, "tones": {"seo_optimized": "Clear and Confident (Best for SEO)", "casual": "Casual", "excited": "Excited", "formal": "Formal", "friendly": "Friendly", "humorous": "Humorous", "professional": "Professional"}, "pov": {"first_person_singular": "First person singular (I, me, my, mine)", "first_person_plural": "First person plural (we, us, our, ours)", "second_person": "Second person (you, your, yours)", "third_person": "Third person (he, she, it, they)"}, "wordcount": {"type-a": {"min_headings": 4, "max_headings": 5, "word_count": 1000, "display_text": "4-5 headings(500-1000 words) Best for SEO"}, "type-b": {"min_headings": 6, "max_headings": 7, "word_count": 2000, "display_text": "6-7 headings(1000-2000 words)"}, "type-c": {"min_headings": 7, "max_headings": 8, "word_count": 3000, "display_text": "7-8 headings(2000-3000 words)"}, "type-d": {"min_headings": 9, "max_headings": 10, "word_count": 4000, "display_text": "9-10 headings(3000-4000 words)"}, "type-e": {"min_headings": 11, "max_headings": 12, "word_count": 5000, "display_text": "11-12 headings(4000-5000 words)"}}, "article_type": {"How-To": {"type": "how-to", "description": "Step-by-step guide teaching readers how to accomplish a specific task", "example_title": "How to Choose the Perfect Running Shoes for Your Feet", "display_text": "How-To Guides"}, "Ultimate-Guide": {"type": "ultimate-guide", "description": "Comprehensive resource covering all aspects of a topic", "example_title": "The Ultimate Guide to Home Coffee Brewing", "display_text": "Ultimate Guides"}, "Listicle": {"type": "listicle", "description": "Article organized as a numbered list of items or tips", "example_title": "10 Essential Camping Gear Items for Beginners", "display_text": "Listicles"}, "Product-Comparison": {"type": "product-comparison", "description": "Detailed comparison of similar products or services", "example_title": "Comparing the Top 5 Wireless Earbuds in 2024", "display_text": "Product Comparisons"}}}