from fastapi import APIRouter, HTTPException, Request, Depends, Query
from typing import Dict, List, Optional, Any
import json
from pydantic import BaseModel, Field
from pathlib import Path
from Utils.Helpers.response_formater import success_response, failure_response, error_response
from Middleware.auth_check import verify_tokens
from Utils.Helpers.validate_token import validate_auth_data
from Logging.logger_config import get_logger
from Agents.storeBlog.article_title_generator import generate_article_titles
from Agents.storeBlog.article_outline_generator import generate_article_outline, regenerate_article_outline
from Utils.product_similarity import (
calculate_weighted_embedding, generate_missing_embeddings, find_matches
) 
from Models.dbHelpers import get_random_products, get_products_with_embeddings, update_product_embeddings
from Utils.embedding_creator import generate_embeddings_using_spacy

router = APIRouter()

logger = get_logger(__name__)

class ProductSuggestionRequest(BaseModel):
    keywords: List[str] = Field(..., description="Keywords to match products against")
    article_type: str = Field(..., description="Type of article for context")
    title_of_the_article: Optional[str] = Field(None, description="Title if available")

class ProductResponse(BaseModel):
    gid: str = Field(..., description="Product's Shopify GID")
    title: str = Field(..., description="Product title")
    image_url: Optional[str] = Field(None, description="Product image URL")
    product_url: Optional[str] = Field(None, description="Product page URL")

class ProductSuggestionResponse(BaseModel):
    products: List[ProductResponse] = Field(..., description="List of suggested products")
    message: str = Field(..., description="Response message")

class ArticleTitleRequest(BaseModel):
    target_keyword: List[str]
    type_of_article: str

class ArticleOutlineRequest(BaseModel):
    target_keyword: Optional[List[str]]
    type_of_article: Optional[str]
    title: str
    minimum_subtitles: Optional[int]
    maximum_subtitles: Optional[int]
    is_regeneration: Optional[bool]
    article_outline: Optional[List[Dict[str, Any]]]
    subtitle_to_regenerate: Optional[str]

#generate article titles
@router.post("/generate-titles")
async def get_article_titles(
    request: Request,
    article_request: ArticleTitleRequest,
    auth_data: dict = Depends(verify_tokens)
) -> Dict:
    """
    Generate article titles based on target keyword and article type.
    Returns a list of generated titles with their metadata.
    """

    try:
        
        target_keyword = article_request.target_keyword
        type_of_article = article_request.type_of_article

        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        logger.info(
            "Starting article title generation request",
            extra={
                "origin": request.client.host,
                "request_id": request_id,
                "store_id": store_id,
                "app_id": app_id,
                "target_keyword": target_keyword,
                "article_type": type_of_article
            }
        )

        generated_titles = await generate_article_titles(target_keyword, article_type=type_of_article, store_id=store_id)
            
        logger.info(
            "Successfully generated article titles",
            request_id=request_id,
        )

        print(generated_titles)
        
        return success_response(
            message="Article titles generated successfully",
            data=generated_titles
        )
            
    except Exception as e:
        logger.error(
            "Failed to generate article titles",
            request_id=request_id if 'request_id' in locals() else None,
            error=str(e),
            error_type=type(e).__name__,
            stack_info=True,
            extra={
                "origin": request.client.host if 'request' in locals() else None,
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "target_keyword": target_keyword,
                "article_type": type_of_article
            }
        )
        return error_response(
            message="Failed to generate article titles",
            error=str(e)
        )


#product suggestion endpoint
@router.post("/suggest-products")
async def suggest_products(
    request: Request,
    suggestion_request: ProductSuggestionRequest,
    limit: int = Query(default=10, ge=1, le=50),
    auth_data: dict = Depends(verify_tokens)
):
    """Suggest products based on keywords and article context."""
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        logger.info(
            "Starting product suggestion request",
            extra={
                "request_id": request_id,
                "store_id": store_id,
                "keywords": suggestion_request.keywords,
                "article_type": suggestion_request.article_type
            }
        )

        products = await get_products_with_embeddings(store_id)
        
        if not products:
            return failure_response(
                message="No products found. Please sync your products using /sync/products endpoint",
                error="Products not found"
            )

        for product in products:
            missing_embeddings = generate_missing_embeddings(product)
            if missing_embeddings:
                await update_product_embeddings(
                    product['shopify_gid'], 
                    missing_embeddings
                )
                product.update(missing_embeddings)

        input_embedding = calculate_weighted_embedding(
            keywords=suggestion_request.keywords,
            article_type=suggestion_request.article_type,
            title=suggestion_request.title_of_the_article
        )

        matches, needs_random, random_count = find_matches(
            products=products,
            input_embedding=input_embedding,
            limit=limit
        )

        if needs_random:
            random_products = await get_random_products(
                store_id,
                limit=limit - len(matches)
            )
            
            for product in random_products:
                matches.append({
                    "gid": product['shopify_gid'],
                    "title": product['title'],
                    "image_url": product.get('image_url'),
                    "product_url": product.get('product_url'),
                    "similarity_score": 0
                })
                random_count += 1

        return success_response(
            message="Products suggested successfully",
            data={
                "products": matches,
                "total_matches": len(matches),
                "has_random_products": random_count > 0,
                "random_products_count": random_count
            }
        )
            
    except Exception as e:
        logger.error(
            "Failed to suggest products",
            extra={
                "request_id": request_id if 'request_id' in locals() else None,
                "error": str(e)
            }
        )
        return error_response(
            message="Failed to suggest products",
            error=str(e)
        )

#outline generation endpoint
@router.post("/generate-outline")
async def generate_outline(
    request: Request,
    outline_request: ArticleOutlineRequest,
    auth_data: dict = Depends(verify_tokens)
) -> Dict:
    """
    Generate article outline based on target keyword and article type.
    Returns a list of generated outline points with their metadata.
    """

    try:   
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)


        target_keyword = outline_request.target_keyword
        type_of_article = outline_request.type_of_article
        title_of_the_article = outline_request.title
        minimum_subtitles = outline_request.minimum_subtitles
        maximum_subtitles = outline_request.maximum_subtitles
        is_regeneration = outline_request.is_regeneration
        subtitle_to_regenerate = outline_request.subtitle_to_regenerate
        article_outline = outline_request.article_outline

        logger.info(
            "Starting article outline generation request",
            extra={
                "origin": request.client.host,
                "request_id": request_id,
                "store_id": store_id,
                "app_id": app_id,
                "target_keyword": target_keyword,
                "article_type": type_of_article,
                "title_of_the_article": title_of_the_article,
                "minimum_subtitles": minimum_subtitles,
                "maximum_subtitles": maximum_subtitles,
                "is_regeneration": is_regeneration,
                "subtitle_to_regenerate": subtitle_to_regenerate,
                "article_outline": article_outline
            }
        )

        if is_regeneration:
            generated_outline = await regenerate_article_outline(
                article_outline=article_outline,
                subtitle_to_regenerate=subtitle_to_regenerate,
                store_id=store_id
            )
        else:
            generated_outline = await generate_article_outline(
                target_keyword=target_keyword,
                article_type=type_of_article,
                title_of_the_article=title_of_the_article,
                minimum_subtitles=minimum_subtitles,
            maximum_subtitles=maximum_subtitles,
            store_id=store_id
        )
            
        logger.info(
            "Successfully generated article outline",
            request_id=request_id,
        )
        
        return success_response(
            message="Article outline generated successfully",
            data=generated_outline['data']
        )
            
    except Exception as e:
        logger.error(
            "Failed to generate article outline",
            request_id=request_id if 'request_id' in locals() else None,
            error=str(e),
            error_type=type(e).__name__,
            stack_info=True,
            extra={
                "origin": request.client.host if 'request' in locals() else None,
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "target_keyword": target_keyword,
                "article_type": type_of_article,
                "title_of_the_article": title_of_the_article,
                "minimum_subtitles": minimum_subtitles,
                "maximum_subtitles": maximum_subtitles
            }
        )
        return error_response(
            message="Failed to generate article outline",
            error=str(e)
        )
