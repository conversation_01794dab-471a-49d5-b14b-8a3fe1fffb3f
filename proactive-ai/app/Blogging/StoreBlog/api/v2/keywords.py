# StoreBlog/api/v2/keywords.py
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from pydantic import BaseModel, HttpUrl
from typing import List, Optional
from pathlib import Path

from Middleware.auth_check import verify_tokens
from Utils.get_trending_keywords import KeywordService, Config
from Utils.Helpers.validate_token import validate_auth_data
from Utils.Helpers.response_formater import success_response, failure_response, error_response
from Logging.logger_config import get_logger

router = APIRouter()
logger = get_logger(__name__)

# Initialize config with ruleset file path
config = Config(ruleset_file=Path(__file__).parent / '../../config/keyword_filter_rulesets.yml')
service = KeywordService(config)

class KeywordRequest(BaseModel):
    competitors_domain: Optional[List[HttpUrl]] = None
    focus_keywords: Optional[List[str]] = None
    force_new_generation: bool = False
    generate_based_on_store_domain: bool = True
    generate_based_on_competitors_domains: bool = False
    generate_based_on_target_keywords: bool = False
    refine_keywords: bool = True
    keywords_to_refine: Optional[int] = 5

@router.post("/get-trending-keywords")
async def get_trending_keywords(
    request: Request,
    keyword_request: KeywordRequest,
    auth_data: dict = Depends(verify_tokens),
    limit: Optional[int] = Query(default=12, gt=0)
):
    """
    Get trending keywords based on various sources:
    - Store domain
    - Competitor domains
    - Target keywords
    """
    try:
        logger.info("Starting trending keywords request", 
                   extra={"origin": request.client.host, 
                         "params": keyword_request.dict()})
        
        # Validate auth data and get store_id
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Validate at least one generation type is selected
        if not any([
            keyword_request.generate_based_on_store_domain,
            keyword_request.generate_based_on_competitors_domains,
            keyword_request.generate_based_on_target_keywords
        ]):
            logger.warning("No generation type selected", request_id=request_id)
            return failure_response("Please select at least one keyword generation type")
        
        # Validate target keywords are provided when that option is selected
        if keyword_request.generate_based_on_target_keywords and not keyword_request.focus_keywords:
            logger.warning("Target keywords generation selected but no focus keywords provided", 
                         request_id=request_id)
            return failure_response("Focus keywords required when target keyword generation is enabled")
        
        # Process the keyword request
        result = await service.process_keywords_request(
            store_id=store_id,
            competitors_domain=[str(url) for url in keyword_request.competitors_domain] if keyword_request.competitors_domain else None,
            focus_keywords=keyword_request.focus_keywords,
            force_new_generation=keyword_request.force_new_generation,
            generate_based_on_store_domain=keyword_request.generate_based_on_store_domain,
            generate_based_on_competitors_domains=keyword_request.generate_based_on_competitors_domains,
            generate_based_on_target_keywords=keyword_request.generate_based_on_target_keywords,
            limit=limit,
            refine_keywords=keyword_request.refine_keywords,
            keywords_to_refine=keyword_request.keywords_to_refine
        )
        
        # Handle empty results
        if not result.get("keywords"):
            logger.error("No keywords generated", request_id=request_id)
            return error_response("Failed to generate keywords", "No keywords available")
        
        logger.info("Keywords processed successfully", 
                   request_id=request_id, 
                   count=result["total_keywords"],
                   result=result)
                   
        return success_response(
            message="Keywords Fetched Successfully",
            data=result.get("refined_keywords", result.get("keywords"))
        )
        
    except ValueError as ve:
        logger.error("Validation error", exc_info=True)
        return error_response("Validation error", str(ve))
        
    except HTTPException as he:
        logger.error("HTTP error", exc_info=True)
        return error_response("HTTP error", str(he))
        
    except Exception as e:
        logger.error("Failed to fetch keywords", exc_info=True)
        return error_response("Failed to fetch keywords", str(e))