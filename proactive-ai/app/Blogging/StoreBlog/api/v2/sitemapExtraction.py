from fastapi import APIRouter, Request, Depends, HTTPException
import httpx
import re
import json
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup
from datetime import datetime
from urllib.parse import urljoin
from Logging.logger_config import get_logger
from Utils.sync_products import sync_all_products


router = APIRouter()

logger = get_logger(__name__)


async def fetch_url(url: str) -> str:
    """Fetch content from a URL."""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, follow_redirects=True)
            response.raise_for_status()
            return response.text
    except httpx.HTTPError as e:
        logger.error(f"Error fetching {url}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch {url}: {str(e)}")

async def get_main_sitemap(base_url: str) -> str:
    """Get the sitemap URL, handling different formats."""
    # Ensure base_url has proper format
    if not base_url.startswith(('http://', 'https://')):
        base_url = f"https://{base_url}"
    
    # Remove trailing slash if present
    base_url = base_url.rstrip('/')
    
    sitemap_url = f"{base_url}/sitemap.xml"
    return sitemap_url

async def extract_product_sitemaps(sitemap_content: str) -> List[str]:
    """Extract product sitemap URLs from the main sitemap."""
    soup = BeautifulSoup(sitemap_content, 'xml')
    product_sitemaps = []
    
    for loc in soup.find_all('loc'):
        url = loc.text
        if 'product' in url.lower():
            product_sitemaps.append(url)
    
    return product_sitemaps

async def extract_product_urls(sitemap_content: str) -> List[str]:
    """Extract product URLs from a product sitemap."""
    soup = BeautifulSoup(sitemap_content, 'xml')
    product_urls = []
    
    for loc in soup.find_all('loc'):
        url = loc.text
        if '/products/' in url:
            product_urls.append(url)
    
    return product_urls

async def extract_product_details(product_url: str, html_content: str) -> Dict[str, Any]:
    """Extract product details from product page."""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find JSON-LD script tag for product details
    product_data = None
    for script in soup.find_all('script', type='application/ld+json'):
        try:
            data = json.loads(script.string)
            # Check if this is product data (could be in @graph array or direct)
            if isinstance(data, dict):
                if data.get('@type') == 'Product':
                    product_data = data
                    break
                elif '@graph' in data:
                    for item in data['@graph']:
                        if item.get('@type') == 'Product':
                            product_data = item
                            break
                    if product_data:
                        break
        except (json.JSONDecodeError, TypeError):
            continue
    
    if not product_data:
        logger.warning(f"Could not find product JSON-LD data in {product_url}")
        return {
            "title": None,
            "description": None,
            "price": None,
            "handle": extract_handle(product_url),
            "tags": [],
            "vendor": None,
            "id": None,
            "variants": [],
            "createdAt": None,
            "image_url": None,
            "product_url": product_url
        }
    
    # Extract product details
    title = product_data.get('name')
    description = product_data.get('description')
    
    # Extract price
    price = None
    offers = product_data.get('offers', {})
    if isinstance(offers, dict):
        price_str = offers.get('price')
        if price_str:
            try:
                price = float(price_str)
            except ValueError:
                pass
    elif isinstance(offers, list) and offers:
        price_str = offers[0].get('price')
        if price_str:
            try:
                price = float(price_str)
            except ValueError:
                pass
    
    # Extract handle from URL
    handle = extract_handle(product_url)
    
    # Extract shopify_gid from meta tags or scripts (now stored as id)
    shopify_id = None
    for script in soup.find_all('script'):
        if script.string and 'ShopifyAnalytics' in script.string:
            match = re.search(r'"productId":(\d+)', script.string)
            if match:
                shopify_id = match.group(1)
                break
    
    # Extract image URL
    image_url = None
    if 'image' in product_data:
        if isinstance(product_data['image'], str):
            image_url = product_data['image']
        elif isinstance(product_data['image'], list) and product_data['image']:
            image_url = product_data['image'][0]
    
    # Extract brand/vendor
    vendor = None
    if 'brand' in product_data:
        if isinstance(product_data['brand'], dict):
            vendor = product_data['brand'].get('name')
        elif isinstance(product_data['brand'], str):
            vendor = product_data['brand']
    
    # Extract variants
    variants = []
    for script in soup.find_all('script'):
        if script.string and ('var meta =' in script.string or 'window.ShopifyAnalytics.meta' in script.string):
            variant_match = re.search(r'"variants":(\[.*?\])', script.string)
            if variant_match:
                try:
                    variants_text = variant_match.group(1)
                    variants_text = re.sub(r'(\w+):', r'"\1":', variants_text)
                    variants_text = variants_text.replace("'", '"')
                    variants = json.loads(variants_text)
                except json.JSONDecodeError:
                    pass
                break
    
    # Extract tags
    tags = []
    for script in soup.find_all('script'):
        if script.string and 'var meta =' in script.string:
            tags_match = re.search(r'"tags":\s*(\[.*?\])', script.string)
            if tags_match:
                try:
                    tags_text = tags_match.group(1)
                    tags_text = tags_text.replace("'", '"')
                    tags = json.loads(tags_text)
                except json.JSONDecodeError:
                    pass
                break
    
    # Attempt to find product creation date
    created_at = None
    published_date = product_data.get('datePublished')
    if published_date:
        try:
            created_at = datetime.fromisoformat(published_date.replace('Z', '+00:00')).isoformat()
        except (ValueError, TypeError):
            pass
    
    return {
        "title": title,
        "description": description,
        "price": price,
        "handle": handle,
        "tags": tags,
        "vendor": vendor,
        "id": shopify_id,
        "variants": variants,
        "createdAt": created_at,
        "image_url": image_url,
        "product_url": product_url
    }

def extract_handle(product_url: str) -> str:
    """Extract product handle from URL."""
    match = re.search(r'/products/([a-zA-Z0-9-]+)', product_url)
    if match:
        return match.group(1)
    return None

@router.get("/scrape-products/", response_model=List[Dict[str, Any]])
async def scrape_products(store_url: str, dev_mode: bool = False, store_id: int = 1):
    """
    Scrape product details from a Shopify store, save to a file, and sync to database.
    
    - **store_url**: URL of the Shopify store (e.g., zouk.co.in)
    - **dev_mode**: If True, only scrapes 10 products for testing (default: False)
    - **store_id**: ID of the store in database (default: 1)
    
    Returns a list of product details, saves them to a JSON file, and syncs to database.
    """
    logger.info(f"Starting to scrape products from {store_url}")
    
    # 1. Prepare the base URL
    store_url = store_url.replace("https://", "").replace("http://", "").split("/")[0]
    base_url = f"https://{store_url}"
    
    # 2. Get the main sitemap
    sitemap_url = await get_main_sitemap(base_url)
    sitemap_content = await fetch_url(sitemap_url)
    
    # 3. Extract product sitemaps
    logger.info("Extracting product sitemaps")
    product_sitemaps = await extract_product_sitemaps(sitemap_content)
    
    if not product_sitemaps:
        logger.warning(f"No product sitemaps found for {store_url}")
        raise HTTPException(
            status_code=404,
            detail=f"No product sitemaps found for {store_url}"
        )
    
    # 4. Extract product URLs from all product sitemaps
    all_product_urls = []
    for sitemap_url in product_sitemaps:
        logger.info(f"Processing product sitemap: {sitemap_url}")
        sitemap_content = await fetch_url(sitemap_url)
        product_urls = await extract_product_urls(sitemap_content)
        all_product_urls.extend(product_urls)
        
        # In dev mode, break after getting enough URLs
        if dev_mode and len(all_product_urls) >= 80:
            all_product_urls = all_product_urls[:80]
            logger.info("Dev mode: Limited to 80 products")
            break
    
    if not all_product_urls:
        logger.warning(f"No product URLs found in the sitemaps for {store_url}")
        raise HTTPException(
            status_code=404,
            detail=f"No product URLs found in the sitemaps for {store_url}"
        )
    
    # 5. Extract product details from each product URL
    logger.info(f"Found {len(all_product_urls)} products. Extracting details...")
    all_products = []
    
    for product_url in all_product_urls:
        try:
            logger.info(f"Processing product: {product_url}")
            product_html = await fetch_url(product_url)
            product_details = await extract_product_details(product_url, product_html)
            all_products.append(product_details)
        except Exception as e:
            logger.error(f"Error processing {product_url}: {str(e)}")
            # Continue with next product instead of failing completely
            continue
    
    logger.info(f"Completed scraping {len(all_products)} products from {store_url}")
    
    # Save results to a file
    output_filename = f"scraped_products_{store_url.replace('.', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(all_products, f, indent=2, ensure_ascii=False)
        logger.info(f"Results saved to {output_filename}")
    except Exception as e:
        logger.error(f"Error saving results to file: {str(e)}")
    
    # Sync products to database
    try:
        sync_result = await sync_all_products(products=all_products, store_id=store_id)
        logger.info(f"Products synced to database. Results: {sync_result}")
    except Exception as e:
        logger.error(f"Error syncing products to database: {str(e)}")
    
    return all_products
