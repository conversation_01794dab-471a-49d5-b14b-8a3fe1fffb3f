#/api/v1/article_creation
from fastapi import APIRouter, Request, Depends, HTTPException
from pydantic import BaseModel, Field, HttpUrl, validator
from typing import Dict, List, Optional
from Middleware.auth_check import verify_tokens
from Utils.Helpers.response_formater import success_response, error_response, failure_response
from Logging.logger_config import get_logger
from Utils.Helpers.validate_token import validate_auth_data
from Models.dbHelpers import get_product_details_by_gid, create_article_job, update_article_job_task_id, get_article_job_by_task_id, get_article_details_by_task_id

try:
    from app.Blogging.StoreBlog.celery_config_article import generate_article_task, get_article_task_status
    from app.Agents.storeBlog.article_generator import create_article
except:
    from Blogging.StoreBlog.celery_config_article import generate_article_task, get_article_task_status
    from Agents.storeBlog.article_generator import create_article

router = APIRouter()
logger = get_logger()

class MetaData(BaseModel):
    article_type: str = Field(..., example="blog")
    word_count: int = Field(..., example=500)
    word_count_type: str = Field(..., example="exact")

class AdditionalFeatures(BaseModel):
    product_embeds: bool = Field(False)
    internal_links: bool = Field(False)

class ArticleRequest(BaseModel):
    language: Optional[str] = Field(..., example="EN")
    location: Optional[str] = Field(..., example="USA")
    user_id: Optional[str] = Field(None)  # Allow null
    id: Optional[str] = Field(None)  # Allow null
    target_keyword: Optional[List[str]] = Field(default_factory=list)  # Default to empty list
    products: Optional[List[str]] = Field(default_factory=list, description="List of product GIDs")  # Default to empty list
    article_title: Optional[str] = Field(None)  # Allow null
    subtitles: Optional[List[Dict[str, str]]] = Field(default_factory=list, description="List of subtitle objects with title and type")  # Default to empty list
    cb_url: HttpUrl = Field(..., description="Callback URL")
    meta_data: Optional[MetaData] = Field(None)  # Allow null
    additional_features: Optional[AdditionalFeatures] = Field(None)  # Allow null

    @validator('subtitles')
    def validate_subtitles(cls, v):
        for subtitle in v:
            if 'title' not in subtitle or 'type' not in subtitle:
                raise ValueError("Each subtitle must contain 'title' and 'type'")
            if subtitle['type'] not in ['h2', 'h3']:
                raise ValueError("Type must be either 'h2' or 'h3'")
        return v

    class Config:
        arbitrary_types_allowed = True

    def dict(self, *args, **kwargs):
        # Override the dict method to ensure URL is converted to string
        d = super().dict(*args, **kwargs)
        d['cb_url'] = str(d['cb_url'])
        return d

@router.post("/articles")
async def create_article_endpoint(
    request: Request,
    article_request: ArticleRequest,
    auth_data: dict = Depends(verify_tokens)
) -> Dict:
    """
    Create an article using AI agents based on provided specifications.
    Returns a task ID for tracking the article generation progress.
    """
    request_id = None
    store_id = None
    
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        logger.info(
            "Starting article creation request",
            extra={
                "origin": request.client.host,
                "request_id": request_id,
                "store_id": store_id,
                "app_id": app_id,
                "title": article_request.article_title
            }
        )

        # Store the article job in the database
        article_job = await create_article_job(
            task_id=request_id,
            user_id=article_request.user_id if article_request.user_id else None,
            article_id=article_request.id if article_request.id else None,
            title=article_request.article_title if article_request.article_title else None,
            subtitles=article_request.subtitles if article_request.subtitles else None,
            target_keyword=article_request.target_keyword if article_request.target_keyword else None,
            product_data=article_request.products if article_request.products else None,
            metadata=article_request.meta_data.dict() if article_request.meta_data else None,
            additional_features=article_request.additional_features.dict() if article_request.additional_features else None,
            total_request=article_request.dict(),
            status="ACCEPTED",
            store_id=store_id
        )

        if not article_job:
            logger.error(
                "Failed to store article job in database",
                extra={
                    "request_id": request_id,
                    "store_id": store_id
                }
            )
            return error_response(
                message="Failed to store article job in database",
                error="Could not store article job"
            )

        # Fetch product details for each GID in the products array if provided
        product_details = []
        if article_request.products:
            try:
                for gid in article_request.products:
                    product = await get_product_details_by_gid(gid)
                    if product:
                        product_details.append(product)
                        
                if not product_details:
                    logger.warning(
                        "No valid products found for article creation",
                        extra={
                            "request_id": request_id,
                            "store_id": store_id
                        }
                    )
                    return failure_response(
                        message="No valid products available for article creation",
                        status_code=404
                    )
            except Exception as product_error:
                logger.error(
                    "Error fetching product details",
                    extra={
                        "request_id": request_id,
                        "store_id": store_id,
                        "error": str(product_error)
                    },
                    stack_info=True
                )
                return error_response(
                    message="Failed to fetch product details",
                    error="Could not retrieve product information"
                )

        try:
            # Create payload for article generation
            payload = article_request.dict()
            payload['products'] = product_details
            payload['task_id'] = request_id

            # Submit task to Celery
            task = generate_article_task.delay(
                payload=payload,
                store_id=store_id,
                request_id=request_id
            )
            
            logger.info(
                "Article generation task submitted",
                extra={
                    "request_id": request_id,
                    "store_id": store_id,
                    "task_id": task.id
                }
            )

            #update article job status to processing
            await update_article_job_task_id(request_id=request_id, task_id=task.id, status='STARTED')

            return success_response(
                message="Article generation task submitted successfully",
                data={
                    "task_id": task.id,
                    "status": "ACCEPTED",
                    "status_endpoint": f"/api/v2/article-status/{task.id}"
                }
            )

        except Exception as generation_error:
            logger.error(
                "Failed to submit article generation task",
                extra={
                    "request_id": request_id,
                    "store_id": store_id,
                    "error": str(generation_error)
                },
                stack_info=True
            )
            return error_response(
                message="Failed to submit article generation task",
                error="Task submission failed"
            )

    except Exception as e:
        logger.error(
            "Failed to process article creation request",
            extra={
                "request_id": request_id,
                "error": str(e),
                "error_type": type(e).__name__,
                "store_id": store_id
            }
        )
        return error_response(
            message="Failed to create article",
            error=str(e)
        )

@router.get("/article-status/{task_id}")
async def get_article_status(
    task_id: str,
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get the status of an article generation task
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        logger.info(
            "Checking article task status",
            extra={
                "request_id": request_id,
                "task_id": task_id
            }
        )
        
        result = get_article_task_status.delay(task_id)
        status = result.get(timeout=5)  # Wait up to 5 seconds for the result
        
        # Get article details if task is completed
        article_details = None
        if status.get('status') == 'COMPLETED':
            article_details = await get_article_details_by_task_id(task_id)
        
        logger.info(
            "Article task status retrieved",
            extra={
                "request_id": request_id,
                "task_id": task_id,
                "status": status
            }
        )
        
        return success_response(
            message="Task status retrieved successfully",
            data={
                "status": status,
                "article": article_details if article_details else None
            }
        )
    except Exception as e:
        logger.error(
            "Error retrieving task status",
            extra={
                "request_id": request_id if 'request_id' in locals() else None,
                "task_id": task_id,
                "error": str(e)
            },
            stack_info=True
        )
        return error_response(
            message="Failed to retrieve task status",
            error=str(e)
        )
