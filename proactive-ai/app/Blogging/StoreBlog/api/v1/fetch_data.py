from fastapi import APIRouter, Depends, HTTPException, Request
from typing import List, Optional, Dict
from datetime import datetime

# Import auth middleware
from Middleware.auth_check import verify_tokens
from Utils.Helpers.response_formater import success_response, error_response
from Utils.Helpers.validate_token import validate_auth_data
from Logging.logger_config import get_logger
from Models.dbHelpers import get_number_of_products_for_a_store, get_competitor_domains
from Blogging.StoreBlog.config.presets import ARTICLE_TYPE, LANGUAGES, MODELS, TONES, POV, WORDCOUNT


router = APIRouter()
logger = get_logger(__name__)


# get product count
@router.get("/get-synced-product-count", response_description="Get number of products synced on db")
async def get_synced_product_count(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start 
        logger.info(
            "Fetching number of products synced on our db",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id
            }
        )

        # get product count from db
        product_count = await get_number_of_products_for_a_store(store_id=store_id)
        
        # Log successful analysis
        logger.info(
            "Successfully retrieved product count",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                
            }
        )
        
        return success_response(
            message="Synced product count retrieved successfully",
            data=product_count
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in get_product_count",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to retrieve product count",
            error=str(e)
        )


'Get presets'
@router.get("/get-presets")
async def get_presets(
    request: Request,
) -> Dict:
    """
    Get all preset options for article generation including languages, models,
    tones, point of view, word count types, and article types.
    """
    try:

        logger.info(
            "Starting presets fetch request",
            extra={
                "origin": request.client.host,
            }
        )

        response_data = {
            "languages": LANGUAGES,
            "models": MODELS,
            "tones": TONES,
            "pov": POV,
            "wordcount": WORDCOUNT,
            "article_type": ARTICLE_TYPE
        }

        logger.info(
            "Successfully fetched presets",
        )
        
        return success_response(
            message="Presets fetched successfully",
            data=response_data
        )
            
    except Exception as e:
        logger.error(
            "Failed to fetch presets",
            error=str(e),
            error_type=type(e).__name__,
            stack_info=True,
            extra={
                "origin": request.client.host if 'request' in locals() else None,
            }
        )
        return error_response(
            message="Failed to fetch presets",
            error=str(e)
        )

# fetch competitor domains
@router.get("/fetch-competitor-domains")
async def fetch_competitor_domains(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
) -> Dict:
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start 
        logger.info(
            "Fetching competitor domains",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id
            }
        )

        # get competitor domains from db
        domains = await get_competitor_domains(store_id=store_id)
        
        # Log successful analysis
        logger.info(
            "Successfully retrieved competitor domains",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                
            }
        )
        
        return success_response(
            message="Competitor domains retrieved successfully",
            data=domains
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in fetch_competitor_domains",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to retrieve competitor domains",
            error=str(e)
        )


