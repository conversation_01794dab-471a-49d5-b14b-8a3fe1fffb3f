from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, Field,  HttpUrl
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
from fastapi.encoders import jsonable_encoder

# Import auth middleware
from Middleware.auth_check import verify_tokens
from Auth.api.v1.auth import AuthManager
from Utils import sync_products
from Logging.logger_config import get_logger
from Utils.Helpers.response_formater import success_response, failure_response, error_response, unauthorized_response
from Utils.Helpers.validate_token import validate_auth_data
from Models.dbHelpers import store_competitor_domains, insert_product_sync_job, get_product_sync_job

# Import necessary modules for Celery - TO-DO: Move to Utils or somewhere else
from app.celery_sync.celery_config_products import sync_products_task

router = APIRouter()

class ProductVariantNode(BaseModel):
    id: str
    title: str
    price: str
    position: int
    displayName: str

class ProductVariant(BaseModel):
    cursor: str
    node: ProductVariantNode

class Product(BaseModel):
    id: str
    title: str
    handle: str
    totalInventory: Optional[int]
    tags: List[str]
    vendor: str
    description: str
    createdAt: datetime
    variants: List[ProductVariant]
    image_url: Optional[str]
    product_url: Optional[str]

class CompetitorDomains(BaseModel):
    competitors_domain: List[HttpUrl]

 
logger = get_logger(__name__)

@router.post("/products", response_description="Sync all products from Shopify to Supabase database.")
async def sync_all_products(
    request: Request,
    products: List[Product],
    callback_url: Optional[str] = None,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start of sync operation with all relevant details
        logger.info(
            "Starting products sync by StoreBlog",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "product_count": len(products)
            }
        )

        # Prepare products for the sync function
        products_data = []
        for product in products:
            product_dict = {
                'id': product.id,
                'title': product.title,
                'handle': product.handle,
                'totalInventory': product.totalInventory,
                'tags': product.tags,
                'vendor': product.vendor,
                'description': product.description,
                'createdAt': product.createdAt.isoformat(),
                'variants': [variant.node.dict() for variant in product.variants],
                'image_url': product.image_url,
                'product_url': product.product_url
            }
            products_data.append(product_dict)

        # Submit the task to Celery
        task = sync_products_task.delay(
            products=products_data,
            store_id=store_id,
            callback_url=callback_url
        )
        
        # Store task information in database
        await insert_product_sync_job(
            task_id=task.id,
            store_id=store_id,
            product_count=len(products_data),
            status='QUEUED'
        )
        
        # Log successful queue
        logger.info(
            "Products sync task queued successfully",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "task_id": task.id,
                "product_count": len(products_data)
            }
        )
        
        return success_response(
            message="Products sync task started successfully",
            data={
                "task_id": task.id,
                "products_count": len(products_data),
                "status": "queued"
            }
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in sync_all_products",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to sync products",
            error=str(e)
        )

@router.get("/products/status/{task_id}", response_description="Get the status of a product sync task.")
async def get_product_sync_status(
    task_id: str,
    request: Request,
):
    # Possible statuses: QUEUED, PROCESSING, COMPLETED, FAILED
    try:
        # Get task status from database
        task_info = await get_product_sync_job(task_id)
        
        if not task_info:
            return error_response(
                message="Task not found",
                error="No task found with the specified ID"
            )
            
        return success_response(
            message="Task status retrieved successfully",
            data=task_info
        )
        
    except Exception as e:
        return error_response(
            message="Failed to get task status",
            error=str(e)
        )

@router.post("/competitor-url", response_description="Store competitor domains in Supabase database")
async def store_competitors(
    request: Request,
    competitors: CompetitorDomains,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log operation start
        logger.info(
            "Storing competitor domains",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "domain_count": len(competitors.competitors_domain)
            }
        )

        # Convert domains to list
        domains = [str(domain) for domain in competitors.competitors_domain]
        
        # Store domains using db helper
        result = await store_competitor_domains(
            store_id=store_id,
            domains= domains
        )
        
        if result:
            logger.info(
                "Successfully stored competitor domains",
                extra={
                    "store_id": store_id,
                    "app_id": app_id,
                    "request_id": request_id,
                    "status": "success"
                }
            )
            
            return success_response(
                message="Competitor domains stored successfully",
                data={"stored_domains": domains}
            )
        else:
            logger.error(
                "Failed to store competitor domains",
                extra={
                    "store_id": store_id,
                    "app_id": app_id,
                    "request_id": request_id,
                    "status": "failure"
                }
            )
            return failure_response(
                message="Failed to store competitor domains",
                error="Sync operation failed"
            )
            
    except Exception as e:
        logger.error(
            "Error storing competitor domains",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to store competitor domains",
            error=str(e)
        )