import os
from fastapi import APIRouter
#version-1
from .api.v1.sync import router as sync_router
# from .api.v1.competitor_analysis import router as competitor_analysis_router
from .api.v1.fetch_data import router as fetch_data_router
#version-2
from .api.v2.keywords import router as keywords_router
from .api.v2.articleSettings import router as article_preset_router
from .api.v2.article_creation import router as article_creation_router
from .api.v2.sitemapExtraction import router as sitemap_extraction_router

router = APIRouter()

router.include_router(sync_router, prefix="/sync", tags=["Sync store Data"])
router.include_router(keywords_router, prefix="/keywords", tags=["Targetted Keywords"])
router.include_router(article_preset_router, prefix="/presets", tags=["Configuration for creating article"])
# router.include_router(competitor_analysis_router, prefix="/analyze", tags=["Analyse"])
router.include_router(article_creation_router, prefix="/create", tags=["Article creation"])
router.include_router(fetch_data_router, prefix="/fetch", tags=["Fetch data"])


if os.getenv('ENVIRONMENT') == 'development':
    router.include_router(sitemap_extraction_router, prefix="/scrape", tags=["Sitemap extraction"])





@router.get("/health", tags=["Health"])
async def health_check():
    """
    Health check endpoint to verify API is running
    """
    return {"status": "Store Blog healthy"}
