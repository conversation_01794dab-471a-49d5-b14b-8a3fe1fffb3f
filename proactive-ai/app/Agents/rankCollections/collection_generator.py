import autogen_core
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import os
from pathlib import Path
from dataclasses import dataclass
from autogen_core import (
    MessageContext,
    RoutedAgent,
    SingleThreadedAgentRuntime,
    TopicId,
    type_subscription,
    message_handler,
)
from autogen_core.models import SystemMessage, UserMessage
from autogen_ext.models.openai import AzureOpenAIChatCompletionClient

import uuid
import logging
import random

try:
    from app.Agents.lib.clients.Azure_Client import get_azure_client
    from app.Logging.logger_config import get_logger
    from app.Models.dbHelpers import get_product_title_and_shopify_gid
    from app.Agents.lib.clients.portkey_client import get_portkey_llm_config
    from app.RankCollections.config.constants import (
        MIN_PRODUCTS_IN_COLLECTION,
        MAX_PRODUCTS_IN_COLLECTION
    )
except:
    from Agents.lib.clients.Azure_Client import get_azure_client
    from Logging.logger_config import get_logger
    from Models.dbHelpers import  get_product_title_and_shopify_gid
    from Agents.lib.clients.portkey_client import get_portkey_llm_config
    from RankCollections.config.constants import (
        MIN_PRODUCTS_IN_COLLECTION,
        MAX_PRODUCTS_IN_COLLECTION
    )

logger = get_logger()

# Configure file logging
def setup_agent_logging():
    log_dir = Path(__file__).parent.parent.parent / "logs" / "agents"
    log_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"collection_agents_{timestamp}.log"
    
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    
    # Add file handler to existing logger
    logger.addHandler(file_handler)
    logger.info(f"Agent logging initialized. Log file: {log_file}")

# Add logging setup call after logger initialization
logger = get_logger()
setup_agent_logging()

# Load prompts
def load_prompts() -> dict:
    prompts_path = Path(__file__).parent.parent / "lib" / "prompts" / "rankcollections" / "collection_generation.yml"
    with open(prompts_path, 'r') as file:
        import yaml
        return yaml.safe_load(file)

prompts = load_prompts()

@dataclass
class CollectionMessage:
    content: Dict[str, Any]

def format_refining_agent_response(content: str) -> dict:
    """
    Parse the RefiningAgent response with robust JSON extraction.
    Similar to the article generator's approach.
    """
    try:
        # First try direct JSON parsing
        try:
            response = json.loads(content)
            if "research_data" in response:
                logger.info("Successfully parsed RefiningAgent response using direct JSON parsing")
                return response
        except json.JSONDecodeError as e:
            logger.warning(f"JSON decode error: {str(e)}")
            
            # If that fails, try to extract JSON from markdown code blocks
            if "```json" in content or "```" in content:
                # Extract content between code blocks
                import re
                
                # First try to extract the complete JSON block
                json_block_patterns = [
                    r'```json\n([\s\S]*?)\n```',  # Standard markdown JSON block
                    r'```\n([\s\S]*?)\n```',      # Generic code block
                    r'```([\s\S]*?)```'           # No newlines after backticks
                ]
                
                for pattern in json_block_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        for json_content in matches:
                            json_content = json_content.strip()
                            try:
                                response = json.loads(json_content)
                                if "research_data" in response:
                                    logger.info("Successfully parsed JSON from code block")
                                    return response
                            except json.JSONDecodeError:
                                continue
            
            # If we couldn't parse from code blocks, try to extract just the JSON object
            try:
                # Find the first { and the last } in the content
                start_idx = content.find('{')
                end_idx = content.rfind('}')
                
                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    json_content = content[start_idx:end_idx+1]
                    # Try to balance braces if needed
                    open_braces = json_content.count('{')
                    close_braces = json_content.count('}')
                    
                    if open_braces > close_braces:
                        json_content += '}' * (open_braces - close_braces)
                    
                    try:
                        response = json.loads(json_content)
                        if "research_data" in response:
                            logger.info("Successfully parsed JSON using brace extraction")
                            return response
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON decode error after brace extraction: {str(e)}")
                        
                        # Try a more aggressive approach - find research_data section
                        if '"research_data"' in json_content:
                            try:
                                # Manually construct a valid JSON with just the research_data
                                research_start = json_content.find('"research_data"')
                                if research_start != -1:
                                    # Count braces to find the end of the research_data object
                                    brace_count = 0
                                    in_research_data = False
                                    research_data_json = ""
                                    
                                    for i in range(research_start, len(json_content)):
                                        char = json_content[i]
                                        research_data_json += char
                                        
                                        if char == '{':
                                            brace_count += 1
                                            in_research_data = True
                                        elif char == '}':
                                            brace_count -= 1
                                            if in_research_data and brace_count == 0:
                                                break
                                    
                                    # Construct a valid JSON object
                                    valid_json = '{' + research_data_json + '}'
                                    
                                    try:
                                        partial_response = json.loads(valid_json)
                                        if "research_data" in partial_response:
                                            logger.info("Successfully parsed research_data section")
                                            return {
                                                "research_data": partial_response["research_data"],
                                                "completion_marker": "RESEARCH_COMPLETE"
                                            }
                                    except json.JSONDecodeError:
                                        pass
                            except Exception as e:
                                logger.warning(f"Failed to extract research_data section: {str(e)}")
            except Exception as e:
                logger.warning(f"Failed to extract JSON using braces: {str(e)}")
            
            # Log the failure with detailed information
            logger.error(f"Failed to parse RefiningAgent response: {content[:200]}...")
            logger.debug(f"Full content: {content}")
    except Exception as e:
        logger.error(f"Error parsing RefiningAgent response: {str(e)}")
    
    # Return fallback response with empty structures
    logger.warning("Returning fallback empty response for RefiningAgent")
    return {
        "research_data": {
            "keyword_matches": {},
            "keyword_variations": {}
        },
        "completion_marker": "RESEARCH_COMPLETE"
    }

def format_collection_creation_agent_response(content: str) -> dict:
    try:
        # First try direct JSON parsing
        try:
            response = json.loads(content)
            if "collections" in response:
                return response
        except json.JSONDecodeError:
            # If that fails, try to extract JSON from markdown code blocks
            if "```json" in content or "```" in content:
                # Extract content between code blocks
                import re
                
                # Try different patterns to match the JSON block
                json_block_patterns = [
                    r'```json\n([\s\S]*?)\n```',  # Standard markdown JSON block
                    r'```\n([\s\S]*?)\n```',      # Generic code block
                    r'```([\s\S]*?)```'           # No newlines after backticks
                ]
                
                for pattern in json_block_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        for json_content in matches:
                            json_content = json_content.strip()
                            try:
                                response = json.loads(json_content)
                                if "collections" in response:
                                    logger.info("Successfully parsed JSON from code block")
                                    return response
                            except json.JSONDecodeError:
                                continue
                
                # Try to extract just the JSON object
                try:
                    start_idx = content.find('{')
                    end_idx = content.rfind('}')
                    
                    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                        json_content = content[start_idx:end_idx+1]
                        response = json.loads(json_content)
                        if "collections" in response:
                            logger.info("Successfully parsed JSON using brace extraction")
                            return response
                except Exception as e:
                    logger.warning(f"Failed to extract JSON using braces: {str(e)}")
            
            logger.error(f"Failed to parse CollectionCreationAgent response: {content[:200]}...")
    except Exception as e:
        logger.error(f"Error parsing CollectionCreationAgent response: {str(e)}")
    
    # Return fallback response
    return {
        "collections": [],
        "completion_marker": "COLLECTION_CREATION_COMPLETE"
    }

def format_collection_validator_agent_response(content: str) -> dict:
    try:
        # First try direct JSON parsing
        try:
            response = json.loads(content)
            if "validation_results" in response:
                return response
        except json.JSONDecodeError:
            # If that fails, try to extract JSON from markdown code blocks
            if "```json" in content or "```" in content:
                # Extract content between code blocks
                import re
                
                # Try different patterns to match the JSON block
                json_block_patterns = [
                    r'```json\n([\s\S]*?)\n```',  # Standard markdown JSON block
                    r'```\n([\s\S]*?)\n```',      # Generic code block
                    r'```([\s\S]*?)```'           # No newlines after backticks
                ]
                
                for pattern in json_block_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        for json_content in matches:
                            json_content = json_content.strip()
                            try:
                                response = json.loads(json_content)
                                if "validation_results" in response:
                                    logger.info("Successfully parsed JSON from code block")
                                    return response
                            except json.JSONDecodeError:
                                continue
                
                # Try to extract just the JSON object
                try:
                    start_idx = content.find('{')
                    end_idx = content.rfind('}')
                    
                    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                        json_content = content[start_idx:end_idx+1]
                        response = json.loads(json_content)
                        if "validation_results" in response:
                            logger.info("Successfully parsed JSON using brace extraction")
                            return response
                except Exception as e:
                    logger.warning(f"Failed to extract JSON using braces: {str(e)}")
            
            logger.error(f"Failed to parse CollectionValidatorAgent response: {content[:200]}...")
    except Exception as e:
        logger.error(f"Error parsing CollectionValidatorAgent response: {str(e)}")
    
    # Return fallback response
    return {
        "validation_results": {
            "valid_collections": [],
            "invalid_collections": [],
            "validation_metrics": {}
        },
        "completion_marker": "VALIDATION_COMPLETE"
    }

@type_subscription(topic_type="RefiningAgent")
class RefiningAgent(RoutedAgent):
    def __init__(self, model_client: AzureOpenAIChatCompletionClient):
        super().__init__("A keyword refining agent")
        self._system_message = SystemMessage(
            content=prompts['system_message']['Refining_agent']['objectives']
        )
        self._model_client = model_client
        logger.info("RefiningAgent initialized")

    @message_handler
    async def handle_message(self, message: CollectionMessage, ctx: MessageContext) -> None:
        try:
            logger.info("RefiningAgent processing message")
            
            # Extract keywords and grouped products from message
            keywords = message.content.get("keywords", [])
            grouped_products = message.content.get("grouped_products", {})
            total_products = message.content.get("total_products", 0)
            
            prompt = {
                "task": "refine_keywords",
                "keywords": keywords,
                "grouped_products": grouped_products,
                "total_products": total_products
            }
            
            logger.info(f"RefiningAgent input: {len(keywords)} keywords, {len(grouped_products)} product groups, {total_products} total products")
            
            llm_result = await self._model_client.create(
                messages=[
                    self._system_message,
                    UserMessage(content=json.dumps(prompt), source=self.id.key)
                ],
                cancellation_token=ctx.cancellation_token,
            )
            
            response = format_refining_agent_response(llm_result.content)
            
            # Validate response has at least some keyword matches
            keyword_matches = response.get("research_data", {}).get("keyword_matches", {})
            if not keyword_matches:
                logger.warning("RefiningAgent returned no keyword matches, creating fallback matches")
                # Create fallback matches if none were returned
                if grouped_products:
                    fallback_matches = {}
                    for group_id, group_data in grouped_products.items():
                        # Fix: Check if group_id is a string and isdigit, or if it's already an int
                        if isinstance(group_id, str) and group_id.isdigit():
                            keyword_idx = int(group_id) % len(keywords) if keywords else 0
                        elif isinstance(group_id, int):
                            keyword_idx = group_id % len(keywords) if keywords else 0
                        else:
                            keyword_idx = 0
                        
                        keyword = keywords[keyword_idx] if keywords and keyword_idx < len(keywords) else "default-collection"
                        
                        # Extract product IDs from the group
                        product_ids = [p.get("id", f"product_{i}") for i, p in enumerate(group_data.get("products", []))]
                        
                        if product_ids:
                            fallback_matches[keyword] = {
                                "products": product_ids,
                                "group_metadata": group_data.get("metadata", {})
                            }
                    
                    # Update the response with fallback matches
                    if fallback_matches:
                        response["research_data"]["keyword_matches"] = fallback_matches
                        logger.info(f"Created fallback matches for {len(fallback_matches)} keywords")
            
            # Add total_products to the response for next agent
            response["total_products"] = total_products
            
            logger.info(f"RefiningAgent completed with {len(keyword_matches)} keyword matches")
            
            await self.publish_message(
                CollectionMessage(content=response),
                topic_id=TopicId("CollectionCreationAgent", source=self.id.key)
            )
            
        except Exception as e:
            logger.error(f"Error in RefiningAgent: {str(e)}", exc_info=True)
            raise

@type_subscription(topic_type="CollectionCreationAgent")
class CollectionCreationAgent(RoutedAgent):
    def __init__(self, model_client: AzureOpenAIChatCompletionClient, store_id=None):
        super().__init__("A collection creation agent")
        self._system_message = SystemMessage(
            content=prompts['system_message']['Collection_creation_agent']['objectives']
        )
        self._model_client = model_client
        self.store_id = store_id
        logger.info("CollectionCreationAgent initialized")

    @message_handler
    async def handle_message(self, message: CollectionMessage, ctx: MessageContext) -> None:
        try:
            logger.info("CollectionCreationAgent processing message")
            research_data = message.content.get("research_data", {})
            total_products = message.content.get("total_products", 0)
            
            # Extract keyword matches and variations
            keyword_matches = research_data.get("keyword_matches", {})
            keyword_variations = research_data.get("keyword_variations", {})
            
            # Fetch product data regardless of keyword matches
            products_title_and_shopify_gid = []
            if self.store_id:
                try:
                    from app.Models.dbHelpers import get_all_collections_title_created_by_a_store, get_product_title_and_shopify_gid
                    existing_titles = await get_all_collections_title_created_by_a_store(self.store_id)
                    products_title_and_shopify_gid = await get_product_title_and_shopify_gid(self.store_id)
                    logger.info(f"Fetched {len(existing_titles)} existing collection titles and {len(products_title_and_shopify_gid)} products")
                except Exception as e:
                    logger.error(f"Error fetching existing collection titles or products: {str(e)}")
            
            # If we have no keyword matches but have products, create default keyword matches
            if not keyword_matches and products_title_and_shopify_gid:
                logger.info("No keyword matches received from RefiningAgent, creating default matches")
                
                # Create at least 2 default keyword groups
                default_keywords = ["Featured Products", "Popular Items"]
                
                # Split products into two groups
                total_products = len(products_title_and_shopify_gid)
                half_point = total_products // 2
                
                keyword_matches = {
                    default_keywords[0]: {
                        "products": [p.get("shopify_gid", "") for p in products_title_and_shopify_gid[:half_point]],
                        "group_metadata": {"group_type": "default_group"}
                    },
                    default_keywords[1]: {
                        "products": [p.get("shopify_gid", "") for p in products_title_and_shopify_gid[half_point:]],
                        "group_metadata": {"group_type": "default_group"}
                    }
                }
                
                logger.info(f"Created default keyword matches with {len(keyword_matches)} groups")
            
            # If we still have no keyword matches or no products, create an empty response
            if not keyword_matches:
                logger.warning("No keyword matches available, creating empty response")
                empty_response = {
                    "collections": [],
                    "completion_marker": "COLLECTION_CREATION_COMPLETE",
                    "total_products": total_products
                }
                await self.publish_message(
                    CollectionMessage(content=empty_response),
                    topic_id=TopicId("CollectionValidatorAgent", source=self.id.key)
                )
                return
            
            # Fetch existing collection titles if store_id is available
            existing_titles = []
            if self.store_id:
                try:
                    existing_titles = await get_all_collections_title_created_by_a_store(self.store_id)
                    logger.info(f"Fetched {len(existing_titles)} existing collection titles")
                except Exception as e:
                    logger.error(f"Error fetching existing collection titles: {str(e)}")
            
            prompt = {
                "task": "create_collections",
                "keyword_matches": keyword_matches,
                "keyword_variations": keyword_variations,
                "existing_collection_titles": existing_titles,
                "product_titles_and_shopify_gid": products_title_and_shopify_gid,
                "total_products": total_products,
                "min_products_ratio": 0.25,  # 1/4th of total products
                "max_products_ratio": 0.8    # 4/5th of total products
            }
            
            logger.info(f"CollectionCreationAgent input: {len(keyword_matches)} keyword matches, {len(existing_titles)} existing titles")
            
            llm_result = await self._model_client.create(
                messages=[
                    self._system_message,
                    UserMessage(content=json.dumps(prompt), source=self.id.key)
                ],
                cancellation_token=ctx.cancellation_token,
            )
            
            response = format_collection_creation_agent_response(llm_result.content)
            
            # Add total_products to the response for next agent
            response["total_products"] = total_products
            
            collections = response.get("collections", [])
                        
            logger.info(f"CollectionCreationAgent completed with {len(collections)} collections")
            
            await self.publish_message(
                CollectionMessage(content=response),
                topic_id=TopicId("CollectionValidatorAgent", source=self.id.key)
            )
            
        except Exception as e:
            logger.error(f"Error in CollectionCreationAgent: {str(e)}", exc_info=True)
            raise

@type_subscription(topic_type="CollectionValidatorAgent")
class CollectionValidatorAgent(RoutedAgent):
    def __init__(self, model_client: AzureOpenAIChatCompletionClient, store_id=None):
        super().__init__("A collection validator agent")
        self._system_message = SystemMessage(
            content=prompts['system_message']['Collection_validator_agent']['objectives']
        )
        self._model_client = model_client
        self.store_id = store_id
        logger.info("CollectionValidatorAgent initialized")

    @message_handler
    async def handle_message(self, message: CollectionMessage, ctx: MessageContext) -> None:
        try:
            logger.info("CollectionValidatorAgent processing message")
            collections = message.content.get("collections", [])
            total_products = message.content.get("total_products", 50)
            
            if not collections:
                logger.warning("No collections received from CollectionCreationAgent")
                # Create empty validation response
                empty_response = {
                    "validation_results": {
                        "valid_collections": [],
                        "invalid_collections": [],
                        "validation_metrics": {
                            "total_collections": 0,
                            "valid_collections": 0,
                            "invalid_collections": 0,
                            "collections_per_keyword": 0
                        }
                    },
                    "completion_marker": "VALIDATION_COMPLETE"
                }
                await self.publish_message(
                    CollectionMessage(content=empty_response),
                    topic_id=TopicId("ResultSaverAgent", source=self.id.key)
                )
                return
            
            # Calculate min and max product thresholds based on total products
            min_products = max(3, int(total_products * 0.25))  # At least 3 products or 1/4 of total
            # max_products = max(1, int(total_products * 0.8))  # Ensure at least 1 product, 4/5 of total products
            max_products = total_products # total products
            if self.store_id:
                try:
                    from app.Models.dbHelpers import  get_product_title_and_shopify_gid
                    products_title_and_shopify_gid = await get_product_title_and_shopify_gid(self.store_id)
                    logger.info(f"Fetched {len(products_title_and_shopify_gid)} products")
                except Exception as e:
                    logger.error(f"Error fetching products: {str(e)}")
            
            prompt = {
                "task": "validate_collections",
                "collections": collections,
                "total_products": total_products,
                "products_title_and_shopify_gid": products_title_and_shopify_gid,
                "min_products": 1,
                "max_products": max_products
            }
            
            logger.info(f"CollectionValidatorAgent input: {len(collections)} collections, min_products={min_products}, max_products={max_products}")
            
            llm_result = await self._model_client.create(
                messages=[
                    self._system_message,
                    UserMessage(content=json.dumps(prompt), source=self.id.key)
                ],
                cancellation_token=ctx.cancellation_token,
            )
            
            response = format_collection_validator_agent_response(llm_result.content)
            
            # Extract validation results
            validation_results = response.get("validation_results", {})
            valid_collections = validation_results.get("valid_collections", [])
            invalid_collections = validation_results.get("invalid_collections", [])
            
            logger.info(f"Validation completed: {len(valid_collections)} valid, {len(invalid_collections)} invalid collections")
            
            await self.publish_message(
                CollectionMessage(content=response),
                topic_id=TopicId("ResultSaverAgent", source=self.id.key)
            )
            
        except Exception as e:
            logger.error(f"Error in CollectionValidatorAgent: {str(e)}", exc_info=True)
            raise

@type_subscription(topic_type="ResultSaverAgent")
class ResultSaverAgent(RoutedAgent):
    """Agent that saves the final results of the workflow."""
    
    _final_message = None
    
    def __init__(self, store_id=None, task_id=None):
        super().__init__("A result saver agent")
        self.store_id = store_id
        self.task_id = task_id
        logger.info("ResultSaverAgent initialized")
    
    @classmethod
    def get_final_message(cls):
        """Get the final message saved by the agent."""
        return cls._final_message
    
    @classmethod
    def reset_final_message(cls):
        """Reset the final message."""
        cls._final_message = None
    
    @message_handler
    async def handle_collection_message(self, message: CollectionMessage, ctx: MessageContext) -> str:
        """Handle collection messages from other agents."""
        logger.info("ResultSaverAgent processing message")
        
        # Extract content from the message
        content = message.content
        
        # Check if this is a validation result message
        if isinstance(content, dict) and "validation_results" in content:
            valid_collections = content.get("validation_results", {}).get("valid_collections", [])
            
            if valid_collections:
                logger.info(f"Generated {len(valid_collections)} valid collections")
                
                # Format the final result
                final_result = {
                    "collections": valid_collections,
                    "processing_metadata": {
                        "total_collections_processed": len(valid_collections),
                        "total_collections_created": len(valid_collections),
                        "completion_status": "success"
                    }
                }

                #Order the collections by product count in it
                final_result["collections"] = sorted(final_result["collections"], key=lambda x: (
                    x.get("collection_title", ""),
                    len(x.get("products_list", []))
                ), reverse=True)
                                
                logger.info(f"@@@@@@@@@@@@@@@SORTED COLLECTIONS: {final_result['collections']}")    
                #Limit the collections to 3
                final_result["collections"] = final_result["collections"][:3]
                logger.info(f"@@@@@@@@@@@@@@@LIMITED COLLECTIONS: {final_result['collections']}")
                
                # Save the final message
                ResultSaverAgent._final_message = final_result
            else:
                logger.warning("No valid collections to save")
        
        # Check if this is a direct collection message
        elif isinstance(content, dict) and "collections" in content:
            collections = content.get("collections", [])
            
            if collections:
                logger.info(f"Generated {len(collections)} collections")
                
                # Format the final result
                final_result = {
                    "collections": collections,
                    "processing_metadata": {
                        "total_collections_processed": len(collections),
                        "total_collections_created": len(collections),
                        "completion_status": "success"
                    }
                }

                #Order the collections by product count in it
                final_result["collections"] = sorted(final_result["collections"], key=lambda x: (
                    x.get("collection_title", ""),
                    len(x.get("products_list", []))
                ), reverse=True)
                
                logger.info(f"@@@@@@@@@@@@@@@SORTED COLLECTIONS: {final_result['collections']}")
                #Limit the collections to 3
                final_result["collections"] = final_result["collections"][:3]
                logger.info(f"@@@@@@@@@@@@@@@LIMITED COLLECTIONS: {final_result['collections']}")
                
                # Save the final message
                ResultSaverAgent._final_message = final_result
            else:
                logger.warning("No collections to save")
        else:
            logger.warning(f"Unrecognized message format: {type(content)}")
            
        # Send a confirmation message
        return "RESULT_SAVED"

class CollectionWorkflowManager:
    def __init__(self, model_client: AzureOpenAIChatCompletionClient):
        self.model_client = model_client
        self.prompts = load_prompts()
        self.runtime = SingleThreadedAgentRuntime()
        logger.info("CollectionWorkflowManager initialized")

    async def setup_agents(self, store_id: int, task_id: str):
        try:
            await RefiningAgent.register(
                self.runtime,
                type="RefiningAgent",
                factory=lambda: RefiningAgent(model_client=self.model_client)
            )
            
            await CollectionCreationAgent.register(
                self.runtime,
                type="CollectionCreationAgent",
                factory=lambda: CollectionCreationAgent(model_client=self.model_client, store_id=store_id)
            )
            
            await CollectionValidatorAgent.register(
                self.runtime,
                type="CollectionValidatorAgent",
                factory=lambda: CollectionValidatorAgent(model_client=self.model_client, store_id=store_id)
            )
            
            await ResultSaverAgent.register(
                self.runtime,
                type="ResultSaverAgent",
                factory=lambda: ResultSaverAgent(store_id=store_id, task_id=task_id)
            )
            
            logger.info("All collection agents registered successfully")
            
        except Exception as e:
            logger.error(f"Error setting up collection agents: {str(e)}", exc_info=True)
            raise

    async def run_workflow(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        try:
            if not self.runtime:
                raise RuntimeError("Runtime not initialized")

            logger.info("Starting collection workflow runtime")
            self.runtime.start()
            
            await self.runtime.publish_message(
                CollectionMessage(content=payload),
                topic_id=TopicId("RefiningAgent", source="default")
            )

            logger.info("Waiting for collection workflow completion")
            await self.runtime.stop_when_idle()
            await self.runtime.close()

        except Exception as e:
            logger.error(f"Error in collection workflow execution: {str(e)}", exc_info=True)
            raise

        finally:
            if getattr(self.runtime, '_started', False):
                await self.runtime.stop()
                logger.info("Collection workflow runtime stopped")
 
async def create_collections(
    keywords: List[str],
    products: List[Dict[str, Any]],
    store_id: int,
    grouped_products: Dict[str, Dict[str, Any]],
    attributes: Optional[Dict[str, Dict[str, bool]]] = None,
    task_id: Optional[str] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create collections using pre-filtered product groups"""
    try:
        model_client = await get_azure_client()
        
        # Use provided task_id or generate a new one
        task_id = task_id or str(uuid.uuid4())
        request_id = request_id or str(uuid.uuid4())
        
        logger.info(f"Starting collection creation for task {task_id}, request {request_id}")

        workflow_manager = CollectionWorkflowManager(model_client)
        await workflow_manager.setup_agents(store_id, task_id)

        product_titles_and_descriptions = await get_product_title_and_shopify_gid(store_id)
        logger.info(f"Grouped products: {json.dumps(grouped_products, indent=2)}")
        # Prepare payload with required data structure
        payload = {
            "keywords": keywords,
            "grouped_products": grouped_products,
            # "product_titles_and_descriptions": product_titles_and_descriptions,
            # "attributes": attributes,
            "total_products": len(products),
            "task_id": task_id,
        }
        
        await workflow_manager.run_workflow(payload)
        collection_data = ResultSaverAgent.get_final_message()
        
        if not collection_data or not collection_data.get('collections'):
            logger.warning("No collections generated by agents")
            return {
                "status": "success",
                "collections": [],
                "timestamp": datetime.now().isoformat(),
                "task_id": task_id,
                "request_id": request_id,
                "metadata": {
                    "total_products": len(products),
                    "filtered_products": sum(len(g["products"]) for g in grouped_products.values()),
                    "method": "clustering" if "cluster" in str(grouped_products) else "similarity",
                    "keywords_used": keywords
                }
            }
        
        return collection_data

    except Exception as e:
        logger.error("Fatal error in collection creation", exc_info=True)
        raise

async def _validate_product_ids_in_collections(self, collections_result: Dict[str, Any]) -> None:
    """
    Ensure collections only include existing product IDs.
    
    Args:
        collections_result: Dictionary with collection results
    """
    if not collections_result or "collections" not in collections_result:
        return
    
    # Get all valid product IDs for the store
    all_products = await get_product_title_and_shopify_gid(self.store_id)
    valid_product_ids = set(p.get("shopify_gid", "") for p in all_products if p.get("shopify_gid"))
    
    print(f"Found {len(valid_product_ids)} valid product IDs")
    
    # Filter collections to only include valid product IDs
    for collection in collections_result["collections"]:
        if "products" in collection:
            # Filter to only include valid product IDs
            valid_products = [pid for pid in collection["products"] if pid in valid_product_ids]
            
            # If we lost too many products, add some random valid ones
            if len(valid_products) < 3 and valid_product_ids:
                needed = 3 - len(valid_products)
                # Get product IDs not already in the collection
                available_ids = list(valid_product_ids - set(valid_products))
                
                if available_ids:
                    # Add random valid product IDs
                    valid_products.extend(random.sample(available_ids, min(needed, len(available_ids))))
            
            # Update the collection with only valid products
            collection["products"] = valid_products
            
            print(f"Collection '{collection.get('title', '')}' has {len(valid_products)} valid products")
            
    collections_with_products = [c for c in collections_result["collections"] if c.get("products")]
    
    # if len(collections_with_products) < 2:
    #     print("Less than 2 collections with valid products, adding fallback collections")
        
    #     # Create fallback collections
    #     if valid_product_ids:
    #         valid_product_ids_list = list(valid_product_ids)
            
    #         # Add fallback collections until we have at least 2
    #         fallback_collections = []
            
    #         # Create properly formatted fallback collections
    #         if len(valid_product_ids_list) >= 5:
    #             fallback_collections = [
    #                 {
    #                     "title": "Featured Products",
    #                     "description": "A curated selection of our featured products",
    #                     "collection_title": "Featured Products",
    #                     "collection_description": "A curated selection of our featured products",
    #                     "collection_metatitle": "Featured Products - Shop Our Best Items",
    #                     "collection_metadescription": "Discover our featured products, carefully selected to showcase the best items in our store.",
    #                     "collection_slug": "featured-products",
    #                     "meta_keywords": ["featured", "products", "best"],
    #                     "target_keyword": "featured",
    #                     "products": random.sample(valid_product_ids_list, min(5, len(valid_product_ids_list)))
    #                 },
    #                 {
    #                     "title": "Popular Items",
    #                     "description": "Our most popular items",
    #                     "collection_title": "Popular Items",
    #                     "collection_description": "Our most popular items that customers love",
    #                     "collection_metatitle": "Popular Items - Customer Favorites",
    #                     "collection_metadescription": "Browse our most popular items that customers love. Find top-rated products and bestsellers in our collection.",
    #                     "collection_slug": "popular-items",
    #                     "meta_keywords": ["popular", "bestsellers", "favorites"],
    #                     "target_keyword": "popular",
    #                     "products": random.sample(valid_product_ids_list, min(5, len(valid_product_ids_list)))
    #                 }
    #             ]
            
    #         current_count = len(collections_with_products)
    #         needed_count = max(0, 2 - current_count)
            
    #         if needed_count > 0:
    #             collections_result["collections"].extend(fallback_collections[:needed_count])
