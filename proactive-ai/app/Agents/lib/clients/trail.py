import autogen

# Import the portkey library to fetch helper functions
from portkey_ai import PORTKEY_GATEWAY_URL, createHeaders

portkey_llm_config = [
    {
        "api_key": 'X',
        "model": "gpt-4o",
        "base_url": PORTKEY_GATEWAY_URL,
        "api_type": "openai", 
         "default_headers": createHeaders(
            api_key = "T0lx1Ib5A33bvWIq2RUOUjIUvhNT",
            virtual_key = "azure-openai-vi-162779",
        )
    }
]

# Create user proxy agent, coder, product manager
user_proxy = autogen.UserProxyAgent(
    name="User_proxy",
    system_message="A human admin who will give the idea for a blog post to write about",
    code_execution_config={"last_n_messages": 2, "work_dir": "groupchat", "use_docker": False},
    human_input_mode="ALWAYS",
)

writer = autogen.AssistantAgent(
    name="Coder",
    system_message="You are talented content writer. You closely work with Product Manager.",
    llm_config={"config_list": portkey_llm_config}
)

pm = autogen.AssistantAgent(
    name="product_manager",
    system_message="You will help break down the initial idea into a well scoped requirement for the writer; Do not involve in future conversations or error fixing",
    llm_config={"config_list": portkey_llm_config},
)

# Create groupchat
groupchat = autogen.GroupChat(
    agents=[user_proxy, writer, pm], messages=[])
manager = autogen.GroupChatManager(groupchat=groupchat, llm_config={"config_list": portkey_llm_config})


def startConvo():
    # Start the conversation
    user_proxy.initiate_chat(
        manager, message="write a blog post on air conditioners"
    )
