from dotenv import load_dotenv
import os
from autogen_ext.models.openai import AzureOpenAIChatCompletionClient
from portkey_ai import PORTKEY_GATEWAY_URL, createHeaders


# Load the .env file
load_dotenv()


async def get_azure_client():
    """Initialize Azure OpenAI client with configuration"""

    return AzureOpenAIChatCompletionClient(
    model="gpt-4o",
    api_base=PORTKEY_GATEWAY_URL,
    default_headers=createHeaders(
        api_key = os.getenv('PORTKEY_API_KEY'),
        virtual_key = os.getenv('PORTKEY_VIRTUAL_KEY')
        ),
        api_type="openai",
        api_version="2024-08-01-preview",
    )