from dotenv import load_dotenv
import os
from portkey_ai import Portkey, PORTKEY_GATEWAY_URL, createHeaders
import asyncio  # Import asyncio to run the async function


# Load the .env file
load_dotenv()

# Construct a client with a virtual key 
portkey = Portkey(
        api_key = os.getenv('PORTKEY_API_KEY'),
        virtual_key = os.getenv('PORTKEY_VIRTUAL_KEY'),
        base_url=os.getenv("PORTKEY_BASE_URL"),
)

def get_portkey_client():
    'This function returns the portkey client with virtual key (Azure Key)'
    return portkey

def get_portkey_llm_config(store_uuid):
    """
    This function is used to get the Portkey LLM configuration.
    Args:
        store.uuid (uuid): Adds this as traceID to portkey for logging conviniance
    Returns:
        Portkey configuration
    """
    try:
        return [{
            "api_key": 'X',
            "model": "gpt-4o",
            "base_url": PORTKEY_GATEWAY_URL,
            "api_type": "openai", 
            "default_headers": createHeaders(
                api_key = os.getenv('PORTKEY_API_KEY'),
                virtual_key = os.getenv('PORTKEY_VIRTUAL_KEY'),
                trace_id = store_uuid
            )
        }]
    except Exception as e:
        raise Exception (f"Error Generating portkey llm config : {str(e)}")


'''
Trail functions - POC ---- below this line
'''


portkey_llm_config = [
    {
        "api_key": 'X',
        "model": "gpt-4o",
        "base_url": PORTKEY_GATEWAY_URL,
        "api_type": "openai", 
         "default_headers": createHeaders(
            api_key = os.getenv('PORTKEY_API_KEY'),
            virtual_key = os.getenv('PORTKEY_VIRTUAL_KEY'),
            trace_id = '123' 
        )
    }
]



async def retrive_prompt_data(prompt_id, variables):
    try:
        render = portkey.prompts.render(
            prompt_id=prompt_id,
            variables=variables
        )

        prompt = render.data

        print("PRINTING PROMPT@@@@@@@@@@@@",prompt)

        return prompt
    except Exception as e:
        raise Exception(f"Error retriving prompt from portkey : {str(e)}")


if __name__ == "__main__":
    # Example usage
    async def main():
        await retrive_prompt_data('pp-outline-ge-5fc1cb', {
            "keyword": "Harry Potter and the Order of phoenix",
            "tone_of_article": "Casual",
            "point_of_view": "Third Person",
            "type_of_article": "Movie-review"
        })

    asyncio.run(main())  # Run the main async function