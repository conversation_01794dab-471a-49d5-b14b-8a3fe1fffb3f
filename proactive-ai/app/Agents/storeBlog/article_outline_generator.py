#generate article outline
import json
import yaml
from typing import List, Dict, Any
from Logging.logger_config import get_logger
from fastapi import HTT<PERSON>Exception
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken
from Agents.lib.clients.Azure_Client import get_azure_client

logger = get_logger()

async def load_prompt_config(file_path: str) -> dict:
    """Load and validate the prompt configuration from YAML file."""
    logger.debug(f"Attempting to load prompt config from: {file_path}")
    try:
        with open(file_path, 'r') as file:
            config = yaml.safe_load(file)
            logger.info("Successfully loaded prompt configuration")
            return config
    except Exception as e:
        logger.error(f"Failed to load prompt configuration: {str(e)}", exc_info=True)
        raise

async def create_outline_generator() -> AssistantAgent:
    """Creates and configures the outline generator agent."""
    prompt = await load_prompt_config('Agents/lib/prompts/storeblog/generate_article_outline.yml')
    
    # Combine all prompt components into a comprehensive system message
    system_message = (
        f"{prompt['system_message']['Outline_Generator']['objectives']}\n\n"
        f"Structural Rules:\n{prompt['system_message']['Outline_Generator']['structural_rules']}\n\n"
        f"Content Patterns:\n{prompt['system_message']['Outline_Generator']['content_patterns']}\n\n"
        f"Search Intent:\n{prompt['system_message']['Outline_Generator']['search_intent_optimization']}\n\n"
        f"Quality Checklist:\n{prompt['system_message']['Outline_Generator']['quality_checklist']}"
    )
    
    return AssistantAgent(
        name="outline_generator",
        system_message=system_message,
        model_client=await get_azure_client()
    )

async def create_outline_regenerator() -> AssistantAgent:
    """Creates and configures the outline regenerator agent."""
    prompt = await load_prompt_config('Agents/lib/prompts/storeblog/regenerate_article_outline.yml')
    
    # Combine all prompt components into a comprehensive system message
    system_message = (
        f"{prompt['system_message']['Outline_Regeneration']['objectives']}\n\n"
        f"Structural Rules:\n{prompt['system_message']['Outline_Regeneration']['structural_rules']}\n\n"
        f"Content Patterns:\n{prompt['system_message']['Outline_Regeneration']['content_patterns']}\n\n"
        f"Search Intent:\n{prompt['system_message']['Outline_Regeneration']['search_intent_optimization']}\n\n"
        f"Quality Checklist:\n{prompt['system_message']['Outline_Regeneration']['quality_checklist']}"
    )
    
    return AssistantAgent(
        name="outline_regenerator",
        system_message=system_message,
        model_client=await get_azure_client()
    )

async def generate_article_outline(
    target_keyword: List[str],
    article_type: str,
    title_of_the_article: str,
    minimum_subtitles: int = 2,
    maximum_subtitles: int = 3,
    store_id: int = None
) -> Dict[str, Any]:
    """Generate SEO-optimized article outlines using an AI agent."""
    try:
        # Create outline generator agent
        outline_generator = await create_outline_generator()
        
        # Prepare the initial message
        initial_message = {
            "task": "generate_outlines",
            "parameters": {
                "target_keyword": target_keyword,
                "article_type": article_type,
                "title_of_the_article": title_of_the_article,
                "minimum_subtitles": minimum_subtitles,
                "maximum_subtitles": maximum_subtitles
            }
        }

        # Generate outline
        response = await outline_generator.on_messages(
            [TextMessage(content=json.dumps(initial_message), source="user")],
            CancellationToken()
        )
        
        # Extract the content from the TextMessage object
        content = response.chat_message.content
        
        # Clean up the content by removing markdown code block formatting
        content = content.replace("```json", "").replace("```", "").strip()
        
        # Parse the content as JSON
        try:
            result = json.loads(content)
            return {
                "data": result
            }
        except json.JSONDecodeError:
            return {
                "data": {"error": "Failed to parse response"}
            }

    except Exception as e:
        logger.error("Error generating outline", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "message": "Failed to generate article outline"
            }
        )

async def regenerate_article_outline(
    article_outline: List[Dict[str, Any]],
    subtitle_to_regenerate: Dict[str, Any],
    store_id: int = None
) -> Dict[str, Any]:
    """Regenerate a specific subtitle in an article outline."""
    try:
        # Create outline regenerator agent
        outline_regenerator = await create_outline_regenerator()
        
        # Prepare the initial message
        initial_message = {
            "task": "regenerate_outline",
            "parameters": {
                "article_outline": article_outline,
                "subtitle_to_regenerate": subtitle_to_regenerate
            }
        }

        # Generate outline
        response = await outline_regenerator.on_messages(
            [TextMessage(content=json.dumps(initial_message), source="user")],
            CancellationToken()
        )
        
        # Extract the content from the TextMessage object
        content = response.chat_message.content
        
        # Clean up the content by removing markdown code block formatting
        content = content.replace("```json", "").replace("```", "").strip()
        
        # Parse the content as JSON
        try:
            result = json.loads(content)
            return {
                "data": result
            }
        except json.JSONDecodeError:
            return {
                "data": {"error": "Failed to parse response"}
            }

    except Exception as e:
        logger.error("Error regenerating outline", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "message": "Failed to regenerate article outline"
            }
        )