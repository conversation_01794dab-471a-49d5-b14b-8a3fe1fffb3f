# app/Agents/storeBlog/article_generator.py
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import os
import re
from pathlib import Path
from app.Logging.logger_config import get_logger
from app.Models.dbHelpers import get_store_uuid, update_article_job_status, insert_article_data
from app.Agents.lib.clients.portkey_client import get_portkey_llm_config
from dataclasses import dataclass
from autogen_core import (
    MessageContext,
    RoutedAgent,
    SingleThreadedAgentRuntime,
    TopicId,
    type_subscription,
    message_handler,
    CancellationToken
)
from autogen_core.models import SystemMessage, UserMessage
from autogen_ext.models.openai import AzureOpenAIChatCompletionClient
from app.Agents.lib.clients.Azure_Client import get_azure_client
from autogen_core.tools import FunctionTool
from typing_extensions import Annotated
import uuid

logger = get_logger()

# Load prompts
def load_prompts() -> dict:
    prompts_path = Path(__file__).parent.parent / "lib" / "prompts" / "storeblog" / "article_generation.yml"
    with open(prompts_path, 'r') as file:
        import yaml
        return yaml.safe_load(file)

prompts = load_prompts()

# Word count tool implementation
async def calculate_word_count(content: str) -> dict:
    """Calculates word count of text content excluding HTML tags"""
    logger.info(f"******************Calculating word count for content****************************")
    clean_text = re.sub(r'<[^>]+>', '', content).strip()
    words = [w for w in clean_text.split() if w]
    return {'wordCount': len(words)}

# Create FunctionTool instance
word_count_tool = FunctionTool(calculate_word_count, description="Calculate word count of HTML content")

# Core workflow configuration as a constant
WORKFLOW_CONFIG = {
    "workflow": [
        {
            "type": "researcher",
            "topic_type": "ResearcherAgent",
            "next": "content_creator",
            "description": "Analyzes input data and researches article topics"
        },
        {
            "type": "content_creator",
            "topic_type": "ContentCreatorAgent",
            "next": "product_integrator",
            "description": "Creates HTML-formatted content"
        },
        {
            "type": "product_integrator",
            "topic_type": "ProductIntegratorAgent",
            "next": "editor",
            "description": "Integrates products and internal product links"
        },
        {
            "type": "editor",
            "topic_type": "EditorAgent",
            "next": "result_saver",
            "description": "Reviews and finalizes the article"
        },
        {
            "type": "result_saver",
            "topic_type": "ResultSaverAgent",
            "next": None,
            "description": "Saves the article to the database"
        }
    ],
    "error_handling": {
        "required_completion_markers": [
            "RESEARCH_COMPLETE",
            "CONTENT_COMPLETE",
            "PRODUCT_INTEGRATION_COMPLETE",
            "REVIEW_COMPLETE"
        ]
    }
}

@dataclass
class Message:
    content: Dict[str, Any]

def format_researcher_response(content: str) -> dict:
    try:
        response = json.loads(content)
        if "research_data" in response:
            return response
    except json.JSONDecodeError:
        pass
    
    return {
        "research_data": {
            "primary_topics": [],
            "topic_product_mapping": {},
            "seo_elements": {
                "keywords": [],
                "meta_description_components": []
            },
            "content_guidelines": {
                "tone": "",
                "style": "",
                "key_points": []
            }
        },
        "raw_content": content,
        "completion_marker": "RESEARCH_COMPLETE"
    }

def format_content_creator_response(content: str) -> dict:
    try:
        response = json.loads(content)
        if "content" in response:
            return response
    except json.JSONDecodeError:
        pass
    
    return {
        "content": {
            "sections": [
                {
                    "content": content,
                    "word_count": len(content.split()),
                    "product_slots": []
                }
            ],
            "total_word_count": len(content.split()),
            "meta_title": "",
            "meta_description": "",
            "slug": ""
        },
        "raw_content": content,
        "completion_marker": "CONTENT_COMPLETE"
    }

def format_product_integrator_response(content: str) -> dict:
    try:
        response = json.loads(content)
        if "content" in response and "integration_stats" in response:
            return response
    except json.JSONDecodeError:
        pass
    
    return {
        "content": {
            "sections": [content],
            "meta_title": "",
            "meta_description": "",
            "slug": "",
            "link_added": [],
            "product_added": [],
            "integration_stats": {
                "total_products": 0,
                "total_links": 0
            }
        },
        "raw_content": content,
        "completion_marker": "PRODUCT_INTEGRATION_COMPLETE"
    }

def format_editor_response(content: str) -> dict:
    try:
        response = json.loads(content)
        if "validation" in response:
            return response
    except json.JSONDecodeError:
        pass
    
    # Create a default response structure using the raw content
    return {
        "result": content,  # Use the raw content directly
        "completion_marker": "REVIEW_COMPLETE"
    }

@type_subscription(topic_type="ResearcherAgent")
class ResearcherAgent(RoutedAgent):
    def __init__(self, model_client: AzureOpenAIChatCompletionClient):
        super().__init__("A researcher agent")
        self._system_message = SystemMessage(
            content=prompts['system_message']['Researcher']['objectives']
        )
        self._model_client = model_client

    @message_handler
    async def handle_message(self, message: Message, ctx: MessageContext) -> None:
        try:
            logger.info("ResearcherAgent processing message")
            prompt = f"Research task: {json.dumps(message.content)}"
            
            llm_result = await self._model_client.create(
                messages=[self._system_message, UserMessage(content=prompt, source=self.id.key)],
                cancellation_token=ctx.cancellation_token,
            )
            
            response = format_researcher_response(llm_result.content)
            await self.publish_message(
                Message(content=response),
                topic_id=TopicId("ContentCreatorAgent", source=self.id.key)
            )
        except Exception as e:
            logger.error(f"Error in ResearcherAgent: {str(e)}", exc_info=True)
            raise

# @type_subscription(topic_type="ContentCreatorAgent")
# class ContentCreatorAgent(RoutedAgent):
#     def __init__(self, model_client: AzureOpenAIChatCompletionClient, use_product_integrator: bool = True):
#         super().__init__("A content creator agent")
#         self._system_message = SystemMessage(
#             content=prompts['system_message']['Content_Creator']['objectives']
#         )
#         self._model_client = model_client
#         self._use_product_integrator = use_product_integrator

#     @message_handler
#     async def handle_message(self, message: Message, ctx: MessageContext) -> None:
#         try:
#             logger.info(f"ContentCreatorAgent processing message")
#             prompt = f"Create HTML content from research: {json.dumps(message.content)}"
            
#             llm_result = await self._model_client.create(
#                 messages=[self._system_message, UserMessage(content=prompt, source=self.id.key)],
#                 cancellation_token=ctx.cancellation_token,
#             )
            
#             response = format_content_creator_response(llm_result.content)
            
#             # Determine the next agent based on whether product integration is needed
#             next_agent = "ProductIntegratorAgent" if self._use_product_integrator else "EditorAgent"
            
#             await self.publish_message(
#                 Message(content=response),
#                 topic_id=TopicId(next_agent, source=self.id.key)
#             )
#         except Exception as e:
#             logger.error(f"Error in ContentCreatorAgent: {str(e)}", exc_info=True)
#             raise

# Modified ContentCreatorAgent with word count validation
@type_subscription(topic_type="ContentCreatorAgent")
class ContentCreatorAgent(RoutedAgent):
    def __init__(self, model_client: AzureOpenAIChatCompletionClient, use_product_integrator: bool = True):
        super().__init__("A content creator agent")
        self._system_message = SystemMessage(
            content=prompts['system_message']['Content_Creator']['objectives']
        )
        self._model_client = model_client
        self._use_product_integrator = use_product_integrator
        self.max_attempts = 3
        self.upper_deviation = 0.07  # +7%
        self.lower_deviation = 0.03   # -3%
        self.word_count_tool = word_count_tool  # Use the FunctionTool instance

    def set_tolerance(self, upper: float, lower: float):
        self.upper_deviation = upper
        self.lower_deviation = lower
        return self

    @message_handler
    async def handle_message(self, message: Message, ctx: MessageContext) -> None:
        try:
            logger.info(f"ContentCreatorAgent processing message")
            target_word_count = message.content.get('word_count', 1000)
            current_attempt = 0
            final_content = None
            
            while current_attempt < self.max_attempts:
                current_attempt += 1
                prompt = self._build_prompt(message.content, target_word_count, current_attempt)
                
                llm_result = await self._model_client.create(
                    messages=[self._system_message, UserMessage(content=prompt, source=self.id.key)],
                    cancellation_token=ctx.cancellation_token,
                )
                
                response = format_content_creator_response(llm_result.content)
                content = response['content']['sections'][0]['content']
                
                # Use the FunctionTool with cancellation token
                count_result = await self.word_count_tool.run_json({"content": content}, ctx.cancellation_token)
                
                if self._validate_word_count(count_result['wordCount'], target_word_count):
                    final_content = content
                    logger.info(f"Word count validated: {count_result['wordCount']}/{target_word_count}")
                    break
                
                logger.warning(f"Attempt {current_attempt}: {count_result['wordCount']} words (Target: {target_word_count})")

            # Handle final content or fallback
            if final_content is None:
                logger.warning("Failed to meet word count after 3 attempts. Using best result")
                final_content = content
                response['content']['word_count_warning'] = True

            # Get final word count
            final_count = await self.word_count_tool.run_json({"content": final_content}, ctx.cancellation_token)
            response['content']['sections'][0]['content'] = final_content
            response['content']['calculated_word_count'] = final_count['wordCount']
            
            next_agent = "ProductIntegratorAgent" if self._use_product_integrator else "EditorAgent"
            await self.publish_message(
                Message(content=response),
                topic_id=TopicId(next_agent, source=self.id.key)
            )

        except Exception as e:
            logger.error(f"Error in ContentCreatorAgent: {str(e)}", exc_info=True)
            raise

    def _build_prompt(self, content: dict, target: int, attempt: int) -> str:
        base_prompt = f"Create HTML content from research: {json.dumps(content)}\n"
        base_prompt += f"Target word count: {target} (±{self.lower_deviation*100}%/+{self.upper_deviation*100}%)"
        
        if attempt > 1:
            base_prompt += "\nPrevious attempts didn't meet word count requirements. "
            base_prompt += "Adjust content length while maintaining key information."
        
        return base_prompt

    def _validate_word_count(self, actual: int, target: int) -> bool:
        lower_bound = target * (1 - self.lower_deviation)
        upper_bound = target * (1 + self.upper_deviation)
        return lower_bound <= actual <= upper_bound

@type_subscription(topic_type="ProductIntegratorAgent")
class ProductIntegratorAgent(RoutedAgent):
    def __init__(self, model_client: AzureOpenAIChatCompletionClient):
        super().__init__("A product integrator agent")
        self._system_message = SystemMessage(
            content=prompts['system_message']['Product_Integrator']['objectives']
        )
        self._model_client = model_client

    @message_handler
    async def handle_message(self, message: Message, ctx: MessageContext) -> None:
        try:
            logger.info(f"ProductIntegratorAgent processing message")
            prompt = f"Integrate products into content: {json.dumps(message.content)}"
            
            llm_result = await self._model_client.create(
                messages=[self._system_message, UserMessage(content=prompt, source=self.id.key)],
                cancellation_token=ctx.cancellation_token,
            )
            
            response = format_product_integrator_response(llm_result.content)
            
            await self.publish_message(
                Message(content=response),
                topic_id=TopicId("EditorAgent", source=self.id.key)
            )
        except Exception as e:
            logger.error(f"Error in ProductIntegratorAgent: {str(e)}", exc_info=True)
            raise

@type_subscription(topic_type="EditorAgent")
class EditorAgent(RoutedAgent):
    def __init__(self, model_client: AzureOpenAIChatCompletionClient):
        super().__init__("An editor agent")
        self._system_message = SystemMessage(
            content=prompts['system_message']['Editor']['objectives']
        )
        self._model_client = model_client

    @message_handler
    async def handle_message(self, message: Message, ctx: MessageContext) -> None:
        try:
            logger.info("EditorAgent processing message")
            prompt = f"Review and edit content: {json.dumps(message.content)}"
            
            llm_result = await self._model_client.create(
                messages=[self._system_message, UserMessage(content=prompt, source=self.id.key)],
                cancellation_token=ctx.cancellation_token,
            )
            
            response = format_editor_response(llm_result.content)
            
            await self.publish_message(
                Message(content=response),
                topic_id=TopicId("ResultSaverAgent", source=self.id.key)
            )
            
        except Exception as e:
            logger.error(f"Error in EditorAgent: {str(e)}", exc_info=True)
            raise

# Modified EditorAgent with separate tolerance
# @type_subscription(topic_type="EditorAgent")
# class EditorAgent(RoutedAgent):
#     def __init__(self, model_client: AzureOpenAIChatCompletionClient):
#         super().__init__("An editor agent")
#         self._system_message = SystemMessage(
#             content=prompts['system_message']['Editor']['objectives']
#         )
#         self._model_client = model_client
#         self.max_attempts = 5
#         self.upper_deviation = 0.05  # +5%
#         self.lower_deviation = 0.02   # -2%
#         self.word_count_tool = word_count_tool  # Use the FunctionTool instance

#     def set_tolerance(self, upper: float, lower: float):
#         self.upper_deviation = upper
#         self.lower_deviation = lower
#         return self

#     @message_handler
#     async def handle_message(self, message: Message, ctx: MessageContext) -> None:
#         try:
#             logger.info("EditorAgent processing message")
#             target_word_count = message.content.get('word_count', 1000)
#             current_attempt = 0
#             final_content = None
            
#             while current_attempt < self.max_attempts:
#                 current_attempt += 1
#                 prompt = self._build_prompt(message.content, target_word_count, current_attempt)
                
#                 llm_result = await self._model_client.create(
#                     messages=[self._system_message, UserMessage(content=prompt, source=self.id.key)],
#                     cancellation_token=ctx.cancellation_token,
#                 )
                
#                 response = format_editor_response(llm_result.content)
#                 content = response.get('result', '')
                
#                 # Use the FunctionTool with cancellation token
#                 count_result = await self.word_count_tool.run_json({"content": content}, ctx.cancellation_token)
                
#                 if self._validate_word_count(count_result['wordCount'], target_word_count):
#                     final_content = content
#                     logger.info(f"Editor validated word count: {count_result['wordCount']}/{target_word_count}")
#                     break
                
#                 logger.warning(f"Editor attempt {current_attempt}: {count_result['wordCount']} words")

#             # Handle final content or fallback
#             if final_content is None:
#                 logger.warning("Editor failed to meet word count after 3 attempts. Using best result")
#                 final_content = content
#                 response['word_count_warning'] = True

#             # Get final word count
#             final_count = await self.word_count_tool.run_json({"content": final_content}, ctx.cancellation_token)
#             response['calculated_word_count'] = final_count['wordCount']
#             response['result'] = final_content
            
#             await self.publish_message(
#                 Message(content=response),
#                 topic_id=TopicId("ResultSaverAgent", source=self.id.key)
#             )

#         except Exception as e:
#             logger.error(f"Error in EditorAgent: {str(e)}", exc_info=True)
#             raise

#     def _build_prompt(self, content: dict, target: int, attempt: int) -> str:
#         base_prompt = f"Review and edit content: {json.dumps(content)}\n"
#         base_prompt += f"Maintain word count: {target} (±{self.lower_deviation*100}%/+{self.upper_deviation*100}%)"
        
#         if attempt > 1:
#             base_prompt += "\nPrevious edit attempts didn't meet length requirements. "
#             base_prompt += "Make minimal adjustments to meet word count."
        
#         return base_prompt

#     def _validate_word_count(self, actual: int, target: int) -> bool:
#         lower_bound = target * (1 - self.lower_deviation)
#         upper_bound = target * (1 + self.upper_deviation)
#         return lower_bound <= actual <= upper_bound

@type_subscription(topic_type="ResultSaverAgent")
class ResultSaverAgent(RoutedAgent):
    final_message = None  # Class variable to store the final message

    def __init__(self, store_id: int, task_id: str):
        super().__init__("A result saver agent")
        self.store_id = store_id
        self.task_id = task_id

    @message_handler
    async def handle_message(self, message: Message, ctx: MessageContext) -> None:
        try:
            logger.info("ResultSaverAgent processing message")
            # logger.info(f"ResultSaverAgent processing message: {message.content}")

            # # Validate and format the message content
            # content = message.content.get("content")   
            # print("#########################")
            # print(type(content), "type of content", content)
            # print("#########################")
            
            # # Destructure the content to store all fields correctly
            # article_data = {
            #     "task_id": self.task_id,
            #     "title": content.get("title", ""),
            #     "meta_title": content.get("meta_title", ""),
            #     "meta_description": content.get("meta_description", ""),
            #     "content": content.get("content", ""), 
            #     "sections": [section.get("html_content", "") for section in content.get("sections", [])],
            #     "word_count": content.get("word_count", 0),
            #     "keywords": content.get("keywords", []),
            #     "stats": content.get("integration_stats", {})  
            # }

            # # Ensure all required fields are present
            # required_fields = ["task_id", "title", "meta_title", "meta_description", "content"]
            # for field in required_fields:
            #     if not article_data[field]:  # Changed to check for falsy values
            #         raise ValueError(f"Missing required field: {field}")

            # # Save the article to the database
            # await insert_article_data(article_data, self.task_id)


            # logger.info(f"ResultSaverAgent processing message: {message.content}")

            # Validate that message.content is a dictionary
            if not isinstance(message.content, dict):
                raise ValueError("Invalid message format: Expected a dictionary.")

            # Extract and parse the JSON string from the "result" key
            result_str = message.content.get("result", "")
            try:
                if "```json" in result_str:
                    json_start = result_str.find('```json\n') + 7
                    json_end = result_str.rfind('```')
                    json_str = result_str[json_start:json_end].strip()
                    content_data = json.loads(json_str)  # Convert JSON string to dictionary
                else:
                    # If the result does not contain a JSON code block, attempt to load it directly
                    content_data = json.loads(result_str)  # Convert the result string to dictionary
            except json.JSONDecodeError as e:
                raise ValueError(f"Error decoding JSON content: {e}")

            # Validate that content_data is a dictionary
            if not isinstance(content_data, dict):
                raise ValueError("Invalid content format: Expected a dictionary.")

            # Extract fields from parsed JSON
            article_data = {
                "task_id": self.task_id,
                "title": content_data.get("title", ""),
                "meta_title": content_data.get("meta_title", ""),
                "meta_description": content_data.get("meta_description", ""),
                "slug": content_data.get("slug", ""),
                "content": content_data.get("content", ""), 
                "sections": [section.get("html_content", "") for section in content_data.get("sections", [])],
                "word_count": content_data.get("word_count", 0),
                "keywords": content_data.get("keywords", []),
                "stats": content_data.get("integration_stats", {})  
            }

            # Save the article to the database
            ResultSaverAgent.final_message = article_data  # Store in class variable

            # Ensure all required fields are present
            required_fields = ["task_id", "title", "meta_title", "meta_description", "content"]
            for field in required_fields:
                if not article_data[field]:  # Check for missing or empty fields
                    raise ValueError(f"Missing required field: {field}")
                
            # For now, remove the slug from the article_data
            # article_data.pop("slug", None) #remove slug from the article_data : TO-DO: add slug to the article_data

            # Save the article to the database
            # await insert_article_data(article_data, self.task_id)

        except Exception as e:
            logger.error(f"Error in ResultSaverAgent: {str(e)}", exc_info=True)
            raise

    @classmethod
    def get_final_message(cls):  # Change to class method
        return cls.final_message


class ArticleWorkflowManager:
    def __init__(self, model_client: AzureOpenAIChatCompletionClient):
        self.model_client = model_client
        self.prompts = self._load_prompts()
        self.runtime = SingleThreadedAgentRuntime()
        self.use_product_integrator = True  # Default value

    def _load_prompts(self) -> dict:
        current_dir = Path(__file__).parent
        prompts_path = current_dir.parent / "lib" / "prompts" / "storeblog" / "article_generation.yml"
        
        try:
            with open(prompts_path, 'r') as file:
                import yaml
                prompts = yaml.safe_load(file)
                if not prompts or 'system_message' not in prompts:
                    raise ValueError("Invalid prompts file format: missing system_message section")
                return prompts
        except Exception as e:
            logger.error(f"Error loading prompts: {str(e)}")
            raise

    async def setup_agents(self, store_id: int, task_id: str, payload: Dict[str, Any]):
        """Register each agent individually"""
        try:
            # Determine if product integration is needed based on payload
            self.use_product_integrator = False
            if 'additional_features' in payload and isinstance(payload['additional_features'], dict):
                product_embeds = payload['additional_features'].get('product_embeds', False)
                internal_links = payload['additional_features'].get('internal_links', False)
                self.use_product_integrator = product_embeds or internal_links
            
            logger.info(f"Product integration enabled: {self.use_product_integrator}")

            # Set tolerance from payload            
            content_creator_tolerance = payload.get('tolerance', {
                'upper': 0.07,
                'lower': 0.03
            })
            
            editor_tolerance = payload.get('editor_tolerance', {
                'upper': 0.05,
                'lower': 0.02
            })

            # Register Researcher Agent
            await ResearcherAgent.register(
                self.runtime,
                type="ResearcherAgent",
                factory=lambda: ResearcherAgent(model_client=self.model_client)
            )

            # Register Content Creator Agent with product integration flag
            # await ContentCreatorAgent.register(
            #     self.runtime,
            #     type="ContentCreatorAgent",
            #     factory=lambda: ContentCreatorAgent(
            #         model_client=self.model_client,
            #         use_product_integrator=self.use_product_integrator
            #     )
            # )

            # Register Content Creator with custom tolerance
            await ContentCreatorAgent.register(
                self.runtime,
                type="ContentCreatorAgent",
                factory=lambda: ContentCreatorAgent(
                    model_client=self.model_client,
                    use_product_integrator=self.use_product_integrator
                ).set_tolerance(
                    content_creator_tolerance['upper'],
                    content_creator_tolerance['lower']
                )
            )

            # Register Product Integrator Agent only if needed
            if self.use_product_integrator:
                await ProductIntegratorAgent.register(
                    self.runtime,
                    type="ProductIntegratorAgent",
                    factory=lambda: ProductIntegratorAgent(model_client=self.model_client)
                )
            else:
                # If not using product integrator, we need to register a direct route from ContentCreator to Editor
                logger.info("Skipping ProductIntegratorAgent in workflow")

            # Register Editor Agent
            # await EditorAgent.register(
            #     self.runtime,
            #     type="EditorAgent",
            #     factory=lambda: EditorAgent(model_client=self.model_client)
            # )

            # Register Editor with custom tolerance
            await EditorAgent.register(
                self.runtime,
                type="EditorAgent",
                factory=lambda: EditorAgent(
                    model_client=self.model_client
                )
            )

            # Register Result Saver Agent
            await ResultSaverAgent.register(
                self.runtime,
                type="ResultSaverAgent",
                factory=lambda: ResultSaverAgent(store_id=store_id, task_id=task_id)
            )

            logger.info("All agents registered successfully")

        except Exception as e:
            logger.error(f"Error setting up agents: {str(e)}", exc_info=True)
            raise

    async def run_workflow(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        try:
            if not self.runtime:
                raise RuntimeError("Runtime not initialized")

            logger.info("Starting workflow runtime")
            self.runtime.start()
            
            # Publish initial message to the ResearcherAgent
            logger.info("Publishing initial message to ResearcherAgent")
            await self.runtime.publish_message(
                Message(content=payload),
                topic_id=TopicId("ResearcherAgent", source="default")
            )

            logger.info("Waiting for workflow completion")
            await self.runtime.stop_when_idle()
            await self.runtime.close()

        except Exception as e:
            logger.error(f"Error in workflow execution: {str(e)}", exc_info=True)
            raise

        finally:
            if getattr(self.runtime, '_started', False):
                await self.runtime.stop()

async def create_article(payload: Dict[str, Any], store_id: int = None) -> Dict[str, Any]:
    try:
        model_client = await get_azure_client()

        if 'task_id' not in payload:
            payload['task_id'] = str(uuid.uuid4())

        logger.info(f"Starting article creation for task {payload.get('task_id')}")

        try:
            workflow_manager = ArticleWorkflowManager(model_client)
            # Pass payload to setup_agents to determine if product integration is needed
            await workflow_manager.setup_agents(store_id, payload.get('task_id'), payload)
            await workflow_manager.run_workflow(payload)
            article_data = ResultSaverAgent.get_final_message()

            #update article in database
            await insert_article_data(article_data, payload.get('task_id'))

            # Update article status to completed
            await update_article_job_status(task_id=payload.get('task_id'), status='COMPLETED')

            # Return a structured response for Celery task
            return {
                "status_code": 200,
                "status": "success",
                "task_id": payload.get('task_id'),
                "data":{"article": article_data},
                "message": "Article creation completed"
            }

        except Exception as workflow_error:
            logger.error(f"Workflow error: {str(workflow_error)}", exc_info=True)
            raise

    except Exception as e:
        logger.error("Fatal error in article creation", exc_info=True)
        # Update article status to failed
        await update_article_job_status(task_id=payload.get('task_id'), status='FAILED')
        return {
            "status_code": 500,
            "status": "error",
            "task_id": payload.get('task_id'),
            "error": str(e),
            "message": "Article creation failed"
        }



