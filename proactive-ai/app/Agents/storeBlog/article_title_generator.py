#generate article title
import os
from typing import List, Dict, Any
from datetime import datetime
import json
import yaml
from fastapi import <PERSON><PERSON>PEx<PERSON>
from Logging.logger_config import get_logger
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken
from autogen_ext.models.openai import AzureOpenAIChatCompletionClient
from portkey_ai import Portkey, PORTKEY_GATEWAY_URL, createHeaders
from Agents.lib.clients.Azure_Client import get_azure_client
# Initialize logger
logger = get_logger()


async def load_prompt_config(file_path: str) -> dict:
    """Load and validate the prompt configuration from YAML file."""
    try:
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)
    except Exception as e:
        logger.error(f"Failed to load prompt configuration: {str(e)}", exc_info=True)
        raise

# Create the AssistantAgent
async def create_title_generator() -> AssistantAgent:
    """Creates and configures the title generator agent."""
    prompt = await load_prompt_config('Agents/lib/prompts/storeblog/generate_article_title.yml')
    
    # Combine all prompt components into a comprehensive system message
    system_message = (
        f"{prompt['system_message']['Title_Generator']['objectives']}\n\n"
        f"Execution Rules:\n{prompt['system_message']['Title_Generator']['execution_rules']}\n\n"
        f"SEO Guidelines:\n{prompt['system_message']['Title_Generator']['seo_guidelines']}\n\n"
        f"Input Schema:\n{prompt['system_message']['Title_Generator']['input_schema']}\n\n"
        f"Error Handling:\n{prompt['system_message']['Title_Generator']['error_handling']}"
    )
    
    return AssistantAgent(
        name="title_generator",
        system_message=system_message,
        model_client=await get_azure_client()
    )

async def generate_article_titles(
    target_keyword: List[str],
    article_type: str,
    count_of_titles: int = 5,
    store_id: int = None
) -> Dict[str, Any]:
    """Generate SEO-optimized article titles using an AI agent."""
    try:
        # Create title generator agent
        title_generator = await create_title_generator()
        
        # Prepare the initial message
        initial_message = {
            "task": "generate_titles",
            "parameters": {
                "target_keyword": target_keyword,
                "article_type": article_type,
                "count_of_titles_needed": count_of_titles
            }
        }

        # Generate titles
        response = await title_generator.on_messages(
            [TextMessage(content=json.dumps(initial_message), source="user")],
            CancellationToken()
        )

        # print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        
        # Extract the content from the TextMessage object
        content = response.chat_message.content
        
        # Clean up the content by removing markdown code block formatting
        content = content.replace("```json", "").replace("```", "").strip()
        
        # Parse the content as JSON
        try:
            result = json.loads(content)
            # Restructure the response to match the endpoint's expected format
            return {
                "generated_titles": result["generated_titles"]
            }
        except json.JSONDecodeError:
            # If the response is not valid JSON, return it as a dictionary
            return {
                "generated_titles": [{"title": content}]
            }

    except Exception as e:
        logger.error("Error generating titles", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "message": "Failed to generate article titles"
            }
        )