from typing import List, Dict, Any
import json
import yaml
from fastapi import <PERSON><PERSON><PERSON>Ex<PERSON>
from Logging.logger_config import get_logger
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken
from Agents.lib.clients.Azure_Client import get_azure_client

logger = get_logger(__name__)

async def load_prompt_config(file_path: str) -> dict:
    """Load and validate the prompt configuration from YAML file."""
    try:
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)
    except Exception as e:
        logger.error(f"Failed to load prompt configuration: {str(e)}", exc_info=True)
        raise

async def create_keyword_refiner() -> AssistantAgent:
    """Creates and configures the keyword refiner agent."""
    try:
        prompt = await load_prompt_config('app/Agents/lib/prompts/storeblog/keyword_refiner.yml')
    except Exception:
        logger.error("Failed to load prompt configuration, using fallback route", exc_info=True)
        prompt = await load_prompt_config('Agents/lib/prompts/storeblog/keyword_refiner.yml')
    
    system_message = (
        f"{prompt['system_message']['Keyword_Refiner']['objectives']}\n\n"
        f"Execution Rules:\n{prompt['system_message']['Keyword_Refiner']['execution_rules']}\n\n"
        f"SEO Guidelines:\n{prompt['system_message']['Keyword_Refiner']['seo_guidelines']}\n\n"
        f"Error Handling:\n{prompt['system_message']['Keyword_Refiner']['error_handling']}"
    )
    
    return AssistantAgent(
        name="keyword_refiner",
        system_message=system_message,
        model_client=await get_azure_client()
    )

async def refine_keywords(
    keywords: List[str],
    required_count: int = 5,
) -> Dict[str, Any]:
    """Refine keywords using AI agent to generate SEO-optimized variations."""
    try:
        keyword_refiner = await create_keyword_refiner()
        
        initial_message = {
            "task": "refine_keywords",
            "parameters": {
                "keywords": keywords,
                "required_count": required_count
            }
        }

        response = await keyword_refiner.on_messages(
            [TextMessage(content=json.dumps(initial_message), source="user")],
            CancellationToken()
        )
        
        content = response.chat_message.content
        content = content.replace("```json", "").replace("```", "").strip()
        
        try:
            result = json.loads(content)
            # Check if the response contains an error
            if "error" in result:
                return {
                    "refined_keywords": [],
                    "metadata": {
                        "error": result["error"]["message"] if isinstance(result["error"], dict) and "message" in result["error"] else str(result["error"]),
                        "completion_status": "error",
                        "original_keywords": len(keywords)
                    }
                }
            return {
                "refined_keywords": result["refined_keywords"],
                "metadata": result["metadata"]
            }
        except json.JSONDecodeError:
            return {
                "refined_keywords": [{"refined": k} for k in keywords],
                "metadata": {
                    "total_refined": len(keywords),
                    "completion_status": "fallback"
                }
            }

    except Exception as e:
        logger.error("Error refining keywords", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "message": "Failed to refine keywords"
            }
        ) 