from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, Depends
from Auth.api.v1.auth import AuthManager


async def verify_tokens(
    request: Request,
    auth_manager: AuthManager = Depends()
) -> dict:
    # Get API tokens from headers
    proactive_token = request.headers.get("X-PROACTIVE-TOKEN")
    bincha_app_token = request.headers.get("X-BINCHA-APP-TOKEN")

    # print("TOKENS!!!!!!!!!", proactive_token, bincha_app_token)
    
    if not proactive_token or not bincha_app_token:
        raise HTTPException(status_code=401, detail="Both App and Auth API token is required")
    
    # Initialize results
    store_details = None
    app_details = None
    
    # Verify proactive token if present
    if proactive_token:
        store_details = await auth_manager.verify_token(proactive_token)
        
    # Verify bincha app token if present
    if bincha_app_token:
        app_details = await auth_manager.verify_app_token(bincha_app_token)
    
    # Check if at least one token is valid
    if not store_details and not app_details:
        raise HTTPException(status_code=401, detail="Invalid API tokens")
    
    # Add results to request state
    request.state.store = store_details
    request.state.app = app_details
    
    # Return combined results
    return {
        "store": store_details,
        "app": app_details
    }

def require_auth(auth_manager: AuthManager):
    async def dependency(request: Request):
        return await verify_tokens(request, auth_manager)
    return Depends(dependency)