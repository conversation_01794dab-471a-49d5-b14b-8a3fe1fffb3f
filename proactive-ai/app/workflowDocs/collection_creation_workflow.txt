Collection Creation Workflow
==========================

1. Entry Points
--------------
- Manual creation via API endpoint (/create-collection)
- Scheduled creation via celery task
- Both handled by generate_collection_task in celery_config_collection.py

2. Initial Setup & Validation
----------------------------
a. Task Creation:
   - Generate task_id
   - Store in Redis for status tracking
   - Update collection job status to "STARTED"
   - Initialize logging

b. Data Gathering:
   - Fetch product data from database
   - Get or generate keywords:
     * Self-origin keywords from KeywordsDiscoveryTable
     * Competitor keywords if specified
     * Fallback to used keywords if needed
   - Load attributes for collection creation
   - Load prompt configuration
   - Configure Portkey LLM

3. Agent-Based Collection Generation
----------------------------------
a. Agent Initialization:
   - Keyword Refiner Agent
   - Collection Creator Agent
   - Product Attributes Agent
   - Customer Behavior Agent
   - Seasonal Trends Agent
   - Market Analysis Agent
   - Collection Manager Agent

b. Workflow Phases:
   1. Keyword Refinement:
      - Analyze product data
      - Expand keywords
      - Remove duplicates
      - Generate relevance scores

   2. Collection Creation:
      - Generate initial collections
      - Include required fields:
        * collection_title
        * collection_description
        * collection_metatitle
        * collection_metadescription
        * meta_keywords
        * target_keyword
        * products_list

   3. Product Attributes Analysis:
      - Analyze product features
      - Identify common attributes
      - Generate attribute scores

   4. Customer Behavior Analysis:
      - Analyze historical data
      - Generate behavior scores

   5. Seasonal Trends Analysis:
      - Analyze seasonal relevance
      - Generate seasonal scores

   6. Market Analysis:
      - Analyze market positioning
      - Generate market scores

   7. Collection Management:
      - Validate collection sizes
      - Attempt recovery if needed
      - Apply optimization suggestions
      - Format final output

4. Validation & Recovery
-----------------------
a. Size Validation:
   - Check against MIN_PRODUCTS_IN_COLLECTION
   - Check against MAX_PRODUCTS_IN_COLLECTION

b. Recovery Process:
   - Adjust similarity threshold
   - Attempt to add more products
   - Revalidate collection size

5. Data Storage
--------------
a. Store Collections:
   - Save to CollectionsCreatedTable
   - Include all metadata and scores
   - Set initial status as "to_be_fetched"

b. Update Status:
   - Update collection job status
   - Update Redis status
   - Update schedule status if scheduled task

6. Error Handling & Logging
--------------------------
- Comprehensive error logging at each step
- Status updates in Redis
- Database status updates
- Error notifications via callback URL

7. Completion
------------
a. Success Path:
   - Mark task as "COMPLETED"
   - Send success response to callback URL
   - Update all relevant status trackers

b. Failure Path:
   - Mark task as "FAILED"
   - Log detailed error information
   - Send error details to callback URL
   - Attempt retry if configured

8. Monitoring & Tracking
-----------------------
- Redis-based status tracking
- Database status updates
- Logging with context
- Optional Slack notifications

Note: This workflow maintains atomic operations and includes rollback mechanisms for failures at any stage. The agent-based approach ensures flexible and intelligent collection creation while maintaining strict validation and recovery procedures. 