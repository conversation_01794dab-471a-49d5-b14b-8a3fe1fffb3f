# Keyword Generation Workflow Documentation

## Overview
This document details the complete workflow for keyword generation in the StoreBlog system, including success paths, error handling, and fallback mechanisms.

## Entry Point
The workflow begins when a POST request is made to `/get-trending-keywords` endpoint with the following possible parameters:
- `competitors_domain` (optional): List of competitor URLs
- `focus_keywords` (optional): List of target keywords
- `force_new_generation` (boolean): Whether to force new keyword generation
- `generate_based_on_store_domain` (boolean)
- `generate_based_on_competitors_domains` (boolean)
- `generate_based_on_target_keywords` (boolean)
- `limit` (optional, default=12): Maximum number of keywords to return

## Primary Workflows

### 1. Normal Flow (force_new_generation = false)
1. Request Validation
   - Validate authentication tokens
   - Extract store_id from auth data
   - Ensure at least one generation type is selected
   - Validate focus_keywords if target generation enabled

2. Database Check
   - Query keywords_discovery table for existing keywords
   - Filter by:
     * store_id
     * currently_active = true
     * origin_type matching generation types
   - If keywords found:
     * Update usage tracking
     * Return filtered keywords up to limit
   - If no keywords found:
     * Proceed to New Generation Flow

### 2. Force New Generation Flow (force_new_generation = true)
1. Store Domain Keywords (if enabled)
   - Clean store domain (remove http/https)
   - Call DataForSEO API
   - Save response to keyword_discovery/domain-com_YYYYMMDD_HHMMSS.json
   - Extract and store keywords with origin_type "self"

2. Competitor Domain Keywords (if enabled)
   - For each competitor domain:
     * Clean domain
     * Call DataForSEO API
     * Save response
     * Extract and store keywords with origin_type "competitor"

3. Target Keywords (if enabled)
   - For each focus keyword:
     * Call DataForSEO API
     * Save response
     * Extract and store keywords with origin_type "target-keyword"

4. Result Processing
   - Combine keywords from all successful sources
   - Remove duplicates
   - Limit to requested count
   - Return formatted response

## Error Handling & Fallback Mechanisms

### 1. API Error Handling
- Individual Domain Failures
  * Log error details
  * Continue with remaining domains
  * Include in error metadata
  * Return partial results if any successful calls

- Rate Limit Handling
  * Implement retry mechanism
  * Maximum 5 calls per second
  * Queue requests if limit exceeded

### 2. Database Error Handling
- Read Failures
  * Log error
  * Fallback to new generation
  * Include in error metadata

- Write Failures
  * Log error
  * Continue processing
  * Return results even if storage fails
  * Flag for retry in background

### 3. Empty Results Handling
- No Keywords Found
  * Return empty array with count 0
  * Include reason in metadata

- Partial Results
  * Return available keywords up to limit
  * Include success/failure counts in metadata

## Data Storage

### 1. Raw API Responses
- Location: keyword_discovery/
- Filename Format: domain-com_YYYYMMDD_HHMMSS.json
- Content: Complete DataForSEO response
- Purpose: Audit trail and debugging

### 2. Database Storage
- Table: keywords_discovery
- Fields:
  * id (auto-generated)
  * store_id (foreign key)
  * generated_on (date)
  * origin_type (self/competitor/target-keyword)
  * keywords (array)
  * available_keywords (array)
  * used_keywords (array)
  * available_keyword_count (integer)
  * currently_active (boolean)
  * url (optional)
  * created_at (timestamp)
  * updated_at (timestamp)

## Response Format

### Success Response
```json
{
  "keywords": ["keyword1", "keyword2", "keyword3"],
  "total_keywords": 3,
}
```

### Error Response
```json
{
"status": "error",
"message": "<error_message>",
"details": {
"failed_domains": [...],
"successful_domains": [...]
}
}
```

## Error Codes
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden


## Development Mode

### 1. Configuration
- Set DATAFORSEO_ENV = 'development'
- Uses sample response from bombay_shirts.json
- Follows same processing flow

### 2. Testing Scenarios
- Normal flow with existing keywords
- Force new generation
- Mixed success/failure scenarios
- Rate limit handling
- Error responses

## Monitoring & Logging

### 1. Key Events Logged
- API calls (request/response)
- File operations
- Database operations
- Error conditions

### 2. Log Data Points
- request_id (UUID)
- store_id
- operation type
- timestamp
- error details (if applicable)
- performance metrics

## Performance Considerations

### 1. Asynchronous Operations
- API calls run asynchronously
- Database operations non-blocking
- File I/O handled in background

### 2. Optimization
- Batch processing where possible
- Response caching
- Rate limit management
- Connection pooling

## Security Measures

### 1. Input Validation
- Domain sanitization
- Parameter validation
- SQL injection prevention

### 2. Authentication
- Token validation
- Permission checking
- Rate limiting per store

### 3. Data Protection
- Secure credential storage
- Response data encryption
- Audit logging

## Maintenance Tasks

### 1. Response File Management
- Regular cleanup of old files
- Archival process
- Storage monitoring

### 2. Database Maintenance
- Index optimization
- Usage statistics tracking
- Inactive record cleanup

This documentation provides a comprehensive guide to the keyword generation system's workflow, including all possible paths, error handling, and implementation details. It serves as the primary reference for developers maintaining or extending the system.