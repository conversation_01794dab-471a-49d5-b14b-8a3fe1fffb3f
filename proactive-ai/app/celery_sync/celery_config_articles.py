import sys
from pathlib import Path
import asyncio
from celery import shared_task
import requests
from typing import List, Dict, Any
import redis
from datetime import datetime

# Add project root to Python path
project_root = str(Path(__file__).resolve().parents[2])
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary modules
try:
    from app.Utils.sync.sync_articles import sync_all_articles
    from app.Logging.logger_config import get_logger
    from app.Utils.redis_utils import redis_client
    from app.Models.dbHelpers import insert_article_sync_job, update_article_sync_job_status
    from app.celery_sync.celery_config_products import run_async
except ImportError:
    from Utils.sync.sync_articles import sync_all_articles
    from Logging.logger_config import get_logger
    from Utils.redis_utils import redis_client
    from Models.dbHelpers import insert_article_sync_job, update_article_sync_job_status
    from celery_config_products import run_async

logger = get_logger(__name__)

# Redis Configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0

# Initialize Redis client if not already available
if not redis_client:
    try:
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            decode_responses=True
        )
        redis_client.ping()
    except redis.ConnectionError as e:
        logger.error(f"Failed to connect to Redis: {e}")
        redis_client = None

@shared_task(
    bind=True,
    max_retries=2,
    name='app.celery_sync.celery_config_articles.sync_articles_task',
    queue='articles'
)
def sync_articles_task(
    self,
    articles: List[Dict[str, Any]],
    store_id: int,
    callback_url: str = None
):
    """
    Celery task for syncing articles in the background
    
    Args:
        articles: List of article dictionaries to sync
        store_id: Store ID
        callback_url: Optional URL to call when processing is complete
    """
    task_id = self.request.id
    
    try:
        logger.info(
            "Starting article sync task",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "article_count": len(articles)
            }
        )
        
        # Update initial status in Redis
        if redis_client:
            redis_client.set(f'article_sync_{task_id}_status', 'PROCESSING')
        
        # Update job status in database
        run_async(update_article_sync_job_status(task_id=task_id, status='PROCESSING'))
        
        # Process the articles
        sync_results = run_async(sync_all_articles(
            articles=articles,
            store_id=store_id
        ))
        
        # Calculate statistics
        total_articles = len(articles)
        synced_articles = sum(1 for result in sync_results.get('sync_results', []) 
                          if isinstance(result, str) and "Article Sync Done" in result)
        
        # Check if any articles were successfully synced
        if synced_articles == 0 and total_articles > 0:
            # Update failed status in Redis
            if redis_client:
                redis_client.set(f'article_sync_{task_id}_status', 'FAILED')
            
            # Update failure status in database
            run_async(update_article_sync_job_status(
                task_id=task_id, 
                status='FAILED',
                error="No articles were successfully synced",
                sync_stats={
                    'total_articles': total_articles,
                    'synced_articles': synced_articles,
                    'errors': sync_results.get('errors', [])
                }
            ))
            
            logger.error(
                "Article sync failed - no articles were successfully synced",
                extra={
                    "task_id": task_id,
                    "store_id": store_id,
                    "total_articles": total_articles,
                    "errors": sync_results.get('errors', [])
                }
            )
            
            return {
                "status": "error",
                "task_id": task_id,
                "error": "No articles were successfully synced",
                "total_articles": total_articles,
                "synced_articles": synced_articles,
                "errors": sync_results.get('errors', [])
            }
        
        # Update success status in Redis
        if redis_client:
            redis_client.set(f'article_sync_{task_id}_status', 'COMPLETED')
        
        # Update completion status in database
        run_async(update_article_sync_job_status(
            task_id=task_id, 
            status='COMPLETED',
            sync_stats={
                'total_articles': total_articles,
                'synced_articles': synced_articles,
                'errors': sync_results.get('errors', [])
            }
        ))
        
        logger.info(
            "Article sync completed",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "total_articles": total_articles,
                "synced_articles": synced_articles
            }
        )
        
        if callback_url:
            try:
                # Count different types of results
                already_synced = sum(1 for result in sync_results.get('sync_results', []) 
                                if isinstance(result, str) and "Article Sync Done - Already" in result)
                newly_synced = sum(1 for result in sync_results.get('sync_results', []) 
                                if isinstance(result, str) and result == "Article Sync Done")
                failed_articles = total_articles - (already_synced + newly_synced)
                
                # Create a more meaningful response
                callback_data = {
                    "status": "success",
                    "task_id": task_id,
                    "store_id": store_id,
                    "summary": {
                        "total_processed": total_articles,
                        "sync_results": {
                            "newly_synced": newly_synced,
                            "already_synced": already_synced,
                            "failed": failed_articles
                        }
                    },
                    "completion_time": datetime.now().isoformat(),
                    "error_samples": sync_results.get('errors', [])[:5] if sync_results.get('errors') else []
                }
                
                response = requests.post(callback_url, json=callback_data)
                
                if response.status_code != 200:
                    logger.error(
                        "Callback failed",
                        extra={
                            "task_id": task_id,
                            "store_id": store_id,
                            "status_code": response.status_code
                        }
                    )
            except Exception as callback_error:
                logger.error(
                    "Failed to send results to callback URL",
                    extra={
                        "task_id": task_id,
                        "error": str(callback_error)
                    }
                )
        
        return {
            "status": "success",
            "task_id": task_id,
            "total_articles": total_articles,
            "synced_articles": synced_articles,
            "sync_results": sync_results
        }
        
    except Exception as e:
        logger.error(
            "Article sync task failed",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "error": str(e)
            },
            exc_info=True
        )
        
        # Update failed status in Redis
        if redis_client:
            redis_client.set(f'article_sync_{task_id}_status', 'FAILED')
        
        # Update failure status in database
        run_async(update_article_sync_job_status(task_id=task_id, status='FAILED', error=str(e)))
        
        # Send error to callback URL if provided
        if callback_url:
            try:
                error_data = {
                    "status": "error",
                    "task_id": task_id,
                    "error": str(e)
                }
                requests.post(callback_url, json=error_data)
            except Exception as callback_error:
                logger.error(f"Failed to send error to callback URL: {str(callback_error)}")
        
        # Retry if not at max retries
        try:
            self.retry(exc=e, countdown=60)
        except self.MaxRetriesExceededError:
            return {
                "status": "error",
                "error": str(e)
            }
        raise e

@shared_task(
    name='app.celery_sync.celery_config_articles.get_article_sync_status',
    time_limit=10,
    soft_time_limit=5
)
def get_article_sync_status(task_id: str) -> Dict[str, Any]:
    """Get the status of an article sync task from Redis"""
    try:
        if not redis_client:
            raise ConnectionError("Redis connection not available")
            
        status = redis_client.get(f'article_sync_{task_id}_status')
        if not status:
            return None
            
        return {
            "task_id": task_id,
            "status": status.decode() if isinstance(status, bytes) else status
        }
    except Exception as e:
        logger.error(f"Error getting article sync task status: {str(e)}")
        raise 