import sys
from pathlib import Path
import asyncio
from celery import shared_task
import requests
from typing import List, Dict, Any
import redis
from datetime import datetime

# Add project root to Python path
project_root = str(Path(__file__).resolve().parents[2])
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary modules
try:
    from app.Utils.sync.sync_collections import sync_all_collections
    from app.Logging.logger_config import get_logger
    from app.Utils.redis_utils import redis_client
    from app.Models.dbHelpers import insert_collection_sync_job, update_collection_sync_job_status
except ImportError:
    from Utils.sync.sync_collections import sync_all_collections
    from Logging.logger_config import get_logger
    from Utils.redis_utils import redis_client
    from Models.dbHelpers import insert_collection_sync_job, update_collection_sync_job_status

logger = get_logger(__name__)

# Redis Configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0

# Initialize Redis client if not already available
if not redis_client:
    try:
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            decode_responses=True
        )
        redis_client.ping()
    except redis.ConnectionError as e:
        logger.error(f"Failed to connect to Redis: {e}")
        redis_client = None

def run_async(coro):
    """Helper function to run async code in sync context"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop.run_until_complete(coro)

@shared_task(
    bind=True,
    max_retries=2,
    name='app.celery_sync.celery_config_collections.sync_collections_task',
    queue='collections'
)
def sync_collections_task(
    self,
    collections: List[Dict[str, Any]],
    store_id: int,
    callback_url: str = None
):
    """
    Celery task for syncing collections in the background
    
    Args:
        collections: List of collection dictionaries to sync
        store_id: Store ID
        callback_url: Optional URL to call when processing is complete
    """
    task_id = self.request.id
    
    try:
        logger.info(
            "Starting collection sync task",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "collection_count": len(collections)
            }
        )
        
        # Update initial status in Redis
        if redis_client:
            redis_client.set(f'collection_sync_{task_id}_status', 'PROCESSING')
        
        # Update job status in database
        run_async(update_collection_sync_job_status(task_id=task_id, status='PROCESSING'))
        
        # Process the collections
        sync_results = run_async(sync_all_collections(
            collections=collections,
            store_id=store_id
        ))
        
        # Calculate statistics
        total_collections = len(collections)
        synced_collections = sum(1 for result in sync_results.get('sync_results', []) 
                               if isinstance(result, str) and "Collection Sync Done" in result)
        
        # Check if any collections were successfully synced
        if synced_collections == 0 and total_collections > 0:
            # Update failed status in Redis
            if redis_client:
                redis_client.set(f'collection_sync_{task_id}_status', 'FAILED')
            
            # Update failure status in database
            run_async(update_collection_sync_job_status(
                task_id=task_id, 
                status='FAILED',
                error="No collections were successfully synced",
                sync_stats={
                    'total_collections': total_collections,
                    'synced_collections': synced_collections,
                    'errors': sync_results.get('errors', [])
                }
            ))
            
            logger.error(
                "Collection sync failed - no collections were successfully synced",
                extra={
                    "task_id": task_id,
                    "store_id": store_id,
                    "total_collections": total_collections,
                    "errors": sync_results.get('errors', [])
                }
            )
            
            return {
                "status": "error",
                "task_id": task_id,
                "error": "No collections were successfully synced",
                "total_collections": total_collections,
                "synced_collections": synced_collections,
                "errors": sync_results.get('errors', [])
            }
        
        # Update success status in Redis
        if redis_client:
            redis_client.set(f'collection_sync_{task_id}_status', 'COMPLETED')
        
        # Update completion status in database
        run_async(update_collection_sync_job_status(
            task_id=task_id, 
            status='COMPLETED',
            sync_stats={
                'total_collections': total_collections,
                'synced_collections': synced_collections,
                'errors': sync_results.get('errors', [])
            }
        ))
        
        logger.info(
            "Collection sync completed",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "total_collections": total_collections,
                "synced_collections": synced_collections
            }
        )
        
        if callback_url:
            try:
                # Count different types of results
                already_synced = sum(1 for result in sync_results.get('sync_results', []) 
                                  if isinstance(result, str) and "Collection Sync Done - Already" in result)
                newly_synced = sum(1 for result in sync_results.get('sync_results', []) 
                                if isinstance(result, str) and result == "Collection Sync Done")
                failed_collections = total_collections - (already_synced + newly_synced)
                
                # Create a more meaningful response
                callback_data = {
                    "status": "success",
                    "task_id": task_id,
                    "store_id": store_id,
                    "summary": {
                        "total_processed": total_collections,
                        "sync_results": {
                            "newly_synced": newly_synced,
                            "already_synced": already_synced,
                            "failed": failed_collections
                        }
                    },
                    "completion_time": datetime.now().isoformat(),
                    "error_samples": sync_results.get('errors', [])[:5] if sync_results.get('errors') else []
                }
                
                response = requests.post(callback_url, json=callback_data)
                
                if response.status_code != 200:
                    logger.error(
                        "Callback failed",
                        extra={
                            "task_id": task_id,
                            "store_id": store_id,
                            "status_code": response.status_code
                        }
                    )
            except Exception as callback_error:
                logger.error(
                    "Failed to send results to callback URL",
                    extra={
                        "task_id": task_id,
                        "error": str(callback_error)
                    }
                )
        
        return {
            "status": "success",
            "task_id": task_id,
            "total_collections": total_collections,
            "synced_collections": synced_collections,
            "sync_results": sync_results
        }
        
    except Exception as e:
        logger.error(
            "Collection sync task failed",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "error": str(e)
            },
            exc_info=True
        )
        
        # Update failed status in Redis
        if redis_client:
            redis_client.set(f'collection_sync_{task_id}_status', 'FAILED')
        
        # Update failure status in database
        run_async(update_collection_sync_job_status(task_id=task_id, status='FAILED', error=str(e)))
        
        # Send error to callback URL if provided
        if callback_url:
            try:
                error_data = {
                    "status": "error",
                    "task_id": task_id,
                    "error": str(e)
                }
                requests.post(callback_url, json=error_data)
            except Exception as callback_error:
                logger.error(f"Failed to send error to callback URL: {str(callback_error)}")
        
        # Retry if not at max retries
        try:
            self.retry(exc=e, countdown=60)
        except self.MaxRetriesExceededError:
            return {
                "status": "error",
                "error": str(e)
            }
        raise e

@shared_task(
    name='app.celery_sync.celery_config_collections.get_collection_sync_status',
    time_limit=10,
    soft_time_limit=5
)
def get_collection_sync_status(task_id: str) -> Dict[str, Any]:
    """Get the status of a collection sync task from Redis"""
    try:
        if not redis_client:
            raise ConnectionError("Redis connection not available")
            
        status = redis_client.get(f'collection_sync_{task_id}_status')
        if not status:
            return None
            
        return {
            "task_id": task_id,
            "status": status.decode() if isinstance(status, bytes) else status
        }
    except Exception as e:
        logger.error(f"Error getting collection sync task status: {str(e)}")
        raise 