import sys
from pathlib import Path
import asyncio
from celery import shared_task
import requests
from typing import List, Dict, Any
import redis
from datetime import datetime

# Add project root to Python path
project_root = str(Path(__file__).resolve().parents[2])
if project_root not in sys.path:
    sys.path.append(project_root)

# Import necessary modules
from app.Utils.sync_products import sync_all_products
from app.Logging.logger_config import get_logger
from app.Utils.redis_utils import redis_client
from app.Models.dbHelpers import insert_product_sync_job, update_product_sync_job_status

logger = get_logger(__name__)

# Redis Configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0

# Initialize Redis client if not already available
if not redis_client:
    try:
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            decode_responses=True
        )
        redis_client.ping()
    except redis.ConnectionError as e:
        logger.error(f"Failed to connect to Redis: {e}")
        redis_client = None

def run_async(coro):
    """Helper function to run async code in sync context"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop.run_until_complete(coro)

@shared_task(
    bind=True,
    max_retries=2,
    name='app.Utils.celery_config_products.sync_products_task',
    queue='products'
)
def sync_products_task(
    self,
    products: List[Dict[str, Any]],
    store_id: int,
    callback_url: str = None
):
    """
    Celery task for syncing products in the background
    
    Args:
        products: List of product dictionaries to sync
        store_id: Store ID
        callback_url: Optional URL to call when processing is complete
    """
    task_id = self.request.id
    
    try:
        logger.info(
            "Starting product sync task",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "product_count": len(products)
            }
        )
        
        # Update initial status in Redis
        if redis_client:
            redis_client.set(f'product_sync_{task_id}_status', 'PROCESSING')
        
        # Update job status in database
        run_async(update_product_sync_job_status(task_id=task_id, status='PROCESSING'))
        
        # Process the products
        sync_results = run_async(sync_all_products(
            products=products,
            store_id=store_id
        ))
        
        # Calculate statistics
        total_products = len(products)
        synced_products = sum(1 for result in sync_results.get('sync_results', []) 
                              if isinstance(result, str) and "Product Sync Done" in result)
        
        # Update success status in Redis
        if redis_client:
            redis_client.set(f'product_sync_{task_id}_status', 'COMPLETED')
        
        # Update completion status in database
        run_async(update_product_sync_job_status(
            task_id=task_id, 
            status='COMPLETED',
            sync_stats={
                'total_products': total_products,
                'synced_products': synced_products,
                'errors': sync_results.get('embedding_errors')
            }
        ))
        
        logger.info(
            "Product sync completed",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "total_products": total_products,
                "synced_products": synced_products
            }
        )
        
        if callback_url:
            try:
                # Count different types of results
                already_synced = sum(1 for result in sync_results.get('sync_results', []) 
                                if isinstance(result, str) and "Product Sync Done - Already" in result)
                newly_synced = sum(1 for result in sync_results.get('sync_results', []) 
                                if isinstance(result, str) and result == "Product Sync Done")
                failed_products = total_products - (already_synced + newly_synced)
                
                # Create a more meaningful response
                callback_data = {
                    "status": "success",
                    "task_id": task_id,
                    "store_id": store_id,
                    "summary": {
                        "total_processed": total_products,
                        "sync_results": {
                            "newly_synced": newly_synced,
                            "already_synced": already_synced,
                            "failed": failed_products
                        },
                        "embedding_stats": {
                            "successfully_embedded": total_products - len(sync_results.get('embedding_errors', [])) if sync_results.get('embedding_errors') else total_products,
                            "embedding_errors": len(sync_results.get('embedding_errors', [])) if sync_results.get('embedding_errors') else 0
                        }
                    },
                    "completion_time": datetime.now().isoformat(),
                    "error_samples": sync_results.get('embedding_errors', [])[:5] if sync_results.get('embedding_errors') else []
                }
                
                response = requests.post(callback_url, json=callback_data)
                
                
                if response.status_code != 200:
                    logger.error(
                        "Callback failed",
                        extra={
                            "task_id": task_id,
                            "store_id": store_id,
                            "status_code": response.status_code
                        }
                    )
            except Exception as callback_error:
                logger.error(
                    "Failed to send results to callback URL",
                    extra={
                        "task_id": task_id,
                        "error": str(callback_error)
                    }
                )
        
        return {
            "status": "success",
            "task_id": task_id,
            "total_products": total_products,
            "synced_products": synced_products,
            "sync_results": sync_results
        }
        
    except Exception as e:
        logger.error(
            "Product sync task failed",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "error": str(e)
            },
            exc_info=True
        )
        
        # Update failed status in Redis
        if redis_client:
            redis_client.set(f'product_sync_{task_id}_status', 'FAILED')
        
        # Update failure status in database
        run_async(update_product_sync_job_status(task_id=task_id, status='FAILED', error=str(e)))
        
        # Send error to callback URL if provided
        if callback_url:
            try:
                error_data = {
                    "status": "error",
                    "task_id": task_id,
                    "error": str(e)
                }
                requests.post(callback_url, json=error_data)
            except Exception as callback_error:
                logger.error(f"Failed to send error to callback URL: {str(callback_error)}")
        
        # Retry if not at max retries
        try:
            self.retry(exc=e, countdown=60)
        except self.MaxRetriesExceededError:
            return {
                "status": "error",
                "error": str(e)
            }
        raise e

@shared_task(
    name='app.Utils.celery_config_products.get_product_sync_status',
    time_limit=10,
    soft_time_limit=5
)
def get_product_sync_status(task_id: str) -> Dict[str, Any]:
    """Get the status of a product sync task from Redis"""
    try:
        if not redis_client:
            raise ConnectionError("Redis connection not available")
            
        status = redis_client.get(f'product_sync_{task_id}_status')
        if not status:
            return None
            
        return {
            "task_id": task_id,
            "status": status.decode() if isinstance(status, bytes) else status
        }
    except Exception as e:
        logger.error(f"Error getting product sync task status: {str(e)}")
        raise 