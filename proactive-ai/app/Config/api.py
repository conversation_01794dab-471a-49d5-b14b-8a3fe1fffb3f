# Contains configuration for the API, such as the API version, the API title, and the API description.

from pydantic import BaseSettings

class CollectionSettings(BaseSettings):
    API_V1_STR: str = "/api/v1/rankcollections"
    PROJECT_NAME: str = "Collections API"
    DESCRIPTION: str = "This is a sub-project of the FastAPI project. It contains the endpoints for the rank-collections app."
    VERSION: str = "0.1.0"
    CURRENT_API_VERSION: str = "v1"


class Config:
    env_file = ".env"
