# app/DataForSeo/dataforseo_service.py
import os
import json
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from datetime import datetime
import aiohttp
import asyncio
from ratelimit import limits, sleep_and_retry
from dotenv import load_dotenv
try:
    from Logging.logger_config import get_logger
except ImportError:
    from app.Logging.logger_config import get_logger
import uuid
try:
    from DataForSeo.client import RestClient
except ImportError:
    from app.DataForSeo.client import RestClient
import yaml
import operator

logger = get_logger()

class DataForSEOConfig:
    """Configuration class for DataForSEO service."""
    BASE_URL = "https://api.dataforseo.com"
    API_VERSION = "v3"
    KEYWORDS_ENDPOINT = f"/{API_VERSION}/dataforseo_labs/google/keywords_for_site/live"
    RESPONSE_DIR = Path(__file__).parent / "keyword_discovery"
    CALLS_PER_SECOND = 5
    REQUEST_TIMEOUT = 30
    DEFAULT_LOCATION_CODE = 2840  # US
    DEFAULT_LANGUAGE_CODE = "en"

class DataForSEOError(Exception):
    """Custom exception for DataForSEO-related errors."""
    pass

class RuleEvaluator:
    """Helper class to evaluate filtering rules."""
    def __init__(self):
        self.operators = {
            'greater_than': operator.gt,
            'less_than': operator.lt,
            'equals': operator.eq,
            'not_equals': operator.ne,
            'greater_equals': operator.ge,
            'less_equals': operator.le,
            'contains': lambda x, y: y in x,
            'starts_with': str.startswith,
            'ends_with': str.endswith
        }

    def _get_field_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """Extract nested field value using dot notation."""
        current = data
        for key in field_path.split('.'):
            if isinstance(current, dict):
                current = current.get(key)
            else:
                return None
        return current

    def _evaluate_condition(self, data: Dict[str, Any], condition: Dict[str, Any]) -> bool:
        """Evaluate a single condition against the data."""
        try:
            field_value = self._get_field_value(data, condition['field'])
            if field_value is None:
                return False
                
            operator_name = condition['operator']
            value = condition['value']
            
            # Convert value based on value_type if specified
            if 'value_type' in condition:
                if condition['value_type'] == 'int':
                    value = int(value)
                elif condition['value_type'] == 'float':
                    value = float(value)
                
            return self.operators[operator_name](field_value, value)
        except Exception as e:
            logger.error(f"Error evaluating condition: {str(e)}")
            return False

    def evaluate_rules(self, data: Dict[str, Any], rules: Dict[str, Any]) -> bool:
        """Evaluate rules recursively."""
        if 'and' in rules:
            return all(self.evaluate_rules(data, rule) for rule in rules['and'])
        elif 'or' in rules:
            return any(self.evaluate_rules(data, rule) for rule in rules['or'])
        else:
            return self._evaluate_condition(data, rules)

class DataForSEOService:
    def __init__(self):
        """Initialize the DataForSEO service."""
        self.request_id = str(uuid.uuid4())
        logger.info("Initializing DataForSEO service", request_id=self.request_id)
        
        # Initialize rule evaluator
        self.rule_evaluator = RuleEvaluator()
        
        # Ensure response directory exists
        self.response_dir = DataForSEOConfig.RESPONSE_DIR
        os.makedirs(self.response_dir, exist_ok=True)
        
        self.email, self.api_token = self._load_credentials()
        self.client = self._initialize_client()
        logger.debug("Service initialization complete", request_id=self.request_id)

    def _load_credentials(self) -> tuple[str, str]:
        """Load API credentials from environment variables."""
        load_dotenv()
        email = os.getenv('DATAFORSEO_EMAIL')
        api_token = os.getenv('DATAFORSEO_API_TOKEN')
        
        if not email or not api_token:
            logger.error("API credentials not found", request_id=self.request_id)
            raise Exception("DATAFORSEO_EMAIL or DATAFORSEO_API_TOKEN not found")
        
        return email, api_token

    def _initialize_client(self) -> RestClient:
        """Initialize the DataForSEO REST client."""
        try:
            return RestClient(self.email, self.api_token)
        except Exception as e:
            logger.error("Failed to initialize REST client", error=str(e))
            raise

    def _clean_domain(self, domain: str) -> str:
        """Remove http/https and clean domain name."""
        cleaned = domain.lower()
        cleaned = cleaned.replace('https://', '').replace('http://', '')
        cleaned = cleaned.replace('www.', '')
        # Remove any trailing slashes
        cleaned = cleaned.rstrip('/')
        # Remove any paths after domain
        cleaned = cleaned.split('/')[0]
        return cleaned

    def _get_response_filename(self, domain: str) -> str:
        """Generate filename for storing API response."""
        clean_domain = self._clean_domain(domain)
        # Replace any remaining special characters with hyphens
        safe_domain = clean_domain.replace('.', '-').replace('/', '-')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"{safe_domain}_{timestamp}.json"

    def _save_response_to_file(self, domain: str, response_data: Dict[str, Any]) -> None:
        """Save API response to a file."""
        try:
            filename = self._get_response_filename(domain)
            filepath = os.path.join(self.response_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(response_data, f, indent=2)
                
            logger.info(f"Response saved to {filepath}")
        except Exception as e:
            logger.warning(f"Failed to save response: {str(e)}")

    # extract keywords from DataForSEO response
    def _extract_keywords_from_response(self, response: Dict[str, Any]) -> List[str]:
        """Extract keywords from DataForSEO response."""
        try:
            if not response.get("result"):
                return []

            items = response["result"][0].get("items", [])
            return [item["keyword"] for item in items if item.get("keyword")]
        except Exception as e:
            logger.error(f"Failed to extract keywords: {str(e)}")
            return []

    def _load_rules(self, ruleset_file: Union[str, Path]) -> Dict[str, Any]:
        """Load keyword filtering rules from YAML file."""
        try:
            with open(ruleset_file, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load rules file: {str(e)}")
            raise

    def _filter_keywords(self, keywords_data: List[Dict[str, Any]], rules: Dict[str, Any]) -> List[str]:
        """Filter keywords based on rules."""
        filtered_keywords = []
        
        for item in keywords_data:
            try:
                # logger.info(f"Item: {item}")
                if self.rule_evaluator.evaluate_rules(item, rules):
                    keyword = item.get('keyword')
                    if keyword:
                        filtered_keywords.append(keyword)
            except Exception as e:
                logger.error(f"Error filtering keyword: {str(e)}")
                continue
                
        return filtered_keywords

    @sleep_and_retry
    @limits(calls=DataForSEOConfig.CALLS_PER_SECOND, period=1)
    async def _make_api_call(self, domain: str, ruleset_file: Path) -> Dict[str, Any]:
        """Make a rate-limited API call to DataForSEO and filter results."""
        try:
            # Load filtering rules
            rules = self._load_rules(ruleset_file)
            
            # Clean domain before processing
            clean_domain = self._clean_domain(domain)
            
            # for development environment
            if os.getenv('DATAFORSEO_ENV') == 'development':
                logger.info("Using development environment", request_id=self.request_id)
                raw_response = await self._load_sample_data()
            else:
                # for production environment
                logger.info("Using production environment", request_id=self.request_id)
                
                payload = [{
                    "target": clean_domain,
                    "location_code": DataForSEOConfig.DEFAULT_LOCATION_CODE,
                    "language_code": DataForSEOConfig.DEFAULT_LANGUAGE_CODE
                }]

                raw_response = self.client.post(DataForSEOConfig.KEYWORDS_ENDPOINT, payload)
                logger.info(f"Got response from DataForSEO")
            # Save raw response
            self._save_response_to_file(clean_domain, raw_response)
            
            # Extract and filter keywords
            if not raw_response.get("tasks"):
                logger.warning("No tasks found in response")
                return {"keywords": [], "metadata": {"error": "No tasks found in response"}}
            
            tasks = raw_response["tasks"]
            if not tasks or not isinstance(tasks, list) or len(tasks) == 0:
                logger.warning("No valid tasks found in response")
                return {"keywords": [], "metadata": {"error": "No valid tasks found in response"}}
            
            result = tasks[0].get("result", [])
            if not result or len(result) == 0:
                logger.warning("No results found in task")
                return {"keywords": [], "metadata": {"error": "No results found in task"}}
            
            items = result[0].get("items", [])
            if not items:
                logger.warning("No items found in result")
                return {"keywords": [], "metadata": {"error": "No items found in result"}}
            
            # logger.info(f"Items: {items}")
            
            filtered_keywords = self._filter_keywords(items, rules)
            # filtered_keywords = []
            # for item in items:
            #     keyword = item.get("keyword")
            #     if keyword:
            #         filtered_keywords.append(keyword)
            
            if not filtered_keywords:
                logger.warning(f"No keywords found for domain {clean_domain} after filtering")
                return {"keywords": [], "metadata": {"error": "No keywords found after filtering"}}
            
            return {
                "keywords": filtered_keywords,
                "metadata": {
                    "total_keywords": len(filtered_keywords),
                    "timestamp": datetime.utcnow().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"API call failed for domain {domain}: {str(e)}")
            return {"keywords": [], "metadata": {"error": str(e)}}

    async def _load_sample_data(self) -> Dict[str, Any]:
        """Load sample data for development environment."""
        # sample_file = Path(__file__).parent / 'sample_response/bombay_shirts.json'
        sample_file = Path(__file__).parent / 'sample_response/drluxor.json'
        try:
            logger.info("Loading sample data", sample_file=sample_file)
            with open(sample_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load sample data: {str(e)}")
            raise

    # fetch keywords using store domain - self origin
    async def fetch_keywords_using_store_domain(
        self,
        store_domain: str,
        products: List[Dict[str, Any]],
        ruleset_file: Path
    ) -> Dict[str, Any]:
        """Fetch keywords based on store domain."""
        try:
            logger.info("Fetching keywords using store domain", store_domain=store_domain)
            response = await self._make_api_call(store_domain, ruleset_file)
            return {
                "keywords": response["keywords"],
                "metadata": {
                    "source": "store",
                    "domain": store_domain,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Failed to fetch store keywords: {str(e)}")
            return {"keywords": [], "metadata": {"error": str(e)}}

    async def fetch_keywords_using_competitors_domain(
        self,
        competitor_url: str,
        ruleset_file: Path
    ) -> Dict[str, Any]:
        """Fetch keywords based on competitor's domain."""
        try:
            logger.info("Fetching keywords using competitor domain", competitor_url=competitor_url)
            response = await self._make_api_call(competitor_url, ruleset_file)
            return {
                "keywords": response["keywords"],
                "metadata": {
                    "source": "competitor",
                    "domain": competitor_url,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Failed to fetch competitor keywords: {str(e)}")
            return {"keywords": [], "metadata": {"error": str(e)}}

    async def fetch_keywords_using_target_keywords(
        self,
        focus_keyword: str,
        ruleset_file: Path
    ) -> Dict[str, Any]:
        """Fetch keywords based on target keyword."""
        try:
            logger.info("Fetching keywords using target keyword", focus_keyword=focus_keyword)
            response = await self._make_api_call(focus_keyword, ruleset_file)
            return {
                "keywords": response["keywords"],
                "metadata": {
                    "source": "target-keyword",
                    "keyword": focus_keyword,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Failed to fetch target keywords: {str(e)}")
            return {"keywords": [], "metadata": {"error": str(e)}}