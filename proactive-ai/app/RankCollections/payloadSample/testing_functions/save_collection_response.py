import os
from pathlib import Path
import logging
from typing import List
from dotenv import load_dotenv
from supabase import create_client, Client


# Get the absolute path of the script's directory
SCRIPT_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = SCRIPT_DIR.parent.parent

# Set up paths
ENV_FILE = PROJECT_ROOT.parent / ".env"


# Load environment variables
load_dotenv(ENV_FILE)

# Constants
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Sample payload from the provided JSON
sample_payload = {
    "status_code": 200,
    "status": "success",
    "message": "Collection created and stored successfully",
    "data": {
        "collection": {
            "status": "success",
            "collection": {
                "collections": [
                    {
                        "collection_title": "New Arrivals: Latest Shoes and Accessories",
                        "collection_description": "Explore the latest releases in footwear and accessories, featuring a versatile selection from renowned brands like Adidas, Vans, and Converse. Perfect for those who want to stay on-trend with fresh styles.",
                        "collection_metatitle": "New Arrivals: Latest Shoe and Accessory Collection",
                        "collection_metadescription": "Discover the newest pieces in footwear and accessories from top brands such as Adidas, Vans, and Converse. Shop the latest trends today!",
                        "meta_keywords": [
                            "new arrivals",
                            "latest releases",
                            "Adidas shoes",
                            "Vans footwear",
                            "Converse accessories"
                        ],
                        "target_keyword": "new arrivals",
                        "products_list": [
                            {"gid": "", "title": "ADIDAS | SUPERSTAR 80S"},
                            {"gid": "", "title": "VANS | OLD SKOOL (BUTTERFLY) TRUE WHITE | BLACK"},
                            {"gid": "", "title": "ADIDAS | KID'S STAN SMITH"}
                        ]
                    },
                    {
                        "collection_title": "Trending Styles: Hot Picks from Top Brands",
                        "collection_description": "Stay ahead with the most sought-after and trending footwear, featuring best-selling brands like Vans, Adidas, and Converse.",
                        "collection_metatitle": "Trending Styles: Popular Shoes from Top Brands",
                        "collection_metadescription": "Explore trending shoes and accessories that are in demand right now.",
                        "meta_keywords": [
                            "trending styles",
                            "popular footwear",
                            "Vans trending shoes"
                        ],
                        "target_keyword": "trending styles",
                        "products_list": [
                            {"gid": "", "title": "VANS | AUTHENTIC | GRADIENT/CRIMSON"},
                            {"gid": "", "title": "VANS | SH-8 HI"},
                            {"gid": "", "title": "TIMBERLAND | MENS 6 INCH PREMIUM BOOT"}
                        ]
                    }
                ]
            }
        }
    }
}


async def store_collection_in_supabase(collection_result: dict, store_id: int) -> List[dict]:
    """
    Store collection data in Supabase collections_created table.
    """
    try:
        logger.info(f"Starting to store collections for store_id: {store_id}")
        
        # Skip if no collection data or status is no_matches
        if not collection_result or collection_result.get('status') == 'no_matches':
            logger.info("No collections to store - empty result or no matches")
            return []
            
        # Extract collections from the nested structure
        collections_data = collection_result.get('data', {}).get('collection', {}).get('collection', {}).get('collections', [])
        
        if not collections_data:
            logger.warning("No collections found in the provided data")
            return []
            
        # Store each collection
        stored_collections = []
        for collection in collections_data:
            try:
                # Validate required fields
                if not all(key in collection for key in ['collection_title', 'products_list']):
                    logger.warning(f"Skipping collection due to missing required fields: {collection.get('collection_title', 'Unknown')}")
                    continue
                    
                # Prepare data for insertion
                collection_record = {
                    'collection_title': collection.get('collection_title'),
                    'collection_description': collection.get('collection_description'),
                    'collection_metatitle': collection.get('collection_metatitle'),
                    'collection_metadescription': collection.get('collection_metadescription'),
                    'meta_keywords': collection.get('meta_keywords', []),  # Ensure this is a list
                    'target_keyword': [collection.get('target_keyword')],  # Wrap in a list to ensure it's an array
                    'product_list': collection.get('products_list', []),
                    'responsed_from_agent': collection_result,
                    'store_id': store_id
                }
                
                # Insert into Supabase
                result = supabase.table('collections_created').insert(collection_record).execute()
                
                if result.data:
                    stored_collections.extend(result.data)
                    logger.info(f"Successfully stored collection: {collection.get('collection_title')}")
                else:
                    logger.warning(f"No data returned when storing collection: {collection.get('collection_title')}")
                    
            except Exception as collection_error:
                logger.error(f"Error storing individual collection: {str(collection_error)}")
                continue
                
        logger.info(f"Completed storing collections - Total stored: {len(stored_collections)}")
        return stored_collections
        
    except Exception as e:
        logger.error(f"Failed to store collections in Supabase: {str(e)}")
        raise Exception(f"Error storing collections: {str(e)}")

# Run the script
import asyncio

async def main():
    store_id = 1  # Example store ID
    try:
        result = await store_collection_in_supabase(sample_payload, store_id)
        logger.info(f"Successfully stored {len(result)} collections")
    except Exception as e:
        logger.error(f"Script failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())