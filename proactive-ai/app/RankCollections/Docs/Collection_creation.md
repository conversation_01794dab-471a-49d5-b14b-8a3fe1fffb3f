# Collection Generation Endpoint Technical Documentation

## Overview
The Collection Generation Endpoint is a FastAPI-based service that automatically creates product collections using advanced text processing, machine learning, and LLM-powered agents. It offers two distinct approaches: a clustering-based method and a direct similarity matching method.

## Architecture

### 1. Core Components

#### 1.1 Data Processing Module
- **Purpose**: Prepares product data for analysis
- **Features**:
  - Configurable feature selection (title, description, variants, price)
  - Text normalization and cleaning
  - Handles missing data and variations
- **Output**: Processed product features ready for embedding generation

#### 1.2 Embedding Generation
- **Implementation**: Uses spaCy's en_core_web_lg model
- **Features**:
  - 300-dimensional word vectors
  - Handles both product text and keywords
  - Contextual understanding of product descriptions

#### 1.3 Agent System
- **Components**:
  - Keyword Refiner: Analyzes and expands keywords
  - Collection Creator: Generates structured collections
  - Collection Manager: Orchestrates the workflow
- **Communication**: Uses autogen for agent interactions
- **Output Format**: Standardized JSON collection structure

### 2. Implementation Approaches

#### 2.1 Clustering-Based Approach
1. **Data Processing Flow**:
   ```
   Raw Products → Feature Extraction → Embeddings → K-means Clustering → 
   Similarity Matching → Agent Processing → Collections
   ```

2. **Key Features**:
   - Dynamic cluster number determination
   - Silhouette analysis for optimal clustering
   - Group-level similarity matching
   - Maintains product group coherence

3. **Advantages**:
   - Better handles large product catalogs
   - Creates more coherent collections
   - Reduces redundancy in collections

#### 2.2 Direct Similarity Approach
1. **Data Processing Flow**:
   ```
   Raw Products → Feature Extraction → Embeddings → 
   Direct Similarity Matching → Agent Processing → Collections
   ```

2. **Key Features**:
   - Direct cosine similarity computation
   - Configurable similarity threshold
   - Product-level matching
   - Simpler implementation

3. **Advantages**:
   - Faster execution time
   - More precise control over matching
   - Better for smaller catalogs
   - Easier to debug and maintain

## API Specification

### Endpoint: POST /create-collection

#### Request Format
```json
{
  "keywords": ["string"],
  "feature_keys": ["string"],
  "similarity_threshold": float,
  "products": [{}],
  "store_id": integer,
  "use_clustering": boolean
}
```

#### Configuration Parameters
1. **feature_keys**:
   - Default: ["title", "description", "variation_title", "price"]
   - Purpose: Specifies which product attributes to consider
   - Customizable based on store needs

2. **similarity_threshold**:
   - Default: 0.5
   - Range: 0.0 to 1.0
   - Purpose: Controls strictness of matching

3. **use_clustering**:
   - Default: true
   - Purpose: Toggles between clustering and direct similarity approaches

#### Response Format
```json
{
  "status": "success",
  "collection": {
    "collections": [{
      "collection_title": "string",
      "collection_description": "string",
      "collection_metatitle": "string",
      "collection_metadescription": "string",
      "meta_keywords": ["string"],
      "target_keyword": "string",
      "products_list": [{"gid": "string", "title": "string"}]
    }],
    "processing_metadata": {
      "total_products_processed": integer,
      "total_collections_created": integer,
      "completion_status": "string"
    }
  }
}
```

## Performance Considerations

### 1. Clustering Approach
- **Time Complexity**: O(n²) for clustering
- **Memory Usage**: Higher due to clustering
- **Recommended For**:
  - Large product catalogs (>1000 products)
  - When collection coherence is priority
  - Complex product relationships

### 2. Direct Similarity Approach
- **Time Complexity**: O(n*k) where k = number of keywords
- **Memory Usage**: Lower, scales linearly
- **Recommended For**:
  - Smaller product catalogs (<1000 products)
  - When speed is priority
  - Simple matching requirements

## Error Handling

1. **Input Validation**:
   - Validates all required fields
   - Checks data types and ranges
   - Handles missing optional parameters

2. **Processing Errors**:
   - Graceful handling of embedding generation failures
   - Clustering algorithm error management
   - Agent communication error recovery

3. **Response Codes**:
   - 200: Successful operation
   - 400: Bad request (invalid input)
   - 401: Authentication error
   - 500: Internal server error

## Best Practices

### 1. Configuration
- Start with default similarity_threshold (0.5)
- Adjust based on results quality
- Use feature_keys relevant to your products

### 2. Method Selection
- Use clustering for:
  - Large, diverse catalogs
  - Complex categorization needs
  - When processing time isn't critical

- Use direct similarity for:
  - Small to medium catalogs
  - Simple matching requirements
  - When speed is essential

### 3. Optimization Tips
- Preprocess product data for better results
- Cache embeddings when possible
- Monitor and adjust thresholds based on results

## Future Improvements

1. **Planned Enhancements**:
   - Parallel processing for large catalogs
   - Advanced caching mechanisms
   - More sophisticated clustering algorithms
   - Enhanced keyword expansion

2. **Potential Features**:
   - A/B testing support
   - Performance analytics
   - Custom scoring algorithms
   - Multi-language support

## Conclusion
The Collection Generation Endpoint provides a flexible and powerful solution for automated product collection creation. Its dual-approach architecture allows for adaptation to different use cases while maintaining high-quality output through the combination of machine learning and LLM-powered agents.


# WorkFlow Explaination
# Understanding Collection Generation Workflows

## 1. Clustering-Based Workflow

### What is it?
This workflow first groups similar products together before matching them with keywords. Think of it like sorting clothes in your wardrobe into different piles before deciding what to wear.

### Step-by-Step Flow:

1. **Data Processing** (`data_processor.py`)
   ```python
   processed_products = data_processor.process_products(products)
   ```
   - Takes raw product data
   - Combines important information (title, description, price, variants)
   - Cleans the text and prepares it for analysis
   - Output: Clean, structured product information

2. **Create Embeddings** (`embedding_creator.py`)
   ```python
   product_embeddings = generate_embeddings_using_spacy(product_texts)
   ```
   - Takes the cleaned product text
   - Converts each product's text into a number list (vector)
   - Uses spaCy's language model to understand product meaning
   - Output: Each product now has a special number code (embedding)

3. **Clustering** (`clustering.py`)
   ```python
   cluster_result = clusterer.cluster(product_embeddings)
   ```
   - Takes all product embeddings
   - Groups similar products together
   - Automatically decides how many groups to make
   - Uses math (silhouette score) to make good groups
   - Output: Products organized into groups

4. **Find Similar Groups** (`similarity.py`)
   ```python
   similar_clusters = similarity_searcher.find_similar_clusters(keyword_embeddings, products, cluster_labels)
   ```
   - Takes keyword embeddings and product groups
   - Finds which groups match each keyword best
   - Uses math (cosine similarity) to measure matches
   - Output: List of matching groups for each keyword

5. **Generate Collections** (Agent System)
   ```python
   result = await generator.create_collections(...)
   ```
   - Takes matching groups and keywords
   - Uses AI agents to:
     - Refine keywords
     - Create collection titles and descriptions
     - Organize products into final collections
   - Output: Complete collections with all required information

## 2. Direct Similarity Workflow (No Clustering)

### What is it?
This workflow matches products directly with keywords, like using a search engine to find exactly what you want.

### Step-by-Step Flow:

1. **Data Processing** (`data_processor.py`)
   ```python
   processed_products = data_processor.process_products(products)
   ```
   - Same as clustering workflow
   - Prepares product data for analysis
   - Output: Clean product information

2. **Create Embeddings** (`embedding_creator.py`)
   ```python
   product_embeddings = generate_embeddings_using_spacy(product_texts)
   keyword_embeddings = generate_embeddings_using_spacy(keywords)
   ```
   - Creates embeddings for both products and keywords
   - Each product and keyword gets its number code
   - Output: Products and keywords as number lists

3. **Find Similar Products** (`direct_similarity_collection.py`)
   ```python
   keyword_matches = similarity_searcher._find_similar_products(
       product_embeddings,
       keyword_embeddings,
       processed_products
   )
   ```
   - Compares each keyword directly with all products
   - Finds products that match each keyword well enough
   - Uses similarity threshold to decide good matches
   - Output: List of matching products for each keyword

4. **Generate Collections** (Agent System)
   ```python
   result = await generator.create_collections(...)
   ```
   - Takes matching products and keywords
   - Uses AI agents to:
     - Refine and expand keywords
     - Create collection details
     - Organize matched products
   - Output: Complete collections

### Key Differences:

1. **Speed**:
   - Clustering: Slower but better for lots of products
   - Direct: Faster but better for fewer products

2. **Organization**:
   - Clustering: Products are pre-grouped, more organized
   - Direct: Products are matched individually, more precise

3. **Use Cases**:
   - Clustering: Good for big stores with many similar products
   - Direct: Good for smaller stores or specific searches

### Example of When to Use Each:

**Clustering Example:**
- You have a clothing store with 1000+ products
- Many similar items (different colors of same shirt)
- Want to create broad collections like "Summer Wear"

**Direct Similarity Example:**
- You have a specialty store with 200 products
- Each product is quite unique
- Want very specific collections like "Blue Running Shoes"


# Frequently Asked Questions: Embeddings and Clustering

## Q1: How is the number of clusters (k) determined?

**A:** The number of clusters is determined dynamically using silhouette analysis. Here's how it works:

1. The system tries different numbers of clusters (k) between `min_clusters` (default: 2) and `max_clusters` (default: 20)
2. For each k:
   - Performs k-means clustering
   - Calculates the silhouette score (measures how similar products are to their own cluster compared to other clusters)
   - Scores range from -1 to 1 (higher is better)

```python
def _find_optimal_clusters(self, embeddings: np.ndarray) -> Tuple[int, float]:
    max_clusters = min(self.max_clusters, len(embeddings) - 1)
    best_n_clusters = self.min_clusters
    best_silhouette = -1
    
    for n_clusters in range(self.min_clusters, max_clusters + 1):
        kmeans = KMeans(n_clusters=n_clusters)
        cluster_labels = kmeans.fit_predict(embeddings)
        silhouette_avg = silhouette_score(embeddings, cluster_labels)
        
        if silhouette_avg > best_silhouette:
            best_silhouette = silhouette_avg
            best_n_clusters = n_clusters
```

The k with the highest silhouette score is chosen as the optimal number of clusters.

## Q2: How are keyword embeddings created?

**A:** Keyword embeddings are created separately for each keyword. Here's the process:

1. Each keyword is processed individually
2. For each keyword, spaCy creates a 300-dimensional vector
3. The result is a matrix of shape (num_keywords, 300)

```python
# Example of how keywords are processed
keywords = ["summer dress", "beach wear", "casual outfits"]
keyword_embeddings = generate_embeddings_using_spacy(keywords)
# Result shape: (3, 300) - one 300-dim vector per keyword
```

This allows us to:
- Compare each keyword independently with products
- Maintain the unique meaning of each keyword
- Find relevant products for each keyword separately

## Q3: How are product embeddings created?

**A:** Similar to keywords, product embeddings are created separately for each product:

1. Each product's features are combined into a single text
2. Individual embedding is generated for each product
3. Results in a matrix of shape (num_products, 300)

```python
# Example of product text preparation
product_text = f"{product.title} {product.description} {product.variation_title}"
product_embeddings = generate_embeddings_using_spacy(product_texts)
# Result shape: (num_products, 300) - one 300-dim vector per product
```

Benefits:
- Preserves unique characteristics of each product
- Allows for accurate similarity comparisons
- Maintains product-specific context

## Q4: What is the optimal similarity threshold value?

**A:** The similarity threshold (using cosine similarity) ranges from 0 to 1, where:
- 0 = completely different
- 1 = exactly the same

Recommended thresholds:
- 0.8 - 0.9: Very strict matching (high precision, low recall)
  - Use when you need very accurate but fewer matches
  - Good for specific product searches
  - May miss some relevant products

- 0.6 - 0.7: Balanced matching (recommended default)
  - Good balance between precision and recall
  - Catches most relevant products
  - Minimal irrelevant matches

- 0.4 - 0.5: Lenient matching (high recall, lower precision)
  - Use when you want more matches
  - May include some less relevant products
  - Good for exploratory collections

```python
# Example threshold usage
similarity_threshold = 0.6  # Default balanced setting

# More strict filtering
strict_threshold = 0.8  # Will return fewer, more relevant matches

# More lenient filtering
lenient_threshold = 0.5  # Will return more matches, some might be less relevant
```

Best Practice:
- Start with 0.6
- Increase threshold if getting too many irrelevant matches
- Decrease threshold if getting too few matches
- Monitor and adjust based on your specific product catalog and needs