# Collection Creation Workflow Documentation

This document provides a detailed overview of the collection creation workflow in the GeneratorAI system. It describes the process flow, functions used, their parameters, and output formats.

## Table of Contents

1. [Overview](#overview)
2. [API Endpoints](#api-endpoints)
3. [Celery Tasks](#celery-tasks)
4. [Collection Generation Pipeline](#collection-generation-pipeline)
5. [Database Operations](#database-operations)
6. [Status Tracking](#status-tracking)
7. [Error Handling](#error-handling)

## Overview

The collection creation workflow allows for automatic generation of product collections based on keywords extracted from competitor websites or from the store's own products. The process involves several steps:

1. API request to initiate collection creation
2. Validation of store and product data
3. Asynchronous task creation for collection generation
4. Keyword extraction and expansion
5. Product grouping based on keywords
6. Collection generation with titles, descriptions, and metadata
7. Storage of collections in the database
8. Status updates and notifications

## API Endpoints

### Create Collection Endpoint

**Endpoint:** `POST /create-collection`

**Function:** `create_collection_endpoint`

**Parameters:**
- `request`: FastAPI Request object
- `collection_request`: CollectionRequest model with:
  - `attributes`: Optional dictionary of collection attributes
  - `callback_url`: Optional URL for callback when collection generation completes
  - `keywords_origin_type`: Source of keywords ('self' or 'competitor')
  - `enforce_strict_collection_creation`: Whether to enforce strict collection creation

**Authentication:**
- Requires valid authentication token via `verify_tokens` dependency

**Process:**
1. Validates authentication and extracts store ID
2. Logs the request details
3. Retrieves store UUID
4. Initializes database entry for the job
5. Checks if the store has products
6. Launches a Celery task for collection generation
7. Updates the task ID in the database
8. Returns a success response with the task ID

**Response Format:**
```json
{
  "status": "success",
  "message": "Collection generation started",
  "data": {
    "task_id": "task-uuid",
    "message": "INITIATED - COLLECTION GENERATION TASK"
  }
}
```

### Collection Status Endpoint

**Endpoint:** `GET /collection-status/{task_id}`

**Function:** `get_collection_status`

**Parameters:**
- `task_id`: UUID of the collection generation task
- `request`: FastAPI Request object

**Authentication:**
- Requires valid authentication token via `verify_tokens` dependency

**Process:**
1. Validates authentication and extracts store ID
2. Checks Redis for the task status
3. Returns the status if found

**Response Format:**
```json
{
  "status": "success",
  "message": "Status retrieved successfully",
  "data": {
    "task_id": "task-uuid",
    "status": "PROCESSING|COMPLETED|FAILED|NO_COLLECTIONS"
  }
}
```

## Celery Tasks

### Generate Collection Task

**Task Name:** `app.RankCollections.celery_config_collection.generate_collection_task`

**Function:** `generate_collection_task`

**Parameters:**
- `store_id`: ID of the store
- `store_uuid`: UUID of the store
- `callback_url`: Optional URL for callback when collection generation completes
- `attributes`: Optional dictionary of collection attributes
- `keywords_origin_type`: Source of keywords ('self' or 'competitor')
- `enforce_strict_collection_creation`: Whether to enforce strict collection creation

**Process:**
1. Sets initial task status in Redis
2. Updates database with processing status
3. Fetches attributes if not provided
4. Calls the collection generation function
5. Processes the results:
   - Updates status based on whether collections were generated
   - Stores collections in the database
   - Sends callback if URL provided
   - Notifies Slack about the status

**Return Value:**
- Dictionary containing the generated collections and metadata

### Get Task Status

**Task Name:** `app.RankCollections.celery_config_collection.get_task_status`

**Function:** `get_task_status`

**Parameters:**
- `task_id`: UUID of the collection generation task

**Process:**
1. Checks Redis for the task status
2. Returns the status if found

**Return Value:**
- Dictionary with task ID and status

## Collection Generation Pipeline

### Product Collection Pipeline

**Class:** `ProductCollectionPipeline`

**Initialization Parameters:**
- `store_id`: ID of the store
- `similarity_threshold`: Threshold for semantic similarity matching
- `use_sentence_transformers`: Whether to use sentence-transformers for embeddings
- `use_keybert`: Whether to use KeyBERT for keyword extraction
- `use_fuzzy_matching`: Whether to use fuzzy matching for keywords
- `keywords_origin_type`: Source of keywords ('self' or 'competitor')
- `attributes`: Optional attributes for collection creation
- `enforce_strict_collection_creation`: Whether to enforce strict collection creation

**Main Methods:**

#### `run`

**Parameters:**
- `store_id`: ID of the store
- `keywords_count`: Number of keywords to fetch
- `max_variations_per_keyword`: Maximum variations per keyword
- `max_product_groups`: Maximum number of product groups to create

**Process:**
1. Fetches keywords from competitors or products
2. Expands keywords with variations
3. Creates keyword-product groups
4. Generates collections using the keyword-product groups
5. Validates product IDs in collections
6. Adds fallback collections if needed

**Return Value:**
- Dictionary with collections and metadata

#### `fetch_keywords`

**Parameters:**
- `store_id`: ID of the store
- `keywords_count`: Number of keywords to fetch

**Process:**
1. Fetches keywords from competitors if `keywords_origin_type` is 'competitor'
2. Extracts keywords from products if `keywords_origin_type` is 'self'
3. Falls back to the other method if the first one fails

**Return Value:**
- List of keywords

#### `expand_keywords`

**Parameters:**
- `keywords`: List of keywords
- `max_variations_per_keyword`: Maximum variations per keyword

**Process:**
1. Extracts product attributes
2. Expands keywords with variations based on product attributes

**Return Value:**
- List of expanded keywords

#### `create_keyword_product_groups`

**Parameters:**
- `keywords`: List of keywords
- `max_groups`: Maximum number of groups to create

**Process:**
1. Fetches products for the store
2. Creates groups of products based on keyword matching
3. Ensures each group has a minimum number of products

**Return Value:**
- Dictionary mapping keywords to product groups

#### `generate_collections`

**Parameters:**
- `keywords`: List of keywords
- `keyword_product_groups`: Dictionary mapping keywords to product groups
- `attributes`: Optional attributes for collection creation

**Process:**
1. Extracts keywords and products from groups
2. Calls the collection creation function
3. Formats the results

**Return Value:**
- Dictionary with collections and metadata

### Create Collections From Competitors

**Function:** `create_collections_from_competitors`

**Parameters:**
- `store_id`: ID of the store
- `task_id`: Task ID for tracking
- `similarity_threshold`: Threshold for semantic similarity matching
- `keywords_count`: Number of keywords to fetch
- `max_variations_per_keyword`: Maximum variations per keyword
- `max_product_groups`: Maximum number of product groups to create
- `attributes`: Optional attributes for collection creation
- `keywords_origin_type`: Source of keywords ('self' or 'competitor')
- `enforce_strict_collection_creation`: Whether to enforce strict collection creation

**Process:**
1. Initializes the product collection pipeline
2. Runs the pipeline
3. Stores the collections in the database

**Return Value:**
- Dictionary with collections and metadata

### Create Collections From Competitors Sync

**Function:** `create_collections_from_competitors_sync`

**Parameters:**
- `store_id`: ID of the store
- `task_id`: Task ID for tracking
- `attributes`: Optional attributes for collection creation
- `keywords_origin_type`: Source of keywords ('self' or 'competitor')
- `enforce_strict_collection_creation`: Whether to enforce strict collection creation

**Process:**
1. Calls the async function with default parameters
2. Runs it in a synchronous context

**Return Value:**
- Dictionary with collections and metadata

## Database Operations

### Insert Collection Job Request Data

**Function:** `insert_collection_job_request_data`

**Parameters:**
- `collection_job_data`: Dictionary with request data
- `task_id`: Task ID for tracking
- `status`: Status of the job

**Process:**
1. Inserts a new record in the collection job table

### Update Collection Job Task ID

**Function:** `update_collection_job_task_id`

**Parameters:**
- `task_id`: Task ID for tracking
- `request_id`: Request ID from the API call

**Process:**
1. Updates the task ID in the collection job table

### Update Collection Job Status

**Function:** `update_collection_job_status_on_collection_job_table`

**Parameters:**
- `task_id`: Task ID for tracking
- `status`: Status of the job

**Process:**
1. Updates the status in the collection job table

### Store Collections In Database

**Function:** `store_collections_in_database`

**Parameters:**
- `task_id`: Task ID for tracking
- `collections`: List of collections
- `responsed_from_agent`: Full response from the agent
- `store_id`: ID of the store
- `is_manual_creation`: Whether the collection was created manually

**Process:**
1. Stores the collections in the database
2. Links them to the store and task

## Status Tracking

The system uses Redis to track the status of collection generation tasks. The following statuses are used:

- `INITIATED`: Task has been initiated
- `PROCESSING`: Task is being processed
- `STARTED`: Collection generation has started
- `COMPLETED`: Collection generation has completed successfully
- `FAILED`: Collection generation has failed
- `NO_COLLECTIONS`: No collections were generated

## Error Handling

The system includes comprehensive error handling at various levels:

1. **API Level**: Catches exceptions and returns appropriate error responses
2. **Task Level**: Catches exceptions, updates status, and sends notifications
3. **Pipeline Level**: Includes fallback mechanisms for keyword extraction and product grouping
4. **Database Level**: Handles database errors and provides fallback options

Each error is logged with details for debugging purposes.

---

This documentation provides a comprehensive overview of the collection creation workflow. For more detailed information about specific components, refer to the code documentation and comments. 