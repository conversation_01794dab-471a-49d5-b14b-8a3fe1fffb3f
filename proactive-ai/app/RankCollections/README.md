# RankCollections Module

This module provides functionality for creating product collections using competitor keywords and autogen agents.

## Features

- Fetch competitor keywords using the existing get_trending_keywords function
- Expand keywords with variations based on product attributes
- Match products to keywords using NLP and semantic similarity
- Group products by keyword relevance
- Generate optimized product collections with SEO-friendly titles and descriptions using autogen agents

## Usage

### API Endpoints

#### Create Collection

```
POST /create-collection
```

**Request Body:**

```json
{
  "store_id": 123,
  "similarity_threshold": 0.6,
  "use_clustering": true,
  "is_manual_creation": false,
  "use_competitor_keywords": true,
  "competitor_urls": [
    "https://competitor1.com",
    "https://competitor2.com"
  ]
}
```

**Parameters:**

- `store_id` (optional): Store ID
- `similarity_threshold` (optional): Threshold for semantic similarity matching (0-1)
- `use_clustering` (optional): Whether to use clustering for product grouping
- `is_manual_creation` (optional): Whether this is a manual creation request
- `use_competitor_keywords` (optional): Whether to use competitor keywords
- `competitor_urls` (optional): List of competitor URLs
- `keywords` (optional): List of keywords to use (if not using competitor keywords)
- `products` (optional): List of products to use (if not fetching from database)
- `attributes` (optional): Attributes for collection creation
- `callback_url` (optional): URL to call when collection creation is complete

#### Check Collection Status

```
GET /collection-status/{task_id}
```

**Parameters:**

- `task_id`: Task ID returned from create-collection endpoint

### Programmatic Usage

```python
from app.Utils.collections.product_collection_pipeline import create_collections_from_competitors_sync

# Create collections using competitor keywords
result = create_collections_from_competitors_sync(
    competitor_urls=["https://competitor1.com", "https://competitor2.com"],
    products=products_data,
    store_id=store_id,
    attributes=attributes_data,
    similarity_threshold=0.6
)

# Access the generated collections
collections = result.get('collections', [])
```

## Example

See the `examples/competitor_collections_example.py` file for a complete example of how to use the competitor-based collection generation pipeline.

To run the example:

```bash
# Run the example
python -m app.RankCollections.examples.competitor_collections_example
```

## Dependencies

- spaCy (with en_core_web_lg model)
- scikit-learn
- sentence-transformers
- rapidfuzz
- keybert
- autogen

## Architecture

The collection generation pipeline consists of the following components:

1. **KeywordExpander**: Expands keywords with variations based on product attributes
2. **StoreKeywordFetcher**: Fetches keywords using the existing get_trending_keywords function
3. **KeywordProductMatcher**: Matches products to keywords using NLP and semantic similarity
4. **ProductFilter**: Filters products by similarity or clustering
5. **AutogenCollectionBuilder**: Creates optimized product collections using autogen agents

The pipeline flow is as follows:

1. Fetch keywords using the existing get_trending_keywords function
2. Extract product attributes for variation generation
3. Expand keywords with variations based on product attributes
4. Match products to keywords using semantic similarity
5. Group products by keyword relevance
6. Generate optimized product collections using autogen agents 