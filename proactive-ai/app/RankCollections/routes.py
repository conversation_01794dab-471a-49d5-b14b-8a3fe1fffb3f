from fastapi import APIRouter
from .api.v1.sync import router as sync_router
from .api.v1.keywords import router as keywords_router
from .api.v1.fetch_data import router as fetch_data_router
# from .api.v1.create_collections import router as collection_creation_router
# from .api.v1.competitor_analysis import router as competitor_analysis_router
from .api.v2.create_collections import router as collection_creation_router

router = APIRouter()

router.include_router(sync_router, prefix="/sync", tags=["Sync Store Data"])
router.include_router(keywords_router, prefix="/keywords", tags=["Target Keywords"])
router.include_router(fetch_data_router, prefix="/fetch", tags=["Get data from our database"])
router.include_router(collection_creation_router, prefix='/collections', tags=['Collection Creation'])
# router.include_router(competitor_analysis_router, prefix='/analyze', tags=['Analyze'])


@router.get("/health", tags=["Health"])
async def health_check():
    """
    Health check endpoint to verify API is running
    """
    return {"status": "Rank Collection healthy"}
