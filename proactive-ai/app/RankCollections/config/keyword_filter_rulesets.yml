# Keyword filtering ruleset
and:
  - field: language_code
    operator: equals
    value: "en"
    value_type: string
  
  - or:
    - field: keyword_properties.keyword_difficulty
      operator: less_than
      value: "40"
      value_type: float
    
    - and:
      - field: keyword_info.search_volume
        operator: greater_than
        value: "900"
        value_type: int
      - field: keyword_info.competition_level
        operator: not_equals
        value: "HIGH"