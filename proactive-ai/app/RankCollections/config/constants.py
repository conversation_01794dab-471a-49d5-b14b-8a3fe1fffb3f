"""Constants for RankCollections module"""
# Attribute Types
ATTRIBUTE_TYPES = {
        "product_attributes": {
            "color_collections": True,
            "price_range_collections": False,
            "long_tail_keywords": True,
            "local_seo_collections": False
        },
        "customer_behavior": {
            "buying_patterns": False,
            "preferences": True,
            "engagement": True
        },
        "seasonal_trends": {
            "current_season": True,
            "upcoming_events": False,
            "trending_categories": True
        },
        "market_analysis": {
            "competition": False,
            "demand": True,
            "positioning": True
        }
    }

# Collection generation parameters
# Keywords Count
KEYWORDS_COUNT = 5

# Max variations per keyword
MAX_VARIATIONS_PER_KEYWORD = 2

# Max product groups
MAX_PRODUCT_GROUPS = 3


# Collection size constraints
MIN_PRODUCTS_IN_COLLECTION = 3
MAX_PRODUCTS_IN_COLLECTION = 50

# Similarity thresholds
DEFAULT_SIMILARITY_THRESHOLD = 0.5
FALLBACK_SIMILARITY_THRESHOLD = 0.5

# Clustering parameters
MIN_CLUSTERS = 2
MAX_CLUSTERS = 10

# Use sentence transformers
USE_SENTENCE_TRANSFORMERS = True

# Use keybert
USE_KEYBERT = True

# Use fuzzy matching
USE_FUZZY_MATCHING = True