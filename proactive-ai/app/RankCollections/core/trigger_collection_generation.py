# looping collection generation for all stores
import os
import requests
import logging
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('collection_generation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
MAX_RETRY_ATTEMPTS = 3
API_URL = os.getenv("API_URL")
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
CREATE_COLLECTION_ENDPOINT = f"{API_URL}/api/v1/collections/collections/create-collection"

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def notify_status(message: str, status: str = "info") -> None:
    """
    Placeholder function for status notifications.
    Will be replaced with Slack integration later.
    
    Args:
        message (str): The message to be sent
        status (str): Status level (info, warning, error)
    """
    if status == "error":
        logger.error(message)
    elif status == "warning":
        logger.warning(message)
    else:
        logger.info(message)

def check_frequency_eligibility(frequency: str, last_generated_on: Optional[datetime]) -> bool:
    """
    Check if store is eligible for collection generation based on frequency.
    
    Args:
        frequency (str): Frequency setting (daily, weekly, monthly)
        last_generated_on (Optional[datetime]): Last generation timestamp, can be None
        
    Returns:
        bool: True if eligible, False otherwise
    """
    # If last_generated_on is None, it means collections were never generated
    # So we should return True to trigger the first generation
    if last_generated_on is None:
        logger.info("No previous generation found, triggering first collection generation")
        return True
        
    current_time = datetime.now(timezone.utc)
    # Ensure last_generated_on is timezone-aware
    if last_generated_on.tzinfo is None:
        last_generated_on = last_generated_on.replace(tzinfo=timezone.utc)
    time_diff = current_time - last_generated_on
    
    if frequency == "daily" and time_diff >= timedelta(days=1):
        return True
    elif frequency == "weekly" and time_diff >= timedelta(weeks=1):
        return True
    elif frequency == "monthly" and time_diff >= timedelta(days=30):
        return True
    
    return False

# async def get_eligible_stores() -> List[Dict[str, Any]]:
#     """
#     Fetch stores eligible for collection generation based on frequency and last generation time.
#     Includes stores that failed generation today.
    
#     Returns:
#         List[Dict]: List of eligible store information
#     """
#     try:
#         result = supabase.table("rank_collections_schedule").select(
#             "store_id", "frequency", "last_generated_on", "last_generation_status"
#         ).eq("is_active_currently", True).execute()
        
#         eligible_stores = []
#         current_time = datetime.now(timezone.utc)
        
#         for store in result.data:
#             try:
#                 # Explicitly handle None case for last_generated_on
#                 if store.get("last_generated_on") is None:
#                     last_generated = None
#                 else:
#                     # Parse the ISO format string to timezone-aware datetime
#                     last_generated = datetime.fromisoformat(store.get("last_generated_on"))
#                     if last_generated.tzinfo is None:
#                         last_generated = last_generated.replace(tzinfo=timezone.utc)
                
#                 # Check if store should be eligible due to failure today
#                 is_failed_today = False
#                 if last_generated is not None:
#                     last_generated_date = last_generated.date()
#                     current_date = current_time.date()
#                     is_failed_today = (
#                         last_generated_date == current_date and 
#                         store.get("last_generation_status") == "failure"
#                     )
#                     if is_failed_today:
#                         logger.info(f"Store {store['store_id']} failed generation today, adding to eligible stores")
                
#                 # Store is eligible if it meets frequency criteria or failed today
#                 if check_frequency_eligibility(store["frequency"], last_generated) or is_failed_today:
#                     eligible_stores.append(store)
#                     if last_generated is None:
#                         logger.info(f"Store {store['store_id']} has no previous generation, adding to eligible stores")
                
#             except ValueError as e:
#                 logger.error(f"Error parsing last_generated_on for store {store['store_id']}: {str(e)}")
#                 continue
#             except ValueError as e:
#                 logger.error(f"Error parsing last_generated_on for store {store['store_id']}: {str(e)}")
#                 continue


        
#         logger.info(f"Found {len(eligible_stores)} eligible stores for collection generation")
#         return eligible_stores
        
#     except Exception as e:
#         logger.error(f"Error fetching eligible stores: {str(e)}")
#         raise

# For development
async def fetch_all_stores() -> List[Dict[str, Any]]:
    """
    Fetch all stores from the rank_collections_schedule table.
    
    Returns:
        List[Dict]: List of all store information
    """
    try:
        result = supabase.table("rank_collections_schedule").select(
            "store_id", "frequency", "last_generated_on", "last_generation_status"
        ).execute()
        
        logger.info(f"Fetched {len(result.data)} stores from the schedule")
        return result.data
        
    except Exception as e:
        logger.error(f"Error fetching all stores: {str(e)}")
        raise

# For development
async def get_eligible_stores() -> List[Dict[str, Any]]:
    """
    Fetch stores eligible for collection generation based on frequency and last generation time.
    Includes stores that failed generation today.
    
    Returns:
        List[Dict]: List of eligible store information
    """
    try:
        result = supabase.table("rank_collections_schedule").select(
            "store_id", "frequency", "last_generated_on", "last_generation_status"
        ).eq("is_active_currently", True).execute()
        
        eligible_stores = []
        current_time = datetime.now(timezone.utc)
        
        for store in result.data:
            try:
                # Explicitly handle None case for last_generated_on
                if store.get("last_generated_on") is None:
                    last_generated = None
                else:
                    # Parse the ISO format string to timezone-aware datetime
                    last_generated = datetime.fromisoformat(store.get("last_generated_on"))
                    if last_generated.tzinfo is None:
                        last_generated = last_generated.replace(tzinfo=timezone.utc)
                
                # Check if store should be eligible due to failure today
                is_failed_today = False
                if last_generated is not None:
                    last_generated_date = last_generated.date()
                    current_date = current_time.date()
                    is_failed_today = (
                        last_generated_date == current_date and 
                        store.get("last_generation_status") == "failure"
                    )
                    if is_failed_today:
                        logger.info(f"Store {store['store_id']} failed generation today, adding to eligible stores")
                
                # Store is eligible if it meets frequency criteria or failed today
                if check_frequency_eligibility(store["frequency"], last_generated) or is_failed_today:
                    eligible_stores.append(store)
                    if last_generated is None:
                        logger.info(f"Store {store['store_id']} has no previous generation, adding to eligible stores")
                
            except ValueError as e:
                logger.error(f"Error parsing last_generated_on for store {store['store_id']}: {str(e)}")
                continue
            except ValueError as e:
                logger.error(f"Error parsing last_generated_on for store {store['store_id']}: {str(e)}")
                continue

            
        
        logger.info(f"Found {len(eligible_stores)} eligible stores for collection generation")
        return eligible_stores
        
    except Exception as e:
        logger.error(f"Error fetching eligible stores: {str(e)}")
        raise

# fecth store details
async def fetch_store_token(store_id: int) -> List[Dict[str, Any]]:
    """
    Fetch api token for a given store.
    
    Args:
        store_id (int): ID of the store
        
    Returns:
        str: api_token
    """
    try:
        result = supabase.table("stores").select(
            "api_token"
        ).eq("id", store_id).execute()
        
        logger.info(f"Fetched token for store {store_id}")
        return result.data[0]['api_token']
        
    except Exception as e:
        logger.error(f"Error token for store {store_id}: {str(e)}")
        raise



async def fetch_store_products(store_id: int) -> List[Dict[str, Any]]:
    """
    Fetch all products for a given store.
    
    Args:
        store_id (int): ID of the store
        
    Returns:
        List[Dict]: List of products
    """
    try:
        result = supabase.table("products").select(
            "title", "description", "price", "handle", "tags", "vendor", "variants"
        ).eq("store_id", store_id).execute()
        
        logger.info(f"Fetched {len(result.data)} products for store {store_id}")
        return result.data
        
    except Exception as e:
        logger.error(f"Error fetching products for store {store_id}: {str(e)}")
        raise

async def create_collections(
    store_id: int,
    products: List[Dict[str, Any]],
    keywords: List[str]
) -> Dict[str, Any]:
    """
    Create collections for a store with retry mechanism.
    
    Args:
        store_id (int): Store ID
        products (List[Dict]): List of products
        keywords (List[str]): List of keywords for collection generation
        
    Returns:
        Dict: API response
    """
    payload = {
        "keywords": keywords,
        "feature_keys": ["title", "description", "tags"],
        "similarity_threshold": 0.5,
        "products": products,
        "store_id": store_id,
        "is_manual_creation": False
    }

    proactive_token = await fetch_store_token(payload['store_id'])

    for attempt in range(MAX_RETRY_ATTEMPTS):
        try:
            headers = {
                "X-PROACTIVE-TOKEN": proactive_token,
                "X-BINCHA-APP-TOKEN": "78f85f8edb2e33fd90e32850725a10c781e13b53ab7c5c33d67c0ec9cc52183f"
            }
            response = requests.post(CREATE_COLLECTION_ENDPOINT, json=payload, headers=headers)
            response.raise_for_status()
            
            logger.info(f"Successfully created collections for store {store_id}")
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.warning(f"Attempt {attempt + 1} failed for store {store_id}: {str(e)}")
            if attempt == MAX_RETRY_ATTEMPTS - 1:
                logger.error(f"All attempts failed for store {store_id}")
                raise
    
async def store_collection_result(
    store_id: int,
    collection_data: Dict[str, Any],
    status: str
) -> None:
    """
    Store collection generation result in database.
    
    Args:
        store_id (int): Store ID
        collection_data (Dict): Collection generation response
        status (str): Generation status (success/failure)
    """
    try:
        current_time = datetime.now(timezone.utc)
        
        # Update last generation status in schedule table
        supabase.table("rank_collections_schedule").update({
            "last_generated_on": current_time.isoformat(),
            "last_generation_status": status
        }).eq("store_id", store_id).execute()
        
        # Store collection data in new table (to be implemented)
        # This is a placeholder for the new table structure
        collection_result = {
            "store_id": store_id,
            "collection_data": collection_data,
            "status": status,
            "generated_at": current_time.isoformat()
        }
        
        # TODO: Replace with actual table name and structure
        supabase.table("collection_results").insert(collection_result).execute()
        
        logger.info(f"Stored collection result for store {store_id}")
        
    except Exception as e:
        logger.error(f"Error storing collection result for store {store_id}: {str(e)}")
        raise

async def main():
    """
    Main function to orchestrate the collection generation process.
    """
    logger.info("Starting automated collection generation process")
    
    try:
        # Get eligible stores
        eligible_stores = await get_eligible_stores()
        
        # Dummy keywords (to be replaced with third-party API call)
        dummy_keywords = ["summer", "new arrival", "trending", "bestseller"]
        
        # Process each eligible store
        for store in eligible_stores:
            store_id = store["store_id"]
            logger.info(f"Processing store {store_id}")
            
            try:
                # Fetch store products
                products = await fetch_store_products(store_id)
                
                if not products:
                    notify_status(
                        f"No products found for store {store_id}",
                        "warning"
                    )
                    continue
                
                # Create collections
                collection_response = await create_collections(
                    store_id,
                    products,
                    dummy_keywords
                )
                
                # Store successful result
                await store_collection_result(
                    store_id,
                    collection_response,
                    "success"
                )
                
                notify_status(
                    f"Successfully generated collections for store {store_id}",
                    "info"
                )
                
            except Exception as e:
                logger.error(f"Failed to process store {store_id}: {str(e)}")
                await store_collection_result(
                    store_id,
                    {"error": str(e)},
                    "failure"
                )
                notify_status(
                    f"Failed to generate collections for store {store_id}: {str(e)}",
                    "error"
                )
                continue
        
        logger.info("Completed automated collection generation process")
        
    except Exception as e:
        logger.error(f"Critical error in collection generation process: {str(e)}")
        notify_status(
            f"Critical error in collection generation process: {str(e)}",
            "error"
        )

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())