#!/usr/bin/env python3

import os
import sys
import asyncio
import signal
import logging
import fcntl
import atexit
from pathlib import Path
import uuid
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional, Tuple
from dotenv import load_dotenv
from supabase import create_client, Client

# Get the absolute path of the script's directory
SCRIPT_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = SCRIPT_DIR.parent

# Set up paths
LOG_DIR = PROJECT_ROOT / "logs"
LOCK_DIR = PROJECT_ROOT / "locks"
ENV_FILE = PROJECT_ROOT.parent / ".env"

# Create necessary directories
LOG_DIR.mkdir(exist_ok=True)
LOCK_DIR.mkdir(exist_ok=True)

# Load environment variables
load_dotenv(ENV_FILE)

# Lock file path
LOCK_FILE = LOCK_DIR / "collection_generator.lock"

# Configure logging
LOG_FILE = LOG_DIR / "collection_generation.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
PROCESS_TIMEOUT = 15 * 60  # 15 minutes in seconds
API_URL = os.getenv("API_URL")
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
CALLBACK_URL = f"{API_URL}/api/v1/collections/collections/automated-collection-result"

# Import after environment setup
from app.RankCollections.config.constants import DEFAULT_SIMILARITY_THRESHOLD
from app.RankCollections.celery_config_collection import generate_collection_task
from app.Models.dbHelpers import (
    get_all_product_data_belongs_to_a_store_except_embedding,
    get_store_uuid,
    get_keywords_for_colection_creation_origin_self,
    get_keywords_for_colection_creation_origin_competitor,
    fallback_to_used_keywords,
    get_attributes_for_collection_creation,
    update_schedule_status
)

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

class ProcessLock:
    """Process lock to prevent multiple instances from running"""
    
    def __init__(self, lock_file: Path):
        self.lock_file = lock_file
        self.lock_fd = None
        
    def acquire(self) -> bool:
        try:
            self.lock_fd = open(self.lock_file, 'w')
            fcntl.flock(self.lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
            self.lock_fd.write(str(os.getpid()))
            self.lock_fd.flush()
            return True
        except (IOError, OSError):
            if self.lock_fd:
                self.lock_fd.close()
                self.lock_fd = None
            return False
            
    def release(self):
        if self.lock_fd:
            fcntl.flock(self.lock_fd, fcntl.LOCK_UN)
            self.lock_fd.close()
            self.lock_fd = None
            
    def __enter__(self):
        if not self.acquire():
            logger.error("Another instance is already running")
            sys.exit(1)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()

class TimeoutHandler:
    """Handle process timeout"""
    def __init__(self, timeout: int):
        self.timeout = timeout
        self.original_handler = None

    def handle_timeout(self, signum, frame):
        raise TimeoutError(f"Process timed out after {self.timeout} seconds")

    def __enter__(self):
        self.original_handler = signal.signal(signal.SIGALRM, self.handle_timeout)
        signal.alarm(self.timeout)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        signal.alarm(0)
        signal.signal(signal.SIGALRM, self.original_handler)

async def notify_slack(message: str, level: str = "info", store_id: Optional[int] = None):
    """
    Placeholder for future Slack notification implementation
    """
    notification = {
        "message": message,
        "level": level,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    if store_id:
        notification["store_id"] = store_id
        
    logger.info(f"Slack notification would be sent: {notification}")

async def get_next_eligible_store() -> Optional[Dict[str, Any]]:
    """Fetch the next eligible store for collection generation"""
    try:
        logger.info("Fetching next eligible store for collection generation")
        
        # Fetch active stores ordered by last generation time
        result = supabase.table("rank_collections_schedule")\
            .select("store_id, frequency, last_generated_on, last_generation_status, is_active_currently")\
            .eq("is_active_currently", True)\
            .order('last_generated_on', desc=False)\
            .execute()
        
        if not result.data:
            return None

        # Find first eligible store
        current_time = datetime.now(timezone.utc)
        for store in result.data:
            last_generated_on = _parse_datetime(store.get("last_generated_on"))
            if _is_store_eligible(store, last_generated_on, current_time):
                return store

        return None

    except Exception as e:
        logger.error(f"Error fetching eligible store: {str(e)}")
        raise

def _parse_datetime(datetime_str: Optional[str]) -> Optional[datetime]:
    """Parse datetime string to datetime object"""
    if not datetime_str:
        return None
    try:
        dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        return dt.replace(tzinfo=timezone.utc) if dt.tzinfo is None else dt
    except ValueError:
        return None

def _is_store_eligible(
    store: Dict[str, Any], 
    last_generated_on: Optional[datetime],
    current_time: datetime
) -> bool:
    """Check if store is eligible for generation"""
    # If store is not active, it's not eligible
    if not store.get("is_active_currently", False):
        return False
        
    # If never generated or last generation failed, it's eligible
    if last_generated_on is None or store.get("last_generation_status") == "failure":
        return True
        
    # Check frequency threshold
    time_diff = current_time - last_generated_on
    
    frequency_thresholds = {
        "daily": timedelta(days=1),
        "weekly": timedelta(weeks=1),
        "monthly": timedelta(days=30)
    }
    
    return time_diff >= frequency_thresholds.get(store["frequency"], timedelta.max)

async def main():
    """Main function to generate collections for a single eligible store"""
    logger.info("Starting collection generation CRON job")
    await notify_slack("Starting collection generation CRON job")
    
    try:
        # Get next eligible store
        store = await get_next_eligible_store()
        
        if not store:
            logger.info("No eligible stores found. Exiting gracefully.")
            await notify_slack("No eligible stores found. Exiting gracefully.")
            return 0
            
        store_id = store["store_id"]
        logger.info(f"Processing store {store_id}")
        
        try:
            # Get store UUID for tracing
            store_uuid = await get_store_uuid(store_id)
            if not store_uuid:
                raise ValueError(f"Store UUID not found for store {store_id}")

            # Fetch store products
            products = await get_all_product_data_belongs_to_a_store_except_embedding(store_id)
            
            if not products:
                logger.warning(f"No products found for store {store_id}")
                await notify_slack(
                    f"No products found for store {store_id}",
                    "warning",
                    store_id
                )
                return 1
            
            # Get keywords from both self and competitor sources
            keywords = []
            competitor_keywords = await get_keywords_for_colection_creation_origin_competitor(store_id)

            if competitor_keywords:
                keywords.extend(competitor_keywords)
            else:
                self_keywords = await get_keywords_for_colection_creation_origin_self(store_id)
                if self_keywords:
                    keywords.extend(self_keywords)

            # Fallback to used keywords if no keywords found
            if not keywords:
                keywords = await fallback_to_used_keywords(store_id)
                
            if not keywords:
                logger.error(f"No keywords available for store {store_id}")
                await notify_slack(
                    f"No keywords available for store {store_id}",
                    "error",
                    store_id
                )
                return 1
            
            # Get collection attributes
            attributes = await get_attributes_for_collection_creation(store_id)
            
            # Generate task ID

            task_id = str(uuid.uuid4())
            
            # Launch Celery task
            generate_collection_task.apply_async(
                kwargs={
                    "keywords": keywords,
                    "similarity_threshold": DEFAULT_SIMILARITY_THRESHOLD,
                    "products_data": products,
                    "store_id": store_id,
                    "store_uuid": store_uuid,
                    "use_clustering": True,
                    "request_id": task_id,
                    "callback_url": None,
                    "attributes": attributes,
                    "is_manual_creation": False,
                    "use_self_keywords": True,
                    "use_competitor_keywords": True
                },
                task_id=task_id
            )
            
            logger.info(f"Successfully initiated collection generation for store {store_id}")
            await update_schedule_status(store_id=store_id, task_id=task_id, status='IN_PROGRESS')
            await notify_slack(
                f"Successfully initiated collection generation for store {store_id}",
                "info",
                store_id
            )
            return 0
            
        except Exception as e:
            logger.error(f"Failed to process store {store_id}: {str(e)}", exc_info=True)
            await notify_slack(
                f"Failed to process store {store_id}: {str(e)}",
                "error",
                store_id
            )
            return 1
            
    except Exception as e:
        logger.error(f"Critical error in collection generation process: {str(e)}", exc_info=True)
        await notify_slack(
            f"Critical error in collection generation process: {str(e)}",
            "error"
        )
        return 1

if __name__ == "__main__":
    with ProcessLock(LOCK_FILE):
        with TimeoutHandler(PROCESS_TIMEOUT):
            try:
                exit_code = asyncio.run(main())
                sys.exit(exit_code)
            except TimeoutError:
                logger.error("Process timed out")
                asyncio.run(notify_slack("Process timed out", "error"))
                sys.exit(1)
            except KeyboardInterrupt:
                logger.info("Process interrupted by user")
                asyncio.run(notify_slack("Process interrupted by user", "warning"))
                sys.exit(1)
            except Exception as e:
                logger.error(f"Unexpected error: {str(e)}", exc_info=True)
                asyncio.run(notify_slack(f"Unexpected error: {str(e)}", "error"))
                sys.exit(1)