import pandas as pd
import numpy as np
import spacy
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans, DBSCAN
import re
from tqdm import tqdm
from typing import List, Dict, Tuple, Set, Optional
from dataforseo_client import RestClient
import autogen
from autogen.agentchat.agent import Agent
from concurrent.futures import ThreadPoolExecutor
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KeywordExpander:
    """Class for expanding keywords based on variations and product matching."""
    
    def __init__(self, nlp_model: str = "en_core_web_lg"):
        """
        Initialize the KeywordExpander.
        
        Args:
            nlp_model: The spaCy model to use for NLP tasks
        """
        self.nlp = spacy.load(nlp_model)
        self.tfidf_vectorizer = TfidfVectorizer(
            min_df=1, 
            max_df=0.9, 
            ngram_range=(1, 3),
            stop_words='english'
        )
        
    def extract_attributes(self, keyword: str) -> Dict[str, str]:
        """
        Extract attributes from a keyword (product type, demographic, price point, etc.).
        
        Args:
            keyword: The keyword to analyze
            
        Returns:
            Dictionary of extracted attributes
        """
        doc = self.nlp(keyword.lower())
        
        # Initial empty attributes
        attributes = {
            "product_type": "",
            "demographic": "",
            "price_point": "",
            "quality": "",
            "other": []
        }
        
        # Extract product type (usually nouns)
        for token in doc:
            if token.pos_ == "NOUN" and not attributes["product_type"]:
                attributes["product_type"] = token.text
                
        # Look for demographics (men, women, kids, etc.)
        demographic_terms = ["men", "women", "kids", "children", "boy", "girl", "teen", "adult", "baby"]
        for term in demographic_terms:
            if term in keyword.lower():
                attributes["demographic"] = term
                break
                
        # Extract price points
        price_pattern = r'under\s*\$(\d+)|(\$\d+)'
        price_matches = re.findall(price_pattern, keyword)
        if price_matches:
            flat_matches = [x for sublist in price_matches for x in sublist if x]
            attributes["price_point"] = flat_matches[0]
            
        # Extract quality indicators
        quality_terms = ["premium", "luxury", "budget", "cheap", "affordable", "high-end", "low-cost"]
        for term in quality_terms:
            if term in keyword.lower():
                attributes["quality"] = term
                break
                
        # Add remaining key terms to "other"
        for token in doc:
            if (token.pos_ in ["ADJ", "ADV"] and 
                token.text not in attributes.values() and 
                token.text not in demographic_terms + quality_terms):
                attributes["other"].append(token.text)
                
        return attributes
    
    def generate_variations(self, keyword: str, product_attributes: List[Dict]) -> List[str]:
        """
        Generate variations of a keyword that would match products in catalog.
        
        Args:
            keyword: Original competitor keyword
            product_attributes: List of attribute dictionaries from your product catalog
            
        Returns:
            List of keyword variations
        """
        variations = []
        keyword_attributes = self.extract_attributes(keyword)
        
        # Generate variations by substituting different attributes
        for prod_attr in product_attributes:
            new_variation = keyword
            
            # If keyword has demographic but product has different demographic
            if keyword_attributes["demographic"] and prod_attr.get("demographic") and keyword_attributes["demographic"] != prod_attr["demographic"]:
                new_variation = new_variation.replace(keyword_attributes["demographic"], prod_attr["demographic"])
                variations.append(new_variation)
                
            # If keyword has price point but product is in different range
            if keyword_attributes["price_point"] and prod_attr.get("price_point"):
                price_pattern = r'under\s*\$\d+|\$\d+'
                new_price_term = f"under ${prod_attr['price_point']}" if "under" in keyword else f"${prod_attr['price_point']}"
                new_variation = re.sub(price_pattern, new_price_term, keyword)
                variations.append(new_variation)
            
            # Add more substitution rules here based on your specific needs
        
        # Add some generic variations
        if keyword_attributes["product_type"]:
            variations.append(f"best {keyword_attributes['product_type']}")
            variations.append(f"{keyword_attributes['product_type']} on sale")
            
        # Remove duplicates and the original keyword
        variations = list(set(variations))
        if keyword in variations:
            variations.remove(keyword)
            
        return variations
    
    def calculate_keyword_similarity(self, keyword1: str, keyword2: str) -> float:
        """
        Calculate semantic similarity between two keywords.
        
        Args:
            keyword1: First keyword
            keyword2: Second keyword
            
        Returns:
            Similarity score between 0 and 1
        """
        doc1 = self.nlp(keyword1.lower())
        doc2 = self.nlp(keyword2.lower())
        
        return doc1.similarity(doc2)
        
    def match_products_to_keywords(self, 
                                  products: List[Dict], 
                                  keywords: List[str],
                                  threshold: float = 0.6) -> Dict[str, List[Dict]]:
        """
        Match products to keywords based on similarity.
        
        Args:
            products: List of product dictionaries
            keywords: List of keywords
            threshold: Minimum similarity score to consider a match
            
        Returns:
            Dictionary mapping keywords to lists of matching products
        """
        # Create product descriptions
        product_descriptions = [
            f"{p.get('title', '')} {p.get('description', '')} {p.get('category', '')} {p.get('tags', '')}"
            for p in products
        ]
        
        # Create TF-IDF vectors
        product_vectors = self.tfidf_vectorizer.fit_transform(product_descriptions)
        keyword_vectors = self.tfidf_vectorizer.transform(keywords)
        
        # Calculate similarity matrix
        similarity_matrix = cosine_similarity(keyword_vectors, product_vectors)
        
        # Match keywords to products
        keyword_to_products = {}
        for i, keyword in enumerate(keywords):
            matched_indices = np.where(similarity_matrix[i] >= threshold)[0]
            keyword_to_products[keyword] = [products[idx] for idx in matched_indices]
            
        return keyword_to_products


class ProductGrouper:
    """Class for grouping products based on keyword matches."""
    
    def __init__(self):
        """Initialize the ProductGrouper."""
        pass
    
    def cluster_products(self, 
                         keyword_product_matches: Dict[str, List[Dict]],
                         n_clusters: Optional[int] = None,
                         min_cluster_size: int = 5) -> List[Dict]:
        """
        Cluster products into collections based on keyword matches.
        
        Args:
            keyword_product_matches: Dictionary mapping keywords to matching products
            n_clusters: Number of clusters to create (optional)
            min_cluster_size: Minimum size for a valid cluster
            
        Returns:
            List of collection dictionaries
        """
        # Create a product-keyword matrix (1 if product matches keyword, 0 otherwise)
        all_products = []
        product_ids = set()
        
        for products in keyword_product_matches.values():
            for product in products:
                if product["id"] not in product_ids:
                    all_products.append(product)
                    product_ids.add(product["id"])
        
        # Create feature matrix
        feature_matrix = np.zeros((len(all_products), len(keyword_product_matches)))
        
        for i, product in enumerate(all_products):
            for j, keyword in enumerate(keyword_product_matches.keys()):
                if product in keyword_product_matches[keyword]:
                    feature_matrix[i, j] = 1
        
        # Determine number of clusters if not specified
        if n_clusters is None:
            n_clusters = min(max(3, len(keyword_product_matches) // 3), 10)
        
        # Apply clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        clusters = kmeans.fit_predict(feature_matrix)
        
        # Create collections
        collections = []
        for i in range(n_clusters):
            cluster_indices = np.where(clusters == i)[0]
            if len(cluster_indices) >= min_cluster_size:
                cluster_products = [all_products[idx] for idx in cluster_indices]
                
                # Find dominant keywords for this cluster
                cluster_keyword_counts = {}
                for idx in cluster_indices:
                    for keyword, products in keyword_product_matches.items():
                        if all_products[idx] in products:
                            cluster_keyword_counts[keyword] = cluster_keyword_counts.get(keyword, 0) + 1
                
                # Sort keywords by frequency
                dominant_keywords = sorted(
                    cluster_keyword_counts.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:3]
                
                collections.append({
                    "id": f"collection_{i}",
                    "products": cluster_products,
                    "keywords": [kw for kw, _ in dominant_keywords],
                    "product_count": len(cluster_products)
                })
        
        return collections


class DataForSEOKeywordFetcher:
    """Class for fetching competitor keywords from DataForSEO API."""
    
    def __init__(self, username: str, password: str):
        """
        Initialize the DataForSEOKeywordFetcher.
        
        Args:
            username: DataForSEO API username
            password: DataForSEO API password
        """
        self.client = RestClient(username, password)
    
    def get_competitor_keywords(self, competitor_url: str, limit: int = 100) -> List[str]:
        """
        Get keywords that a competitor ranks for.
        
        Args:
            competitor_url: URL of the competitor
            limit: Maximum number of keywords to return
            
        Returns:
            List of keywords
        """
        # This is a simplified example - adjust to match actual DataForSEO API calls
        try:
            response = self.client.post(
                "/v3/dataforseo_labs/google/organic_competitors_domain",
                {
                    "target": competitor_url,
                    "limit": limit
                }
            )
            
            # Extract keywords from response (adjust based on actual API response structure)
            keywords = []
            if response and "results" in response:
                for result in response["results"]:
                    if "items" in result:
                        for item in result["items"]:
                            if "relevant_keywords" in item:
                                keywords.extend(item["relevant_keywords"])
            
            return keywords[:limit]
            
        except Exception as e:
            logger.error(f"Error fetching competitor keywords: {e}")
            return []


class AutogenCollectionBuilder:
    """Class for creating product collections using autogen agents."""
    
    def __init__(self):
        """Initialize the AutogenCollectionBuilder."""
        # Configure agents
        self.config_list = [
            {
                "model": "gpt-4",  # You can use any model you have access to
                "api_key": "your-api-key"  # Replace with actual key or env variable
            }
        ]
        
        # Define agents
        self.curator_agent = autogen.AssistantAgent(
            name="Curator",
            system_message="You are a retail merchandising expert. Your task is to curate collections of products based on keywords and product data.",
            llm_config={"config_list": self.config_list}
        )
        
        self.seo_agent = autogen.AssistantAgent(
            name="SEO_Expert",
            system_message="You are an SEO expert. Your task is to optimize collection names and descriptions for search engines.",
            llm_config={"config_list": self.config_list}
        )
        
        self.user_proxy = autogen.UserProxyAgent(
            name="User_Proxy",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=10
        )
    
    def build_collection(self, collection_data: Dict) -> Dict:
        """
        Build a finalized collection using autogen agents.
        
        Args:
            collection_data: Raw collection data with products and keywords
            
        Returns:
            Enhanced collection with name, description, and SEO elements
        """
        # Prepare the initial message with collection data
        product_sample = collection_data["products"][:5]  # Sample of products for context
        keywords = collection_data["keywords"]
        
        initial_message = f"""
        Create a product collection based on these keywords: {', '.join(keywords)}
        
        The collection contains {collection_data['product_count']} products.
        
        Here's a sample of the products:
        {product_sample}
        
        Please provide:
        1. A compelling collection name
        2. A short description (1-2 sentences)
        3. A longer description (3-4 sentences)
        4. SEO metadata (title, description, keywords)
        5. Suggested sorting/filtering options
        """
        
        # Start the conversation
        self.user_proxy.initiate_chat(
            self.curator_agent,
            message=initial_message
        )
        
        # Extract the final result
        final_message = self.user_proxy.chat_messages[self.curator_agent][-1]["content"]
        
        # Process the result (this is simplified - you'd need to parse the agent's response)
        result = {
            "id": collection_data["id"],
            "name": "Extract name from agent response",
            "short_description": "Extract short description from agent response",
            "long_description": "Extract long description from agent response",
            "seo_metadata": {
                "title": "Extract SEO title from agent response",
                "description": "Extract SEO description from agent response",
                "keywords": "Extract SEO keywords from agent response"
            },
            "products": collection_data["products"],
            "keywords": collection_data["keywords"]
        }
        
        return result


class ProductCollectionPipeline:
    """Main pipeline for forming product collections."""
    
    def __init__(self, 
                 dataseo_username: str, 
                 dataseo_password: str,
                 nlp_model: str = "en_core_web_lg"):
        """
        Initialize the ProductCollectionPipeline.
        
        Args:
            dataseo_username: DataForSEO API username
            dataseo_password: DataForSEO API password
            nlp_model: The spaCy model to use
        """
        self.keyword_fetcher = DataForSEOKeywordFetcher(dataseo_username, dataseo_password)
        self.keyword_expander = KeywordExpander(nlp_model)
        self.product_grouper = ProductGrouper()
        self.collection_builder = AutogenCollectionBuilder()
    
    def run(self, 
            competitor_urls: List[str], 
            products: List[Dict],
            keywords_per_url: int = 30,
            similarity_threshold: float = 0.6,
            n_collections: Optional[int] = None) -> List[Dict]:
        """
        Run the full pipeline to create product collections.
        
        Args:
            competitor_urls: List of competitor URLs to analyze
            products: List of your product dictionaries
            keywords_per_url: Number of keywords to fetch per competitor URL
            similarity_threshold: Threshold for product-keyword matching
            n_collections: Number of collections to create (optional)
            
        Returns:
            List of finalized collections
        """
        logger.info("Starting product collection pipeline")
        
        # 1. Fetch competitor keywords
        all_keywords = []
        for url in tqdm(competitor_urls, desc="Fetching competitor keywords"):
            keywords = self.keyword_fetcher.get_competitor_keywords(url, limit=keywords_per_url)
            all_keywords.extend(keywords)
        
        logger.info(f"Fetched {len(all_keywords)} keywords from competitors")
        
        # 2. Extract product attributes for variation generation
        product_attributes = []
        for product in tqdm(products, desc="Extracting product attributes"):
            product_text = f"{product.get('title', '')} {product.get('description', '')}"
            attributes = self.keyword_expander.extract_attributes(product_text)
            product_attributes.append(attributes)
        
        # 3. Generate keyword variations
        expanded_keywords = all_keywords.copy()
        for keyword in tqdm(all_keywords, desc="Generating keyword variations"):
            variations = self.keyword_expander.generate_variations(keyword, product_attributes)
            expanded_keywords.extend(variations)
        
        # Remove duplicates
        expanded_keywords = list(set(expanded_keywords))
        logger.info(f"Generated {len(expanded_keywords)} keywords after expansion")
        
        # 4. Match products to keywords
        keyword_product_matches = self.keyword_expander.match_products_to_keywords(
            products, 
            expanded_keywords,
            threshold=similarity_threshold
        )
        
        logger.info(f"Matched products to {len(keyword_product_matches)} keywords")
        
        # 5. Group products into collections
        raw_collections = self.product_grouper.cluster_products(
            keyword_product_matches,
            n_clusters=n_collections
        )
        
        logger.info(f"Created {len(raw_collections)} raw collections")
        
        # 6. Build final collections using autogen agents
        final_collections = []
        for collection in tqdm(raw_collections, desc="Building final collections"):
            enhanced_collection = self.collection_builder.build_collection(collection)
            final_collections.append(enhanced_collection)
        
        logger.info(f"Finalized {len(final_collections)} collections")
        
        return final_collections


# Example usage
if __name__ == "__main__":
    # Initialize the pipeline
    pipeline = ProductCollectionPipeline(
        dataseo_username="your_username",
        dataseo_password="your_password"
    )
    
    # Define competitor URLs and load products
    competitor_urls = [
        "https://competitor1.com",
        "https://competitor2.com",
        "https://competitor3.com"
    ]
    
    # Load your products (example)
    products = [
        {"id": 1, "title": "Men's Running Shoes", "description": "Premium running shoes for men", "price": 89.99},
        {"id": 2, "title": "Women's Yoga Pants", "description": "Comfortable yoga pants for women", "price": 49.99},
        # Add more products...
    ]
    
    # Run the pipeline
    collections = pipeline.run(
        competitor_urls=competitor_urls,
        products=products,
        n_collections=5
    )
    
    # Output the collections
    for collection in collections:
        print(f"Collection: {collection['name']}")
        print(f"Products: {len(collection['products'])}")
        print(f"Keywords: {collection['keywords']}")
        print("---")