#/api/v2/create-collections.py
from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel, HttpUrl
from typing import Dict, Optional
from fastapi.responses import JSONResponse

try:
    from app.Middleware.auth_check import verify_tokens
    from app.Utils.Helpers.validate_token import validate_auth_data
    from app.Utils.Helpers.response_formater import success_response, failure_response, error_response
    from app.Logging.logger_config import get_logger
    from app.Models.dbHelpers import (
        get_store_uuid,
        insert_collection_job_request_data,
        get_number_of_products_for_a_store,
        update_collection_job_task_id,
        update_collection_job_status_on_collection_job_table
    )
    from app.RankCollections.celery_config_collection import generate_collection_task
    from app.Utils.redis_utils import redis_client
except ImportError:
    # Fall back to direct imports without 'app.' prefix
    from Middleware.auth_check import verify_tokens
    from Utils.Helpers.validate_token import validate_auth_data
    from Utils.Helpers.response_formater import success_response, failure_response, error_response
    from Logging.logger_config import get_logger
    from Models.dbHelpers import (
        get_store_uuid,
        insert_collection_job_request_data,
        get_number_of_products_for_a_store,
        update_collection_job_task_id,
        update_collection_job_status_on_collection_job_table
    )
    from RankCollections.celery_config_collection import generate_collection_task
    from Utils.redis_utils import redis_client

router = APIRouter()
logger = get_logger(__name__)

class CollectionRequest(BaseModel):
    attributes: Optional[Dict[str, Dict[str, bool]]] = None
    callback_url: Optional[HttpUrl] = None
    keywords_origin_type: Optional[str] = 'self'
    enforce_strict_collection_creation: Optional[bool] = False

@router.post("/create-collection")
async def create_collection_endpoint(
    request: Request,
    collection_request: CollectionRequest,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate authentication and store
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        logger.info(
            "Starting collection creation request",
            extra={
                "origin": request.client.host,
                "input_params": collection_request.dict(),
                "is_manual_creation": collection_request.callback_url is not None,
                "keywords_origin_type": collection_request.keywords_origin_type
            }
        )
        
        store_uuid = await get_store_uuid(store_id)
        
        if not store_uuid:
            logger.error("Invalid store UUID", extra={"request_id": request_id, "store_id": store_id})
            return failure_response(message="Store not found", status_code=404)

        # Initialize database entry
        request_data = collection_request.dict()
        if request_data.get('callback_url'):
            request_data['callback_url'] = str(request_data['callback_url'])
        await insert_collection_job_request_data(request_data, request_id, 'INITIATED')

        # Fetch products data
        products_count = await get_number_of_products_for_a_store(store_id)
        if products_count == 0:
            logger.warning("No products found", extra={"request_id": request_id, "store_id": store_id})
            return failure_response(message="No products available", status_code=404)
        
        # Launch Celery task
        task = generate_collection_task.delay(
            store_id=store_id,
            store_uuid=store_uuid,
            callback_url=str(collection_request.callback_url) if collection_request.callback_url else None,
            attributes=collection_request.attributes,
            keywords_origin_type=collection_request.keywords_origin_type,
            enforce_strict_collection_creation=collection_request.enforce_strict_collection_creation
        )
        
        # Update task ID in database
        await update_collection_job_task_id(task.id, request_id)

        return success_response(
            message="Collection generation started",
            data={
                "task_id": task.id,
                "message": "INITIATED - COLLECTION GENERATION TASK",
            }
        )

    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        if auth_data.get("request_id"):
            await update_collection_job_status_on_collection_job_table(auth_data.get("request_id"), 'FAILED')
        return error_response(message="Internal server error", error=str(e))

@router.get("/collection-status/{task_id}")
async def get_collection_status(
    task_id: str,
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        store_id, _, _ = await validate_auth_data(auth_data, request)
        
        logger.info("Checking collection status", extra={"task_id": task_id, "store_id": store_id})

        if not redis_client:
            return error_response(message="Status service unavailable")
        
        status = redis_client.get(f'collection_task_{task_id}_status')
        if not status:
            return failure_response(message="Task not found", status_code=404)
        
        status_value = status.decode() if isinstance(status, bytes) else status
        
        return success_response(
            message="Status retrieved successfully",
            data={
                "task_id": task_id,
                "status": status_value
            }
        )

    except Exception as e:
        logger.error(f"Status check failed: {str(e)}", exc_info=True)
        return error_response(message="Status check failed", error=str(e)) 
