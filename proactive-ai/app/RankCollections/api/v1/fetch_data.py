from fastapi import APIRouter, Depends, HTTPException, Request
from typing import List, Optional
from datetime import datetime
import random

# Import auth middleware
from Middleware.auth_check import verify_tokens
from Utils.Helpers.response_formater import success_response, error_response
from Utils.Helpers.validate_token import validate_auth_data
from Logging.logger_config import get_logger
from Models.dbHelpers import get_created_collections_from_db_and_update_status, get_number_of_products_for_a_store

router = APIRouter()
logger = get_logger(__name__)

# Dummy function to get best schedule timing
async def get_best_schedule_timing(store_id: str):
    # This is a placeholder - replace with actual ML model or database query

    data = [{
        "day": "Tuesday",
        "time": "14:30",
        "traffic_details": "High",
        "competition_details": "Low",
        "score": 92  # Score between 75-100
    },
    {
        "day": "Thursday",
        "time": "4:30",
        "traffic_details": "Medium",
        "competition_details": "Low",
        "score": 83  # Score between 75-100
    },
    {
        "day": "Friday",
        "time": "18:30",
        "traffic_details": "High",
        "competition_details": "High",
        "score": 97  # Score between 75-100
    },
    {
        "day": "Monday",
        "time": "10:00",
        "traffic_details": "Low",
        "competition_details": "Medium",
        "score": 78
    },
    {
        "day": "Wednesday",
        "time": "12:00",
        "traffic_details": "High",
        "competition_details": "Low",
        "score": 85
    },
    {
        "day": "Saturday",
        "time": "16:00",
        "traffic_details": "Medium",
        "competition_details": "High",
        "score": 80
    },
    {
        "day": "Sunday",
        "time": "11:00",
        "traffic_details": "High",
        "competition_details": "Medium",
        "score": 90
    },
    {
        "day": "Tuesday",
        "time": "09:00",
        "traffic_details": "Low",
        "competition_details": "Low",
        "score": 75
    },
    {
        "day": "Thursday",
        "time": "15:00",
        "traffic_details": "Medium",
        "competition_details": "Medium",
        "score": 88
    },
    {
        "day": "Friday",
        "time": "20:00",
        "traffic_details": "High",
        "competition_details": "Low",
        "score": 95
    },
    {
        "day": "Saturday",
        "time": "13:00",
        "traffic_details": "Medium",
        "competition_details": "High",
        "score": 82
    },
    {
        "day": "Sunday",
        "time": "17:00",
        "traffic_details": "Low",
        "competition_details": "Medium",
        "score": 79
    }]
    
    return random.sample(data, 3)

@router.get("/created-collections", response_description="Fetch created collections from database")
async def get_created_collections(
    request: Request,
    count: int = 2,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start of fetch operation
        logger.info(
            "Starting collections fetch",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "requested_count": count
            }
        )

        # Fetch collections from database
        collections = await get_created_collections_from_db_and_update_status(store_id=store_id, count=count)
        print(f"collections: {collections}")
        
        # Check if warning exists before accessing it
        if collections.get('warning'):
            print("WARNING - CREATE MORE COLLECTIONS", collections['warning'])
            
        # Log successful fetch
        logger.info(
            "Successfully fetched collections",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "fetch_status": "success"
            }
        )
        
        return success_response(
            message="Collections fetched successfully",
            data={
                "collections": collections['data'],
                "total_count": len(collections['data'])
            }
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in get_created_collections",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to fetch collections",
            error=str(e)
        )

@router.get("/best-schedule-timing", response_description="Get optimal scheduling time for collections")
async def get_best_timing(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start of timing analysis
        logger.info(
            "Starting best timing analysis",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id
            }
        )

        # Get timing recommendation
        timing_data = await get_best_schedule_timing(store_id)
        
        # Log successful analysis
        logger.info(
            "Successfully retrieved timing recommendation",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "analysis_status": "success"
            }
        )
        
        return success_response(
            message="Best scheduling time retrieved successfully",
            data=timing_data
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in get_best_timing",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to retrieve best scheduling time",
            error=str(e)
        )


@router.get("/get-synced-product-count", response_description="Get number of products synced on db")
async def get_synced_product_count(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start 
        logger.info(
            "Fetching number of products synced on our db",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id
            }
        )

        # get product count from db
        product_count = await get_number_of_products_for_a_store(store_id=store_id)
        
        # Log successful analysis
        logger.info(
            "Successfully retrieved product count",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                
            }
        )
        
        return success_response(
            message="Synced product count retrieved successfully",
            data=product_count
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in get_product_count",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to retrieve product count",
            error=str(e)
        )

@router.get("/dashboard-metrics", response_description="Get dashboard metrics and analytics")
async def get_dashboard_metrics(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start of metrics fetch
        logger.info(
            "Starting dashboard metrics fetch",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id
            }
        )

        # TODO: Replace with actual metrics calculation
        # This is a placeholder response
        metrics = {
            # "conversion_rate": random.randint(10, 40),
            # "opportunity_score": random.randint(10, 35)
            "conversion_rate": 24,
            "opportunity_score": 40
        }
        
        # Log successful metrics fetch
        logger.info(
            "Successfully retrieved dashboard metrics",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "metrics_fetch_status": "success"
            }
        )
        
        return success_response(
            message="Dashboard metrics retrieved successfully",
            data=metrics
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in get_dashboard_metrics",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to retrieve dashboard metrics",
            error=str(e)
        )

