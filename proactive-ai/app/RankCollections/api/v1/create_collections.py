#/api/v1/create-collections.py
'''
Create Collections for our product lists with enhanced security, logging and error handling
ROUTES:
1. /create-collection
2. /collection-status/{task_id}
'''
from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel, HttpUrl, validator
from typing import List, Dict, Any, Optional
import redis
from pathlib import Path
import json

# Import auth middleware and utilities
from Middleware.auth_check import  verify_tokens
from Utils.Helpers.validate_token import validate_auth_data
from Utils.Helpers.response_formater import success_response, failure_response, error_response
from Logging.logger_config import get_logger
from Models.dbHelpers import (
    get_store_uuid,
    insert_collection_job_request_data,
    update_collection_job_task_id,
    update_collection_job_status_on_collection_job_table,
    get_all_products_data_belongs_to_a_store,
    get_keywords_for_colection_creation_origin_self,
    get_keywords_for_colection_creation_origin_competitor,
    fallback_to_used_keywords,
    get_attributes_for_collection_creation,
    get_competitor_domains
)
from app.RankCollections.celery_config_collection import generate_collection_task, get_task_status
from RankCollections.config.default_attributes import default_attributes
from app.Utils.redis_utils import redis_client
from Utils.get_trending_keywords import KeywordService, Config
from app.RankCollections.api.v1.keywords import get_trending_keywords, KeywordRequest

router = APIRouter()
logger = get_logger(__name__)

# Configuration flag to determine status check method
USE_DIRECT_REDIS = True  # Set to False to use Celery

# Initialize KeywordService with ruleset file
try:
    ruleset_file = Path(__file__).parent / '../../config/keyword_filter_rulesets.yml'
    config = Config(ruleset_file=str(ruleset_file))
except FileNotFoundError:
    logger.warning(
        "Custom ruleset file not found, using default ruleset",
        ruleset_file=str(ruleset_file)
    )
    config = Config()  # Will use default ruleset

keyword_service = KeywordService(config)

async def get_default_attributes(store_id: int) -> list:
    return default_attributes


class CollectionRequest(BaseModel):
    attributes: Optional[Dict[str, Dict[str, bool]]] = None
    keywords: Optional[List[str]] = None
    feature_keys: Optional[List[str]] = None
    similarity_threshold: Optional[float] = 0.5
    products: Optional[List[Dict[str, Any]]] = None
    store_id: Optional[int] = None
    use_clustering: Optional[bool] = True
    callback_url: Optional[HttpUrl] = None
    is_manual_creation: Optional[bool] = False
    keywords_origin_type: Optional[str] = 'self'

    @validator('callback_url', always=True)
    def validate_callback_url(cls, v, values):
        if values.get('is_manual_creation') and v is None:
            raise ValueError('callback_url must be provided when is_manual_creation is true')
        return v

    @validator('similarity_threshold')
    def validate_similarity_threshold(cls, v):
        if v is not None and (v < 0 or v > 1):
            raise ValueError('similarity_threshold must be between 0 and 1')
        return v

@router.post("/create-collection", response_description="Initialize collection generation process")
async def create_collection_endpoint(
    request: Request,
    collection_request: CollectionRequest,
    auth_data: dict = Depends(verify_tokens)
):
    # Initialize request-level logging context
    request_id = None
    store_id = None
    task = None  # Initialize task to None
    
    try:
        logger.info(
            "Starting collection creation request",
            extra={
                "origin": request.client.host,
                "input_params": collection_request.dict(exclude={'products'}),
                "is_manual": collection_request.is_manual_creation
            }
        )
        
        # Validate authentication
        try:
            store_id, app_id, request_id = await validate_auth_data(auth_data, request)
            store_id = collection_request.store_id or store_id
        except Exception as auth_error:
            logger.error(
                "Authentication failed",
                error=str(auth_error),
                stack_info=True
            )
            return error_response(
                message="Authentication failed",
                error="Invalid or expired authentication tokens"
            )
            
        # Validate store and fetch UUID
        try:
            store_uuid = await get_store_uuid(store_id)
            if not store_uuid:
                logger.error(
                    "Invalid store UUID",
                    request_id=request_id,
                    store_id=store_id
                )
                return failure_response(
                    message="Store not found or invalid store ID",
                    status_code=404
                )
        except Exception as store_error:
            logger.error(
                "Error fetching store UUID",
                request_id=request_id,
                store_id=store_id,
                error=str(store_error),
                stack_info=True
            )
            return error_response(
                message="Failed to validate store",
                error="Could not fetch store information"
            )

        # Store the collection job request data in the collection jobs table
        try:
            await insert_collection_job_request_data(collection_request.dict(), request_id, 'INITIATED')
        except Exception as e:
            logger.error(f"Error storing collection job request data: {str(e)}")

    # Fetch all data required for collection creation

        #Products data:
        products_data = collection_request.products
        if not products_data:
            try:
                products_data = await get_all_products_data_belongs_to_a_store(store_id=store_id)
                # print("products_data", products_data)
                if not products_data:
                    logger.warning(
                        "No products found",
                        request_id=request_id,
                        store_id=store_id
                    )
                    return failure_response(
                        message="No products available for collection creation",
                        status_code=404
                    )
            except Exception as e:
                logger.error(f"Error fetching products data: {str(e)}")
                return error_response(
                    message="Failed to fetch product data",
                    error="Could not retrieve store products"
                )
        
        #keywords data: 
        keywords_data = collection_request.keywords
        if not keywords_data:
            origin_type = collection_request.keywords_origin_type
            try:
                # Create base keyword request
                keyword_request = KeywordRequest(
                    force_new_generation=False,
                    generate_based_on_store_domain=False,
                    generate_based_on_competitors_domains=False,
                    generate_based_on_target_keywords=False
                )

                if origin_type == 'self':
                    keyword_request.generate_based_on_store_domain = True
                elif origin_type == 'competitor':
                    # Fetch competitor domains
                    competitor_domains = await get_competitor_domains(store_id)
                    if not competitor_domains:
                        logger.warning(
                            "No competitor domains found for store",
                            store_id=store_id
                        )
                        raise ValueError("No competitor domains found")
                        
                    keyword_request.generate_based_on_competitors_domains = True
                    keyword_request.competitors_domain = competitor_domains
                else:
                    raise ValueError(f"Invalid keywords origin type: {origin_type}")

                logger.debug({
                    "keyword_request": keyword_request.dict(),
                    "origin_type": origin_type,
                    "store_id": store_id,
                    "request_id": request_id
                })

                # Get trending keywords
                keywords_response = await get_trending_keywords(
                    request=request,
                    keyword_request=keyword_request,
                    auth_data=auth_data,
                    limit=3
                )

                # Access the response content properly
                response_content = keywords_response.body.decode()
                response_data = json.loads(response_content)

                if response_data.get("status") == "success":
                    keywords_data = response_data.get("data", {}).get("keywords", [])
                    if not keywords_data:
                        raise ValueError("No keywords generated")
                else:
                    raise ValueError(f"Failed to generate keywords: {response_data.get('message')}")

            except Exception as e:
                logger.error(
                    "Error generating keywords - falling back to default keywords",
                    request_id=request_id,
                    store_id=store_id,
                    error=str(e),
                    stack_info=True
                )
                # Fallback to generic product keywords
                keywords_data = [
                    "seasonal-products",
                    "trending-items",
                    "hot-sale",
                    "best-sellers",
                    "new-arrivals",
                    "popular-items"
                ]

        if not keywords_data:
            logger.error(
                "No keywords available after all attempts",
                request_id=request_id,
                store_id=store_id
            )
            return failure_response(
                message="Failed to generate keywords for collection creation",
                status_code=500
            )
        
        # print("keywords_data!!!!!!!!!!!!!!!!!!!!!!!!!!!", keywords_data)

        # attributes data:
        attributes_data = collection_request.attributes
        if not attributes_data:
            attributes_data = await get_attributes_for_collection_creation(store_id=store_id)
        if not attributes_data:
            logger.warning(
                "No attributes found - fallback to default attributes",
                request_id=request_id,
                store_id=store_id
            )
            attributes_data = await get_default_attributes(store_id=store_id)
        


        # Handle collection creation based on type
        try:
            logger.info(f"Creating collection task for store {store_id}")
            # Create Celery task
            task = generate_collection_task.delay(
                keywords=collection_request.keywords,
                # keywords=["snowboard", "liquid", "hydrogen"],
                similarity_threshold=collection_request.similarity_threshold,
                products_data=products_data,
                store_id=store_id,
                store_uuid=store_uuid,
                use_clustering=collection_request.use_clustering,
                request_id=request_id,
                is_manual_creation=collection_request.is_manual_creation,
                callback_url=str(collection_request.callback_url),
                attributes=collection_request.attributes,
                use_self_keywords=collection_request.keywords_origin_type == 'self' if collection_request.keywords_origin_type else True,
                use_competitor_keywords=collection_request.keywords_origin_type == 'competitor' if collection_request.keywords_origin_type else False
            )

            #log the process
            logger.info(
                "Collection Job created",
                request_id=request_id,
                task_id=task.id,
                store_id=store_id,
                is_manual=collection_request.is_manual_creation
            )

            # Update the task id in the collection jobs table
            try:
                await update_collection_job_task_id(task.id, request_id)
            except Exception as e:
                logger.error(f"Error updating collection job task_id: {str(e)}")

            # # Append task ID to schedule
            # await append_task_id_to_schedule(store_id=store_id, task_id=task.id)

            return success_response(
                message="Collection generation process started",
                data={"task_id": task.id}
            )

        except Exception as creation_error:
            logger.error(
                "Collection creation failed",
                request_id=request_id,
                store_id=store_id,
                error=str(creation_error),
                is_manual=collection_request.is_manual_creation,
                stack_info=True
            )

            # Update the status in the collection jobs table
            try:
                await update_collection_job_status_on_collection_job_table(task_id=task.id, status='FAILED')
            except Exception as e:
                logger.error(f"Error updating collection job status: {str(e)}")


            return error_response(
                message="Failed to create collection",
                error="An unexpected error occurred during collection creation"
            )
            
    except Exception as e:
        # Catch-all for unexpected errors
        logger.error(
            "Unexpected error in create_collection_endpoint",
            request_id=request_id,
            store_id=store_id,
            error=str(e),
            stack_info=True
        )

        # Update the status in the collection jobs table
        try:
            await update_collection_job_status_on_collection_job_table(task_id=task.id, status='FAILED')
        except Exception as e:
            logger.error(f"Error updating collection job status: {str(e)}")

        return error_response(
            message="Internal server error",
            error="An unexpected error occurred"
        )


@router.get("/collection-status/{task_id}", response_description="Get collection generation task status")
async def get_collection_status(
    task_id: str,
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        logger.info(
            "Checking collection task status",
            request_id=request_id,
            task_id=task_id
        )

        if USE_DIRECT_REDIS:
            try:
                if not redis_client:
                    raise ConnectionError("Redis connection not available")
                
                status = redis_client.get(f'collection_task_{task_id}_status')
                if not status:
                    return failure_response(
                        message="Task not found",
                        status_code=404
                    )
                
                status_data = {
                    "task_id": task_id,
                    "status": status
                }
            except redis.RedisError as e:
                logger.error(
                    "Redis connection error",
                    request_id=request_id,
                    task_id=task_id,
                    error=str(e)
                )
                return error_response(
                    message="Redis service unavailable",
                    error="Could not connect to status service"
                )
        else:
            try: 
                # Use Celery for status check : TO-DO: Fix this import issue
                from asgiref.sync import sync_to_async
                
                # Convert sync Celery task to async
                task_result = await sync_to_async(get_task_status.delay)(task_id)
                status_data = await sync_to_async(task_result.get)(timeout=5)
                
                if not status_data:
                    return failure_response(
                        message="Task not found",
                        status_code=404
                    )
            except TimeoutError:
                logger.error(
                    "Status check timeout",
                    request_id=request_id,
                    task_id=task_id
                )
                return error_response(
                    message="Status check timed out",
                    error="Operation timed out while retrieving task status"
                )
        
        logger.info(
            "Collection task status retrieved",
            request_id=request_id,
            task_id=task_id,
            status=status_data
        )
        
        return success_response(
            message="Task status retrieved successfully",
            data=status_data
        )
        
    except Exception as e:
        logger.error(
            "Error retrieving task status",
            request_id=request_id if 'request_id' in locals() else None,
            task_id=task_id,
            error=str(e),
            stack_info=True
        )
        return error_response(
            message="Failed to retrieve task status",
            error=str(e)
        )