from fastapi import APIRouter, Request, Depends
from typing import Dict, List
from pydantic import BaseModel
from Middleware.auth_check import verify_tokens
from Utils.Helpers.response_formater import success_response, error_response
from Logging.logger_config import get_logger
from Utils.Helpers.validate_token import validate_auth_data
from Utils.competitor_analysis import analyze_competitor, analyze_competitor_for_keyword

router = APIRouter()
logger = get_logger()

class CompetitorAnalysisRequest(BaseModel):
    competitor_urls: List[str]

@router.post("/competitors")
async def analyze_competitors(
    request: Request,
    analysis_request: CompetitorAnalysisRequest,
    auth_data: dict = Depends(verify_tokens)
) -> Dict:
    """
    Analyze competitor websites to generate strategic product collections and keywords.
    Returns analysis results and recommendations.
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        logger.info(
            "Starting competitor analysis request",
            extra={
                "origin": request.client.host,
                "request_id": request_id,
                "store_id": store_id,
                "app_id": app_id,
                "competitor_urls": analysis_request.competitor_urls
            }
        )
        
        results = await analyze_competitor(
            competitor_urls=analysis_request.competitor_urls,
            store_id=store_id
        )
        
        logger.info(
            "Successfully completed competitor analysis",
            request_id=request_id,
        )
        
        return success_response(
            message="Competitor analysis completed successfully",
            data=results['data']
        )
        
    except Exception as e:
        logger.error(
            "Failed to complete competitor analysis",
            request_id=request_id if 'request_id' in locals() else None,
            error=str(e),
            error_type=type(e).__name__,
            stack_info=True,
            extra={
                "origin": request.client.host if 'request' in locals() else None,
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "competitor_urls": analysis_request.competitor_urls
            }
        )
        return error_response(
            message="Failed to complete competitor analysis",
            error=str(e)
        )
    


@router.post("/keywords")
async def analyze_competitors(
    request: Request,
    analysis_request: CompetitorAnalysisRequest,
    auth_data: dict = Depends(verify_tokens)
) -> Dict:
    """
    Analyze competitor websites to generate strategic product collections and keywords.
    Returns analysis results and recommendations.
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        logger.info(
            "Starting competitor analysis request",
            extra={
                "origin": request.client.host,
                "request_id": request_id,
                "store_id": store_id,
                "app_id": app_id,
                "competitor_urls": analysis_request.competitor_urls
            }
        )
        
        results = await analyze_competitor_for_keyword(
            competitor_urls=analysis_request.competitor_urls,
            store_id=store_id
        )
        
        logger.info(
            "Successfully completed competitor analysis",
            request_id=request_id,
        )
        
        return success_response(
            message="Competitor analysis completed successfully",
            data=results['data']
        )
        
    except Exception as e:
        logger.error(
            "Failed to complete competitor analysis",
            request_id=request_id if 'request_id' in locals() else None,
            error=str(e),
            error_type=type(e).__name__,
            stack_info=True,
            extra={
                "origin": request.client.host if 'request' in locals() else None,
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "competitor_urls": analysis_request.competitor_urls
            }
        )
        return error_response(
            message="Failed to complete competitor analysis",
            error=str(e)
        )
    