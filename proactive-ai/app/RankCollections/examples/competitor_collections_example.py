"""
Example script demonstrating how to use the competitor-based collection generation pipeline.
"""
import asyncio
import json
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# Add project root to Python path
project_root = str(Path(__file__).resolve().parents[3])
if project_root not in sys.path:
    sys.path.append(project_root)

from app.Utils.collections.product_collection_pipeline import (
    ProductCollectionPipeline,
    create_collections_from_competitors
)
from app.Logging.logger_config import get_logger

logger = get_logger(__name__)

# Sample product data for testing
SAMPLE_PRODUCTS = [
    {
        "id": "1",
        "title": "Men's Premium Snowboard",
        "description": "High-end snowboard for advanced riders. Perfect for powder and backcountry.",
        "price": 599.99,
        "category": "Winter Sports",
        "tags": ["snowboard", "winter", "premium", "men"]
    },
    {
        "id": "2",
        "title": "Women's All-Mountain Snowboard",
        "description": "Versatile snowboard for all terrain types. Great for intermediate riders.",
        "price": 449.99,
        "category": "Winter Sports",
        "tags": ["snowboard", "winter", "women", "all-mountain"]
    },
    {
        "id": "3",
        "title": "Kids' Beginner Snowboard Package",
        "description": "Complete package for young beginners including board, bindings and boots.",
        "price": 299.99,
        "category": "Winter Sports",
        "tags": ["snowboard", "winter", "kids", "beginner"]
    },
    {
        "id": "4",
        "title": "Freestyle Snowboard",
        "description": "Flexible board designed for park and pipe tricks. Perfect for freestyle riders.",
        "price": 379.99,
        "category": "Winter Sports",
        "tags": ["snowboard", "winter", "freestyle", "park"]
    },
    {
        "id": "5",
        "title": "Men's Insulated Ski Jacket",
        "description": "Waterproof and breathable jacket with thermal insulation for cold weather.",
        "price": 199.99,
        "category": "Winter Apparel",
        "tags": ["jacket", "ski", "men", "insulated"]
    },
    {
        "id": "6",
        "title": "Women's 3-in-1 Ski Jacket",
        "description": "Versatile jacket with removable inner layer for varying weather conditions.",
        "price": 249.99,
        "category": "Winter Apparel",
        "tags": ["jacket", "ski", "women", "3-in-1"]
    },
    {
        "id": "7",
        "title": "Unisex Ski Goggles",
        "description": "Anti-fog goggles with UV protection and wide field of view.",
        "price": 89.99,
        "category": "Winter Accessories",
        "tags": ["goggles", "ski", "unisex", "accessories"]
    },
    {
        "id": "8",
        "title": "Ski and Snowboard Helmet",
        "description": "Lightweight helmet with adjustable ventilation and audio compatibility.",
        "price": 129.99,
        "category": "Winter Accessories",
        "tags": ["helmet", "ski", "snowboard", "safety"]
    },
    {
        "id": "9",
        "title": "Thermal Base Layer Set",
        "description": "Moisture-wicking thermal top and bottom for maximum warmth and comfort.",
        "price": 79.99,
        "category": "Winter Apparel",
        "tags": ["base layer", "thermal", "unisex"]
    },
    {
        "id": "10",
        "title": "Ski Boot Bag",
        "description": "Durable bag with separate compartments for boots, helmet, and accessories.",
        "price": 59.99,
        "category": "Winter Accessories",
        "tags": ["bag", "ski", "boots", "accessories"]
    }
]

# Sample competitor URLs
COMPETITOR_URLS = [
    "https://www.burton.com",
    "https://www.evo.com",
    "https://www.the-house.com"
]

# Sample store ID for testing
SAMPLE_STORE_ID = 1

async def run_example():
    """Run the example pipeline."""
    try:
        print("Starting competitor-based collection generation...")
        
        # Initialize the pipeline
        pipeline = ProductCollectionPipeline(
            store_id=SAMPLE_STORE_ID,
            similarity_threshold=0.6,
            use_sentence_transformers=True,
            use_keybert=True,
            use_fuzzy_matching=True
        )
        
        # Run the pipeline
        result = await pipeline.run(
            competitor_urls=COMPETITOR_URLS,
            products=SAMPLE_PRODUCTS,
            keywords_per_url=10,
            max_variations_per_keyword=3
        )
        
        # Print the results
        print("\nCollection Generation Results:")
        print(f"Status: {result.get('status', 'unknown')}")
        
        collections = result.get('collections', [])
        print(f"Generated {len(collections)} collections")
        
        for i, collection in enumerate(collections, 1):
            print(f"\nCollection {i}:")
            print(f"Title: {collection.get('collection_title', 'Untitled')}")
            print(f"Description: {collection.get('collection_description', 'No description')[:100]}...")
            print(f"Products: {len(collection.get('products_list', []))}")
            print(f"Target Keyword: {collection.get('target_keyword', 'None')}")
        
        # Save the results to a file
        output_file = Path(__file__).parent / "collection_results.json"
        with open(output_file, "w") as f:
            json.dump(result, f, indent=2)
        
        print(f"\nResults saved to {output_file}")
        
    except Exception as e:
        logger.error(f"Error running example: {e}", exc_info=True)
        print(f"Error: {e}")

def main():
    """Main entry point."""
    asyncio.run(run_example())

if __name__ == "__main__":
    main() 