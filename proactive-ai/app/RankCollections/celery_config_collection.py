#rankcollections/celery_config_collection.py
import sys
from pathlib import Path
from celery import Celery, shared_task
from typing import Dict, Any, Optional
import requests
import asyncio
from datetime import datetime
import redis

# Add project root to Python path
project_root = str(Path(__file__).resolve().parents[3])
if project_root not in sys.path:
    sys.path.append(project_root)

# Try importing with 'app.' prefix first, then fall back to direct imports
try:
    from app.Utils.collections.product_collection_pipeline import create_collections_from_competitors_sync
    from app.Models.dbHelpers import (
        insert_collection_job_request_data,
        update_collection_job_status_on_collection_job_table,
        get_attributes_for_collection_creation,
        store_collections_in_database,
        update_task_status_in_schedule
    )        
    from app.Logging.logger_config import get_logger
    from app.Utils.redis_utils import redis_client
except ImportError:
    # Fall back to direct imports without 'app.' prefix
    from Utils.collections.product_collection_pipeline import create_collections_from_competitors_sync
    from Models.dbHelpers import (
        insert_collection_job_request_data,
        update_collection_job_status_on_collection_job_table,
        get_attributes_for_collection_creation,
        store_collections_in_database,
        update_task_status_in_schedule
    )        
    from Logging.logger_config import get_logger
    from Utils.redis_utils import redis_client

logger = get_logger(__name__)

# Redis Configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0

# Initialize Redis client if not already available
if not redis_client:
    try:
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            decode_responses=True
        )
        redis_client.ping()
    except redis.ConnectionError as e:
        logger.error(f"Failed to connect to Redis: {e}")
        redis_client = None

# Initialize Celery
celery_app = Celery('generatorai')

# Celery Configuration
celery_app.conf.update(
    broker_url=f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}',
    result_backend=f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}',
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_always_eager=False,
    task_create_missing_queues=True,
    task_default_queue='default',
    broker_connection_retry_on_startup=True
)

def run_async(coro):
    """Helper function to run async code in sync context"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop.run_until_complete(coro)

@shared_task(
    bind=True,
    max_retries=1,
    name='app.RankCollections.celery_config_collection.generate_collection_task',
    queue='collections'
)
def generate_collection_task(
    self,
    store_id: int,
    store_uuid: str,
    callback_url: Optional[str] = None,
    attributes: Optional[Dict[str, Dict[str, bool]]] = None,
    keywords_origin_type: str = 'competitor',
    enforce_strict_collection_creation: Optional[bool] = False
):
    """Celery task for generating collections using agent-based system"""
    task_id = self.request.id
    
    try:
        is_manual_creation = callback_url is not None
        # Store the task_id in Redis to ensure consistency throughout the process
        if redis_client:
            redis_client.set(f'collection_task_{task_id}_status', 'PROCESSING')
   
        logger.info(
            "Starting collection generation task",
            extra={
                "task_id": task_id,
                "store_id": store_id,
                "store_uuid": store_uuid,
                "attributes": attributes
            }
        )
        
        # Update initial status for auto creation
        if not is_manual_creation:
            run_async(insert_collection_job_request_data(
                collection_job_data={
                    "total_request": {
                        "store_id": store_id
                    },
                    "attributes": attributes
                },
                task_id=task_id,
                status='PROCESSING'
            ))

        # Update Redis status
        if redis_client:
            redis_client.set(f'collection_task_{task_id}_status', 'STARTED')
        run_async(update_collection_job_status_on_collection_job_table(task_id=task_id, status='PROCESSING'))
        
        # Fetch attributes if not provided
        if not attributes:
            attributes = run_async(get_attributes_for_collection_creation(store_id))
        
        logger.info("Running collection generation function")
        result = create_collections_from_competitors_sync(
            store_id=store_id,
            task_id=task_id,
            attributes=attributes,
            keywords_origin_type=keywords_origin_type,
            enforce_strict_collection_creation=enforce_strict_collection_creation
        )
            
        # Extract collections from result
        collections = result.get('collections', [])
        
        # Process results
        if not collections:
            # Update status for no collections
            if redis_client:
                redis_client.set(f'collection_task_{task_id}_status', 'NO_COLLECTIONS')
            
            logger.info("No collections found")
            run_async(update_collection_job_status_on_collection_job_table(task_id=task_id, status='NO_COLLECTIONS'))
            
            if not is_manual_creation:
                run_async(update_task_status_in_schedule(store_id=store_id, status='NO_COLLECTIONS'))
            
            # Notify slack with zero collection size
            run_async(notify_slack(
                task_id=task_id, 
                store_id=store_id, 
                status='NO_COLLECTIONS', 
                collection_size=0
            ))
            
            return result

        # Update success status
        if redis_client:
            redis_client.set(f'collection_task_{task_id}_status', 'COMPLETED')

        logger.info("Collection generation completed")
        run_async(update_collection_job_status_on_collection_job_table(task_id=task_id, status='COMPLETED'))
        
        if not is_manual_creation:
            run_async(update_task_status_in_schedule(store_id=store_id, status='COMPLETED'))
        
        # Store collections in database
        run_async(store_collections_in_database(
            task_id=task_id,
            collections=collections,
            responsed_from_agent=result,
            store_id=store_id,
            is_manual_creation=is_manual_creation
        ))

        # Handle callback if provided
        if callback_url:
            response = requests.post(
                callback_url,
                json={
                    "status": "success",
                    "collections": collections,
                    "metadata": result.get('metadata', {}),
                    "task_id": task_id,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            if response.status_code != 200:
                logger.error(f"Callback failed with status code: {response.status_code}")

        return result

    except Exception as e:
        logger.error(f"Collection generation task failed: {str(e)}", exc_info=True)
        
        # Update error status
        if redis_client:
            redis_client.set(f'collection_task_{task_id}_status', 'FAILED')
        run_async(update_collection_job_status_on_collection_job_table(task_id=task_id, status='FAILED'))
        
        if not is_manual_creation:
            run_async(update_task_status_in_schedule(store_id=store_id, status='FAILED'))
        
        # Send error to callback URL if provided
        if callback_url:
            try:
                requests.post(
                    callback_url,
                    json={
                        "status": "error",
                        "error": str(e),
                        "task_id": task_id
                    }
                )
            except Exception as callback_error:
                logger.error(f"Failed to send error to callback URL: {str(callback_error)}")
            
        raise

@shared_task(
    name='app.RankCollections.celery_config_collection.get_task_status',
    time_limit=10,
    soft_time_limit=5
)
def get_task_status(task_id: str) -> Dict[str, Any]:
    """Get the status of a task from Redis"""
    try:
        if not redis_client:
            raise ConnectionError("Redis connection not available")
            
        status = redis_client.get(f'collection_task_{task_id}_status')
        if not status:
            return None
            
        return {
            "task_id": task_id,
            "status": status.decode() if isinstance(status, bytes) else status
        }
    except Exception as e:
        logger.error(f"Error getting task status: {str(e)}")
        raise

async def notify_slack(task_id: str, store_id: int, status: str, collection_size: int = None, error_message: str = None):
    """
    Notify Slack about collection generation status.
    
    Args:
        task_id: Task ID
        store_id: Store ID
        status: Status message
        collection_size: Number of collections generated
        error_message: Error message if any
    """
    message = {
        "task_id": task_id,
        "store_id": store_id,
        "status": status
    }
    
    if collection_size is not None:
        message["collection_size"] = collection_size
    if error_message is not None:
        message["error"] = error_message
        
    logger.info(
        "Slack notification would be sent",
        extra={"notification": message}
    )
    return message
