import json
import logging
from .db_config import supabase, StoreWorkersAgentsTable, StoreWorkersTasksTable, StoreWorkersCrewsTable, StoreWorkersTeamsTable, StoreWorkersTaskConfigurationsTable, SwTemplatesTable, SwTemplateConfigurationsTable, SwTemplateRunJobTable, SwTemplateDeploymentTable, StoreWorkersAzureFunctionsAppDetailsTable
from datetime import datetime
from typing import List, Dict, Any, Optional

# Set up logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
formatter = logging.Formatter('[%(asctime)s] %(levelname)s %(name)s: %(message)s')
handler.setFormatter(formatter)
if not logger.hasHandlers():
    logger.addHandler(handler)

"""
Template-specific DB helper functions for storing agents, tasks, crews, teams, and task configurations with is_template=True.
These functions are used by the template endpoints and do not interfere with workflow endpoints.
All functions include detailed logging, error handling, and comprehensive docstrings.
"""

# Functions will be added below for:
# - store_template_agents
# - store_template_tasks
# - store_template_crew
# - store_template_team
# - store_template_task_configuration

# Store template agents
async def store_template_agents(agents: List[dict], store_id: int, team_id: str) -> List[dict]:
    """
    Async: Insert agents into StoreWorkersAgentsTable with is_template=True.
    Parameters:
        agents (List[dict]): List of agent dictionaries to insert.
        store_id (int): Store ID to associate with the agents.
        team_id (str): Team ID to associate with the agents.
    Returns:
        List[dict]: List of inserted agent records.
    Exceptions:
        Returns an empty list if an error occurs.
    """
    try:
        logger.info(f"Inserting {len(agents)} agents for store_id={store_id}, team_id={team_id}")
        for agent in agents:
            agent['is_template'] = True
            agent['store_id'] = store_id
            agent['team_id'] = [team_id]
            agent['created_at'] = datetime.utcnow().isoformat()
            agent['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(StoreWorkersAgentsTable).insert(agents).execute()
        logger.info(f"Inserted agents result: {result.data}")
        return result.data
    except Exception as e:
        logger.error(f"store_template_agents Error: {e}")
        return []

# Store template tasks
async def store_template_tasks(tasks: List[dict], store_id: int, team_id: str) -> List[dict]:
    """
    Async: Insert tasks into StoreWorkersTasksTable with is_template=True.
    Parameters:
        tasks (List[dict]): List of task dictionaries to insert.
        store_id (int): Store ID to associate with the tasks.
        team_id (str): Team ID to associate with the tasks.
    Returns:
        List[dict]: List of inserted task records.
    Exceptions:
        Returns an empty list if an error occurs.
    """
    try:
        logger.info(f"Inserting {len(tasks)} tasks for store_id={store_id}, team_id={team_id}")
        for task in tasks:
            task['is_template'] = True
            task['store_id'] = store_id
            task['team_id'] = [team_id]
            task['created_at'] = datetime.utcnow().isoformat()
            task['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(StoreWorkersTasksTable).insert(tasks).execute()
        logger.info(f"Inserted tasks result: {result.data}")
        return result.data
    except Exception as e:
        logger.error(f"store_template_tasks Error: {e}")
        return []

# Store template crew
async def store_template_crew(crew: dict, store_id: int, team_id: str) -> dict:
    """
    Async: Insert a crew into StoreWorkersCrewsTable with is_template=True.
    Parameters:
        crew (dict): Crew dictionary to insert.
        store_id (int): Store ID to associate with the crew.
        team_id (str): Team ID to associate with the crew.
    Returns:
        dict: Inserted crew record, or empty dict if error.
    """
    try:
        logger.info(f"Inserting crew for store_id={store_id}, team_id={team_id}")
        crew['is_template'] = True
        crew['store_id'] = store_id
        crew['team_id'] = [team_id]
        crew['created_at'] = datetime.utcnow().isoformat()
        crew['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(StoreWorkersCrewsTable).insert(crew).execute()
        logger.info(f"Inserted crew result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"store_template_crew Error: {e}")
        return {}

# Store template team
async def store_template_team(team: dict, store_id: int, team_id: str) -> dict:
    """
    Async: Insert a team into StoreWorkersTeamsTable with is_template=True.
    Parameters:
        team (dict): Team dictionary to insert.
        store_id (int): Store ID to associate with the team.
        team_id (str): Team ID to use for the team.
    Returns:
        dict: Inserted team record, or empty dict if error.
    """
    try:
        logger.info(f"Inserting team for store_id={store_id}, team_id={team_id}")
        team['is_template'] = True
        team['store_id'] = store_id
        team['team_id'] = team_id
        team['created_at'] = datetime.utcnow().isoformat()
        team['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(StoreWorkersTeamsTable).insert(team).execute()
        logger.info(f"Inserted team result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"store_template_team Error: {e}")
        return {}

# Store template task configuration
async def store_template_task_configuration(config: dict, team_id: str) -> dict:
    """
    Async: Insert a task configuration into StoreWorkersTaskConfigurationsTable with is_template=True.
    Parameters:
        config (dict): Configuration dictionary to insert.
        team_id (str): Team ID to associate with the configuration.
    Returns:
        dict: Inserted configuration record, or empty dict if error.
    """
    try:
        logger.info(f"Inserting configuration for team_id={team_id}")
        config['is_template'] = True
        config['team_id'] = team_id
        config['created_at'] = datetime.utcnow().isoformat()
        config['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(StoreWorkersTaskConfigurationsTable).insert(config).execute()
        logger.info(f"Inserted configuration result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"store_template_task_configuration Error: {e}")
        return {}

# ----------------Store Worker Templates Tables-------------

# 1. Create a record in sw_templates
async def create_sw_template(template: dict) -> dict:
    """
    Async: Insert a new template record into sw_templates.
    Parameters:
        template (dict): Template dictionary to insert.
    Returns:
        dict: Inserted template record, or empty dict if error.
    """
    try:
        logger.info(f"Inserting template: {template}")
        template['created_at'] = datetime.utcnow().isoformat()
        template['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(SwTemplatesTable).insert(template).execute()
        logger.info(f"Inserted template result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"create_sw_template Error: {e}")
        return {}

# 2. Get template details (all columns)
async def get_sw_template_details(template_id: int) -> Optional[dict]:
    """
    Async: Retrieve all columns for a template by its ID.
    Parameters:
        template_id (int): The ID of the template to retrieve.
    Returns:
        dict or None: Template record if found, else None.
    """
    try:
        logger.info(f"Fetching template with id={template_id}")
        result = supabase.table(SwTemplatesTable).select('*').eq('id', template_id).single().execute()
        logger.info(f"get_sw_template_details Result: {result.data}")
        return result.data if result.data else None
    except Exception as e:
        logger.error(f"get_sw_template_details Error: {e}")
        return None

# 3. Create a record in sw_template_configurations
async def create_sw_template_configuration(config: dict) -> dict:
    """
    Async: Insert a new configuration record into sw_template_configurations.
    Parameters:
        config (dict): Configuration dictionary to insert.
    Returns:
        dict: Inserted configuration record, or empty dict if error.
    """
    try:
        logger.info(f"Inserting configuration: {config}")
        config['created_at'] = datetime.utcnow().isoformat()
        config['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(SwTemplateConfigurationsTable).insert(config).execute()
        logger.info(f"Inserted configuration result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"create_sw_template_configuration Error: {e}")
        return {}

# 4. Update a record in sw_template_configurations
async def update_sw_template_configuration(config_id: int, updates: dict) -> dict:
    """
    Async: Update a configuration record in sw_template_configurations by its ID.
    Parameters:
        config_id (int): The ID of the configuration to update.
        updates (dict): Dictionary of fields to update.
    Returns:
        dict: Updated configuration record, or empty dict if error.
    """
    try:
        logger.info(f"Updating configuration id={config_id} with updates: {updates}")
        updates['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(SwTemplateConfigurationsTable).update(updates).eq('id', config_id).execute()
        logger.info(f"Updated configuration result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"update_sw_template_configuration Error: {e}")
        return {}

# 5. Get current configuration for a particular store and template
async def get_current_sw_template_configuration(store_id: int, template_id: int) -> Optional[dict]:
    """
    Async: Retrieve the configuration for a given store and template (should be only one row).
    Parameters:
        store_id (int): Store ID to filter by.
        template_id (int): Template ID to filter by.
    Returns:
        dict or None: The configuration record, or None if not found.
    """
    try:
        logger.info(f"Fetching configuration for store_id={store_id}, template_id={template_id}")
        result = supabase.table(SwTemplateConfigurationsTable).select('*').eq('store_id', store_id).eq('template_id', template_id).single().execute()
        logger.info(f"get_current_sw_template_configuration Result: {result.data}")
        return result.data if result.data else None
    except Exception as e:
        logger.error(f"get_current_sw_template_configuration Error: {e}")
        return None

# 6. Create a record in sw_template_run_job
async def create_sw_template_run_job(job: dict) -> dict:
    """
    Async: Insert a new run job record into sw_template_run_job.
    Parameters:
        job (dict): Run job dictionary to insert.
    Returns:
        dict: Inserted run job record, or empty dict if error.
    """
    try:
        logger.info(f"Inserting run job: {job}")
        job['created_at'] = datetime.utcnow().isoformat()
        job['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(SwTemplateRunJobTable).insert(job).execute()
        logger.info(f"Inserted run job result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"create_sw_template_run_job Error: {e}")
        return {}

# 7. Update a record in sw_template_run_job
async def update_sw_template_run_job(job_id: int, updates: dict) -> dict:
    """
    Async: Update a run job record in sw_template_run_job by its ID.
    Parameters:
        job_id (int): The ID of the run job to update.
        updates (dict): Dictionary of fields to update.
    Returns:
        dict: Updated run job record, or empty dict if error.
    """
    try:
        logger.info(f"Updating run job id={job_id} with updates: {updates}")
        updates['updated_at'] = datetime.utcnow().isoformat()
        result = supabase.table(SwTemplateRunJobTable).update(updates).eq('id', job_id).execute()
        logger.info(f"Updated run job result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"update_sw_template_run_job Error: {e}")
        return {}

# 8. Get job details for a particular store and template
async def get_sw_template_run_jobs(store_id: int, template_id: int) -> List[dict]:
    """
    Async: Retrieve all run jobs for a particular store and template.
    Parameters:
        store_id (int): Store ID to filter by.
        template_id (int): Template ID to filter by.
    Returns:
        List[dict]: List of run job records, or empty list if error.
    """
    try:
        logger.info(f"Fetching run jobs for store_id={store_id}, template_id={template_id}")
        result = supabase.table(SwTemplateRunJobTable).select('*').eq('store_id', store_id).eq('template_id', template_id).order('created_at', desc=True).execute()
        logger.info(f"get_sw_template_run_jobs Result: {result.data}")
        return result.data if result.data else []
    except Exception as e:
        logger.error(f"get_sw_template_run_jobs Error: {e}")
        return []

# 9. Soft delete functions for all tables
async def soft_delete_sw_template(template_id: int) -> bool:
    """
    Async: Soft delete a template record in sw_templates by setting marked_for_deletion=True.
    Parameters:
        template_id (int): The ID of the template to soft delete.
    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        logger.info(f"Soft deleting template id={template_id}")
        result = supabase.table(SwTemplatesTable).update({'marked_for_deletion': True, 'updated_at': datetime.utcnow().isoformat()}).eq('id', template_id).execute()
        logger.info(f"soft_delete_sw_template Result: {result.data}")
        return bool(result.data)
    except Exception as e:
        logger.error(f"soft_delete_sw_template Error: {e}")
        return False

async def soft_delete_sw_template_configuration(config_id: int) -> bool:
    """
    Async: Soft delete a configuration record in sw_template_configurations by setting marked_for_deletion=True.
    Parameters:
        config_id (int): The ID of the configuration to soft delete.
    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        logger.info(f"Soft deleting configuration id={config_id}")
        result = supabase.table(SwTemplateConfigurationsTable).update({'marked_for_deletion': True, 'updated_at': datetime.utcnow().isoformat()}).eq('id', config_id).execute()
        logger.info(f"soft_delete_sw_template_configuration Result: {result.data}")
        return bool(result.data)
    except Exception as e:
        logger.error(f"soft_delete_sw_template_configuration Error: {e}")
        return False

async def soft_delete_sw_template_run_job(job_id: int) -> bool:
    """
    Async: Soft delete a run job record in sw_template_run_job by setting marked_for_deletion=True.
    Parameters:
        job_id (int): The ID of the run job to soft delete.
    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        logger.info(f"Soft deleting run job id={job_id}")
        result = supabase.table(SwTemplateRunJobTable).update({'marked_for_deletion': True, 'updated_at': datetime.utcnow().isoformat()}).eq('id', job_id).execute()
        logger.info(f"soft_delete_sw_template_run_job Result: {result.data}")
        return bool(result.data)
    except Exception as e:
        logger.error(f"soft_delete_sw_template_run_job Error: {e}")
        return False

# Get all templates (for /home endpoint)
async def get_all_sw_templates() -> list[dict]:
    """
    Async: Retrieve all templates (not marked for deletion), returning only uuid, title, description, emoji, template_type, category, tags, summary.
    Returns:
        list[dict]: List of template objects.
    """
    try:
        logger.info("Fetching all templates for /home endpoint")
        result = supabase.table(SwTemplatesTable).select('uuid, title, description, emoji, template_type, category, tags, summary').eq('marked_for_deletion', False).eq('is_visible', True).execute()
        logger.info(f"get_all_sw_templates Result: {result.data}")
        return result.data if result.data else []
    except Exception as e:
        logger.error(f"get_all_sw_templates Error: {e}")
        return []

# Get template details by uuid (for /template-details endpoint)
async def get_sw_template_details_by_uuid(template_uuid: str) -> dict | None:
    """
    Async: Retrieve all columns for a template by its uuid.
    Parameters:
        template_uuid (str): The uuid of the template to retrieve.
    Returns:
        dict or None: Template record if found, else None.
    """
    try:
        logger.info(f"Fetching template with uuid={template_uuid}")
        result = supabase.table(SwTemplatesTable).select('*').eq('uuid', template_uuid).eq('marked_for_deletion', False).eq('is_visible', True).single().execute()
        logger.info(f"get_sw_template_details_by_uuid Result: {result.data}")
        return result.data if result.data else None
    except Exception as e:
        logger.error(f"get_sw_template_details_by_uuid Error: {e}")
        return None

# Get template id by uuid (helper for config lookup)
async def get_sw_template_id_by_uuid(template_uuid: str) -> int | None:
    """
    Async: Retrieve the template id for a given uuid.
    Parameters:
        template_uuid (str): The uuid of the template.
    Returns:
        int or None: The template id if found, else None.
    """
    try:
        logger.info(f"Fetching template id for uuid={template_uuid}")
        result = supabase.table(SwTemplatesTable).select('id').eq('uuid', template_uuid).eq('marked_for_deletion', False).single().execute()
        logger.info(f"get_sw_template_id_by_uuid Result: {result.data}")
        return result.data['id'] if result.data and 'id' in result.data else None
    except Exception as e:
        logger.error(f"get_sw_template_id_by_uuid Error: {e}")
        return None

# Get all templates mapped to a particular store (with visibility and access control)
async def get_sw_templates_mapped_to_this_store(store_id: int) -> list[dict]:
    """
    Async: Retrieve all templates visible to a particular store.
    Includes all visible (is_visible=True) and not marked for deletion (marked_for_deletion=False) templates.
    If is_restricted_viewing_enabled is True, only include if store_id is in store_ids_with_access.
    return only uuid, title, description, emoji, template_type, category, tags, summary
    Parameters:
        store_id (int): The store ID to check access for.
    Returns:
        list[dict]: List of template objects visible to this store.
    """
    try:
        logger.info(f"Fetching templates mapped to store_id={store_id}")
        # Fetch all visible, not deleted templates
        result = supabase.table(SwTemplatesTable).select('*').eq('marked_for_deletion', False).eq('is_visible', True).execute()
        templates = result.data if result.data else []
        # Filter for restricted viewing
        filtered = []
        for tpl in templates:
            if tpl.get('is_restricted_viewing_enabled'):
                # Only include if store_id is in store_ids_with_access
                access_list = tpl.get('store_ids_with_access') or []
                if store_id in access_list:
                    filtered.append(tpl)
            else:
                filtered.append(tpl)
        logger.info(f"get_sw_templates_mapped_to_this_store Result: {len(filtered)} templates for store_id={store_id}")
        # remove extra fields
        filtered = [
            {
                'uuid': tpl['uuid'],
                'title': tpl['title'],
                'description': tpl['description'],
                'emoji': tpl['emoji'],
                'template_type': tpl['template_type'],
                'category': tpl['category'],
                'tags': tpl['tags'],
                'summary': tpl['summary']
            }
            for tpl in filtered
        ]
        return filtered

    except Exception as e:
        logger.error(f"get_sw_templates_mapped_to_this_store Error: {e}")
        return []

async def store_template_configuration_for_store(store_id: int, template_id: int, config_data: dict) -> dict:
    """
    Async: Create or update a template configuration for a store.

    Requirements:
    1. If no configuration exists for (store_id, template_id): Create new config regardless of config_update
    2. If configuration exists:
       - If config_update=True: Bump version, backup current config to history, update values
       - If config_update=False: Just update values without version bump or backup

    Parameters:
        store_id (int): Store ID
        template_id (int): Template ID
        config_data (dict): Configuration data with optional config_update flag

    Returns:
        dict: The created/updated configuration record, or empty dict if error
    """
    try:
        logger.info(f"Starting store_template_configuration_for_store: store_id={store_id}, template_id={template_id}")

        # Extract config_update flag and remove from data
        config_update = config_data.get('config_update', False)
        data_to_store = {k: v for k, v in config_data.items() if k != 'config_update'}

        # Map API field names to database column names
        if 'configurations' in data_to_store:
            data_to_store['current_configuration'] = data_to_store.pop('configurations')
        if 'values' in data_to_store:
            data_to_store['current_key_values'] = data_to_store.pop('values')

        logger.info(f"Mapped data keys: {list(data_to_store.keys())}, config_update={config_update}")

        # Try to fetch existing configuration using a simple approach
        existing_config = None
        try:
            # Use .execute() without .single() first to avoid exceptions
            result = supabase.table(SwTemplateConfigurationsTable).select('*').eq('store_id', store_id).eq('template_id', template_id).execute()

            if result.data and len(result.data) > 0:
                existing_config = result.data[0]
                logger.info(f"Found existing config with id={existing_config.get('id')}")
            else:
                logger.info("No existing configuration found")

        except Exception as e:
            logger.info(f"Exception while fetching config (treating as no config exists): {e}")
            existing_config = None

        now = datetime.utcnow().isoformat()

        if existing_config:
            # Configuration exists - update it
            logger.info("Updating existing configuration")

            # Prepare update fields - start with existing values
            update_fields = {
                'current_configuration': existing_config.get('current_configuration', []),
                'current_key_values': existing_config.get('current_key_values', {}),
                'additional_inputs': existing_config.get('additional_inputs'),
                'store_data_type': existing_config.get('store_data_type'),
                'store_data': existing_config.get('store_data'),
                'include_metadata': existing_config.get('include_metadata'),
                'configuration_history': existing_config.get('configuration_history', []),
                'current_version': existing_config.get('current_version', 1),
                'updated_at': now
            }

            # Override with provided values
            for key, value in data_to_store.items():
                if value is not None:
                    update_fields[key] = value

            # Handle version bumping and history backup if config_update=True
            if config_update:
                logger.info("config_update=True: Bumping version and backing up to history")

                # Create history entry from current state
                history_entry = {
                    'version': existing_config.get('current_version', 1),
                    'updated_at': existing_config.get('updated_at'),
                    'current_configuration': existing_config.get('current_configuration'),
                    'current_key_values': existing_config.get('current_key_values'),
                    'store_data_type': existing_config.get('store_data_type'),
                    'store_data': existing_config.get('store_data'),
                    'additional_inputs': existing_config.get('additional_inputs'),
                    'include_metadata': existing_config.get('include_metadata')
                }

                # Add to history and bump version
                update_fields['configuration_history'].append(history_entry)
                update_fields['current_version'] = existing_config.get('current_version', 1) + 1

            # Ensure required fields are not null
            if update_fields['current_configuration'] is None:
                update_fields['current_configuration'] = []
            if update_fields['current_key_values'] is None:
                update_fields['current_key_values'] = {}

            # Perform update
            result = supabase.table(SwTemplateConfigurationsTable).update(update_fields).eq('id', existing_config['id']).execute()

            if result.data:
                logger.info(f"Successfully updated configuration: {result.data[0].get('id')}")
                return result.data[0]
            else:
                logger.error(f"Update failed: {getattr(result, 'error', 'Unknown error')}")
                return {}

        else:
            # No configuration exists - create new one
            logger.info("Creating new configuration")

            insert_fields = {
                'store_id': store_id,
                'template_id': template_id,
                'current_version': 1,
                'configuration_history': [],
                'created_at': now,
                'updated_at': now,
                'current_configuration': [],  # Default required field
                'current_key_values': {}      # Default required field
            }

            # Add provided fields
            for key, value in data_to_store.items():
                if value is not None:
                    insert_fields[key] = value

            # Ensure required fields are not null (override if provided as null)
            if insert_fields['current_configuration'] is None:
                insert_fields['current_configuration'] = []
            if insert_fields['current_key_values'] is None:
                insert_fields['current_key_values'] = {}

            # Perform insert
            result = supabase.table(SwTemplateConfigurationsTable).insert(insert_fields).execute()

            if result.data:
                logger.info(f"Successfully created configuration: {result.data[0].get('id')}")
                return result.data[0]
            else:
                logger.error(f"Insert failed: {getattr(result, 'error', 'Unknown error')}")
                return {}

    except Exception as e:
        logger.error(f"Unexpected error in store_template_configuration_for_store: {e}")
        return {}

# Update the values in the current active template configuration for a store/template
async def update_template_configuration_values(store_id: int, template_id: int, values: dict) -> dict:
    """
    Async: Update the current_key_values in the config for a store/template, bump version, and append to configuration_history.
    Parameters:
        store_id (int): Store ID
        template_id (int): Template ID
        values (dict): The new values to set in current_key_values
    Returns:
        dict: The updated configuration record, or empty dict if error.
    """
    try:
        logger.info(f"Updating template configuration values for store_id={store_id}, template_id={template_id}")
        # Use the same version/history logic as store_template_configuration_for_store
        result = supabase.table(SwTemplateConfigurationsTable).select('*').eq('store_id', store_id).eq('template_id', template_id).single().execute()
        current = result.data if result.data else None
        now = datetime.utcnow().isoformat()
        if not current:
            logger.warning(f"No configuration found for store_id={store_id}, template_id={template_id}")
            return {}
        # Prepare history entry
        history_entry = {
            'current_version': current.get('current_version'),
            'updated_at': current.get('updated_at'),
            'current_configuration': current.get('current_configuration'),
            'current_key_values': current.get('current_key_values'),
            'store_data_type': current.get('store_data_type'),
            'store_data': current.get('store_data'),
            'additional_inputs': current.get('additional_inputs'),
            'include_metadata': current.get('include_metadata'),
        }
        configuration_history = current.get('configuration_history') or []
        configuration_history.append(history_entry)
        new_version = (current.get('current_version') or 1) + 1
        update_fields = {
            'current_key_values': values,
            'current_version': new_version,
            'configuration_history': configuration_history,
            'updated_at': now
        }
        updated = supabase.table(SwTemplateConfigurationsTable).update(update_fields).eq('id', current['id']).execute()
        logger.info(f"update_template_configuration_values: Updated config: {updated.data}")
        return updated.data[0] if updated.data else {}
    except Exception as e:
        logger.error(f"update_template_configuration_values Error: {e}")
        return {}

# Get deployment details for a template (sw_template_deployment)
async def get_sw_template_deployment_by_template_id(template_id: int) -> dict | None:
    """
    Async: Retrieve the latest deployment record for a template from sw_template_deployment.
    Parameters:
        template_id (int): The template ID to look up.
    Returns:
        dict or None: Deployment record if found, else None.
    """
    try:
        logger.info(f"Fetching deployment for template_id={template_id}")
        result = supabase.table(SwTemplateDeploymentTable).select('*').eq('template_id', template_id).order('last_deployed_on', desc=True).limit(1).execute()
        logger.info(f"get_sw_template_deployment_by_template_id Result: {result.data}")
        return result.data[0] if result.data else None
    except Exception as e:
        logger.error(f"get_sw_template_deployment_by_template_id Error: {e}")
        return None

# Get Azure Function App details by function app name
async def get_azure_function_app_details_by_function_app_name(function_app_name: str) -> dict | None:
    """
    Async: Fetch the row from sw_azure_functions_app_details for the given function name.
    Parameters:
        function_name (str): The function name to look up (name column).
    Returns:
        dict or None: App details if found, else None.
    """
    try:
        logger.info(f"Fetching Azure Function App details for function_app_name={function_app_name}")
        result = supabase.table(StoreWorkersAzureFunctionsAppDetailsTable).select('*').eq('name', function_app_name).limit(1).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        logger.error(f"get_azure_function_app_details_by_function_app_name Error: {e}")
        return None

# ----------------Admin-specific helper functions-------------

async def get_all_sw_templates_admin() -> List[dict]:
    """
    Async: Retrieve all templates with full details for admin access.
    Includes all fields including admin-only fields and deleted templates.
    Returns:
        List[dict]: List of all template objects with complete information.
    """
    try:
        logger.info("Fetching all templates for admin endpoint")
        result = supabase.table(SwTemplatesTable).select('*').order('created_at', desc=True).execute()
        templates = result.data if result.data else []
        for t in templates:
            if 'agents_list' not in t or t['agents_list'] is None:
                t['agents_list'] = []
        logger.info(f"get_all_sw_templates_admin Result: {len(templates)} templates")
        return templates
    except Exception as e:
        logger.error(f"get_all_sw_templates_admin Error: {e}")
        return []

async def get_sw_template_details_by_uuid_admin(template_uuid: str) -> dict | None:
    """
    Async: Retrieve all columns for a template by its uuid for admin access.
    Includes all fields including admin-only fields and deleted templates.
    Parameters:
        template_uuid (str): The uuid of the template to retrieve.
    Returns:
        dict or None: Template record if found, else None.
    """
    try:
        logger.info(f"Fetching admin template details with uuid={template_uuid}")
        result = supabase.table(SwTemplatesTable).select('*').eq('uuid', template_uuid).single().execute()
        template = result.data if result.data else None
        if template is not None:
            if 'agents_list' not in template or template['agents_list'] is None:
                template['agents_list'] = []
        logger.info(f"get_sw_template_details_by_uuid_admin Result: {template}")
        return template
    except Exception as e:
        logger.error(f"get_sw_template_details_by_uuid_admin Error: {e}")
        return None

async def create_sw_template_admin(template_data: dict) -> dict:
    """
    Async: Create a new template record with admin privileges.
    Parameters:
        template_data (dict): Template data to insert.
    Returns:
        dict: Inserted template record, or empty dict if error.
    """
    try:
        logger.info(f"Creating admin template with data keys: {list(template_data.keys())}")
        # Add required fields
        template_data['created_at'] = datetime.utcnow().isoformat()
        template_data['updated_at'] = datetime.utcnow().isoformat()
        template_data['marked_for_deletion'] = False
        # Generate UUID if not provided
        if 'uuid' not in template_data:
            from uuid import uuid4
            template_data['uuid'] = str(uuid4())
        # Set defaults for required fields if not provided
        if 'is_visible' not in template_data:
            template_data['is_visible'] = True
        if 'is_restricted_viewing_enabled' not in template_data:
            template_data['is_restricted_viewing_enabled'] = False
        if 'include_metadata' not in template_data:
            template_data['include_metadata'] = True
        # Ensure agents_list is a list or None
        if 'agents_list' not in template_data or template_data['agents_list'] is None:
            template_data['agents_list'] = []
        logger.info(f"[create_sw_template_admin] agents_list: {template_data['agents_list']}")
        result = supabase.table(SwTemplatesTable).insert(template_data).execute()
        logger.info(f"create_sw_template_admin Result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"create_sw_template_admin Error: {e}")
        return {}

async def update_sw_template_admin(template_uuid: str, update_data: dict) -> dict:
    """
    Async: Update a template record with admin privileges.
    Auto-detects whether to update template details, configurations, or both.
    Parameters:
        template_uuid (str): The uuid of the template to update.
        update_data (dict): Dictionary of fields to update.
    Returns:
        dict: Updated template record, or empty dict if error.
    """
    try:
        logger.info(f"Updating admin template uuid={template_uuid} with data keys: {list(update_data.keys())}")
        # Add updated timestamp
        update_data['updated_at'] = datetime.utcnow().isoformat()
        # Ensure agents_list is a list or None
        if 'agents_list' not in update_data or update_data['agents_list'] is None:
            update_data['agents_list'] = []
        logger.info(f"[update_sw_template_admin] agents_list: {update_data['agents_list']}")
        result = supabase.table(SwTemplatesTable).update(update_data).eq('uuid', template_uuid).execute()
        logger.info(f"update_sw_template_admin Result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"update_sw_template_admin Error: {e}")
        return {}

async def soft_delete_sw_template_by_uuid(template_uuid: str) -> bool:
    """
    Async: Soft delete a template record by UUID with admin privileges.
    Parameters:
        template_uuid (str): The UUID of the template to soft delete.
    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        logger.info(f"Soft deleting admin template uuid={template_uuid}")
        result = supabase.table(SwTemplatesTable).update({
            'marked_for_deletion': True,
            'updated_at': datetime.utcnow().isoformat()
        }).eq('uuid', template_uuid).execute()
        logger.info(f"soft_delete_sw_template_by_uuid Result: {result.data}")
        return bool(result.data)
    except Exception as e:
        logger.error(f"soft_delete_sw_template_by_uuid Error: {e}")
        return False

async def create_sw_template_deployment(deployment_data: dict) -> dict:
    """
    Async: Insert a new deployment record into sw_template_deployment.
    Parameters:
        deployment_data (dict): Deployment data to insert.
    Returns:
        dict: Inserted deployment record, or empty dict if error.
    """
    try:
        logger.info(f"Inserting deployment record with data keys: {list(deployment_data.keys())}")
        result = supabase.table(SwTemplateDeploymentTable).insert(deployment_data).execute()
        logger.info(f"create_sw_template_deployment Result: {result.data}")
        return result.data[0] if result.data else {}
    except Exception as e:
        logger.error(f"create_sw_template_deployment Error: {e}")
        return {}

async def get_sw_templates_full_for_store(store_id: int) -> list[dict]:
    """
    Async: Retrieve all fields for templates visible to a particular store.
    Includes all visible (is_visible=True) and not marked for deletion (marked_for_deletion=False) templates.
    If is_restricted_viewing_enabled is True, only include if store_id is in store_ids_with_access.
    Returns:
        list[dict]: List of template objects visible to this store (all fields).
    """
    try:
        logger.info(f"Fetching full templates for store_id={store_id}")
        result = supabase.table(SwTemplatesTable).select('*').eq('marked_for_deletion', False).eq('is_visible', True).execute()
        templates = result.data if result.data else []
        filtered = []
        for tpl in templates:
            if tpl.get('is_restricted_viewing_enabled'):
                access_list = tpl.get('store_ids_with_access') or []
                if store_id in access_list:
                    filtered.append(tpl)
            else:
                filtered.append(tpl)
        logger.info(f"get_sw_templates_full_for_store Result: {len(filtered)} templates for store_id={store_id}")
        return filtered
    except Exception as e:
        logger.error(f"get_sw_templates_full_for_store Error: {e}")
        return []

async def get_sw_template_run_jobs_for_store(store_id: int) -> list[dict]:
    """
    Async: Retrieve all run jobs for a particular store (across all templates).
    Parameters:
        store_id (int): Store ID to filter by.
    Returns:
        list[dict]: List of run job records, or empty list if error.
    """
    try:
        logger.info(f"Fetching all run jobs for store_id={store_id}")
        result = supabase.table(SwTemplateRunJobTable).select('*').eq('store_id', store_id).order('created_at', desc=True).execute()
        logger.info(f"get_sw_template_run_jobs_for_store Result: {len(result.data) if result.data else 0}")
        return result.data if result.data else []
    except Exception as e:
        logger.error(f"get_sw_template_run_jobs_for_store Error: {e}")
        return []