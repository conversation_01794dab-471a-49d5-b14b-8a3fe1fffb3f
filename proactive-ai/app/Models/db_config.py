from dotenv import load_dotenv
import os
from supabase import create_client, Client
# from .schema import create_stores_table, create_products_table

# Load the .env file
load_dotenv()



# supabase client creation
url: str = os.environ.get("SUPABASE_URL")
key: str = os.environ.get("SUPABASE_KEY")

supabase: Client = create_client(url, key)

#table Names
ProductsTable = 'products'
StoresTable = 'stores'
BinchaAppsTable = 'bincha_apps'
CollectionJobsTable = 'collection_jobs'
RankCollectionsScheduleTable = 'rank_collections_schedule'
CollectionsCreatedTable = 'collections_created'
KeywordsDiscoveryTable = 'keywords_discovery'
ArticleJobsTable = 'article_jobs'
ArticlesCreatedTable = 'articles_created'
ProductSyncJobsTable = 'product_sync_jobs'
SyncedBlogsTable = 'synced_blogs'
BlogSyncJobsTable = 'blog_sync_jobs'
ArticleSyncJobsTable = 'article_sync_jobs'
SyncedArticlesTable = 'synced_articles'
CollectionSyncJobsTable = 'collection_sync_jobs'
SyncedCollectionsTable = 'synced_collections'
StoreWorkersNlqWorkflowTable = 'sw_nlq_entered'

# New table names for Store Workers
StoreWorkersAgentsTable = 'sw_agents'
StoreWorkersTasksTable = 'sw_tasks'
StoreWorkersCrewsTable = 'sw_crews'
StoreWorkersTaskConfigurationsTable = 'sw_task_configurations'
StoreWorkersTeamsTable = 'sw_teams'
StoreWorkersAzureFunctionsAppDetailsTable = 'sw_azure_functions_app_details'
TeamDeployStatusTable = 'sw_team_deploy_status'
StoreWorkersTeamRunJobsTable = 'sw_team_run_jobs'

# New table names for templates
SwTemplatesTable = 'sw_templates'
SwTemplateConfigurationsTable = 'sw_template_configurations'
SwTemplateRunJobTable = 'sw_template_run_job'
SwTemplateDeploymentTable = 'sw_template_deployment'

# # initialze tables
# try:
#     # Execute the table creation query
#     ProductsTable = supabase.query(create_products_table).execute()
#     StoresTable = supabase.query(create_stores_table).execute()

#     print("Table 'products' created successfully!")
# except Exception as e:
#     print(f"Error creating table: {str(e)}")