-- Create all tables in the correct order based on dependencies
-- Base tables first (no foreign key dependencies)

-- 1. Stores table (base table, referenced by many others)
CREATE TABLE IF NOT EXISTS stores (
    id bigint generated by default as identity not null,
    uuid uuid null default gen_random_uuid (),
    name text null,
    url text null,
    is_rankcollections_product boolean null,
    is_blogd_product boolean null,
    is_storeblog_product boolean null,
    is_storeworkers_product boolean null,
    api_token text null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    competitors_domains text[] null,
    constraint stores_pkey primary key (id)
);

-- 2. Bincha Apps table (independent table)
CREATE TABLE IF NOT EXISTS bincha_apps (
    id bigint generated by default as identity not null,
    app_name text null,
    api_token text null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint bincha_apps_pkey primary key (id),
    constraint bincha_apps_api_token_key unique (api_token),
    constraint bincha_apps_app_name_key unique (app_name)
) tablespace pg_default;

-- 3. Products table (depends on stores)
CREATE TABLE IF NOT EXISTS products (
    id bigint generated by default as identity not null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    uuid uuid null default gen_random_uuid (),
    title text null,
    description text null,
    price real null,
    handle text null,
    tags text[] null,
    vendor text null,
    store_id bigint null,
    shopify_gid text null,
    variants json[] null,
    description_embedding public.vector null,
    handle_embedding public.vector null,
    title_embedding public.vector null,
    image_url text null,
    product_url text null,
    constraint products_pkey primary key (id),
    constraint products_store_id_fkey foreign key (store_id) references stores (id)
);

-- 4. Collection Jobs table (independent table, referenced by collections)
CREATE TABLE IF NOT EXISTS collection_jobs (
    id bigint generated by default as identity not null,
    task_id uuid null,
    user_metadata json null,
    attributes json null,
    schedule_settings json null,
    total_request json null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    status text null,
    constraint collection_jobs_pkey primary key (id),
    constraint collection_jobs_task_id_key unique (task_id)
) tablespace pg_default;

-- 5. Rank Collections Schedule table (depends on stores)
CREATE TABLE IF NOT EXISTS rank_collections_schedule (
    id bigint generated by default as identity not null,
    frequency text null,
    count integer null,
    start_date timestamp with time zone null,
    is_active_currently boolean null,
    product_attributes json null,
    seasonal_trends json null,
    customer_behaviour json null,
    market_analysis json null,
    schedule_settings json null,
    user_metadata json null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    store_id bigint null,
    last_generated_on timestamp with time zone null,
    task_id uuid[] null,
    last_generation_status text null,
    constraint rank_collections_schedule_pkey primary key (id),
    constraint rank_collections_schedule_store_id_fkey foreign key (store_id) references stores (id)
) tablespace pg_default;

-- 6. Keywords Discovery table (depends on stores)
CREATE TABLE IF NOT EXISTS keywords_discovery (
    id bigint generated by default as identity not null,
    generated_on date null,
    origin_type character varying null,
    keywords json[] null,
    available_keywords text[] null,
    used_keywords text[] null,
    available_keyword_count bigint null,
    currently_active boolean null default true,
    url text null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    store_id bigint null,
    constraint keywords_discovery_pkey primary key (id),
    constraint keywords_discovery_store_id_fkey foreign key (store_id) references stores (id)
) tablespace pg_default;

-- 7. Collections Created table (depends on stores and collection_jobs)
CREATE TABLE IF NOT EXISTS collections_created (
    id bigint generated by default as identity not null,
    task_id uuid null,
    collection_title text null,
    collection_description text null,
    collection_metatitle text null,
    collection_metadescription text null,
    meta_keywords text[] null,
    target_keyword text[] null,
    product_list text[] null,
    responsed_from_agent json null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    store_id bigint null,
    status text null default 'to_be_fetched'::text,
    constraint collections_created_pkey primary key (id),
    constraint collections_created_store_id_fkey foreign KEY (store_id) references stores (id),
    constraint collections_created_task_id_fkey foreign KEY (task_id) references collection_jobs (task_id)
) TABLESPACE pg_default;

-- 8. Article Jobs table (depends on stores)
CREATE TABLE IF NOT EXISTS article_jobs (
    id bigint generated by default as identity not null,
    uuid uuid null default gen_random_uuid (),
    task_id uuid null,
    user_id uuid null,
    article_id uuid null,
    title text null,
    subtitles json[] null,
    target_keyword text[] null,
    product_data text[] null,
    metadata json null,
    additional_features json null,
    total_request json null,
    status text null,
    store_id bigint null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint article_job_pkey primary key (id),
    constraint article_jobs_task_id_key unique (task_id),
    constraint article_job_store_id_fkey foreign key (store_id) references stores (id)
) tablespace pg_default;

-- 9. Articles Created table (depends on article_jobs)
CREATE TABLE IF NOT EXISTS articles_created (
    id bigint generated by default as identity not null,
    task_id uuid null,
    title text null,
    meta_title text null,
    meta_description text null,
    content text null,
    sections json[] null,
    word_count bigint null,
    keywords text[] null,
    stats json null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint articles_created_pkey primary key (id),
    constraint articles_created_task_id_fkey foreign key (task_id) references article_jobs (task_id)
) tablespace pg_default;

-- Sync related tables
-- 10. Product Sync Jobs table (depends on stores)
CREATE TABLE IF NOT EXISTS product_sync_jobs (
    id bigint generated by default as identity not null,
    task_id text not null,
    store_id bigint not null,
    product_count integer null,
    status text null,
    sync_stats json null,
    error text null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint product_sync_jobs_pkey primary key (id),
    constraint product_sync_jobs_task_id_key unique (task_id),
    constraint product_sync_jobs_store_id_fkey foreign key (store_id) references stores (id)
) tablespace pg_default;

-- 11. Synced Blogs table (depends on stores)
CREATE TABLE IF NOT EXISTS synced_blogs (
    id serial not null,
    shopify_gid text not null,
    handle text not null,
    title text not null,
    comment_policy text null,
    feed text null,
    template_suffix text null,
    tags text[] null,
    blog_created_at timestamp with time zone null,
    blog_updated_at timestamp with time zone null,
    store_id bigint null,
    created_at timestamp with time zone null default CURRENT_TIMESTAMP,
    updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
    constraint blogs_pkey primary key (id),
    constraint blogs_store_id_fkey foreign KEY (store_id) references stores (id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_blogs_shopify_gid on public.synced_blogs using btree (shopify_gid) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_blogs_store_id on public.synced_blogs using btree (store_id) TABLESPACE pg_default;

-- 12. Blog Sync Jobs table (depends on stores)
CREATE TABLE IF NOT EXISTS blog_sync_jobs (
    id bigint generated by default as identity not null,
    task_id text not null,
    store_id bigint not null,
    blog_count integer null,
    status text null,
    sync_stats json null,
    error text null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint blog_sync_jobs_pkey primary key (id),
    constraint blog_sync_jobs_task_id_key unique (task_id),
    constraint blog_sync_jobs_store_id_fkey foreign KEY (store_id) references stores (id)
) TABLESPACE pg_default;

-- 13. Article Sync Jobs table (depends on stores)
CREATE TABLE IF NOT EXISTS article_sync_jobs (
    id bigint generated by default as identity not null,
    task_id text not null,
    store_id bigint not null,
    article_count integer null,
    status text null,
    sync_stats json null,
    error text null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint article_sync_jobs_pkey primary key (id),
    constraint article_sync_jobs_task_id_key unique (task_id),
    constraint article_sync_jobs_store_id_fkey foreign KEY (store_id) references stores (id)
) TABLESPACE pg_default;

-- 14. Synced Articles table (depends on stores and synced_blogs)
CREATE TABLE IF NOT EXISTS synced_articles (
    id bigint generated by default as identity not null,
    shopify_gid text not null,
    handle text not null,
    title text not null,
    body text not null,
    author_name text not null,
    blog_id bigint not null,
    article_created_at timestamp with time zone null,
    article_published_at timestamp with time zone null,
    image_data json not null,
    store_id bigint not null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint articles_pkey primary key (id),
    constraint articles_shopify_gid_key unique (shopify_gid),
    constraint articles_blog_id_fkey foreign KEY (blog_id) references synced_blogs (id),
    constraint articles_store_id_fkey foreign KEY (store_id) references stores (id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_articles_shopify_gid on public.synced_articles using btree (shopify_gid) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_articles_store_id on public.synced_articles using btree (store_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_articles_blog_id on public.synced_articles using btree (blog_id) TABLESPACE pg_default;

-- 15. Collection Sync Jobs table (depends on stores)
CREATE TABLE IF NOT EXISTS collection_sync_jobs (
    id bigint generated by default as identity not null,
    task_id text not null,
    store_id bigint not null,
    collection_count integer null,
    status text null,
    sync_stats json null,
    error text null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint collection_sync_jobs_pkey primary key (id),
    constraint collection_sync_jobs_task_id_key unique (task_id),
    constraint collection_sync_jobs_store_id_fkey foreign KEY (store_id) references stores (id)
) TABLESPACE pg_default;

-- 16. Synced Collections table (depends on stores)
CREATE TABLE IF NOT EXISTS synced_collections (
    id bigint generated by default as identity not null,
    shopify_gid text not null,
    title text not null,
    description text null,
    products_data json[] not null,
    store_id bigint not null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint collections_pkey primary key (id),
    constraint collections_shopify_gid_key unique (shopify_gid),
    constraint collections_store_id_fkey foreign KEY (store_id) references stores (id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_collections_shopify_gid on public.synced_collections using btree (shopify_gid) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_collections_store_id on public.synced_collections using btree (store_id) TABLESPACE pg_default;

-- Store Workers related tables
-- 17. Store Workers Task Configurations table (independent table, referenced by teams)
CREATE TABLE IF NOT EXISTS sw_task_configurations (
    id bigint generated by default as identity not null,
    team_id uuid not null,
    name text not null,
    type text not null,
    current_configuration json[] not null,
    current_key_values json not null,
    configuration_history json[] not null,
    status text not null check (status in ('active', 'inactive')),
    version integer not null default 1,
    store_data_type json null,
    store_data json[] null,
    include_metadata boolean null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    created_by uuid not null,
    modified_by uuid not null,
    constraint sw_task_configurations_pkey primary key (id),
    constraint sw_task_configurations_team_id_key unique (team_id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_sw_task_configurations_team_id on public.sw_task_configurations using btree (team_id) TABLESPACE pg_default;

-- 18. Store Workers Teams table (depends on stores and sw_task_configurations)
CREATE TABLE IF NOT EXISTS sw_teams (
    id bigint generated by default as identity not null,
    team_id uuid not null,
    name text not null,
    description text not null,
    summary json[] null,
    store_id bigint not null,
    configuration_id bigint null,
    workflow_data json not null,
    status text not null,
    visible text not null default 'private'::text,
    store_ids_with_access bigint[] null,
    tags text[] null,
    is_template boolean null default false,
    current_running_status text null,
    running_status_history json[] null,
    performance_metrics json null,
    configuration_settings json null,
    dependencies json[] null,
    created_by bigint null,
    last_modified_by bigint null,
    deploy_status_id bigint null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint sw_teams_pkey primary key (id),
    constraint sw_teams_team_id_key unique (team_id),
    constraint sw_teams_store_id_fkey foreign KEY (store_id) references stores (id),
    constraint sw_teams_configuration_id_fkey foreign KEY (configuration_id) references sw_task_configurations (id),
    constraint sw_crews_visible_check check (
        (visible = any (array['private'::text, 'custom'::text, 'public'::text, 'hidden'::text]))
    ),
    constraint sw_teams_status_check check (
        (status = any (array['pending'::text, 'processing'::text, 'completed'::text, 'failed'::text]))
    )
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_sw_teams_store_id on public.sw_teams using btree (store_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_sw_teams_team_id on public.sw_teams using btree (team_id) TABLESPACE pg_default;

-- 19. Team Deploy Status table (depends on sw_teams and stores)
CREATE TABLE IF NOT EXISTS sw_team_deploy_status (
    id bigint generated by default as identity not null,
    team_id uuid not null,
    deploy_status text null,
    errors text null,
    function_name text null,
    url text null,
    endpoint text null,
    last_deployed_on timestamp with time zone null,
    deployment_history json[] null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    store_id bigint not null,
    constraint sw_team_deploy_status_pkey primary key (id),
    constraint sw_team_deploy_status_team_id_fkey foreign key (team_id) references sw_teams (team_id),
    constraint sw_team_deploy_status_store_id_fkey foreign key (store_id) references stores (id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_sw_team_deploy_status_team_id on public.sw_team_deploy_status using btree (team_id) TABLESPACE pg_default;

-- 19a. Store Workers Team Run Jobs table (depends on sw_teams and stores)
CREATE TABLE IF NOT EXISTS sw_team_run_jobs (
    id bigint generated by default as identity not null,
    job_id uuid not null,
    team_id uuid not null,
    run_by_store_id bigint not null,
    job_title text null,
    input_request json not null,
    output json null,
    job_status text not null check (job_status in ('processing', 'running', 'completed', 'failed')),
    error_messages text null,
    job_duration interval null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint sw_team_run_jobs_pkey primary key (id),
    constraint sw_team_run_jobs_job_id_key unique (job_id),
    constraint sw_team_run_jobs_team_id_fkey foreign key (team_id) references sw_teams (team_id),
    constraint sw_team_run_jobs_store_id_fkey foreign key (run_by_store_id) references stores (id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_sw_team_run_jobs_job_id on public.sw_team_run_jobs using btree (job_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_sw_team_run_jobs_team_id on public.sw_team_run_jobs using btree (team_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_sw_team_run_jobs_store_id on public.sw_team_run_jobs using btree (run_by_store_id) TABLESPACE pg_default;

-- 20. Store Workers NLQ Workflow table (depends on stores and sw_teams)
CREATE TABLE IF NOT EXISTS sw_nlq_entered (
    id bigint generated by default as identity not null,
    team_id uuid not null,
    store_id bigint not null,
    query text not null,
    status text not null,
    workflow_name text null,
    tasks json[] null,
    selected_tasks json[] null,
    valid_variations text[] null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint sw_nlq_entered_pkey primary key (id),
    constraint sw_nlq_entered_team_id_key unique (team_id),
    constraint sw_nlq_entered_store_id_fkey foreign key (store_id) references stores (id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_sw_nlq_entered_store_id on public.sw_nlq_entered using btree (store_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_sw_nlq_entered_team_id on public.sw_nlq_entered using btree (team_id) TABLESPACE pg_default;

-- 21. Store Workers Agents table (depends on stores)
CREATE TABLE IF NOT EXISTS sw_agents (
    id bigint generated by default as identity not null,
    uuid uuid not null default gen_random_uuid(),
    name text not null,
    role text not null,
    goal text not null,
    backstory text not null,
    tools text[] not null,
    total_attributes json null,
    performance_metrics json null,
    configuration_settings json null,
    dependencies json[] null,
    store_id bigint not null,
    team_id uuid[] null,
    visible text not null,
    store_ids_with_access bigint[] null,
    tags text[] null,
    is_template boolean null default false,
    created_by bigint null,
    last_modified_by bigint null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint sw_agents_pkey primary key (id),
    constraint sw_agents_store_id_fkey foreign KEY (store_id) references stores (id),
    constraint sw_agents_visible_check check (
        (visible = any (array['private'::text, 'custom'::text, 'public'::text, 'hidden'::text]))
    )
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_sw_agents_store_id on public.sw_agents using btree (store_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_sw_agents_team_id on public.sw_agents using gin (team_id) TABLESPACE pg_default;

-- 22. Store Workers Tasks table (depends on stores)
CREATE TABLE IF NOT EXISTS sw_tasks (
    id bigint generated by default as identity not null,
    uuid uuid not null default gen_random_uuid (),
    name text not null,
    description text not null,
    expected_output text not null,
    agents text[] null,
    agent_uuid uuid[] null,
    other_attributes json null,
    dependencies json[] null,
    execution_history json[] null,
    scheduling_info json null,
    store_id bigint not null,
    team_id uuid[] null,
    visible text not null default 'private'::text,
    store_ids_with_access bigint[] null,
    tags text[] null,
    is_template boolean null default false,
    created_by bigint null,
    last_modified_by bigint null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint sw_tasks_pkey primary key (id),
    constraint sw_tasks_store_id_fkey foreign KEY (store_id) references stores (id),
    constraint sw_tasks_visible_check check (
        (visible = any (array['private'::text, 'custom'::text, 'public'::text, 'hidden'::text]))
    )
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_sw_tasks_store_id on public.sw_tasks using btree (store_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_sw_tasks_team_id on public.sw_tasks using gin (team_id) TABLESPACE pg_default;

-- 23. Store Workers Crews table (depends on stores)
CREATE TABLE IF NOT EXISTS sw_crews (
    id bigint generated by default as identity not null,
    name text null,
    agents json[] not null,
    tasks json[] not null,
    agents_uuid uuid[] null,
    tasks_uuid uuid[] null,
    other_attributes json null,
    performance_metrics json null,
    configuration_settings json null,
    dependencies json[] null,
    store_id bigint not null,
    team_id uuid[] null,
    visible text not null default 'private'::text,
    store_ids_with_access bigint[] null,
    tags text[] null,
    is_template boolean null default false,
    created_by bigint null,
    last_modified_by bigint null,
    created_at timestamp with time zone null default now(),
    updated_at timestamp with time zone null default now(),
    constraint sw_crews_pkey primary key (id),
    constraint sw_crews_store_id_fkey foreign KEY (store_id) references stores (id),
    constraint sw_crews_visible_check check (
        (visible = any (array['private'::text, 'custom'::text, 'public'::text, 'hidden'::text]))
    )
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_sw_crews_store_id on public.sw_crews using btree (store_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_sw_crews_team_id on public.sw_crews using gin (team_id) TABLESPACE pg_default;

-- 24. Store Workers Azure Functions App Details table (depends on stores)
CREATE TABLE IF NOT EXISTS sw_azure_functions_app_details (
    id bigint generated by default as identity not null,
    name text not null,
    url text not null,
    api_key text not null,
    store_id bigint null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    constraint sw_azure_functions_app_details_pkey primary key (id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_sw_azure_functions_app_details_store_id on public.sw_azure_functions_app_details using btree (store_id) TABLESPACE pg_default;

-- Add foreign key constraint for deploy_status_id in sw_teams after sw_team_deploy_status is created
ALTER TABLE sw_teams 
ADD CONSTRAINT sw_teams_deploy_status_id_fkey 
FOREIGN KEY (deploy_status_id) 
REFERENCES sw_team_deploy_status (id);

-- 25. Templates table (independent, master list)
CREATE TABLE IF NOT EXISTS sw_templates (
    id bigint generated by default as identity not null,
    uuid uuid null default gen_random_uuid(),
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    title text not null,
    description text null,
    emoji text null,
    template_type text null,
    category text null,
    tags text[] null,
    agents_list json[] null,
    base_configuration json[] null,
    store_data_type json null,
    include_metadata boolean null,
    summary text null,
    time_saved_per_run_minutes float null,
    is_visible boolean not null default true,
    is_restricted_viewing_enabled boolean not null default false,
    store_ids_with_access bigint[] null,
    marked_for_deletion boolean not null default false,
    constraint sw_templates_pkey primary key (id)
) TABLESPACE pg_default;

-- 26. Template Configurations table (per store, per template, versioned)
CREATE TABLE IF NOT EXISTS sw_template_configurations (
    id bigint generated by default as identity not null,
    uuid uuid null default gen_random_uuid(),
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    template_id bigint not null,
    store_id bigint not null,
    current_configuration json[] not null,
    current_key_values json not null,
    current_version integer not null,
    configuration_history json[] null,
    store_data_type json null,
    store_data json[] null,
    additional_inputs json null,
    include_metadata boolean null,
    marked_for_deletion boolean not null default false,
    constraint sw_template_configurations_pkey primary key (id),
    constraint sw_template_configurations_template_id_fkey foreign key (template_id) references sw_templates (id),
    constraint sw_template_configurations_store_id_fkey foreign key (store_id) references stores (id)
) TABLESPACE pg_default;

CREATE UNIQUE INDEX IF NOT EXISTS idx_sw_template_configurations_template_store_version ON public.sw_template_configurations (template_id, store_id, version) TABLESPACE pg_default;

-- 27. Template Run Job table (tracks each run/execution)
CREATE TABLE IF NOT EXISTS sw_template_run_job (
    id bigint generated by default as identity not null,
    uuid uuid null default gen_random_uuid(),
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    input_data json null,
    output_data json null,
    status text null,
    job_duration interval null,
    job_title text null,
    errors text null,
    template_id bigint not null,
    store_id bigint not null,
    started_at timestamp with time zone null,
    finished_at timestamp with time zone null,
    marked_for_deletion boolean not null default false,
    constraint sw_template_run_job_pkey primary key (id),
    constraint sw_template_run_job_template_id_fkey foreign key (template_id) references sw_templates (id),
    constraint sw_template_run_job_configuration_id_fkey foreign key (configuration_id) references sw_template_configurations (id),
    constraint sw_template_run_job_store_id_fkey foreign key (store_id) references stores (id)
) TABLESPACE pg_default;

-- 28. Template Deployment table (tracks deployment status of a template)
CREATE TABLE IF NOT EXISTS sw_template_deployment (
    id bigint generated by default as identity not null,
    uuid uuid null default gen_random_uuid(),
    function_name text null,
    endpoint text null,
    url text null,
    last_deployed_on timestamp with time zone null,
    deployment_history json[] null,
    deploy_status text null,
    errors text null,
    template_id bigint not null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone null default now(),
    function_app_name text null,
    function_app_id bigint null,
    constraint sw_template_deployment_pkey primary key (id),
    constraint sw_template_deployment_template_id_fkey foreign key (template_id) references sw_templates (id),
    constraint sw_template_deployment_function_app_id_fkey foreign key (function_app_id) references sw_azure_functions_app_details (id)
) TABLESPACE pg_default; 