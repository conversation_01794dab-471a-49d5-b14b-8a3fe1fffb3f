import json
from .db_config import supabase, ProductsTable, StoresTable, BinchaAppsTable, RankCollectionsScheduleTable, CollectionsCreatedTable, KeywordsDiscoveryTable, ArticleJobsTable, ArticlesCreatedTable, CollectionJobsTable, ProductSyncJobsTable, SyncedBlogsTable, BlogSyncJobsTable, ArticleSyncJobsTable, SyncedArticlesTable, CollectionSyncJobsTable, SyncedCollectionsTable, StoreWorkersNlqWorkflowTable, StoreWorkersAgentsTable, StoreWorkersTasksTable, StoreWorkersCrewsTable, StoreWorkersTaskConfigurationsTable, StoreWorkersTeamsTable, StoreWorkersAzureFunctionsAppDetailsTable, TeamDeployStatusTable, StoreWorkersTeamRunJobsTable
from datetime import datetime, timedelta, timezone, date
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID
import random
import traceback
# from Logging.logger_config import get_logger
# logger = get_logger(__name__)
try:
    from app.StoreWorker.logger.logger import LogContext, info, error, debug
except Exception as e:
    from StoreWorker.logger.logger import LogContext, info, error, debug
    



# BINCHA-APPS-TABLE
'check api token in apps table'
async def check_api_token_in_apps_table(api_token: str):
    try:
        result = supabase.table(BinchaAppsTable).select("*").eq("api_token", api_token).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error checking API token: {str(e)}")

'get app details by app name'
async def get_app_details(app_name: str):
    try:
        result = supabase.table(BinchaAppsTable).select("*").eq("app_name", app_name).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error getting App details: {str(e)}")


# PRODUCTS TABLE
'insert data into products table'
async def insert_data_into_products_table(data: dict):
    try:
        # First check if the product already exists
        existing_product = supabase.table(ProductsTable)\
            .select("*")\
            .eq('shopify_gid', data['shopify_gid'])\
            .eq('store_id', data['store_id'])\
            .execute()

        # If product exists, return the existing data
        if existing_product.data and len(existing_product.data) > 0:
            return "Product Sync Done - Already"
        
        # If product doesn't exist, insert new data
        result = supabase.table(ProductsTable).insert(data).execute()
        if result.data and len(result.data) > 0:
            return "Product Sync Done"

    except Exception as e:
        print("ERROR HERE", e)
        raise Exception(f"Error handling product: {str(e)}")

'get product title,descriptions'
async def get_product_titles_and_descriptions(store_id):
    try:
        result = supabase.table(ProductsTable).select("title, description").eq('store_id', store_id).execute()
        return result.data
    except Exception as e:
        raise Exception(f"Error fetching data for generating collection opportunities : {str(e)}")
    
'get product title,descriptions, handle, price, tags, shopify_gid,vendor'
async def get_product_details_with_only_necessary_fields(store_id):
    try:
        result = supabase.table(ProductsTable).select("title, description, handle, price, tags, shopify_gid,vendor").eq('store_id', store_id).execute()
        return result.data
    except Exception as e:
        raise Exception(f"Error fetching data for generating collection opportunities : {str(e)}")
    
'get product title and shopify_gid'
async def get_product_title_and_shopify_gid(store_id):
    try:
        result = supabase.table(ProductsTable).select("title, shopify_gid").eq('store_id', store_id).execute()
        return result.data
    except Exception as e:
        raise Exception(f"Error fetching data for generating collection opportunities : {str(e)}")

async def get_all_product_data_belongs_to_a_store(store_domain: str) -> list:
    """
    Fetch all products belonging to a store using its domain name.
    
    Args:
        store_domain (str): Domain name of the store
        
    Returns:
        list: List of products belonging to the store
        
    Raises:
        Exception: If there's an error fetching store or product data
    """
    print(f"Fetching products for store domain: {store_domain}")
    
    try:
        # Get store data from stores table using store_domain
        store_data = supabase.table(StoresTable).select('id').eq('name', store_domain).execute()
        
        if not store_data.data:
            raise ValueError(f"No store found with domain: {store_domain}")
            
        store_id = store_data.data[0]['id']
        
        # Using store id, fetch all the products
        products_data = supabase.table(ProductsTable).select('*').eq('store_id', store_id).execute()
        
        if not products_data.data:
            print(f"No products found for store: {store_domain}")
            return []
            
        return products_data.data
        
    except ValueError as e:
        print(f"Validation error: {str(e)}")
        raise
    except Exception as e:
        print(f"Database error fetching store products: {str(e)}", exc_info=True)
        raise Exception(f"Error fetching store products: {str(e)}")



async def get_all_product_data_belongs_to_a_store_except_embedding(store_id: int) -> list:
    """
    Fetch all products belonging to a store using its store ID, excluding embedding data.
    
    Args:
        store_id (int): ID of the store
        
    Returns:
        list: List of products belonging to the store with selected fields
        
    Raises:
        ValueError: If store_id is invalid or store not found
        Exception: If there's an error fetching product data
    """
    print(f"Fetching products for store ID: {store_id}")
    
    try:
        if not isinstance(store_id, int) or store_id <= 0:
            raise ValueError(f"Invalid store_id: {store_id}")

        # Verify store exists
        store_data = supabase.table(StoresTable).select('id').eq('id', store_id).execute()
        
        if not store_data.data:
            raise ValueError(f"No store found with ID: {store_id}")
            
        # Using store id, fetch products with specific fields
        products_data = supabase.table(ProductsTable)\
            .select('title, description, variants, price, tags, shopify_gid')\
            .eq('store_id', store_id)\
            .execute()
        
        if not products_data.data:
            print(f"No products found for store ID: {store_id}")
            return []
            
        # Validate required fields in products
        validated_products = []
        for product in products_data.data:
            if not product.get('title'):
                print(f"Product missing title for store ID: {store_id}")
                continue
            validated_products.append(product)
            
        return validated_products
        
    except ValueError as e:
        print(f"Validation error: {str(e)}")
        raise
    except Exception as e:
        print(
            "Database error fetching store products",
            extra={
                'store_id': store_id,
                'error': str(e)
            },
            exc_info=True
        )
        raise Exception(f"Error fetching store products: {str(e)}")

async def get_all_product_embedding_data_belongs_to_a_store(store_id: int) -> list:
    """
    Fetch all products with their embeddings for a store.
    
    Args:
        store_id (int): ID of the store
        
    Returns:
        list: List of products with their embeddings
        
    Raises:
        ValueError: If store_id is invalid or store not found
        Exception: If there's an error fetching product data
    """
    print(f"Fetching products with embeddings for store ID: {store_id}")
    
    try:
        if not isinstance(store_id, int) or store_id <= 0:
            raise ValueError(f"Invalid store_id: {store_id}")

        # Verify store exists
        store_data = supabase.table(StoresTable).select('id').eq('id', store_id).execute()
        
        if not store_data.data:
            raise ValueError(f"No store found with ID: {store_id}")
            
        # Using store id, fetch products with title, description, and handle
        products_data = supabase.table(ProductsTable)\
            .select('title, description, handle')\
            .eq('store_id', store_id)\
            .execute()
        
        if not products_data.data:
            print(f"No products found for store ID: {store_id}")
            return []
            
        # Validate required fields
        validated_products = []
        for product in products_data.data:
            if all(key in product for key in ['title', 'description', 'handle']):
                validated_products.append(product)
            else:
                print(f"Product missing required fields for store ID: {store_id}")
                
        return validated_products
        
    except ValueError as e:
        print(f"Validation error: {str(e)}")
        raise
    except Exception as e:
        print(
            "Database error fetching product embeddings",
            extra={
                'store_id': store_id,
                'error': str(e)
            },
            exc_info=True
        )
        raise Exception(f"Error fetching product embeddings: {str(e)}")



async def get_product_details_by_gid(gid: str):
    """
    Fetch product details from the products table using the provided GID.
    
    Args:
        gid (str): The GID of the product.
        
    Returns:
        dict: The product details or None if not found.
    """
    try:
        result = supabase.table(ProductsTable).select('shopify_gid, handle, title, product_url, image_url').eq('shopify_gid', gid).execute()  # add image to this TO DO:
        return result.data  
    except Exception as e:
        raise Exception(f"Error fetching product details: {str(e)}")

async def get_number_of_products_for_a_store(store_id: int) -> list:
    """
    Fetch product count to a store using its id.
    
    Args:
        store_id (str): id of the store
        
    Returns:
        list: Count of products in the store
        
    Raises:
        Exception: If there's an error fetching store or product data
    """
    print(f"Fetching products for store id: {store_id}")
    
    try:
        # Fetch products for the specified store_id
        result = supabase.table(ProductsTable).select('id', count='exact').eq('store_id', store_id).execute()

        # Extract count from the response
        products_count = result.count if result.count else 0

        # Return the count
        return { "product_count": products_count}
        
    except Exception as e:
        raise Exception(f"Error fetching store products: {str(e)}")


# get all products data belongs to a store
async def get_all_products_data_belongs_to_a_store(store_id: int) -> list:
    try:
        result = supabase.table(ProductsTable).select('*').eq('store_id', store_id).execute()
        return result.data
    except Exception as e:
        raise Exception(f"Error fetching store products: {str(e)}")


# STORES TABLE
'inset data into stores table'
async def insert_data_into_users_table(data):
    try:
        result = supabase.table(StoresTable).insert(data).execute()
        return result.data[0]
    except Exception as e:
        raise Exception(f"Error inserting store: {str(e)}")
    
'check api token in stores table'
async def check_api_token_in_stores_table(api_token: str):
    try:
        result = supabase.table(StoresTable).select("*").eq("api_token", api_token).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error checking API token: {str(e)}")
    
'check if the store already present'
async def check_if_store_exists(name: str):
    try:
        result = supabase.table(StoresTable).select("*").eq("name", name).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error checking if store exists: {str(e)}")

'update store record'
async def update_store_record(id: int, data):
    try:
        result = supabase.table(StoresTable).update(data).eq('id', id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error updating store: {str(e)}")
    
'get store uuid using store_id'
async def get_store_uuid(id: int):
    try:
        result = supabase.table(StoresTable).select('uuid').eq('id', id).execute()
        return result.data[0]['uuid'] if result.data else None
    except Exception as e:
        raise Exception(f"Error fetching store uuid: {str(e)}")

'get store name using store_id'
async def get_store_name(id: int):
    try:
        result = supabase.table(StoresTable).select('name').eq('id', id).execute()
        return result.data[0]['name'] if result.data else None
    except Exception as e:
        raise Exception(f"Error fetching store name: {str(e)}")

'add competitor domains'
async def store_competitor_domains(domains: List[str], store_id: int) -> bool:
    try:
        # Update store record with new domains and timestamp
        result = supabase.table(StoresTable).update({
            'competitors_domains': domains,
            'updated_at': datetime.utcnow().isoformat()
        }).eq('id', store_id).execute()
        
        return bool(result.data)
        
    except Exception as e:
        raise Exception(f"Failed to store competitor domains: {str(e)}")
    
'fetch competitor domains'
async def get_competitor_domains(store_id: int) -> List[str]:
    try:
        result = supabase.table(StoresTable).select('competitors_domains').eq('id', store_id).execute()
        return result.data[0]['competitors_domains'] if result.data else []
    except Exception as e:
        raise Exception(f"Failed to fetch competitor domains: {str(e)}")

# RANK-COLLECTIONS-SCHEDULE TABLE

'Store data'
async def store_collection_schedule(store_id: int, scheduling_data: dict):
    try:
        # Map the data according to table schema
        schedule_data = {
            "store_id": store_id,
            "frequency": scheduling_data.get("schedule_settings", {}).get("frequency"),
            "count": scheduling_data.get("schedule_settings", {}).get("count"),
            "start_date": scheduling_data.get("schedule_settings", {}).get("start_date"),
            "is_active_currently": True,
            "product_attributes": scheduling_data.get("attributes", {}).get("product"),
            "seasonal_trends": scheduling_data.get("attributes", {}).get("seasonal"),
            "customer_behaviour": scheduling_data.get("attributes", {}).get("customer"),
            "market_analysis": scheduling_data.get("attributes", {}).get("market"),
            "schedule_settings": scheduling_data.get("schedule_settings"),
            "user_metadata": scheduling_data.get("user_metadata")
        }

        # Upsert data into rank collections schedule table
        result = supabase.table(RankCollectionsScheduleTable).upsert(schedule_data, on_conflict=['store_id']).execute()
        return result.data[0] if result.data else None

    except Exception as e:
        raise Exception(f"Error storing collection schedule: {str(e)}")


async def update_schedule_status(store_id: int, task_id: str, status: str) -> None:
    """Update schedule table with generation status"""
    try:
        # Fetch the existing task_id
        existing_task_id_result = supabase.table(RankCollectionsScheduleTable).select('task_id').eq('store_id', store_id).execute()
        existing_task_id = existing_task_id_result.data[0]['task_id'] if existing_task_id_result.data else []
        # Handle the null case for first time entry and append the new task_id to the existing ones
        updated_task_id = existing_task_id if existing_task_id else []
        updated_task_id.append(task_id)

        # Update the schedule table with the new task_id and status
        supabase.table(RankCollectionsScheduleTable)\
            .update({
                "last_generated_on": datetime.now(timezone.utc).isoformat(),
                "last_generation_status": status,
                "task_id": updated_task_id
            })\
            .eq("store_id", store_id)\
            .execute()
    except Exception as e:
        print(f"Failed to update schedule status: {str(e)}")
        raise

async def append_task_id_to_schedule(store_id: int, task_id: str) -> None:
    """
    Appends a new task_id to the existing task_id array in RankCollectionsScheduleTable.
    If no existing task_ids, creates a new array with the task_id.
    
    Args:
        store_id (int): The store ID to update
        task_id (str): The task ID to append
        
    Returns:
        None
        
    Raises:
        Exception: If the database operation fails
    """
    try:
        # Fetch the existing task_id array
        existing_task_id_result = supabase.table(RankCollectionsScheduleTable)\
            .select('task_id')\
            .eq('store_id', store_id)\
            .execute()
        
        existing_task_id = existing_task_id_result.data[0]['task_id'] if existing_task_id_result.data else []
        
        # Handle the null case for first time entry and append the new task_id
        updated_task_id = existing_task_id if existing_task_id else []
        updated_task_id.append(task_id)
        
        # Update the schedule table with the new task_id array
        supabase.table(RankCollectionsScheduleTable)\
            .update({
                "task_id": updated_task_id,
                "updated_at": datetime.now(timezone.utc).isoformat()
            })\
            .eq("store_id", store_id)\
            .execute()
            
    except Exception as e:
        print(f"Failed to append task_id to schedule: {str(e)}")
        raise
#update the status of the task in the schedule table
async def update_task_status_in_schedule(store_id: int, status: str) -> None:
    try:
        result = supabase.table(RankCollectionsScheduleTable).update({"last_generation_status": status}).eq("store_id", store_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error updating task status in schedule: {str(e)}")

# COLLECTIONS TABLE

async def insert_data_into_collections_table(data: Dict[str, Any]) -> str:
    """
    Insert or update collection data in the collections table
    
    Args:
        data: Dictionary containing collection data with the following structure:
            {
                'shopify_gid': str,
                'title': str,
                'description': Optional[str],
                'products_data': List[Dict[str, Any]],  # Array of product objects
                'store_id': int
            }
        
    Returns:
        str: Success message
    """
    try:            
        # Add timestamps
        current_time = datetime.now(timezone.utc).isoformat()
        data['updated_at'] = current_time
        
        # Check if collection already exists
        existing_collection = supabase.table(SyncedCollectionsTable).select('*').eq('shopify_gid', data['shopify_gid']).execute()
        
        if existing_collection.data:
            # Update existing collection
            result = supabase.table(SyncedCollectionsTable).update({
                'title': data['title'],
                'description': data['description'],
                'products_data': data['products_data'],  # Store as JSON array directly
                'updated_at': current_time
            }).eq('shopify_gid', data['shopify_gid']).execute()
            return "Collection Sync Done - Updated"
        else:
            # Add created_at for new collections
            data['created_at'] = current_time
            # Insert new collection
            result = supabase.table(SyncedCollectionsTable).insert(data).execute()
            return "Collection Sync Done"
            
    except Exception as e:
        print(f"Error inserting/updating collection data: {str(e)}")
        raise

async def store_collection_in_supabase(collection_result: dict, store_id: int) -> List[dict]:
    """
    Store collection data in Supabase collections_created table.

    Args:
        collection_result (dict): The collection data from the generator
        store_id (int): The ID of the store
        
    Returns:
        List[dict]: List of inserted collection records
        
    Raises:
        Exception: If there's an error storing the collections
    """

    try:
        print(f"Starting to store collections for store_id: {store_id}")
        
        # Skip if no collection data or status is no_matches
        if not collection_result or collection_result.get('status') == 'no_matches':
            print("No collections to store - empty result or no matches")
            return []
            
        # Extract collections from the nested structure
        collections_data = collection_result.get('collection', {}).get('collection', {}).get('collections', [])
        task_id = collection_result.get('task_id')
        
        if not collections_data:
            print("No collections found in the provided data")
            return []
            
        # Store each collection
        stored_collections = []
        for collection in collections_data:
            try:
                # Validate required fields
                if not all(key in collection for key in ['collection_title', 'products_list']):
                    print(f"Skipping collection due to missing required fields: {collection.get('collection_title', 'Unknown')}")
                    continue
                    
                # Prepare data for insertion
                collection_record = {
                    'task_id': task_id,
                    'collection_title': collection.get('collection_title'),
                    'collection_description': collection.get('collection_description'),
                    'collection_metatitle': collection.get('collection_metatitle'),
                    'collection_metadescription': collection.get('collection_metadescription'),
                    'meta_keywords': collection.get('meta_keywords', []),  # Ensure this is a list
                    'target_keyword': [collection.get('target_keyword')],
                    'product_list': collection.get('products_list', []),
                    'responsed_from_agent': collection_result,  # Store complete agent response
                    'store_id': store_id,
                    'status': 'to_be_fetched'
                }
                
                # Insert into Supabase
                result = supabase.table(CollectionsCreatedTable).insert(collection_record).execute()
                
                if result.data:
                    stored_collections.extend(result.data)
                    print(f"Successfully stored collection: {collection.get('collection_title')}")
                else:
                    print(f"No data returned when storing collection: {collection.get('collection_title')}")
                    
            except Exception as collection_error:
                print(
                    f"Error storing individual collection",
                    extra={
                        'collection_title': collection.get('collection_title', 'Unknown'),
                        'error': str(collection_error)
                    },
                    exc_info=True
                )
                continue  # Continue with next collection even if one fails
                
        print(
            f"Completed storing collections",
            extra={
                'store_id': store_id,
                'total_stored': len(stored_collections),
                'total_attempted': len(collections_data)
            }
        )
        
        return stored_collections
        
    except Exception as e:
        print(
            "Failed to store collections in Supabase",
            extra={
                'store_id': store_id,
                'error': str(e)
            },
            exc_info=True
        )
        raise Exception(f"Error storing collections: {str(e)}")


async def update_article_status(store_id: int, task_id: str, status: str) -> dict:
    """Update the article table with the generation status."""
    # try:
    #     # Define the table name (replace 'ArticleTable' with your actual table name)
    #     ArticleTable = 'articles'

    #     # Fetch the existing task_id for the article
    #     existing_task_id_result = supabase.table(ArticleTable).select('task_id').eq('store_id', store_id).execute()
    #     existing_task_id = existing_task_id_result.data[0]['task_id'] if existing_task_id_result.data else []
        
    #     # Handle the null case for first-time entry and append the new task_id to the existing ones
    #     updated_task_id = existing_task_id if existing_task_id else []
    #     updated_task_id.append(task_id)

    #     # Update the article table with the new task_id and status
    #     result = supabase.table(ArticleTable)\
    #         .update({
    #             "last_updated_on": datetime.now(timezone.utc).isoformat(),
    #             "status": status,
    #             "task_id": updated_task_id
    #         })\
    #         .eq("store_id", store_id)\
    #         .execute()

        # Return success response
    return {"status": "success", "message": "Article status updated"}
    # except Exception as e:
    #     print(f"Failed to update article status: {str(e)}")
    #     return {"status": "error", "message": f"Failed to update article status: {str(e)}"}

async def get_created_collections_from_db_and_update_status(store_id: str, count: int = 2):
    try:
        # Step 1: Fetch the required data
        result = supabase.table(CollectionsCreatedTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .eq('status', 'to_be_fetched')\
            .limit(count)\
            .execute()

        data = result.data if result.data else []
        # print(f"data: {data}")
        # Step 2: Check if the required count of records is available
        warning = None
        if len(data) < count:
            warning = f"Only {len(data)} records available out of the requested {count}."

        # Step 3: Update the status to 'fetched' for the fetched records
        fetched_ids = [record['id'] for record in data]  # Assuming 'id' is the primary key
        if fetched_ids:
            supabase.table(CollectionsCreatedTable)\
                .update({"status": "fetched"})\
                .in_("id", fetched_ids)\
                .execute()

        # Step 4: Return the data and warning (if applicable)
        response = {"data": data}
        if warning:
            response["warning"] = warning

        return response

    except Exception as e:
        print(f"Failed to fetch and update status: {str(e)}")
        return {"status": "error", "message": f"Failed to fetch and update status: {str(e)}"}
    

#Keyword Discovery


async def get_store_details(store_id: int) -> Dict[str, Any]:
    """Fetch store details including name, url, and competitor domains."""
    try:
        result = supabase.table(StoresTable).select('name,url,competitors_domains').eq('id', store_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        print(f"Failed to fetch store details: {str(e)}", store_id=store_id)
        raise Exception(f"Failed to fetch store details: {str(e)}")

# get existing keywords for self origin
async def get_existing_keywords_self_origin(store_id: int) -> List[Dict[str, Any]]:
    """Fetch existing keywords for self origin."""
    try:
        #fetch name from stores table
        storeUrl = supabase.table(StoresTable).select('url').eq('id', store_id).execute()
        storeUrl = storeUrl.data[0]['url'] if storeUrl.data else None
        if not storeUrl:
            raise Exception(f"Store url not found for store_id: {store_id}")
        
        #fetch keywords from keywords_discovery table
        result = supabase.table(KeywordsDiscoveryTable).select('*').eq('store_id', store_id).eq('currently_active', True).eq('origin_type', 'self').eq('url', storeUrl).order('generated_on', desc=True).execute()
        return result.data if result.data else []
    except Exception as e:
        print(f"Failed to fetch existing keywords for self origin: {str(e)}", store_id=store_id)
        raise Exception(f"Failed to fetch existing keywords for self origin: {str(e)}")
    
# get existing keywords for competitor origin
async def get_existing_keywords_competitor_origin(store_id: int, competitors_domain: str) -> List[Dict[str, Any]]:
    """Fetch existing keywords for competitor origin."""
    try:
        result = supabase.table(KeywordsDiscoveryTable).select('*').eq('store_id', store_id).eq('currently_active', True).eq('origin_type', 'competitor').eq('url', competitors_domain).order('generated_on', desc=True).execute()
        return result.data if result.data else []
    except Exception as e:
        print(f"Failed to fetch existing keywords for competitor origin: {str(e)}", store_id=store_id)
        raise Exception(f"Failed to fetch existing keywords for competitor origin: {str(e)}")

# get existing keywords from database
async def get_existing_keywords(
    store_id: int,
    competitors_domain: Optional[List[str]],
    generate_from_store: bool,
    generate_from_competitors: bool
) -> List[Dict[str, Any]]:
    """
    Fetch existing keywords from keywords_discovery table based on generation type.
    Returns records sorted by generated_on in descending order.
    """
    try:
        keywords = []

        if generate_from_store:
            store_keywords = supabase.table(KeywordsDiscoveryTable)\
                .select('*')\
                .eq('store_id', store_id)\
                .eq('currently_active', True)\
                .eq('origin_type', 'self')\
                .order('generated_on', desc=True)\
                .execute()
            if store_keywords.data:
                keywords.extend(store_keywords.data)

        if generate_from_competitors and competitors_domain:
            for competitor in competitors_domain:
                competitor_keywords = supabase.table(KeywordsDiscoveryTable)\
                    .select('*')\
                    .eq('store_id', store_id)\
                    .eq('currently_active', True)\
                    .eq('url', competitor)\
                    .order('generated_on', desc=True)\
                    .execute()
                if competitor_keywords.data:
                    keywords.extend(competitor_keywords.data)

        return sorted(keywords, key=lambda x: x['generated_on'], reverse=True) if keywords else []
    except Exception as e:
        print(f"Failed to fetch existing keywords: {str(e)}", 
                    store_id=store_id, 
                    from_store=generate_from_store,
                    from_competitors=generate_from_competitors)
        raise Exception(f"Failed to fetch existing keywords: {str(e)}")


def json_serial(obj):
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")

async def create_keyword_record(
    store_id: int,
    origin_type: str,
    keywords: List[str],
    used_keywords: Optional[List[str]] = None,
    url: Optional[str] = None
) -> Dict[str, Any]:
    try:
        cleaned_keywords = list(set(k.strip() for k in keywords if k and k.strip()))
        if not cleaned_keywords:
            return None
            
        data = {
            "store_id": store_id,
            "generated_on": datetime.now().isoformat().split('T')[0],
            "origin_type": origin_type,
            "keywords": cleaned_keywords,  # Postgres will handle array conversion
            "available_keywords": cleaned_keywords,
            "used_keywords": used_keywords or [],
            "available_keyword_count": len(cleaned_keywords),
            "currently_active": True,
            "url": url
        }
        
        result = supabase.table(KeywordsDiscoveryTable).insert(data).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        print(f"Failed to create keyword record: {str(e)}")
        raise

async def update_keyword_usage(
    record_id: int,
    new_used_keywords: List[str],
    available_keywords: List[str]
) -> Dict[str, Any]:
    try:
        data = {
            "used_keywords": new_used_keywords,
            "available_keywords": available_keywords,
            "available_keyword_count": len(available_keywords),
            "updated_at": datetime.now().isoformat()  # Convert to ISO string
        }
        
        result = supabase.table(KeywordsDiscoveryTable)\
            .update(data)\
            .eq('id', record_id)\
            .execute()
        return result.data[0] if result.data else None
    except Exception as e:
        print(f"Failed to update keyword usage: {str(e)}")
        raise
    
async def get_filtered_keywords(
    existing_records: List[Dict[str, Any]],
    limit: int
) -> Tuple[List[str], Dict[int, List[str]]]:
    """
    Filter and select keywords from existing records.
    Returns tuple of (selected_keywords, updates_by_record_id).
    """
    try:
        available_keywords = []
        record_map = {}
        
        for record in existing_records:
            keywords = set(record.get('available_keywords', []))
            used = set(record.get('used_keywords', []))
            unused = list(keywords - used)
            
            if unused:
                available_keywords.extend(unused)
                record_map[record['id']] = unused
                
        selected = available_keywords[:limit]
        
        # Track which keywords were used from each record
        updates = {}
        for record_id, record_keywords in record_map.items():
            used_from_record = [k for k in selected if k in record_keywords]
            if used_from_record:
                updates[record_id] = used_from_record
                
        return selected, updates
    except Exception as e:
        print(f"Failed to filter keywords: {str(e)}")
        raise Exception(f"Failed to filter keywords: {str(e)}")

# Product Suggestion
 
async def get_products_with_embeddings(store_id: int) -> List[Dict]:
    """Fetch products with embeddings for a store."""
    try:
        result = supabase.table("products")\
            .select('shopify_gid, title, description, image_url, product_url, title_embedding, description_embedding, handle_embedding')\
            .eq('store_id', store_id)\
            .execute()
        return result.data if result.data else []
    except Exception as e:
        raise Exception(f"Error fetching products: {str(e)}")

async def update_product_embeddings(product_id: str, embeddings: Dict[str, List[float]]) -> None:
    """Update embeddings for a product."""
    try:
        supabase.table("products")\
            .update(embeddings)\
            .eq('shopify_gid', product_id)\
            .execute()
    except Exception as e:
        raise Exception(f"Error updating product embeddings: {str(e)}")

async def get_random_products(store_id: int, limit: int = 10) -> List[Dict]:
    """Fetch random products when similarity matching fails."""
    try:
        result = supabase.table("products")\
            .select('shopify_gid, title, image_url, product_url')\
            .eq('store_id', store_id)\
            .order('random()')\
            .limit(limit)\
            .execute()
        return result.data if result.data else []
    except Exception as e:
        raise Exception(f"Error fetching random products: {str(e)}")
    

# article job
async def create_article_job(
   task_id: Optional[UUID],
   user_id: UUID,
   article_id: UUID,
   title: str,
   subtitles: list,
   target_keyword: list,
   product_data: Optional[list],
   metadata: Dict[str, Any],
   additional_features: Dict[str, Any],
   total_request: Dict[str, Any],
   status: str,
   store_id: int
) -> Dict[str, Any]:
   try:
       result = supabase.table(ArticleJobsTable).insert({
           "task_id": task_id,
           "user_id": user_id,
           "article_id": article_id,
           "title": title,
           "subtitles": subtitles,
           "target_keyword": target_keyword,
           "product_data": product_data,
           "metadata": metadata,
           "additional_features": additional_features,
           "total_request": total_request,
           "status": status,
           "store_id": store_id
       }).execute()
       return result.data[0] if result.data else None
   except Exception as e:
       raise Exception(f"Error creating article job: {str(e)}")

async def update_article_job_task_id(request_id: UUID, task_id: Optional[UUID], status: str) -> bool:
   try:
       result = supabase.table(ArticleJobsTable).update({
           "task_id": task_id,
           "updated_at": "NOW()",
           "status": status
       }).eq("task_id", str(request_id)).execute()
       return bool(result.data)
   except Exception as e:
       raise Exception(f"Error updating article job task_id: {str(e)}")

async def get_article_job_by_task_id(task_id: UUID) -> Dict[str, Any]:
   try:
       result = supabase.table(ArticleJobsTable).select("*").eq("task_id", str(task_id)).execute()
       return result.data[0] if result.data else None
   except Exception as e:   
       print(f"Error fetching article job: {str(e)}")
       raise Exception(f"Error fetching article job: {str(e)}")

async def update_article_job_status(task_id: UUID, status: str) -> bool:
   try:
       result = supabase.table(ArticleJobsTable).update({"status": status}).eq("task_id", str(task_id)).execute()
       return bool(result.data)
   except Exception as e:
       print(f"Error updating article job status: {str(e)}")
       raise Exception(f"Error updating article job status: {str(e)}")
   

#articles created table
async def insert_article_data(article_data: Dict[str, Any], task_id: UUID) -> None:
    try:
        article_data['task_id'] = task_id
        result = supabase.table(ArticlesCreatedTable).insert(article_data).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error inserting article data: {str(e)}")

#collection jobs table

#Store the collection job data in the collection jobs table
async def insert_collection_job_request_data(collection_job_data: Dict[str, Any], task_id: UUID, status: str) -> None:
    try:
        collection_job_data['task_id'] = task_id
        collection_job_data['status'] = status

        #format the request data according to the table schema
        refined_collection_job_data = {
            "task_id": task_id,
            "status": status,
            "total_request": collection_job_data,
            "created_at": "NOW()",
            "updated_at": "NOW()",
            "attributes": collection_job_data['attributes'] if 'attributes' in collection_job_data else None
        }
        result = supabase.table(CollectionJobsTable).insert(refined_collection_job_data).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error inserting collection job data: {str(e)}")

# update task id in collection jobs table
async def update_collection_job_task_id(task_id: UUID, old_request_id: int) -> None:
    try:
        result = supabase.table(CollectionJobsTable).update({"task_id": task_id, "status": "STARTED", "updated_at": "NOW()"}).eq("task_id", old_request_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error updating collection job task_id: {str(e)}")

#update collection job status
async def update_collection_job_status_on_collection_job_table(task_id: int, status: str) -> None:
    try:
        result = supabase.table(CollectionJobsTable).update({"status": status}).eq("task_id", task_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error updating collection job status: {str(e)}")
    
# Keywords for collection creation
async def get_keywords_for_colection_creation_origin_self(store_id: int) -> list:
    try:
        result = supabase.table(KeywordsDiscoveryTable) \
            .select('available_keywords, used_keywords') \
            .eq('store_id', store_id) \
            .eq('origin_type', 'self') \
            .eq('currently_active', True) \
            .order('generated_on', desc=True) \
            .execute()
        # flatten the list of lists and fetch the first 3
        keywords = result.data[0]['available_keywords']
        used_keywords = result.data[0]['used_keywords']
        keywords = keywords[:3]
        # add the keywords to the used_keywords list
        used_keywords.extend(keywords)

        # update the used_keywords list
        supabase.table(KeywordsDiscoveryTable) \
            .update({"used_keywords": used_keywords}) \
            .eq("store_id", store_id) \
            .eq("origin_type", "self") \
            .eq("currently_active", True) \
            .execute()
        
        if not keywords:
            return []
        
        return keywords
    except Exception as e:
        raise Exception(f"Error fetching keywords for collection creation: {str(e)}")

# Keywords for collection creation
async def get_keywords_for_colection_creation_origin_competitor(store_id: int) -> list:
    try:
        # get the competitors domains from the stores table
        try:    
            result = supabase.table(StoresTable).select('competitors_domains').eq('id', store_id).execute()
            if not result.data:
                raise Exception("No competitors domains found for store")
            competitors_domains = result.data[0]['competitors_domains']
            if not competitors_domains:
                raise Exception("Empty competitors domains list")
        except Exception as e:
            raise Exception(f"Error fetching competitors domains: {str(e)}")   

        # loop through the competitors domains and fetch the keywords from the keywords discovery table
        all_keywords = []
        for competitor_domain in competitors_domains:
            result = supabase.table(KeywordsDiscoveryTable) \
                .select('available_keywords, used_keywords') \
                .eq('store_id', store_id) \
                .eq('origin_type', 'competitor') \
                .eq('currently_active', True) \
                .eq('url', competitor_domain) \
                .execute()
                
            if result.data and len(result.data) > 0:
                available_keywords = result.data[0]['available_keywords']
                used_keywords = result.data[0]['used_keywords'] or []
                
                # Get up to 3 keywords from available keywords
                selected_keywords = available_keywords[:3]
                if selected_keywords:
                    # Update used keywords
                    used_keywords.extend(selected_keywords)
                    all_keywords.extend(selected_keywords)

                    # Update the used_keywords list in database
                    supabase.table(KeywordsDiscoveryTable) \
                        .update({"used_keywords": used_keywords, "updated_at": "NOW()", "available_keyword_count": len(available_keywords), "available_keywords": available_keywords}) \
                        .eq("store_id", store_id) \
                        .eq("origin_type", "competitor") \
                        .eq("currently_active", True) \
                        .eq("url", competitor_domain) \
                        .execute()
            
        if not all_keywords:
            return []
            
        return all_keywords
    
    except Exception as e:
        raise Exception(f"Error fetching keywords for collection creation: {str(e)}")
    
async def fallback_to_used_keywords(store_id: int) -> list:
    try:
        result = supabase.table(KeywordsDiscoveryTable) \
            .select('used_keywords') \
            .eq('store_id', store_id) \
            .eq('currently_active', True) \
            .eq('origin_type', 'self') \
            .order('generated_on', desc=True) \
            .execute()
        
        keywords = result.data[0]['used_keywords']
        # get random 4 keywords from the list
        keywords = random.sample(keywords, 4)
        return keywords
    except Exception as e:
        raise Exception(f"Error fetching used keywords for collection creation: {str(e)}")

async def get_attributes_for_collection_creation(store_id: int) -> list:
    try:
        result = supabase.table(RankCollectionsScheduleTable).select('product_attributes, seasonal_trends, customer_behaviour, market_analysis').eq('store_id', store_id).execute()
        return result.data[0] if result.data else []
    except Exception as e:
        raise Exception(f"Error fetching attributes for collection creation: {str(e)}")
    
async def store_collections_in_database(task_id: str, collections: dict, responsed_from_agent: dict, store_id: int, is_manual_creation: bool = False) -> None:
    """Store collections data in the database.
    
    Args:
        task_id: The unique task identifier
        collections: The collections data containing a list of collections
        
    Returns:
        None
        
    Raises:
        Exception: If there's an error storing the collections
    """
    try:
        # Collections is already a list in the 'collections' key
        collections_data = collections
        
        stored_collections = []
        for collection in collections_data:
            # Format collection data according to table schema
            refined_collection_data = {
                "task_id": task_id,
                "collection_title": collection.get('collection_title'),
                "collection_description": collection.get('collection_description'),
                "collection_metatitle": collection.get('collection_metatitle'),
                "collection_metadescription": collection.get('collection_metadescription'),
                "meta_keywords": collection.get('meta_keywords', []),
                "target_keyword": [collection.get('target_keyword')] if collection.get('target_keyword') else [],
                "product_list": collection.get('products_list', []),
                "responsed_from_agent": responsed_from_agent,
                "store_id": store_id,
                "created_at": "NOW()",
                "updated_at": "NOW()",
                "status": "fetched" if is_manual_creation else "to_be_fetched"
            }
            
            result = supabase.table(CollectionsCreatedTable).insert(refined_collection_data).execute()
            if result.data:
                stored_collections.extend(result.data)
                
        return stored_collections if stored_collections else None
        
    except Exception as e:
        print(f"Error storing collections: {str(e)}")
        raise Exception(f"Error storing collections in database: {str(e)}")

# get article details by task id
async def get_article_details_by_task_id(task_id: UUID) -> Dict[str, Any]:
    try:
        result = supabase.table(ArticlesCreatedTable).select("*").eq("task_id", task_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error fetching article details: {str(e)}")
    
# get all collections title created by a store
async def get_all_collections_title_created_by_a_store(store_id: int) -> list:
    try:
        result = supabase.table(CollectionsCreatedTable).select('collection_title').eq('store_id', store_id).execute()
        return [collection['collection_title'] for collection in result.data]
    except Exception as e:
        raise Exception(f"Error fetching collections title: {str(e)}")

# You'll need to create a new table for product sync jobs
ProductSyncJobsTable = 'product_sync_jobs'

async def insert_product_sync_job(
    task_id: str,
    store_id: int,
    product_count: int,
    status: str = 'QUEUED'
) -> Dict[str, Any]:
    """Insert a new product sync job entry"""
    try:
        data = {
            "task_id": task_id,
            "store_id": store_id,
            "product_count": product_count,
            "status": status,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table(ProductSyncJobsTable).insert(data).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error inserting product sync job: {str(e)}")

async def update_product_sync_job_status(
    task_id: str,
    status: str,
    sync_stats: Dict[str, Any] = None,
    error: str = None
) -> bool:
    """Update the status of a product sync job"""
    try:
        update_data = {
            "status": status,
            "updated_at": datetime.now(timezone.utc).isoformat()
        }
        
        if sync_stats:
            update_data["sync_stats"] = sync_stats
            
        if error:
            update_data["error"] = error
            
        result = supabase.table(ProductSyncJobsTable).update(update_data).eq("task_id", task_id).execute()
        return bool(result.data)
    except Exception as e:
        raise Exception(f"Error updating product sync job status: {str(e)}")

async def get_product_sync_job(task_id: str) -> Dict[str, Any]:
    """Get product sync job details by task ID"""
    try:
        result = supabase.table(ProductSyncJobsTable).select("*").eq("task_id", task_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error fetching product sync job: {str(e)}")

#---------------------------------BLOG SYNC JOBS TABLE---------------------------------
async def insert_blog_sync_job(
    task_id: str,
    store_id: int,
    blog_count: int,
    status: str = 'QUEUED'
) -> Dict[str, Any]:
    """Insert a new blog sync job entry"""
    try:
        data = {
            "task_id": task_id,
            "store_id": store_id,
            "blog_count": blog_count,
            "status": status,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table(BlogSyncJobsTable).insert(data).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error inserting blog sync job: {str(e)}")

async def update_blog_sync_job_status(
    task_id: str,
    status: str,
    sync_stats: Dict[str, Any] = None,
    error: str = None
) -> bool:
    """Update the status of a blog sync job"""
    try:
        update_data = {
            "status": status,
            "updated_at": datetime.now(timezone.utc).isoformat()
        }
        
        if sync_stats:
            update_data["sync_stats"] = sync_stats
            
        if error:
            update_data["error"] = error
            
        result = supabase.table(BlogSyncJobsTable).update(update_data).eq("task_id", task_id).execute()
        return bool(result.data)
    except Exception as e:
        raise Exception(f"Error updating blog sync job status: {str(e)}")

async def get_blog_sync_job(task_id: str) -> Dict[str, Any]:
    """Get blog sync job details by task ID"""
    try:
        result = supabase.table(BlogSyncJobsTable).select("*").eq("task_id", task_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error fetching blog sync job: {str(e)}")

async def insert_data_into_blogs_table(data: dict):
    """Insert or update blog data in the blogs table"""
    try:
        # First check if the blog already exists
        existing_blog = supabase.table(SyncedBlogsTable)\
            .select("*")\
            .eq('shopify_gid', data['shopify_gid'])\
            .eq('store_id', data['store_id'])\
            .execute()

        # If blog exists, return the existing data
        if existing_blog.data and len(existing_blog.data) > 0:
            return "Blog Sync Done - Already"
        
        # If blog doesn't exist, insert new data
        result = supabase.table(SyncedBlogsTable).insert(data).execute()
        if result.data and len(result.data) > 0:
            return "Blog Sync Done"

    except Exception as e:
        raise Exception(f"Error handling blog: {str(e)}")

async def get_blog_id_by_shopify_gid(shopify_gid: str) -> Optional[int]:
    """Get the blog ID from synced_blogs table using shopify_gid"""
    try:
        result = supabase.table(SyncedBlogsTable)\
            .select("id")\
            .eq('shopify_gid', shopify_gid)\
            .execute()
            
        if result.data and len(result.data) > 0:
            return result.data[0]['id']
        return None
    except Exception as e:
        raise Exception(f"Error getting blog ID: {str(e)}")
    
#---------------------------------ARTICLE SYNC JOBS TABLE---------------------------------

async def insert_data_into_articles_table(data: dict):
    """Insert or update article data in the articles table"""
    try:
        # First check if the article already exists
        existing_article = supabase.table(SyncedArticlesTable)\
            .select("*")\
            .eq('shopify_gid', data['shopify_gid'])\
            .eq('store_id', data['store_id'])\
            .execute()

        # If article exists, return the existing data
        if existing_article.data and len(existing_article.data) > 0:
            return "Article Sync Done - Already"
        
        # If article doesn't exist, insert new data
        result = supabase.table(SyncedArticlesTable).insert(data).execute()
        if result.data and len(result.data) > 0:
            return "Article Sync Done"

    except Exception as e:
        raise Exception(f"Error handling article: {str(e)}")

async def insert_article_sync_job(
    task_id: str,
    store_id: int,
    article_count: int,
    status: str = 'QUEUED'
) -> Dict[str, Any]:
    """Insert a new article sync job entry"""
    try:
        data = {
            "task_id": task_id,
            "store_id": store_id,
            "article_count": article_count,
            "status": status,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table(ArticleSyncJobsTable).insert(data).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error inserting article sync job: {str(e)}")

async def update_article_sync_job_status(
    task_id: str,
    status: str,
    sync_stats: Dict[str, Any] = None,
    error: str = None
) -> bool:
    """Update the status of an article sync job"""
    try:
        update_data = {
            "status": status,
            "updated_at": datetime.now(timezone.utc).isoformat()
        }
        
        if sync_stats:
            update_data["sync_stats"] = sync_stats
            
        if error:
            update_data["error"] = error
            
        result = supabase.table(ArticleSyncJobsTable).update(update_data).eq("task_id", task_id).execute()
        return bool(result.data)
    except Exception as e:
        raise Exception(f"Error updating article sync job status: {str(e)}")

async def get_article_sync_job(task_id: str) -> Dict[str, Any]:
    """Get article sync job details by task ID"""
    try:
        result = supabase.table(ArticleSyncJobsTable).select("*").eq("task_id", task_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        raise Exception(f"Error fetching article sync job: {str(e)}")
    
#---------------------------------COLLECTION SYNC JOBS TABLE---------------------------------

async def insert_collection_sync_job(
    task_id: str,
    store_id: int,
    collection_count: int,
    status: str = 'QUEUED'
) -> Dict[str, Any]:
    """
    Insert a new collection sync job entry
    
    Args:
        task_id: Celery task ID
        store_id: Store ID
        collection_count: Number of collections to sync
        status: Initial status of the job
        
    Returns:
        Dictionary containing the inserted job data
    """
    try:
        data = {
            'task_id': task_id,
            'store_id': store_id,
            'collection_count': collection_count,
            'status': status,
            'created_at': datetime.now().isoformat()
        }
        
        result = supabase.table(CollectionSyncJobsTable).insert(data).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error inserting collection sync job: {str(e)}")
        raise

async def update_collection_sync_job_status(
    task_id: str,
    status: str,
    error: Optional[str] = None,
    sync_stats: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Update the status of a collection sync job
    
    Args:
        task_id: Celery task ID
        status: New status
        error: Optional error message
        sync_stats: Optional sync statistics
        
    Returns:
        Dictionary containing the updated job data
    """
    try:
        update_data = {
            'status': status,
            'updated_at': datetime.now().isoformat()
        }
        
        if error:
            update_data['error'] = error
            
        if sync_stats:
            update_data['sync_stats'] = sync_stats
            
        result = supabase.table(CollectionSyncJobsTable).update(update_data).eq('task_id', task_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error updating collection sync job status: {str(e)}")
        raise

async def get_collection_sync_job(task_id: str) -> Dict[str, Any]:
    """
    Get collection sync job details by task ID
    
    Args:
        task_id: Celery task ID
        
    Returns:
        Dictionary containing the job data
    """
    try:
        result = supabase.table(CollectionSyncJobsTable).select('*').eq('task_id', task_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error getting collection sync job: {str(e)}")
        raise

# NLQ Workflow Table
async def store_nlq_workflow(
    team_id: str,
    store_id: int,
    query: str,
    status: str,
    workflow_name: Optional[str] = None,
    tasks: Optional[List[dict]] = None,
    valid_variations: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Store NLQ workflow data in the database
    
    Args:
        team_id: Unique team identifier
        store_id: Store ID
        query: Original NLQ query
        status: Status of the workflow (generated/approved)
        workflow_name: Name of the workflow if created
        tasks: List of tasks if created
        valid_variations: List of valid variations if not created
        
    Returns:
        Dictionary containing the stored workflow data
    """
    try:
        data = {
            'team_id': team_id,
            'store_id': store_id,
            'query': query,
            'status': status,
            'workflow_name': workflow_name,
            'tasks': tasks,
            'valid_variations': valid_variations,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table(StoreWorkersNlqWorkflowTable).insert(data).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error storing NLQ workflow: {str(e)}")
        raise

async def update_nlq_workflow_approval(
    team_id: str,
    selected_tasks: List[dict],
    status: str = 'approved'
) -> Dict[str, Any]:
    """
    Update NLQ workflow with approved tasks
    
    Args:
        team_id: Team ID to update
        selected_tasks: List of selected tasks
        status: New status (default: 'approved')
        
    Returns:
        Dictionary containing the updated workflow data
    """
    try:
        # First verify if the task exists
        existing_task = supabase.table(StoreWorkersNlqWorkflowTable)\
            .select('*')\
            .eq('team_id', team_id)\
            .execute()
            
        if not existing_task.data:
            raise Exception(f"No workflow found for team_id: {team_id}")
            
        update_data = {
            'selected_tasks': selected_tasks,
            'status': status,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table(StoreWorkersNlqWorkflowTable)\
            .update(update_data)\
            .eq('team_id', team_id)\
            .execute()
            
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error updating NLQ workflow approval: {str(e)}")
        raise

async def get_nlq_workflow_by_team_id(team_id: str) -> Dict[str, Any]:
    """
    Get NLQ workflow details by team ID
    
    Args:
        task_id: Task ID to look up
        
    Returns:
        Dictionary containing the workflow data
    """
    try:
        result = supabase.table(StoreWorkersNlqWorkflowTable).select('*').eq('team_id', team_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error getting NLQ workflow: {str(e)}")
        raise

async def get_nlq_workflows_by_store_id(store_id: int) -> List[Dict[str, Any]]:
    """
    Get all NLQ workflows for a store
    
    Args:
        store_id: Store ID to look up
        
    Returns:
        List of dictionaries containing workflow data
    """
    try:
        result = supabase.table(StoreWorkersNlqWorkflowTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .order('created_at', desc=True)\
            .execute()
        return result.data if result.data else []
        
    except Exception as e:
        print(f"Error getting store NLQ workflows: {str(e)}")
        raise

async def update_nlq_workflow_status(
    task_id: str,
    status: str,
    workflow_name: Optional[str] = None,
    sub_tasks: Optional[List[dict]] = None,
    valid_variations: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Update the status and details of an NLQ workflow
    
    Args:
        task_id: Task ID to update
        status: New status
        workflow_name: Updated workflow name
        sub_tasks: Updated sub-tasks
        valid_variations: Updated valid variations
        
    Returns:
        Dictionary containing the updated workflow data
    """
    try:
        update_data = {
            'status': status,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        if workflow_name is not None:
            update_data['workflow_name'] = workflow_name
            
        if sub_tasks is not None:
            update_data['sub_tasks'] = sub_tasks
            
        if valid_variations is not None:
            update_data['valid_variations'] = valid_variations
            
        result = supabase.table(StoreWorkersNlqWorkflowTable).update(update_data).eq('task_id', task_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error updating NLQ workflow: {str(e)}")
        raise

# Store Workers Agents Table
async def insert_store_worker_agent(
    name: str,
    role: str,
    goal: List[str],
    backstory: str,
    tools: List[dict],
    store_id: int,
    task_ids: List[str],
    visible: str,
    user_ids_with_access: Optional[List[str]] = None,
    tags: Optional[List[str]] = None,
    created_by: str = None,
    modified_by: str = None
) -> Dict[str, Any]:
    """
    Insert a new store worker agent
    
    Args:
        name: Agent name
        role: Agent role
        goal: List of agent goals
        backstory: Agent backstory
        tools: List of agent tools
        store_id: Store ID
        task_ids: List of task IDs
        visible: Visibility level
        user_ids_with_access: List of user IDs with access (for custom visibility)
        tags: List of tags
        created_by: User ID who created the agent
        modified_by: User ID who last modified the agent
        
    Returns:
        Dictionary containing the inserted agent data
    """
    try:
        data = {
            'name': name,
            'role': role,
            'goal': goal,
            'backstory': backstory,
            'tools': tools,
            'store_id': store_id,
            'task_ids': task_ids,
            'visible': visible,
            'user_ids_with_access': user_ids_with_access,
            'tags': tags,
            'created_by': created_by,
            'modified_by': modified_by,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table(StoreWorkersAgentsTable).insert(data).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error inserting store worker agent: {str(e)}")
        raise

async def get_store_worker_agent_by_id(agent_id: int) -> Dict[str, Any]:
    """
    Get store worker agent by ID
    
    Args:
        agent_id: Agent ID to look up
        
    Returns:
        Dictionary containing the agent data
    """
    try:
        result = supabase.table(StoreWorkersAgentsTable).select('*').eq('id', agent_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error getting store worker agent: {str(e)}")
        raise

async def get_store_worker_agents_by_store_id(store_id: int) -> List[Dict[str, Any]]:
    """
    Get all store worker agents for a store
    
    Args:
        store_id: Store ID to look up
        
    Returns:
        List of dictionaries containing agent data
    """
    try:
        result = supabase.table(StoreWorkersAgentsTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .order('created_at', desc=True)\
            .execute()
        return result.data if result.data else []
        
    except Exception as e:
        print(f"Error getting store worker agents: {str(e)}")
        raise

async def update_store_worker_agent(
    agent_id: int,
    name: Optional[str] = None,
    role: Optional[str] = None,
    goal: Optional[List[str]] = None,
    backstory: Optional[str] = None,
    tools: Optional[List[dict]] = None,
    task_ids: Optional[List[str]] = None,
    visible: Optional[str] = None,
    user_ids_with_access: Optional[List[str]] = None,
    tags: Optional[List[str]] = None,
    modified_by: str = None
) -> Dict[str, Any]:
    """
    Update a store worker agent
    
    Args:
        agent_id: Agent ID to update
        name: Updated agent name
        role: Updated agent role
        goal: Updated list of agent goals
        backstory: Updated agent backstory
        tools: Updated list of agent tools
        task_ids: Updated list of task IDs
        visible: Updated visibility level
        user_ids_with_access: Updated list of user IDs with access
        tags: Updated list of tags
        modified_by: User ID who modified the agent
        
    Returns:
        Dictionary containing the updated agent data
    """
    try:
        update_data = {
            'modified_by': modified_by,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        if name is not None:
            update_data['name'] = name
        if role is not None:
            update_data['role'] = role
        if goal is not None:
            update_data['goal'] = goal
        if backstory is not None:
            update_data['backstory'] = backstory
        if tools is not None:
            update_data['tools'] = tools
        if task_ids is not None:
            update_data['task_ids'] = task_ids
        if visible is not None:
            update_data['visible'] = visible
        if user_ids_with_access is not None:
            update_data['user_ids_with_access'] = user_ids_with_access
        if tags is not None:
            update_data['tags'] = tags
            
        result = supabase.table(StoreWorkersAgentsTable).update(update_data).eq('id', agent_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error updating store worker agent: {str(e)}")
        raise

# Store Workers Tasks Table
async def insert_store_worker_task(
    name: str,
    description: str,
    expected_output: str,
    status: str,
    priority: int,
    store_id: int,
    task_ids: List[str],
    visible: str,
    dependencies: Optional[List[dict]] = None,
    execution_history: Optional[List[dict]] = None,
    scheduling_info: Optional[dict] = None,
    user_ids_with_access: Optional[List[str]] = None,
    tags: Optional[List[str]] = None,
    created_by: str = None,
    modified_by: str = None
) -> Dict[str, Any]:
    """
    Insert a new store worker task
    
    Args:
        name: Task name
        description: Task description
        expected_output: Expected output
        status: Task status
        priority: Task priority
        store_id: Store ID
        task_ids: List of task IDs
        visible: Visibility level
        dependencies: List of task dependencies
        execution_history: List of execution history
        scheduling_info: Scheduling information
        user_ids_with_access: List of user IDs with access
        tags: List of tags
        created_by: User ID who created the task
        modified_by: User ID who last modified the task
        
    Returns:
        Dictionary containing the inserted task data
    """
    try:
        data = {
            'name': name,
            'description': description,
            'expected_output': expected_output,
            'status': status,
            'priority': priority,
            'store_id': store_id,
            'task_ids': task_ids,
            'visible': visible,
            'dependencies': dependencies,
            'execution_history': execution_history,
            'scheduling_info': scheduling_info,
            'user_ids_with_access': user_ids_with_access,
            'tags': tags,
            'created_by': created_by,
            'modified_by': modified_by,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table(StoreWorkersTasksTable).insert(data).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error inserting store worker task: {str(e)}")
        raise

async def get_store_worker_task_by_id(task_id: int) -> Dict[str, Any]:
    """
    Get store worker task by ID
    
    Args:
        task_id: Task ID to look up
        
    Returns:
        Dictionary containing the task data
    """
    try:
        result = supabase.table(StoreWorkersTasksTable).select('*').eq('id', task_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error getting store worker task: {str(e)}")
        raise

async def get_store_worker_tasks_by_store_id(store_id: int) -> List[Dict[str, Any]]:
    """
    Get all store worker tasks for a store
    
    Args:
        store_id: Store ID to look up
        
    Returns:
        List of dictionaries containing task data
    """
    try:
        result = supabase.table(StoreWorkersTasksTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .order('created_at', desc=True)\
            .execute()
        return result.data if result.data else []
        
    except Exception as e:
        print(f"Error getting store worker tasks: {str(e)}")
        raise

async def update_store_worker_task(
    task_id: int,
    name: Optional[str] = None,
    description: Optional[str] = None,
    expected_output: Optional[str] = None,
    status: Optional[str] = None,
    priority: Optional[int] = None,
    task_ids: Optional[List[str]] = None,
    visible: Optional[str] = None,
    dependencies: Optional[List[dict]] = None,
    execution_history: Optional[List[dict]] = None,
    scheduling_info: Optional[dict] = None,
    user_ids_with_access: Optional[List[str]] = None,
    tags: Optional[List[str]] = None,
    modified_by: str = None
) -> Dict[str, Any]:
    """
    Update a store worker task
    
    Args:
        task_id: Task ID to update
        name: Updated task name
        description: Updated task description
        expected_output: Updated expected output
        status: Updated task status
        priority: Updated task priority
        task_ids: Updated list of task IDs
        visible: Updated visibility level
        dependencies: Updated list of task dependencies
        execution_history: Updated list of execution history
        scheduling_info: Updated scheduling information
        user_ids_with_access: Updated list of user IDs with access
        tags: Updated list of tags
        modified_by: User ID who modified the task
        
    Returns:
        Dictionary containing the updated task data
    """
    try:
        update_data = {
            'modified_by': modified_by,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        if name is not None:
            update_data['name'] = name
        if description is not None:
            update_data['description'] = description
        if expected_output is not None:
            update_data['expected_output'] = expected_output
        if status is not None:
            update_data['status'] = status
        if priority is not None:
            update_data['priority'] = priority
        if task_ids is not None:
            update_data['task_ids'] = task_ids
        if visible is not None:
            update_data['visible'] = visible
        if dependencies is not None:
            update_data['dependencies'] = dependencies
        if execution_history is not None:
            update_data['execution_history'] = execution_history
        if scheduling_info is not None:
            update_data['scheduling_info'] = scheduling_info
        if user_ids_with_access is not None:
            update_data['user_ids_with_access'] = user_ids_with_access
        if tags is not None:
            update_data['tags'] = tags
            
        result = supabase.table(StoreWorkersTasksTable).update(update_data).eq('id', task_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error updating store worker task: {str(e)}")
        raise

# Store Workers Crews Table
async def insert_store_worker_crew(
    name: str,
    description: str,
    agents: List[dict],
    tasks: List[dict],
    store_id: int,
    task_ids: List[str],
    visible: str,
    performance_metrics: Optional[dict] = None,
    configuration_settings: Optional[dict] = None,
    dependencies: Optional[List[dict]] = None,
    user_ids_with_access: Optional[List[str]] = None,
    tags: Optional[List[str]] = None,
    created_by: str = None,
    modified_by: str = None
) -> Dict[str, Any]:
    """
    Insert a new store worker crew
    
    Args:
        name: Crew name
        description: Crew description
        agents: List of agents
        tasks: List of tasks
        store_id: Store ID
        task_ids: List of task IDs
        visible: Visibility level
        performance_metrics: Performance metrics
        configuration_settings: Configuration settings
        dependencies: List of dependencies
        user_ids_with_access: List of user IDs with access
        tags: List of tags
        created_by: User ID who created the crew
        modified_by: User ID who last modified the crew
        
    Returns:
        Dictionary containing the inserted crew data
    """
    try:
        data = {
            'name': name,
            'description': description,
            'agents': agents,
            'tasks': tasks,
            'store_id': store_id,
            'task_ids': task_ids,
            'visible': visible,
            'performance_metrics': performance_metrics,
            'configuration_settings': configuration_settings,
            'dependencies': dependencies,
            'user_ids_with_access': user_ids_with_access,
            'tags': tags,
            'created_by': created_by,
            'modified_by': modified_by,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table(StoreWorkersCrewsTable).insert(data).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error inserting store worker crew: {str(e)}")
        raise

async def get_store_worker_crew_by_id(crew_id: int) -> Dict[str, Any]:
    """
    Get store worker crew by ID
    
    Args:
        crew_id: Crew ID to look up
        
    Returns:
        Dictionary containing the crew data
    """
    try:
        result = supabase.table(StoreWorkersCrewsTable).select('*').eq('id', crew_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error getting store worker crew: {str(e)}")
        raise

async def get_store_worker_crews_by_store_id(store_id: int) -> List[Dict[str, Any]]:
    """
    Get all store worker crews for a store
    
    Args:
        store_id: Store ID to look up
        
    Returns:
        List of dictionaries containing crew data
    """
    try:
        result = supabase.table(StoreWorkersCrewsTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .order('created_at', desc=True)\
            .execute()
        return result.data if result.data else []
        
    except Exception as e:
        print(f"Error getting store worker crews: {str(e)}")
        raise

async def update_store_worker_crew(
    crew_id: int,
    name: Optional[str] = None,
    description: Optional[str] = None,
    agents: Optional[List[dict]] = None,
    tasks: Optional[List[dict]] = None,
    task_ids: Optional[List[str]] = None,
    visible: Optional[str] = None,
    performance_metrics: Optional[dict] = None,
    configuration_settings: Optional[dict] = None,
    dependencies: Optional[List[dict]] = None,
    user_ids_with_access: Optional[List[str]] = None,
    tags: Optional[List[str]] = None,
    modified_by: str = None
) -> Dict[str, Any]:
    """
    Update a store worker crew
    
    Args:
        crew_id: Crew ID to update
        name: Updated crew name
        description: Updated crew description
        agents: Updated list of agents
        tasks: Updated list of tasks
        task_ids: Updated list of task IDs
        visible: Updated visibility level
        performance_metrics: Updated performance metrics
        configuration_settings: Updated configuration settings
        dependencies: Updated list of dependencies
        user_ids_with_access: Updated list of user IDs with access
        tags: Updated list of tags
        modified_by: User ID who modified the crew
        
    Returns:
        Dictionary containing the updated crew data
    """
    try:
        update_data = {
            'modified_by': modified_by,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        if name is not None:
            update_data['name'] = name
        if description is not None:
            update_data['description'] = description
        if agents is not None:
            update_data['agents'] = agents
        if tasks is not None:
            update_data['tasks'] = tasks
        if task_ids is not None:
            update_data['task_ids'] = task_ids
        if visible is not None:
            update_data['visible'] = visible
        if performance_metrics is not None:
            update_data['performance_metrics'] = performance_metrics
        if configuration_settings is not None:
            update_data['configuration_settings'] = configuration_settings
        if dependencies is not None:
            update_data['dependencies'] = dependencies
        if user_ids_with_access is not None:
            update_data['user_ids_with_access'] = user_ids_with_access
        if tags is not None:
            update_data['tags'] = tags
            
        result = supabase.table(StoreWorkersCrewsTable).update(update_data).eq('id', crew_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error updating store worker crew: {str(e)}")
        raise

# Store Workers Task Configurations Table
async def insert_store_worker_task_configuration(
    task_id: str,
    name: str,
    type: str,
    current_configuration: List[dict],
    current_key_values: dict,
    created_by: str,
    modified_by: str,
    status: str = 'active'
) -> Dict[str, Any]:
    """
    Insert a new store worker task configuration
    
    Args:
        task_id: Task ID
        name: Configuration name
        type: Configuration type
        current_configuration: Current configuration array
        current_key_values: Current key-value pairs
        created_by: User ID who created the configuration
        modified_by: User ID who last modified the configuration
        status: Configuration status
        
    Returns:
        Dictionary containing the inserted configuration data
    """
    try:
        data = {
            'task_id': task_id,
            'name': name,
            'type': type,
            'current_configuration': current_configuration,
            'current_key_values': current_key_values,
            'configuration_history': [{
                'configuration': current_configuration,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'modified_by': modified_by
            }],
            'status': status,
            'version': 1,
            'created_by': created_by,
            'modified_by': modified_by,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        result = supabase.table(StoreWorkersTaskConfigurationsTable).insert(data).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error inserting store worker task configuration: {str(e)}")
        raise

async def get_store_worker_task_configuration_by_task_id(task_id: str) -> Dict[str, Any]:
    """
    Get store worker task configuration by task ID
    
    Args:
        task_id: Task ID to look up
        
    Returns:
        Dictionary containing the configuration data
    """
    try:
        result = supabase.table(StoreWorkersTaskConfigurationsTable).select('*').eq('task_id', task_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error getting store worker task configuration: {str(e)}")
        raise

async def update_store_worker_task_configuration(
    task_id: str,
    current_configuration: Optional[List[dict]] = None,
    current_key_values: Optional[dict] = None,
    status: Optional[str] = None,
    modified_by: str = None
) -> Dict[str, Any]:
    """
    Update a store worker task configuration
    
    Args:
        task_id: Task ID to update
        current_configuration: Updated configuration array
        current_key_values: Updated key-value pairs
        status: Updated status
        modified_by: User ID who modified the configuration
        
    Returns:
        Dictionary containing the updated configuration data
    """
    try:
        # Get current configuration
        current = await get_store_worker_task_configuration_by_task_id(task_id)
        if not current:
            raise Exception(f"No configuration found for task_id: {task_id}")
            
        # Prepare update data
        update_data = {
            'modified_by': modified_by,
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'version': current['version'] + 1
        }
        
        if current_configuration is not None:
            update_data['current_configuration'] = current_configuration
        if current_key_values is not None:
            update_data['current_key_values'] = current_key_values
        if status is not None:
            update_data['status'] = status
            
        # Add to history
        history_entry = {
            'configuration': current_configuration or current['current_configuration'],
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'modified_by': modified_by
        }
        update_data['configuration_history'] = current['configuration_history'] + [history_entry]
            
        result = supabase.table(StoreWorkersTaskConfigurationsTable).update(update_data).eq('task_id', task_id).execute()
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error updating store worker task configuration: {str(e)}")
        raise

async def get_store_worker_task_configuration_history(task_id: str) -> List[Dict[str, Any]]:
    """
    Get configuration history for a task
    
    Args:
        task_id: Task ID to look up
        
    Returns:
        List of dictionaries containing configuration history
    """
    try:
        result = supabase.table(StoreWorkersTaskConfigurationsTable)\
            .select('configuration_history')\
            .eq('task_id', task_id)\
            .execute()
            
        if not result.data:
            return []
            
        return result.data[0]['configuration_history']
        
    except Exception as e:
        print(f"Error getting store worker task configuration history: {str(e)}")
        raise

async def store_approved_workers(
    team_id: str,
    store_id: int,
    agents: List[dict],
    operation: str = 'create',
    created_by: str = None,
    modified_by: str = None
) -> Dict[str, Any]:
    """
    Store or update approved workers in the database
    
    Args:
        team_id: Team ID to link with
        store_id: Store ID
        agents: List of approved workers/agents
        operation: 'create' or 'update' (default: 'create')
        created_by: User ID who created the record
        modified_by: User ID who modified the record
        
    Returns:
        Dictionary containing the stored/updated data
    """
    try:
        # First verify if the task exists in NLQ workflow
        existing_task = supabase.table(StoreWorkersNlqWorkflowTable)\
            .select('*')\
            .eq('team_id', team_id)\
            .execute()
            
        if not existing_task.data:
            raise Exception(f"No workflow found for team_id: {team_id}")
            
        # Prepare the data
        data = {
            'name': f"Approved Workers for Team {team_id}",
            'role': 'approved_worker',
            'goal': ['Execute approved tasks'],
            'backstory': 'Approved workers for NLQ task execution',
            'tools': [],
            'store_id': store_id,
            'team_id': [team_id],  # Store as array of team_ids
            'visible': 'private',
            'created_by': created_by,
            'modified_by': modified_by,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        if operation == 'create':
            # Insert new record
            result = supabase.table(StoreWorkersAgentsTable).insert(data).execute()
        else:
            # Update existing record
            result = supabase.table(StoreWorkersAgentsTable)\
                .update(data)\
                .eq('team_id', [team_id])\
                .execute()
                
        return result.data[0] if result.data else None
        
    except Exception as e:
        print(f"Error storing approved workers: {str(e)}")
        raise

# Storing task configuration
async def store_task_configuration(
    team_id: str,
    current_configuration: List[dict],
    current_key_values: dict,
    operation: str = 'create',
    name: Optional[str] = None,
    type: Optional[str] = None,
    store_data_type: Optional[dict] = None,
    store_data: Optional[list] = None,
    include_metadata: Optional[bool] = None,
    created_by: str = None,
    modified_by: str = None
) -> Dict[str, Any]:
    """
    Store or update task configuration in the database.
    """
    try:
        created_by = created_by or team_id
        modified_by = modified_by or team_id
        existing_config = supabase.table(StoreWorkersTaskConfigurationsTable).select("*").eq("team_id", team_id).execute()
        data = {
            "team_id": team_id,
            "name": name,
            "type": type,
            "current_configuration": current_configuration,
            "current_key_values": current_key_values,
            "status": "active",
            "modified_by": modified_by,
            "store_data_type": store_data_type,
            "store_data": store_data,
            "include_metadata": include_metadata
        }
        if existing_config.data:
            if operation == 'create':
                error(f"Configuration already exists for team_id: {team_id}")
                return None
            current = existing_config.data[0]
            version = current.get('version', 1) + 1
            configuration_history = current.get('configuration_history', [])
            configuration_history.append({
                'configuration': current.get('current_configuration', []),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'modified_by': current.get('modified_by')
            })
            data.update({
                'version': version,
                'configuration_history': configuration_history
            })
            result = supabase.table(StoreWorkersTaskConfigurationsTable).update(data).eq("team_id", team_id).execute()
        else:
            if operation == 'update':
                error(f"No configuration found for team_id: {team_id}")
                return None
            data.update({
                'version': 1,
                'configuration_history': [],
                'created_by': created_by
            })
            result = supabase.table(StoreWorkersTaskConfigurationsTable).insert(data).execute()
        if not result.data:
            error(f"Failed to store task configuration for team_id: {team_id}")
            return None
        return result.data[0]
    except Exception as e:
        error(f"Error storing task configuration: {str(e)}", exc_info=True)
        return None

async def update_task_configuration(
    team_id: str,
    current_configuration: List[dict],
    current_key_values: dict,
    store_data_type: Optional[dict] = None,
    store_data: Optional[list] = None,
    include_metadata: Optional[bool] = None
) -> Dict[str, Any]:
    """
    Update task configuration in the database.
    """
    try:
        data = {
            "team_id": team_id,
            "current_configuration": current_configuration,
            "current_key_values": current_key_values,
            "status": "active",
            "store_data_type": store_data_type,
            "store_data": store_data,
            "include_metadata": include_metadata
        }
        result = supabase.table(StoreWorkersTaskConfigurationsTable).update(data).eq("team_id", team_id).execute()
        if not result.data:
            error(f"Failed to update task configuration for team_id: {team_id}")
            return None
        return result.data[0]
    except Exception as e:
        error(f"Error updating task configuration: {str(e)}", exc_info=True)
        return None

async def check_task_configuration_exists(team_id: str) -> tuple[bool, str]:
    """
    Check if a task configuration exists for the given team_id and determine the operation type.
    
    Args:
        team_id (str): The team ID to check
        
    Returns:
        tuple[bool, str]: A tuple containing (exists, operation)
            - exists (bool): Whether the configuration exists
            - operation (str): The operation type ('create' or 'update')
    """
    try:
        existing_config = supabase.table(StoreWorkersTaskConfigurationsTable).select("*").eq("team_id", team_id).execute()
        exists = bool(existing_config.data)
        operation = 'update' if exists else 'create'
        return exists, operation
    except Exception as e:
        error(f"Error checking task configuration existence: {str(e)}", exc_info=True)
        return False, 'create'  # Default to create on error


# Store created team
async def store_created_team(
    team_id: str,
    store_id: int,
    commander_output: dict
) -> Dict[str, Any]:
    """
    Store the created team and its components in the database.
    
    Args:
        team_id: The ID of the team
        store_id: The ID of the store
        commander_output: The output of the commander containing agents, tasks, crew and team data
        
    Returns:
        Dict[str, Any]: The stored team data
        
    Raises:
        Exception: If any part of the operation fails
    """
    try:
        print(f"Starting to store created team for team_id: {team_id}, store_id: {store_id}")
        
        # Process and store agents
        for task in commander_output.get('tasks', []):
            print(f"Processing task: {task.Name if hasattr(task, 'Name') else 'Unknown'}")
            
            for agent in task.Agents if hasattr(task, 'Agents') else []:
                print(f"Processing agent: {agent.Name if hasattr(agent, 'Name') else 'Unknown'}")
                
                try:
                    # Check if agent exists by name
                    existing_agent = supabase.table(StoreWorkersAgentsTable)\
                        .select('*')\
                        .eq('name', agent.Name)\
                        .execute()
                    
                    if existing_agent.data:
                        print(f"Found existing agent: {agent.Name}")
                        # Update existing agent's team_id array
                        agent_data = existing_agent.data[0]
                        team_ids = agent_data.get('team_id', []) or []
                        if team_id not in team_ids:
                            team_ids.append(team_id)
                            
                        supabase.table(StoreWorkersAgentsTable)\
                            .update({'team_id': team_ids})\
                            .eq('id', agent_data['id'])\
                            .execute()
                        print(f"Updated team_id array for agent: {agent.Name}")
                    else:
                        print(f"Creating new agent: {agent.Name}")
                        # Create new agent
                        new_agent = {
                            'name': agent.Name,
                            'role': agent.Role,
                            'goal': agent.Goal,
                            'backstory': agent.Backstory,
                            'tools': agent.Tools if hasattr(agent, 'Tools') else [],
                            'store_id': store_id,
                            'team_id': [team_id],
                            'visible': 'private',
                            'created_by': store_id,
                            'last_modified_by': store_id
                        }
                        supabase.table(StoreWorkersAgentsTable)\
                            .insert(new_agent)\
                            .execute()
                        print(f"Created new agent: {agent.Name}")
                        
                except Exception as agent_error:
                    print(f"Error processing agent {agent.Name if hasattr(agent, 'Name') else 'Unknown'}: {str(agent_error)}")
                    raise
            
            # Process and store tasks
            for task_item in task.Tasks if hasattr(task, 'Tasks') else []:
                print(f"Processing task item: {task_item.Name if hasattr(task_item, 'Name') else 'Unknown'}")
                
                try:
                    # Check if task exists by name
                    existing_task = supabase.table(StoreWorkersTasksTable)\
                        .select('*')\
                        .eq('name', task_item.Name)\
                        .execute()
                    
                    if existing_task.data:
                        print(f"Found existing task: {task_item.Name}")
                        # Update existing task's team_id array
                        task_data = existing_task.data[0]
                        team_ids = task_data.get('team_id', []) or []
                        if team_id not in team_ids:
                            team_ids.append(team_id)
                            
                        supabase.table(StoreWorkersTasksTable)\
                            .update({'team_id': team_ids})\
                            .eq('id', task_data['id'])\
                            .execute()
                        print(f"Updated team_id array for task: {task_item.Name}")
                    else:
                        print(f"Creating new task: {task_item.Name}")
                        # Create new task
                        new_task = {
                            'name': task_item.Name,
                            'description': task_item.Description,
                            'expected_output': task_item.Output,
                            'agents': [task_item.Agent],
                            'store_id': store_id,
                            'team_id': [team_id],
                            'visible': 'private',
                            'created_by': store_id,
                            'last_modified_by': store_id
                        }
                        supabase.table(StoreWorkersTasksTable)\
                            .insert(new_task)\
                            .execute()
                        print(f"Created new task: {task_item.Name}")
                        
                except Exception as task_error:
                    print(f"Error processing task {task_item.Name if hasattr(task_item, 'Name') else 'Unknown'}: {str(task_error)}")
                    raise
            
            # Store crew data
            crew_data = task.Crew if hasattr(task, 'Crew') else {}
            print(f"Processing crew: {crew_data.Name if hasattr(crew_data, 'Name') else 'Unknown'}")
            
            try:
                new_crew = {
                    'name': crew_data.Name if hasattr(crew_data, 'Name') else '',
                    'agents': crew_data.Agents if hasattr(crew_data, 'Agents') else [],
                    'tasks': crew_data.Tasks if hasattr(crew_data, 'Tasks') else [],
                    'store_id': store_id,
                    'team_id': [team_id],
                    'visible': 'private',
                    'created_by': store_id,
                    'last_modified_by': store_id
                }
                supabase.table(StoreWorkersCrewsTable)\
                    .insert(new_crew)\
                    .execute()
                print(f"Created new crew: {crew_data.Name if hasattr(crew_data, 'Name') else 'Unknown'}")
                
            except Exception as crew_error:
                print(f"Error processing crew {crew_data.Name if hasattr(crew_data, 'Name') else 'Unknown'}: {str(crew_error)}")
                raise
        
        # Store team data
        print("Storing team data")
        try:
            first_task = commander_output.get('tasks', [{}])[0]
            new_team = {
                'team_id': team_id,
                'name': first_task.Name if hasattr(first_task, 'Name') else '',
                'description': first_task.Description if hasattr(first_task, 'Description') else '',
                'store_id': store_id,
                'workflow_data': json.loads(json.dumps(commander_output, default=lambda o: o.__dict__ if hasattr(o, "__dict__") else str(o))),
                'status': 'pending',
                'visible': 'private',
                'created_by': store_id,
                'last_modified_by': store_id
            }
            
            team_result = supabase.table(StoreWorkersTeamsTable)\
                .insert(new_team)\
                .execute()
            
            print(f"Successfully stored team data for team_id: {team_id}")
            return team_result.data[0] if team_result.data else None
            
        except Exception as team_error:
            print(f"Error storing team data: {str(team_error)}")
            raise
        
    except Exception as e:
        print(f"Error storing created team: {str(e)}")
        print(f"Error details: {traceback.format_exc()}")
        raise Exception(f"Failed to store created team: {str(e)}")

async def get_teams_by_store_id(store_id: int) -> List[Dict[str, Any]]:
    """
    Get all teams that belong to a particular user.
    
    Args:
        user_id: The ID of the user
        
    Returns:
        List[Dict[str, Any]]: List of teams belonging to the user
        
    Raises:
        Exception: If there's an error fetching the teams
    """
    try:
        print(f"Fetching teams for store_id: {store_id}")
        
        # Get teams where user has access
        result = supabase.table(StoreWorkersTeamsTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .order('created_at', desc=True)\
            .execute()
            
        if not result.data:
            print(f"No teams found for store_id: {store_id}")
            return []
            
        return result.data
        
    except Exception as e:
        print(f"Error fetching teams for store_id: {str(e)}")
        print(f"Error details: {traceback.format_exc()}")
        raise Exception(f"Failed to fetch teams: {str(e)}")

async def get_team_details(team_id: str) -> Dict[str, Any]:
    """
    Get detailed information about a team including agents and configurations.
    
    Args:
        team_id: The ID of the team
        
    Returns:
        Dict[str, Any]: Team details including agents and configurations
        
    Raises:
        Exception: If there's an error fetching the team details
    """
    try:
        print(f"Fetching details for team_id: {team_id}")
        
        # Get team basic info
        team_result = supabase.table(StoreWorkersTeamsTable)\
            .select('*')\
            .eq('team_id', team_id)\
            .execute()
            
        if not team_result.data:
            print(f"No team found for team_id: {team_id}")
            return None
            
        team = team_result.data[0]
        
        # Get team's agents
        agents_result = supabase.table(StoreWorkersAgentsTable)\
            .select('*')\
            .contains('team_id', [team_id])\
            .execute()
            
        # Map agent data to match Agent model
        agents = []
        if agents_result.data:
            for agent in agents_result.data:
                # Convert tools to list if it's None or not a list
                tools = agent.get('tools', [])
                if tools is None:
                    tools = []
                elif not isinstance(tools, list):
                    tools = [tools]
                
                mapped_agent = {
                    'Name': agent.get('name', ''),
                    'Role': agent.get('role', ''),
                    'Goal': agent.get('goal', ''),
                    'Tools': tools,
                    'uuid': agent.get('uuid'),
                    'backstory': agent.get('backstory')
                }
                agents.append(mapped_agent)
        
        # Get team's task configuration
        config_result = supabase.table(StoreWorkersTaskConfigurationsTable)\
            .select('*')\
            .eq('team_id', team_id)\
            .execute()
            
        config = config_result.data[0] if config_result.data else None
        
        # Prepare response
        response = {
            'team_id': team['team_id'],
            'store_id': team['store_id'],
            'name': team['name'],
            'description': team['description'],
            'status': team['status'],
            'current_running_status': team.get('current_running_status'),
            'created_at': team['created_at'],
            'agents': agents,
            'current_configuration': config['current_configuration'] if config else [],
            'current_key_values': config['current_key_values'] if config else {},
            'store_data_type': config['store_data_type'] if config else None,
            'store_data': config['store_data'] if config else None,
            'include_metadata': config['include_metadata'] if config else None
        }
        
        return response
        
    except Exception as e:
        print(f"Error fetching team details: {str(e)}")
        print(f"Error details: {traceback.format_exc()}")
        raise Exception(f"Failed to fetch team details: {str(e)}")

async def get_all_workers_for_store(store_id: int) -> List[Dict[str, Any]]:
    """
    Get all workers (agents) for a store, including public workers from other stores.
    
    Args:
        store_id: The ID of the store
        
    Returns:
        List[Dict[str, Any]]: List of workers/agents
        
    Raises:
        Exception: If there's an error fetching the workers
    """
    try:
        print(f"Fetching workers for store_id: {store_id}")
        
        # Get store's workers
        store_workers = supabase.table(StoreWorkersAgentsTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .order('created_at', desc=True)\
            .execute()
            
        # Get public workers from other stores
        public_workers = supabase.table(StoreWorkersAgentsTable)\
            .select('*')\
            .eq('visible', 'public')\
            .neq('store_id', store_id)\
            .order('created_at', desc=True)\
            .execute()
            
        # Combine results
        all_workers = []
        if store_workers.data:
            all_workers.extend(store_workers.data)
        if public_workers.data:
            all_workers.extend(public_workers.data)
            
        return all_workers
        
    except Exception as e:
        print(f"Error fetching workers for store_id: {str(e)}")
        print(f"Error details: {traceback.format_exc()}")
        raise Exception(f"Failed to fetch workers: {str(e)}")

async def update_team_summary(team_id: str, summary: List[dict]) -> Dict[str, Any]:
    """
    Update the summary field for a team in the database.
    
    Args:
        team_id: The ID of the team
        summary: List of summary items to store
        
    Returns:
        Dict[str, Any]: The updated team data
        
    Raises:
        Exception: If there's an error updating the team
    """
    try:
        print(f"Updating summary for team_id: {team_id}")
        
        # Update the team's summary
        result = supabase.table(StoreWorkersTeamsTable)\
            .update({'summary': summary})\
            .eq('team_id', team_id)\
            .execute()
            
        if not result.data:
            print(f"No team found for team_id: {team_id}")
            return None
            
        return result.data[0]
        
    except Exception as e:
        print(f"Error updating team summary: {str(e)}")
        print(f"Error details: {traceback.format_exc()}")
        raise Exception(f"Failed to update team summary: {str(e)}")

async def update_team_configuration_id(team_id: str, configuration_id: int) -> Dict[str, Any]:
    """
    Update the configuration_id for a team in the sw_teams table.
    
    Args:
        team_id (str): The team ID to update
        configuration_id (int): The configuration ID to set
        
    Returns:
        Dict[str, Any]: The updated team data
    """
    try:
        # Update the team's configuration_id
        result = supabase.table(StoreWorkersTeamsTable)\
            .update({'configuration_id': configuration_id})\
            .eq('team_id', team_id)\
            .execute()
            
        if not result.data:
            error(f"Failed to update configuration_id for team_id: {team_id}")
            return None
            
        return result.data[0]
        
    except Exception as e:
        error(f"Error updating team configuration_id: {str(e)}", exc_info=True)
        return None


# fetch all the tasks for a store
async def get_all_agents_and_tasks_for_store(store_id: int) -> List[Dict[str, Any]]:
    """
    Get all agents and tasks for a store.
     
    input:
        store_id: int
    output:
        List[Dict[str, Any]]: List of agents and tasks
    """

    try:
        agents = []
        tasks = []
        # Get all agents for the store
        store_agents_result = supabase.table(StoreWorkersAgentsTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .execute()
        
        if store_agents_result.data:
            for agent in store_agents_result.data:
                agents.append(agent)
        
        # include all public agents from other stores
        public_agents_result = supabase.table(StoreWorkersAgentsTable)\
            .select('*')\
            .eq('visible', 'public')\
            .execute()
        if public_agents_result.data:
            for agent in public_agents_result.data:
                agents.append(agent)

        # Get all tasks for the store
        store_tasks_result = supabase.table(StoreWorkersTasksTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .execute()
        if store_tasks_result.data:
            for task in store_tasks_result.data:
                tasks.append(task)

        # include all public tasks from other stores
        public_tasks_result = supabase.table(StoreWorkersTasksTable)\
            .select('*')\
            .eq('visible', 'public')\
            .execute()
        if public_tasks_result.data:
            for task in public_tasks_result.data:
                tasks.append(task)
                
        return agents, tasks
    except Exception as e:
        print(f"Error fetching agents and tasks: {str(e)}")
        return [], []
    
# Dashboard Metrics Utilities
async def get_dashboard_total_workers(store_id: int) -> int:
    """
    Count all workers for a store, including public workers from other stores.
    """
    try:
        store_workers = supabase.table(StoreWorkersAgentsTable).select('id').eq('store_id', store_id).execute()
        public_workers = supabase.table(StoreWorkersAgentsTable).select('id').eq('visible', 'public').neq('store_id', store_id).execute()
        count = 0
        if store_workers.data:
            count += len(store_workers.data)
        if public_workers.data:
            count += len(public_workers.data)
        return count
    except Exception as e:
        print(f"Error in get_dashboard_total_workers: {str(e)}")
        raise

async def get_dashboard_total_teams(store_id: int) -> int:
    """
    Count all teams for a store, including public teams from other stores.
    """
    try:
        store_teams = supabase.table(StoreWorkersTeamsTable).select('id').eq('store_id', store_id).execute()
        public_teams = supabase.table(StoreWorkersTeamsTable).select('id').eq('visible', 'public').neq('store_id', store_id).execute()
        count = 0
        if store_teams.data:
            count += len(store_teams.data)
        if public_teams.data:
            count += len(public_teams.data)
        return count
    except Exception as e:
        print(f"Error in get_dashboard_total_teams: {str(e)}")
        raise

async def get_dashboard_current_running_teams(store_id: int) -> dict:
    """
    List all teams for a store where current_running_status = 'processing',
    returning their name and status, and the count.
    (Do not include public teams from other stores.)
    """
    try:
        running_teams = supabase.table(StoreWorkersTeamsTable)\
            .select('name,status')\
            .eq('store_id', store_id)\
            .eq('current_running_status', 'processing')\
            .execute()
        workflows = []
        if running_teams.data:
            workflows = [
                { 'name': t['name'], 'status': t['status'] }
                for t in running_teams.data
            ]
        return {
            'count': len(workflows),
            'workflows': workflows
        }
    except Exception as e:
        print(f"Error in get_dashboard_current_running_teams: {str(e)}")
        raise

async def get_dashboard_recent_teams(store_id: int, limit: int = 5) -> list:
    """
    Get the most recent teams for a store (or public), sorted by created_at, return name and status.
    """
    try:
        store_teams = supabase.table(StoreWorkersTeamsTable).select('name,status,created_at').eq('store_id', store_id).order('created_at', desc=True).limit(limit).execute()
        public_teams = supabase.table(StoreWorkersTeamsTable).select('name,status,created_at').eq('visible', 'public').neq('store_id', store_id).order('created_at', desc=True).limit(limit).execute()
        all_teams = []
        if store_teams.data:
            all_teams.extend(store_teams.data)
        if public_teams.data:
            all_teams.extend(public_teams.data)
        # Sort by created_at descending and limit
        all_teams_sorted = sorted(all_teams, key=lambda x: x.get('created_at', ''), reverse=True)
        return [{ 'name': t['name'], 'status': t['status'] } for t in all_teams_sorted[:limit]]
    except Exception as e:
        print(f"Error in get_dashboard_recent_teams: {str(e)}")
        raise

async def get_dashboard_workflow_status_rate(store_id: int) -> dict:
    """
    Calculate the success and failure percentage for workflows (teams) for a store (and public teams).
    Only consider status = 'completed' or 'failed'.
    """
    try:
        store_teams = supabase.table(StoreWorkersTeamsTable).select('status').eq('store_id', store_id).in_('status', ['completed', 'failed']).execute()
        public_teams = supabase.table(StoreWorkersTeamsTable).select('status').eq('visible', 'public').neq('store_id', store_id).in_('status', ['completed', 'failed']).execute()
        statuses = []
        if store_teams.data:
            statuses.extend([t['status'] for t in store_teams.data])
        if public_teams.data:
            statuses.extend([t['status'] for t in public_teams.data])
        total = len(statuses)
        if total == 0:
            return { 'success_percentage': 0, 'failure_percentage': 0 }
        success = statuses.count('completed')
        failure = statuses.count('failed')
        return {
            'success_percentage': round((success / total) * 100, 2),
            'failure_percentage': round((failure / total) * 100, 2)
        }
    except Exception as e:
        print(f"Error in get_dashboard_workflow_status_rate: {str(e)}")
        raise

async def get_agents_tasks_crew_for_team(team_id: str) -> dict:
    # Fetch agents
    agents_resp = supabase.table(StoreWorkersAgentsTable).select(
        "name, role, goal, backstory, tools, total_attributes, team_id"
    ).contains("team_id", [team_id]).execute()
    if not agents_resp.data:
        raise Exception(f"No agents found for team_id: {team_id}")
    agents = [
        {
            "name": a["name"],
            "role": a["role"],
            "goal": a["goal"],
            "backstory": a["backstory"],
            "tools": a["tools"],
            "total_attributes": a.get("total_attributes")
        }
        for a in agents_resp.data
    ]

    # Fetch tasks
    tasks_resp = supabase.table(StoreWorkersTasksTable).select(
        "name, description, expected_output, agents, other_attributes, team_id"
    ).contains("team_id", [team_id]).execute()
    if not tasks_resp.data:
        raise Exception(f"No tasks found for team_id: {team_id}")
    tasks = [
        {
            "name": t["name"],
            "description": t["description"],
            "expected_output": t["expected_output"],
            "agents": t["agents"],
            "other_attributes": t.get("other_attributes")
        }
        for t in tasks_resp.data
    ]

    # Fetch crew (should be only one per team)
    crew_resp = supabase.table(StoreWorkersCrewsTable).select(
        "name, agents, tasks, other_attributes, team_id"
    ).contains("team_id", [team_id]).execute()
    if not crew_resp.data or len(crew_resp.data) == 0:
        raise Exception(f"No crew found for team_id: {team_id}")
    crew = {
        "name": crew_resp.data[0]["name"],
        "agents": crew_resp.data[0]["agents"],
        "tasks": crew_resp.data[0]["tasks"],
        "other_attributes": crew_resp.data[0].get("other_attributes")
    }

    return {"agents": agents, "tasks": tasks, "crew": crew}

async def get_task_configuration_keys_for_team(team_id: str) -> list:
    resp = supabase.table(StoreWorkersTaskConfigurationsTable).select("current_key_values").eq("team_id", team_id).execute()
    if not resp.data or len(resp.data) == 0:
        raise Exception(f"No task configuration found for team_id: {team_id}")
    current_key_values = resp.data[0]["current_key_values"]
    if not isinstance(current_key_values, dict):
        raise Exception(f"current_key_values is not a dict for team_id: {team_id}")
    return list(current_key_values.keys())

# Azure Functions App Assignment
async def assign_azure_function_app_to_store(store_id: int) -> Optional[Dict[str, str]]:
    """
    Assign the first available (unassigned) Azure Function App to the given store_id.
    If the store already has an assigned app, return that one.
    Returns the assigned app's name and url, or None if no app is available.
    """
    try:
        # First, check if this store already has an assigned Azure Function App
        existing = supabase.table(StoreWorkersAzureFunctionsAppDetailsTable).select("id, name, url").eq("store_id", store_id).limit(1).execute()
        if existing.data:
            app = existing.data[0]
            return {"name": app["name"], "url": app["url"]}
        # If not, find the first unassigned Azure Function App (store_id is null)
        result = supabase.table(StoreWorkersAzureFunctionsAppDetailsTable).select("id, name, url").is_("store_id", None).limit(1).execute()
        if not result.data:
            return None
        app = result.data[0]
        # Assign this app to the store
        update_result = supabase.table(StoreWorkersAzureFunctionsAppDetailsTable).update({"store_id": store_id}).eq("id", app["id"]).execute()
        if update_result.data:
            return {"name": app["name"], "url": app["url"]}
        else:
            return None
    except Exception as e:
        print(f"Error assigning Azure Function App: {str(e)}")
        return None

# Helper to get function_app_name for a store_id
async def get_function_app_name_for_store(store_id: int) -> str:
    """
    Get the Azure Function App name for a given store_id from sw_azure_functions_app_details.
    Returns the 'name' field or None if not found.
    """
    try:
        result = supabase.table(StoreWorkersAzureFunctionsAppDetailsTable).select('name').eq('store_id', store_id).limit(1).execute()
        if result.data and len(result.data) > 0:
            return result.data[0]['name']
        return None
    except Exception as e:
        print(f"Error fetching function app name for store_id {store_id}: {str(e)}")
        return None

# Helper to get store_name for a store_id
async def get_store_name_for_id(store_id: int) -> str:
    """
    Get the store name for a given store_id from stores table.
    Returns the 'name' field or None if not found.
    """
    try:
        result = supabase.table(StoresTable).select('name').eq('id', store_id).limit(1).execute()
        if result.data and len(result.data) > 0:
            return result.data[0]['name']
        return None
    except Exception as e:
        print(f"Error fetching store name for store_id {store_id}: {str(e)}")
        return None

# Helper to insert a new deploy status row for a team
def insert_team_deploy_status(team_id: str, store_id: int, deploy_status: str = None, errors: str = None):
    """
    Insert a new row into sw_team_deploy_status for a team, or update if exists (with deployment_history).
    """
    from datetime import datetime, timezone
    try:
        # Check if a row for this team_id exists
        existing = supabase.table(TeamDeployStatusTable).select('*').eq('team_id', team_id).limit(1).execute()
        now = datetime.now(timezone.utc).isoformat()
        if existing.data and len(existing.data) > 0:
            row = existing.data[0]
            # Prepare history entry
            history_entry = {
                'deploy_status': row.get('deploy_status'),
                'errors': row.get('errors'),
                'created_at': row.get('created_at'),
                'updated_at': row.get('updated_at'),
                'last_deployed_on': row.get('last_deployed_on')
            }
            deployment_history = row.get('deployment_history') or []
            if not isinstance(deployment_history, list):
                try:
                    deployment_history = json.loads(deployment_history)
                except Exception:
                    deployment_history = []
            deployment_history.append(history_entry)
            update_data = {
                'deploy_status': deploy_status,
                'errors': errors,
                'updated_at': now,
                'last_deployed_on': now,
                'deployment_history': deployment_history,
                'store_id': store_id
            }
            result = supabase.table(TeamDeployStatusTable).update(update_data).eq('team_id', team_id).execute()
            return result.data[0] if result.data else None
        else:
            data = {
                'team_id': team_id,
                'store_id': store_id,
                'deploy_status': deploy_status,
                'errors': errors,
                'created_at': now,
                'updated_at': now,
                'last_deployed_on': now,
                'deployment_history': [],
            }
            result = supabase.table(TeamDeployStatusTable).insert(data).execute()
            return result.data[0] if result.data else None
    except Exception as e:
        print(f"Error inserting team deploy status: {str(e)}")
        return None

def update_team_deploy_status(team_id: str, store_id: int, deploy_status: str = None, errors: str = None):
    """
    Update the deploy status row for a team in sw_team_deploy_status, with deployment_history.
    """
    from datetime import datetime, timezone
    try:
        # Fetch current row
        existing = supabase.table(TeamDeployStatusTable).select('*').eq('team_id', team_id).limit(1).execute()

        now = datetime.now(timezone.utc).isoformat()
        if existing.data and len(existing.data) > 0:
            row = existing.data[0]
            # Prepare history entry
            history_entry = {
                'deploy_status': row.get('deploy_status'),
                'errors': row.get('errors'),
                'created_at': row.get('created_at'),
                'updated_at': row.get('updated_at'),
                'last_deployed_on': row.get('last_deployed_on')
            }
            deployment_history = row.get('deployment_history') or []
            if not isinstance(deployment_history, list):
                try:
                    deployment_history = json.loads(deployment_history)
                except Exception:
                    deployment_history = []
            deployment_history.append(history_entry)
            update_data = {
                'deploy_status': deploy_status,
                'errors': errors,
                'updated_at': now,
                'last_deployed_on': now,
                'deployment_history': deployment_history,
                'store_id': store_id
            }
            result = supabase.table(TeamDeployStatusTable).update(update_data).eq('team_id', team_id).execute()
            return result.data[0] if result.data else None
        else:
            # If no row exists, insert a new one
            data = {
                'team_id': team_id,
                'store_id': store_id,
                'deploy_status': deploy_status,
                'errors': errors,
                'created_at': now,
                'updated_at': now,
                'last_deployed_on': now,
                'deployment_history': [],
            }
            result = supabase.table(TeamDeployStatusTable).insert(data).execute()
            return result.data[0] if result.data else None
    except Exception as e:
        print(f"Error updating team deploy status: {str(e)}")
        return None

# Helper to update team table status
def update_team_table_status(team_id: str, status: str):
    """
    Update the status of a team in the sw_teams table.
    """
    try:
        supabase.table(StoreWorkersTeamsTable).update({'status': status}).eq('team_id', team_id).execute()
    except Exception as e:
        print(f"Error updating team table status: {str(e)}")
        return None
    
# Helper to update team name
def update_team_name(team_id: str, name: str):
    """
    Update the name of a team in the sw_teams table.
    """
    try:
        supabase.table(StoreWorkersTeamsTable).update({'name': name}).eq('team_id', team_id).execute()
    except Exception as e:
        print(f"Error updating team name: {str(e)}")
        return None

# Helper to get team deploy status by team_id
async def get_team_deploy_status_by_team_id(team_id: str) -> dict:
    """
    Fetch the row from sw_team_deploy_status for the given team_id.
    """
    try:
        result = supabase.table(TeamDeployStatusTable).select('*').eq('team_id', team_id).limit(1).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        print(f"Error fetching team deploy status for team_id {team_id}: {str(e)}")
        return None

# Helper to get Azure Function App details by store_id
async def get_azure_function_app_details_by_store_id(store_id: int) -> dict:
    """
    Fetch the row from sw_azure_functions_app_details for the given store_id.
    """
    try:
        result = supabase.table(StoreWorkersAzureFunctionsAppDetailsTable).select('*').eq('store_id', store_id).limit(1).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        print(f"Error fetching Azure Function App details for store_id {store_id}: {str(e)}")
        return None

def update_team_deploy_function_details(team_id: str, store_id: int, function_name: str = None, url: str = None, endpoint: str = None):
    """
    Update the function_name, url, and endpoint fields in the team_deploy_status table for the given team.
    If any value is missing, set it as None (null in DB).
    """
    from datetime import datetime, timezone
    try:
        now = datetime.now(timezone.utc).isoformat()
        update_data = {
            'function_name': function_name,
            'url': url,
            'endpoint': endpoint,
            'updated_at': now
        }
        result = supabase.table(TeamDeployStatusTable).update(update_data).eq('team_id', team_id).eq('store_id', store_id).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        print(f"Error updating team deploy function details: {str(e)}")
        return None

# COLLECTIONS HELPER FUNCTIONS
async def get_all_collections_for_store(store_id: int) -> list:
    """
    Fetch all collections belonging to a store.
    
    Args:
        store_id (int): ID of the store
        
    Returns:
        list: List of collections belonging to the store
        
    Raises:
        Exception: If there's an error fetching collection data
    """
    try:
        result = supabase.table(SyncedCollectionsTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .execute()
        return result.data if result.data else []
    except Exception as e:
        raise Exception(f"Error fetching store collections: {str(e)}")

# BLOGS HELPER FUNCTIONS
async def get_all_blogs_for_store(store_id: int) -> list:
    """
    Fetch all blogs belonging to a store.
    
    Args:
        store_id (int): ID of the store
        
    Returns:
        list: List of blogs belonging to the store
        
    Raises:
        Exception: If there's an error fetching blog data
    """
    try:
        result = supabase.table(SyncedBlogsTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .execute()
        return result.data if result.data else []
    except Exception as e:
        raise Exception(f"Error fetching store blogs: {str(e)}")

# ARTICLES HELPER FUNCTIONS
async def get_all_articles_for_store(store_id: int) -> list:
    """
    Fetch all articles belonging to a store.
    
    Args:
        store_id (int): ID of the store
        
    Returns:
        list: List of articles belonging to the store
        
    Raises:
        Exception: If there's an error fetching article data
    """
    try:
        result = supabase.table(SyncedArticlesTable)\
            .select('*')\
            .eq('store_id', store_id)\
            .execute()
        return result.data if result.data else []
    except Exception as e:
        raise Exception(f"Error fetching store articles: {str(e)}")

# UPDATE HELPER FUNCTIONS

async def update_product_by_id_and_store_id(store_id: int, product_id: str, update_data: Dict[str, Any]) -> bool:
    """
    Update a product in the products table by its ID and store ID.
    
    Args:
        store_id (int): ID of the store
        product_id (str): Shopify GID of the product
        update_data (Dict[str, Any]): Dictionary containing the fields to update.
            Note: Vector embeddings (description_embedding, handle_embedding, title_embedding)
            should be updated separately using update_product_embeddings.
        
    Returns:
        bool: True if update was successful, False otherwise
        
    Raises:
        Exception: If there's an error updating the product
    """
    try:
        # Remove vector embeddings from update_data if present
        embeddings = {}
        for field in ['description_embedding', 'handle_embedding', 'title_embedding']:
            if field in update_data:
                embeddings[field] = update_data.pop(field)
        
        # First verify the product exists and belongs to the store
        existing_product = supabase.table(ProductsTable)\
            .select("*")\
            .eq('shopify_gid', product_id)\
            .eq('store_id', store_id)\
            .execute()
            
        if not existing_product.data:
            return False
            
        # Add updated_at timestamp
        update_data['updated_at'] = datetime.now(timezone.utc).isoformat()
        
        # Update the product
        result = supabase.table(ProductsTable)\
            .update(update_data)\
            .eq('shopify_gid', product_id)\
            .eq('store_id', store_id)\
            .execute()
            
        # If update was successful and we have embeddings, update them
        if result.data and embeddings:
            await update_product_embeddings(product_id, embeddings)
            
        return bool(result.data)
        
    except Exception as e:
        print(f"Error updating product {product_id} for store {store_id}: {str(e)}")
        raise Exception(f"Error updating product: {str(e)}")

async def update_collection_by_id_and_store_id(store_id: int, collection_id: str, update_data: Dict[str, Any]) -> bool:
    """
    Update a collection in the synced_collections table by its ID and store ID.
    
    Args:
        store_id (int): ID of the store
        collection_id (str): Shopify GID of the collection
        update_data (Dict[str, Any]): Dictionary containing the fields to update.
            Must include 'products_data' as a json array.
        
    Returns:
        bool: True if update was successful, False otherwise
        
    Raises:
        Exception: If there's an error updating the collection
    """
    try:
        # Validate required fields
        if 'products_data' in update_data and not isinstance(update_data['products_data'], list):
            raise ValueError("products_data must be a list/array")
            
        # First verify the collection exists and belongs to the store
        existing_collection = supabase.table(SyncedCollectionsTable)\
            .select("*")\
            .eq('shopify_gid', collection_id)\
            .eq('store_id', store_id)\
            .execute()
            
        if not existing_collection.data:
            return False
            
        # Add updated_at timestamp
        update_data['updated_at'] = datetime.now(timezone.utc).isoformat()
        
        # Update the collection
        result = supabase.table(SyncedCollectionsTable)\
            .update(update_data)\
            .eq('shopify_gid', collection_id)\
            .eq('store_id', store_id)\
            .execute()
            
        return bool(result.data)
        
    except ValueError as e:
        print(f"Validation error updating collection {collection_id}: {str(e)}")
        raise
    except Exception as e:
        print(f"Error updating collection {collection_id} for store {store_id}: {str(e)}")
        raise Exception(f"Error updating collection: {str(e)}")

async def update_blog_by_id_and_store_id(store_id: int, blog_id: str, update_data: Dict[str, Any]) -> bool:
    """
    Update a blog in the synced_blogs table by its ID and store ID.
    
    Args:
        store_id (int): ID of the store
        blog_id (str): Shopify GID of the blog
        update_data (Dict[str, Any]): Dictionary containing the fields to update.
            Note: blog_created_at and blog_updated_at are handled separately from updated_at.
        
    Returns:
        bool: True if update was successful, False otherwise
        
    Raises:
        Exception: If there's an error updating the blog
    """
    try:
        # Handle blog-specific timestamps
        blog_timestamps = {}
        for field in ['blog_created_at', 'blog_updated_at']:
            if field in update_data:
                blog_timestamps[field] = update_data.pop(field)
        
        # First verify the blog exists and belongs to the store
        existing_blog = supabase.table(SyncedBlogsTable)\
            .select("*")\
            .eq('shopify_gid', blog_id)\
            .eq('store_id', store_id)\
            .execute()
            
        if not existing_blog.data:
            return False
            
        # Add updated_at timestamp
        update_data['updated_at'] = datetime.now(timezone.utc).isoformat()
        
        # If blog_updated_at is being updated, set it to current time
        if 'blog_updated_at' in blog_timestamps:
            blog_timestamps['blog_updated_at'] = datetime.now(timezone.utc).isoformat()
        
        # Merge blog timestamps back into update data
        update_data.update(blog_timestamps)
        
        # Update the blog
        result = supabase.table(SyncedBlogsTable)\
            .update(update_data)\
            .eq('shopify_gid', blog_id)\
            .eq('store_id', store_id)\
            .execute()
            
        return bool(result.data)
        
    except Exception as e:
        print(f"Error updating blog {blog_id} for store {store_id}: {str(e)}")
        raise Exception(f"Error updating blog: {str(e)}")

async def update_article_by_id_and_store_id(store_id: int, article_id: str, update_data: Dict[str, Any]) -> bool:
    """
    Update an article in the synced_articles table by its ID and store ID.
    
    Args:
        store_id (int): ID of the store
        article_id (str): Shopify GID of the article
        update_data (Dict[str, Any]): Dictionary containing the fields to update.
            Note: blog_id is required and must reference an existing blog.
            article_created_at and article_published_at are handled separately from updated_at.
        
    Returns:
        bool: True if update was successful, False otherwise
        
    Raises:
        Exception: If there's an error updating the article
    """
    try:
        # First verify the article exists and belongs to the store
        existing_article = supabase.table(SyncedArticlesTable)\
            .select("*")\
            .eq('shopify_gid', article_id)\
            .eq('store_id', store_id)\
            .execute()
            
        if not existing_article.data:
            return False
            
        # Add updated_at timestamp
        update_data['updated_at'] = datetime.now(timezone.utc).isoformat()
        
        # Update the article
        result = supabase.table(SyncedArticlesTable)\
            .update(update_data)\
            .eq('shopify_gid', article_id)\
            .eq('store_id', store_id)\
            .execute()
            
        return bool(result.data)
        
    except Exception as e:
        print(f"Error updating article {article_id} for store {store_id}: {str(e)}")
        raise Exception(f"Error updating article: {str(e)}")

    

# ----------------- TEAM RUN JOBS -----------------

async def insert_team_run_job(
    job_id: str,
    team_id: str,
    run_by_store_id: int,
    input_request: dict,
    output: dict = None,
    job_status: str = 'processing',
    error_messages: str = None,
    job_duration: str = None,
    job_title: str = None
) -> dict:
    """
    Insert a new team run job into sw_team_run_jobs table.
    """
    try:
        data = {
            'job_id': job_id,
            'team_id': team_id,
            'run_by_store_id': run_by_store_id,
            'job_title': job_title,
            'input_request': input_request,
            'output': output,
            'job_status': job_status,
            'error_messages': error_messages,
            'job_duration': job_duration,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        result = supabase.table(StoreWorkersTeamRunJobsTable).insert(data).execute()
        if not result.data:
            raise Exception(f"Failed to insert team run job for job_id: {job_id}")
        return result.data[0]
    except Exception as e:
        print(f"Error inserting team run job: {str(e)}")
        raise

async def update_team_running_status_and_history(
    team_id: str,
    new_status: str,
    job_id: str,
    result_status: str,
    run_by_store_id: int
) -> dict:
    """
    Update sw_teams current_running_status and append to running_status_history.
    """
    try:
        # Fetch current running_status_history
        team_result = supabase.table(StoreWorkersTeamsTable).select('running_status_history').eq('team_id', team_id).execute()
        history = team_result.data[0]['running_status_history'] if team_result.data and team_result.data[0]['running_status_history'] else []
        # Append new entry
        new_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'job_id': job_id,
            'result_status': result_status,
            'run_by_store_id': run_by_store_id
        }
        history.append(new_entry)
        # Update current_running_status and running_status_history
        update_data = {
            'current_running_status': new_status,
            'running_status_history': history,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        result = supabase.table(StoreWorkersTeamsTable).update(update_data).eq('team_id', team_id).execute()
        if not result.data:
            raise Exception(f"Failed to update running status/history for team_id: {team_id}")
        return result.data[0]
    except Exception as e:
        print(f"Error updating team running status/history: {str(e)}")
        raise

async def get_latest_team_run_jobs_for_team_and_store(team_id: str, store_id: int, limit: int = 5, team_title: str = None) -> list:
    """
    Fetch the latest job runs for a team filtered by team_id and store_id, ordered by created_at descending.
    If job_title is missing, use team_title as job_title in the returned dict.
    """
    try:
        result = supabase.table(StoreWorkersTeamRunJobsTable)\
            .select('*')\
            .eq('team_id', team_id)\
            .eq('run_by_store_id', store_id)\
            .order('created_at', desc=True)\
            .limit(limit)\
            .execute()
        jobs = result.data if result.data else []
        # Patch job_title if missing
        for job in jobs:
            if not job.get('job_title') and team_title:
                job['job_title'] = team_title
        return jobs
    except Exception as e:
        print(f"Error fetching latest team run jobs: {str(e)}")
        return []

async def update_team_run_job_status(
    job_id: str,
    output: dict = None,
    job_status: str = None,
    error_messages: str = None,
    job_duration: float = None,
    job_title: str = None
) -> dict:
    """
    Update a team run job in sw_team_run_jobs by job_id.
    """
    from datetime import datetime, timezone
    try:
        update_data = {
            'output': output,
            'job_status': job_status,
            'error_messages': error_messages,
            'job_duration': job_duration,
            'job_title': job_title,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        result = supabase.table(StoreWorkersTeamRunJobsTable).update(update_data).eq('job_id', job_id).execute()
        if not result.data:
            raise Exception(f"Failed to update team run job for job_id: {job_id}")
        return result.data[0]
    except Exception as e:
        print(f"Error updating team run job: {str(e)}")
        raise