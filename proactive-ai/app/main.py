'''
Description: This is the main file for the FastAPI application.
Contains the FastAPI app initialization, the health endpoint and all endpoints imported from the routers.
'''
import os
from fastapi import FastAPI, HTTPException, Body, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from RankCollections.routes import router as collections_router
from Auth.routes import router as auth_router
from Blogging.StoreBlog.routes import router as store_blog_router
from Middleware.auth_check import require_auth, verify_tokens
from Auth.api.v1.auth import AuthManager
from StoreWorker.routes import router as store_worker_router
from StoreWorker.api.v1.callback import router as callback_router
from StoreWorker.middleware.exception_handlers import (
    validation_exception_handler,
    request_validation_exception_handler
)
# poc - trail
from Utils.Similarity_checker.embeddings import SimilaritySearcher, SeparateEmbeddingComparator, safe_similarity_search
from Models.dbHelpers import get_all_product_embedding_data_belongs_to_a_store


# Initialize the FastAPI app
app = FastAPI()

# Initialize auth manager
auth_manager = AuthManager()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], # add domains to restrict access
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add exception handlers
app.add_exception_handler(ValidationError, validation_exception_handler)
app.add_exception_handler(RequestValidationError, request_validation_exception_handler)

# Importing the routers
app.include_router(
    auth_router,
    prefix='/api/v1/auth',
    tags=['Authentication'],
)
app.include_router(
    collections_router,
    prefix='/api/v1/collections',
    tags=["Rank Collections"],
    dependencies=[require_auth(auth_manager)]
)
app.include_router(
    store_blog_router,
    prefix='/api/v1/storeblog',
    tags=["Store Blog"],
    dependencies=[require_auth(auth_manager)]
)
app.include_router(
    store_worker_router,
    prefix='/api/v1/storeworker',
    tags=["Store Worker"],
    dependencies=[require_auth(auth_manager)]
)


# callback router - store worker
app.include_router(
    callback_router,
    prefix='/api/v1/storeworker/callback',
    tags=["Store Worker Callback"]
)

# Heath Endpoint
@app.get("/")
async def health():
    return {"status": "I am alive!"}

# Sample Protected Route
@app.get("/protected-endpoint")
async def protected_endpoint(request: Request, store=Depends(verify_tokens)):
    """Example protected endpoint"""
    store = request.state.store
    return {"message": f"Hello {store.name}!"}

@app.post("/trail-endpoint")
async def trail(request: Request, store=Depends(verify_tokens)):
    "Trail endpoint - for poc"
    store = request.state.store
    print(f"STORE-NAME: {store.name}")
    #get product data
    product_embedding_data = await get_all_product_embedding_data_belongs_to_a_store(store.id)

    print(f"TYPEOF DB RESULT:::::: {type(product_embedding_data)}")

# Initialize the searcher
    searcher = SimilaritySearcher()
    comparator = SeparateEmbeddingComparator()
    keywords = ['gift', 'liquid', 'snowboard']
    results, error = safe_similarity_search(
        keywords=keywords,
        products=product_embedding_data,
        top_k=10,
        threshold=0.3,
        return_field_scores=True
    )

    if error:
        raise Exception (f"Error in text_to_embedding_search: {error}")

    print("RESULTS FROM TEXT_TO_EMBEDDING$$$$$$$$$$$", results)

    return results
