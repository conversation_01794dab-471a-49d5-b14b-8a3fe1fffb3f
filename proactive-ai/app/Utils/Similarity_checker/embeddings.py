import spacy
import numpy as np
from typing import List, Dict, Tuple, Union, Optional, Any
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
import traceback
from Logging.logger_config import get_logger

class EmbeddingError(Exception):
    """Custom exception for embedding-related errors"""
    pass

class ProductValidationError(Exception):
    """Custom exception for product data validation errors"""
    pass

class SeparateEmbeddingComparator:
    def __init__(self, model_name: str = "en_core_web_lg"):
        """Initialize the comparator with a spaCy model."""
        self.logger = get_logger(__name__)
        self.logger.info(f"Initializing SeparateEmbeddingComparator with model: {model_name}")
        
        try:
            self.nlp = spacy.load(model_name, disable=['parser', 'ner'])
            self.logger.info("Successfully loaded spaCy model")
        except OSError as e:
            self.logger.error(f"Failed to load spaCy model: {model_name}")
            self.logger.error(traceback.format_exc())
            raise EmbeddingError(f"Failed to load model {model_name}. Error: {str(e)}")

    def compute_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Compute cosine similarity between two vectors."""
        if vec1.shape != vec2.shape:
            raise ValueError(f"Vector shapes don't match: {vec1.shape} vs {vec2.shape}")
        return float(np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2)))

    def get_field_scores(self, product: Dict[str, str], keyword_embedding: np.ndarray) -> Dict[str, float]:
        """Compute similarity scores for each field of a product."""
        scores = {}
        for field in ['title', 'description', 'handle']:
            try:
                field_embedding = self.get_embedding(product[field])
                scores[field] = self.compute_similarity(keyword_embedding, field_embedding)
            except Exception as e:
                self.logger.error(f"Error computing similarity for field {field}: {str(e)}")
                scores[field] = 0.0
        return scores

    # Rest of the SeparateEmbeddingComparator methods remain the same...
    def validate_product(self, product: Dict[str, str]) -> None:
            """
            Validate product data structure.
            Raises ProductValidationError if validation fails.
            """
            required_fields = ['title', 'description', 'handle']
            for field in required_fields:
                if field not in product:
                    raise ProductValidationError(f"Missing required field: {field}")
                if not isinstance(product[field], str):
                    raise ProductValidationError(f"Field {field} must be string, got {type(product[field])}")
                if not product[field].strip():
                    raise ProductValidationError(f"Field {field} cannot be empty")

    @lru_cache(maxsize=1000)
    def get_embedding(self, text: str) -> np.ndarray:
        """Generate embedding for a single text string with caching."""
        try:
            if not isinstance(text, str):
                raise ValueError(f"Expected string input, got {type(text)}")
            
            if not text.strip():
                raise ValueError("Empty text input")
                
            doc = self.nlp(text)
            if doc.vector_norm == 0:
                raise EmbeddingError("Zero vector generated")
                
            return doc.vector / np.linalg.norm(doc.vector)
            
        except Exception as e:
            self.logger.error(f"Error generating embedding for text: {text[:100]}...")
            self.logger.error(traceback.format_exc())
            raise EmbeddingError(f"Failed to generate embedding: {str(e)}")

    def process_product_fields(self, products: List[Dict[str, str]]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Process all product fields separately in parallel."""
        self.logger.info(f"Processing {len(products)} products")
        
        try:
            # Validate all products first
            for i, product in enumerate(products):
                try:
                    self.validate_product(product)
                except ProductValidationError as e:
                    self.logger.error(f"Validation failed for product at index {i}")
                    raise

            titles = [p['title'] for p in products]
            descriptions = [p['description'] for p in products]
            handles = [p['handle'] for p in products]
            
            failed_embeddings = []
            
            def safe_get_embedding(text: str, field_type: str, index: int) -> Optional[np.ndarray]:
                try:
                    return self.get_embedding(text)
                except Exception as e:
                    failed_embeddings.append((field_type, index, str(e)))
                    return None

            with ThreadPoolExecutor() as executor:
                title_embeddings = list(executor.map(
                    lambda x: safe_get_embedding(x[1], 'title', x[0]), 
                    enumerate(titles)
                ))
                desc_embeddings = list(executor.map(
                    lambda x: safe_get_embedding(x[1], 'description', x[0]), 
                    enumerate(descriptions)
                ))
                handle_embeddings = list(executor.map(
                    lambda x: safe_get_embedding(x[1], 'handle', x[0]), 
                    enumerate(handles)
                ))

            if failed_embeddings:
                error_msg = "Failed to generate embeddings for:\n"
                for field, idx, error in failed_embeddings:
                    error_msg += f"- {field} at index {idx}: {error}\n"
                raise EmbeddingError(error_msg)

            # Remove None values and convert to numpy arrays
            title_embeddings = np.vstack([e for e in title_embeddings if e is not None])
            desc_embeddings = np.vstack([e for e in desc_embeddings if e is not None])
            handle_embeddings = np.vstack([e for e in handle_embeddings if e is not None])

            return title_embeddings, desc_embeddings, handle_embeddings

        except Exception as e:
            self.logger.error("Error in process_product_fields")
            self.logger.error(traceback.format_exc())
            raise

    def get_keyword_embedding(self, keywords: Union[str, List[str]]) -> np.ndarray:
        """Get embedding for keyword(s)."""
        self.logger.info(f"Processing keywords: {keywords if isinstance(keywords, str) else len(keywords)} keywords")
        
        try:
            if isinstance(keywords, str):
                keywords = [keywords]
                
            if not keywords:
                raise ValueError("Empty keywords list")
                
            keyword_embeddings = []
            failed_keywords = []
            
            for idx, keyword in enumerate(keywords):
                try:
                    embedding = self.get_embedding(keyword)
                    keyword_embeddings.append(embedding)
                except Exception as e:
                    failed_keywords.append((keyword, str(e)))

            if failed_keywords:
                error_msg = "Failed to generate embeddings for keywords:\n"
                for keyword, error in failed_keywords:
                    error_msg += f"- {keyword}: {error}\n"
                raise EmbeddingError(error_msg)

            keyword_embeddings = np.vstack(keyword_embeddings)
            
            if len(keywords) > 1:
                keyword_embedding = np.mean(keyword_embeddings, axis=0)
                return keyword_embedding / np.linalg.norm(keyword_embedding)
            return keyword_embeddings[0]
            
        except Exception as e:
            self.logger.error("Error in get_keyword_embedding")
            self.logger.error(traceback.format_exc())
            raise

    def find_similar_products(self,
                            keywords: Union[str, List[str]],
                            products: List[Dict[str, str]],
                            weights: Dict[str, float] = None,
                            top_k: int = None,
                            threshold: float = None,
                            return_field_scores: bool = False) -> List[Union[Tuple[Dict, float], 
                                                                          Tuple[Dict, float, Dict[str, float]]]]:
        """Find similar products using separate field comparisons."""
        self.logger.info(f"Finding similar products for {len(products)} products")
        
        try:
            if not products:
                raise ValueError("Empty products list")
                
            if weights is None:
                weights = {'title': 0.4, 'description': 0.4, 'handle': 0.2}
            
            if not isinstance(weights, dict) or not all(isinstance(v, (int, float)) for v in weights.values()):
                raise ValueError("Invalid weights format")
                
            if top_k is not None and (not isinstance(top_k, int) or top_k <= 0):
                raise ValueError("top_k must be a positive integer")
                
            if threshold is not None and (not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 1):
                raise ValueError("threshold must be between 0 and 1")

            if top_k is None:
                top_k = len(products)

            # Get embeddings
            keyword_embedding = self.get_keyword_embedding(keywords)
            title_embeddings, desc_embeddings, handle_embeddings = self.process_product_fields(products)

            # Compute similarities
            title_similarities = np.dot(title_embeddings, keyword_embedding)
            desc_similarities = np.dot(desc_embeddings, keyword_embedding)
            handle_similarities = np.dot(handle_embeddings, keyword_embedding)

            # Compute weighted average
            final_similarities = (
                weights['title'] * title_similarities +
                weights['description'] * desc_similarities +
                weights['handle'] * handle_similarities
            )

            # Get top indices
            top_indices = np.argsort(final_similarities)[-top_k:][::-1]

            results = []
            for idx in top_indices:
                similarity = final_similarities[idx]
                if threshold is None or similarity >= threshold:
                    if return_field_scores:
                        field_scores = {
                            'title': float(title_similarities[idx]),
                            'description': float(desc_similarities[idx]),
                            'handle': float(handle_similarities[idx])
                        }
                        results.append((products[idx], float(similarity), field_scores))
                    else:
                        results.append((products[idx], float(similarity)))

            self.logger.info(f"Found {len(results)} matching products")
            return results

        except Exception as e:
            self.logger.error("Error in find_similar_products")
            self.logger.error(traceback.format_exc())
            raise

    def analyze_matching_fields(self,
                              keywords: Union[str, List[str]],
                              product: Dict[str, str],
                              weights: Dict[str, float] = None) -> Dict[str, float]:
        """Analyze which fields contributed most to the match."""
        self.logger.info(f"Analyzing field matches for product: {product.get('title', 'Unknown')}")
        
        try:
            self.validate_product(product)
            
            if weights is None:
                weights = {'title': 0.4, 'description': 0.4, 'handle': 0.2}

            keyword_embedding = self.get_keyword_embedding(keywords)
            
            field_scores = {}
            for field in ['title', 'description', 'handle']:
                try:
                    field_embedding = self.get_embedding(product[field])
                    similarity = float(np.dot(keyword_embedding, field_embedding))
                    field_scores[field] = similarity
                except Exception as e:
                    self.logger.error(f"Error processing field {field}")
                    self.logger.error(traceback.format_exc())
                    field_scores[field] = 0.0

            return field_scores

        except Exception as e:
            self.logger.error("Error in analyze_matching_fields")
            self.logger.error(traceback.format_exc())
            raise


class SimilaritySearcher:
    def __init__(self, model_name: str = "en_core_web_lg"):
        self.comparator = SeparateEmbeddingComparator(model_name)
        self.logger = get_logger(__name__)

    def compute_batch_similarity(self, 
                               query_embedding: np.ndarray, 
                               product_embeddings: List[np.ndarray]) -> np.ndarray:
        """Compute similarities between a query embedding and multiple product embeddings."""
        query_embedding = query_embedding / np.linalg.norm(query_embedding)
        product_embeddings = np.vstack(product_embeddings)
        product_embeddings = product_embeddings / np.linalg.norm(product_embeddings, axis=1)[:, np.newaxis]
        return np.dot(product_embeddings, query_embedding)

    def text_to_embedding_search(
        self,
        keywords: Union[str, List[str]],
        products: List[Dict[str, str]],
        top_k: int = 10,
        threshold: float = 0.3,
        return_field_scores: bool = True
    ) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        Compare text keywords with products using embeddings.
        """
        try:
            if isinstance(keywords, str):
                keywords = [keywords]

            # Get keyword embedding
            keyword_embedding = self.comparator.get_keyword_embedding(keywords)
            
            # Process all products
            results = []
            for idx, product in enumerate(products):
                try:
                    self.comparator.validate_product(product)
                    
                    # Get product embedding (average of all fields)
                    field_scores = self.comparator.get_field_scores(product, keyword_embedding)
                    avg_similarity = sum(field_scores.values()) / len(field_scores)
                    
                    if avg_similarity >= threshold:
                        result = {
                            'product': product,
                            'similarity': avg_similarity
                        }
                        if return_field_scores:
                            result['field_scores'] = field_scores
                        results.append(result)
                        
                except Exception as e:
                    self.logger.error(f"Error processing product {idx}: {str(e)}")
                    continue
            
            # Sort and limit results
            results = sorted(results, key=lambda x: x['similarity'], reverse=True)[:top_k]
            
            return results, None
            
        except Exception as e:
            error_msg = f"Text to embedding search failed: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            return None, error_msg

def safe_similarity_search(
    keywords: Union[str, List[str]], 
    products: List[Dict[str, str]], 
    **kwargs
) -> Tuple[List, Optional[str]]:
    """
    Wrapper function for safe similarity search with error handling.
    """
    logger = get_logger(__name__)
    logger.info(f"Starting similarity search for {len(products)} products")
    
    try:
        searcher = SimilaritySearcher()
        results, error = searcher.text_to_embedding_search(keywords, products, **kwargs)
        if error:
            return [], error
        return results, None
        
    except Exception as e:
        error_msg = f"Unexpected error in similarity search: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        return [], error_msg

# Example usage
if __name__ == "__main__":
    # Sample products
    products = [
        {
            'title': 'Blue Cotton T-Shirt',
            'description': 'Comfortable cotton t-shirt in blue color',
            'handle': 'blue-cotton-tshirt'
        },
        {
            'title': 'Red Leather Jacket',
            'description': 'Stylish leather jacket in red color',
            'handle': 'red-leather-jacket'
        },
        {
            'title': 'Cotton Polo Shirt',
            'description': 'Classic polo shirt made from premium cotton',
            'handle': 'cotton-polo-shirt'
        }
    ]
    
    # Search for similar products
    results, error = safe_similarity_search(
        keywords=['cotton', 'comfortable'],
        products=products,
        top_k=2,
        threshold=0.3,
        return_field_scores=True
    )
    
    if error:
        print(f"Error occurred: {error}")
    else:
        print("\nSearch Results:")
        for result in results:
            print(f"\nProduct: {result['product']['title']}")
            print(f"Overall Similarity: {result['similarity']:.3f}")
            if 'field_scores' in result:
                print("Field Scores:")
                for field, score in result['field_scores'].items():
                    print(f"  {field}: {score:.3f}")