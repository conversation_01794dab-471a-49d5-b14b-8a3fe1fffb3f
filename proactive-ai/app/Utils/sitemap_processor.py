# sitemap_processor.py

import aiohttp
import asyncio
import xml.etree.ElementTree as ET
from typing import List, Dict, Set, Optional
import logging
from urllib.parse import urljoin
import yaml
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SitemapProcessor:
    """
    Process sitemaps from competitor websites to extract product URLs and analyze patterns.
    """
    
    def __init__(self, urls: List[str], max_concurrent: int = 3, rate_limit: float = 1.0):
        """
        Initialize the processor with competitor URLs.
        
        Args:
            urls: List of competitor website URLs
            max_concurrent: Maximum number of concurrent requests
            rate_limit: Minimum delay between requests in seconds
        """
        self.urls = [url.rstrip('/') for url in urls]  # Normalize URLs
        self.max_concurrent = max_concurrent
        self.rate_limit = rate_limit
        self.session = None
        
    async def __aenter__(self):
        """Set up async context with session creation."""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Clean up async context by closing session."""
        if self.session:
            await self.session.close()
            
    async def fetch_url(self, url: str) -> Optional[str]:
        """Fetch content from URL with rate limiting and error handling."""
        logger.info(f"Attempting to fetch URL: {url}")
        try:
            await asyncio.sleep(self.rate_limit)
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.text()
                else:
                    logger.warning(f"Failed to fetch {url}: Status {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching {url}: {str(e)}")
            return None
            
    def parse_sitemap_index(self, content: str) -> List[str]:
        """Parse sitemap index XML to extract product sitemap URLs."""
        try:
            root = ET.fromstring(content)
            ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
            
            product_sitemaps = []
            for sitemap in root.findall('.//sm:sitemap/sm:loc', ns):
                url = sitemap.text
                if url and 'product' in url.lower() and '.xml' in url.lower():
                    product_sitemaps.append(url)
                    
            return product_sitemaps
        except ET.ParseError as e:
            logger.error(f"Failed to parse sitemap XML: {str(e)}")
            return []
            
    def extract_product_urls(self, content: str) -> Set[str]:
        """Extract product URLs from sitemap content."""
        try:
            root = ET.fromstring(content)
            ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
            
            urls = set()
            for url in root.findall('.//sm:url/sm:loc', ns):
                if url.text:
                    urls.add(url.text)
            return urls
        except ET.ParseError as e:
            logger.error(f"Failed to parse product sitemap XML: {str(e)}")
            return set()
            
    async def process_single_site(self, base_url: str) -> Set[str]:
        """Process a single competitor website."""
        logger.info(f"Processing single site: {base_url}")
        sitemap_url = urljoin(base_url, '/sitemap.xml')
        
        content = await self.fetch_url(sitemap_url)
        if not content:
            logger.warning(f"No sitemap found at {sitemap_url}")
            return set()
            
        product_sitemaps = self.parse_sitemap_index(content)
        all_product_urls = set()
        
        for sitemap_url in product_sitemaps:
            content = await self.fetch_url(sitemap_url)
            if content:
                urls = self.extract_product_urls(content)
                all_product_urls.update(urls)
                
        return all_product_urls
        
    async def process_all_sites(self) -> Dict[str, Set[str]]:
        """
        Process all competitor websites concurrently with rate limiting.
        
        Returns:
            Dictionary mapping base URLs to their respective product URLs
        """
        logger.info("Processing all competitor websites.")
        results = {}
        
        async with self:  # Use context manager for session handling
            # Process sites in chunks to respect max_concurrent
            for i in range(0, len(self.urls), self.max_concurrent):
                chunk = self.urls[i:i + self.max_concurrent]
                tasks = []
                
                # Create tasks for current chunk
                for url in chunk:
                    task = asyncio.create_task(self.process_single_site(url))
                    tasks.append((url, task))
                
                # Wait for all tasks in chunk to complete
                for url, task in tasks:
                    try:
                        product_urls = await task
                        if product_urls:  # Only add non-empty results
                            results[url] = product_urls
                    except Exception as e:
                        logger.error(f"Error processing {url}: {str(e)}")
                        continue
                
                # Add rate limiting between chunks
                if i + self.max_concurrent < len(self.urls):
                    await asyncio.sleep(self.rate_limit)
        
        return results
        
    def analyze_product_patterns(self, urls: Set[str]) -> Dict:
        """Analyze patterns in product URLs to infer product types and categories."""
        patterns = {
            'categories': set(),
            'product_types': set(),
            'identifiable_patterns': set()
        }
        
        for url in urls:
            parts = url.split('/')
            for part in parts:
                if any(category in part.lower() for category in ['shirts', 'pants', 'shoes', 'accessories']):
                    patterns['categories'].add(part.lower())
                
        return {k: list(v) for k, v in patterns.items()}