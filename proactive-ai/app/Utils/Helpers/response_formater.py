# from typing import Any, Optional

# def success_response(message: str, data: Optional[Any] = None, status_code: int = 200) -> dict:
#     """Format a successful response"""
#     return {
#         "status_code": status_code,
#         "status": "success",
#         "message": message,
#         "data": data
#     }

# def failure_response(message: str, error: Optional[str] = None, status_code: int = 400) -> dict:
#     """Format a failure response"""
#     return {
#         "status_code": status_code,
#         "status": "failure", 
#         "message": message,
#         "error": error,
#         "apology": "We apologize for the inconvenience. Please try again or contact support if the issue persists."
#     }

# def error_response(message: str, error: Optional[str] = None, status_code: int = 500) -> dict:
#     """Format an error response"""
#     return {
#         "status_code": status_code,
#         "status": "error",
#         "message": message,
#         "error": error,
#         "apology": "We apologize for this unexpected error. Our team has been notified and is working to resolve it."
#     }

# def unauthorized_response(message: str = "Unauthorized access", status_code: int = 401) -> dict:
#     """Format an unauthorized response"""
#     return {
#         "status_code": status_code,
#         "status": "unauthorized",
#         "message": message,
#         "error": "Authentication required",
#         "apology": "Access denied. Please ensure you are properly authenticated and have the necessary permissions."
#     }


from typing import Any, Optional
from fastapi.responses import JSONResponse

def success_response(message: str, data: Optional[Any] = None, status_code: int = 200) -> JSONResponse:
    """Format and send a successful response"""
    return JSONResponse(
        status_code=status_code,
        content={
            "status_code": status_code,
            "status": "success",
            "message": message,
            "data": data,
        }
    )

def failure_response(message: str, error: Optional[str] = None, status_code: int = 400) -> JSONResponse:
    """Format and send a failure response"""
    return JSONResponse(
        status_code=status_code,
        content={
            "status_code": status_code,
            "status": "failure",
            "message": message,
            "error": error,
            "apology": "We apologize for the inconvenience. Please try again or contact support if the issue persists.",
        }
    )

def error_response(message: str, error: Optional[str] = None, status_code: int = 500) -> JSONResponse:
    """Format and send an error response"""
    return JSONResponse(
        status_code=status_code,
        content={
            "status_code": status_code,
            "status": "error",
            "message": message,
            "error": error,
            "apology": "We apologize for this unexpected error. Our team has been notified and is working to resolve it.",
        }
    )

def unauthorized_response(message: str = "Unauthorized access", status_code: int = 401) -> JSONResponse:
    """Format and send an unauthorized response"""
    return JSONResponse(
        status_code=status_code,
        content={
            "status_code": status_code,
            "status": "unauthorized",
            "message": message,
            "error": "Authentication required",
            "apology": "Access denied. Please ensure you are properly authenticated and have the necessary permissions.",
        }
    )
