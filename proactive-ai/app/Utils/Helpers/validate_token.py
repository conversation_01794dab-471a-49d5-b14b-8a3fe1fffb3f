from Logging.logger_config import get_logger
from Utils.Helpers.response_formater import unauthorized_response
import uuid

logger = get_logger(__name__)

async def validate_auth_data(auth_data: dict, request=None) -> tuple:
    # Extract store and app details from auth_data
    store = auth_data.get("store")
    app = auth_data.get("app")
    
    # Get IDs safely using getattr with None as default
    store_id = getattr(store, 'id', None)
    app_id = getattr(app, 'id', None)

    # Add request Id to it
    request_id = str(uuid.uuid4())

    # Validate that we have valid authentication
    if not store_id or not app_id:
        logger.warning(
            "Authentication failed",
            extra={
                "origin": request.client.host,
                "error": "No valid store or app authentication"
            }
        )
        return unauthorized_response()
    
    return store_id, app_id, request_id