"""
Utils/embedding_creator.py
Module for creating embeddings from text using spaCy with singleton pattern for model management.
"""
import os
import spacy
import logging
from typing import List, Union, Optional
import numpy as np
from pathlib import Path
try:
    from app.Logging.logger_config import get_logger
except ImportError:
    try:
        from Logging.logger_config import get_logger
    except ImportError as e:
        raise ImportError("Failed to import logger configuration from both paths.") from e
from threading import Lock

logger = get_logger(__name__)

class EmbeddingError(Exception):
    """Custom exception for embedding-related errors."""
    pass

class ModelLoadError(Exception):
    """Custom exception for model loading errors."""
    pass

class SpacyModelSingleton:
    _instance = None
    _lock = Lock()
    _model = None

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._model = None
            return cls._instance

    def get_model(self, model_name: str = 'en_core_web_lg') -> spacy.language.Language:
        if self._model is None:
            with self._lock:
                if self._model is None:
                    try:
                        logger.info(f"Loading spaCy model: {model_name}")
                        try:
                            self._model = spacy.load(model_name)
                        except OSError:
                            custom_path = Path('/Users/<USER>/Documents/projects/GeneratorAiModule/GeneratorAI/.venv/lib/python3.12/site-packages/en_core_web_lg/en_core_web_lg-3.4.0')
                            if custom_path.exists():
                                self._model = spacy.load(str(custom_path))
                            else:
                                raise ModelLoadError(f"Failed to load model '{model_name}' from both default and custom paths")
                        
                        if not self._model or not hasattr(self._model, 'vocab'):
                            raise ModelLoadError("Model not properly initialized")
                        
                        if not self._model.has_pipe('tok2vec') or self._model.vocab.vectors.shape[1] == 0:
                            raise ModelLoadError(f"Model '{model_name}' does not support word vectors or has no vectors")
                                
                        logger.info(f"Successfully loaded model with vector size: {self._model.vocab.vectors.shape[1]}")
                    except Exception as e:
                        logger.error(f"Model initialization error: {str(e)}")
                        raise ModelLoadError(f"Unexpected error loading model: {e}")
        
        return self._model
def generate_embeddings_using_spacy(
    texts: Union[str, List[str]],
    fallback_strategy: str = 'zeros'  # Options: 'zeros', 'mean', 'skip'
) -> np.ndarray:
    """
    Generate embeddings for input texts using spaCy with singleton model instance.
    
    Args:
        texts: Single string or list of strings to generate embeddings for
        fallback_strategy: Strategy for handling texts with no vectors
    
    Returns:
        numpy.ndarray: Array of embeddings
    
    Raises:
        EmbeddingError: If embedding generation fails
        ValueError: If input validation fails
    """
    if not texts:
        raise ValueError("Input texts cannot be empty")
    
    if isinstance(texts, str):
        texts = [texts]
    
    try:
        nlp = SpacyModelSingleton().get_model()
        vector_size = nlp.vocab.vectors.shape[1]
        
        embeddings = []
        skipped_indices = []
        
        for idx, text in enumerate(texts):
            try:
                if not isinstance(text, str):
                    raise ValueError(f"Input at index {idx} is not a string: {type(text)}")
                
                doc = nlp(text)
                vectors = [token.vector for token in doc if token.has_vector and not token.is_punct and not token.is_space]
                
                if vectors:
                    embedding = np.mean(vectors, axis=0)
                else:
                    if fallback_strategy == 'zeros':
                        logger.warning(f"No vectors found for text at index {idx}. Using zero vector.")
                        embedding = np.zeros(vector_size)
                    elif fallback_strategy == 'mean':
                        logger.warning(f"No vectors found for text at index {idx}. Using vocabulary mean vector.")
                        embedding = np.mean([v for v in nlp.vocab.vectors], axis=0)
                    else:  # 'skip'
                        logger.warning(f"No vectors found for text at index {idx}. Skipping.")
                        skipped_indices.append(idx)
                        continue
                
                embeddings.append(embedding)
                
            except Exception as e:
                logger.error(f"Error processing text at index {idx}: {e}")
                if fallback_strategy != 'skip':
                    embeddings.append(np.zeros(vector_size))
                else:
                    skipped_indices.append(idx)
        
        if not embeddings:
            raise EmbeddingError("No valid embeddings generated for any input texts")
        
        embeddings = np.array(embeddings)
        logger.info(f"Successfully processed {len(embeddings)} texts. Skipped {len(skipped_indices)} texts.")
        
        return embeddings[0] if len(texts) == 1 else embeddings
        
    except Exception as e:
        logger.error(f"Failed to generate embeddings: {e}")
        raise EmbeddingError(f"Embedding generation failed: {e}")

if __name__ == "__main__":
    # Example usage and testing
    try:
        test_texts = [
            "Hello world",
            "This is a test sentence",
            "Another example text"
        ]
        
        embeddings = generate_embeddings_using_spacy(
            texts=test_texts,
            fallback_strategy='zeros'
        )
        
        logger.info(f"Generated embeddings shape: {embeddings.shape}")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")