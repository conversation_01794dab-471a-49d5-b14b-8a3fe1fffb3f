from typing import List, Dict, Any, Optional, Protocol
from dataclasses import dataclass
import numpy as np

@dataclass
class CollectionResult:
    """Data class for collection generation results"""
    collections: List[Dict[str, Any]]
    timestamp: str
    status: str = "success"

class CollectionGeneratorProtocol(Protocol):
    """Protocol defining the interface for collection generators"""
    async def create_collections(
        self,
        products: List[Dict[str, Any]],
        keywords: List[str],
        prompt: dict,
        portkey_config: List[Dict[str, Any]],
        attributes: Optional[Dict[str, Dict[str, bool]]] = None,
        store_id: Optional[int] = None
    ) -> Dict[str, Any]: ... 