"""
Utils/collections/keyword_expansion.py
Module for expanding keywords and matching them with products using NLP techniques.
"""

from typing import List, Dict, Any, Optional
import numpy as np
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

try:
    from app.Utils.collections.embedding_creator import generate_embeddings_using_spacy, SpacyModelSingleton
    from app.Models.dbHelpers import get_competitor_domains
except ImportError:
    from Utils.collections.embedding_creator import generate_embeddings_using_spacy, SpacyModelSingleton
    from Models.dbHelpers import get_competitor_domains

class KeywordExpander:
    """Class for expanding keywords based on variations and product matching."""
    
    def __init__(self, 
                 nlp_model: str = "en_core_web_lg",
                 use_sentence_transformers: bool = True,
                 use_keybert: bool = True):
        """
        Initialize the KeywordExpander.
        
        Args:
            nlp_model: The spaCy model to use for NLP tasks
            use_sentence_transformers: Whether to use sentence-transformers for embeddings
            use_keybert: Whether to use KeyBERT for keyword extraction
        """
        self.nlp = SpacyModelSingleton().get_model()
        self.tfidf_vectorizer = TfidfVectorizer(
            min_df=1, 
            max_df=0.9, 
            ngram_range=(1, 3),
            stop_words='english'
        )
        
        # Initialize sentence-transformers if enabled
        self.use_sentence_transformers = use_sentence_transformers
        if use_sentence_transformers:
            try:
                # Check if running on macOS with MPS
                import platform
                import torch
                
                is_macos = platform.system() == 'Darwin'
                has_mps = hasattr(torch, 'backends') and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()
                
                if is_macos and has_mps:
                    # On macOS with MPS, force CPU usage to avoid segfault
                    import os
                    os.environ["TOKENIZERS_PARALLELISM"] = "false"
                    os.environ["CUDA_VISIBLE_DEVICES"] = ""
                    os.environ["USE_TORCH"] = "1" 
                    
                    # Force CPU device
                    from sentence_transformers import SentenceTransformer
                    self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2', device='cpu')
                else:
                    # Normal initialization for other platforms
                    from sentence_transformers import SentenceTransformer
                    self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
            except Exception:
                self.use_sentence_transformers = False
        
        # Initialize KeyBERT if enabled
        self.use_keybert = use_keybert
        if use_keybert:
            try:
                # Check if running on macOS with MPS
                import platform
                import torch
                
                is_macos = platform.system() == 'Darwin'
                has_mps = hasattr(torch, 'backends') and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()
                
                if is_macos and has_mps:
                    # On macOS with MPS, force CPU usage for KeyBERT
                    import os
                    os.environ["TOKENIZERS_PARALLELISM"] = "false"
                    os.environ["CUDA_VISIBLE_DEVICES"] = ""
                    os.environ["USE_TORCH"] = "1"
                    
                    # Initialize KeyBERT with CPU device
                    from keybert import KeyBERT
                    self.keybert_model = KeyBERT(model='all-MiniLM-L6-v2')
                else:
                    # Normal initialization for other platforms
                    from keybert import KeyBERT
                    self.keybert_model = KeyBERT()
            except Exception:
                self.use_keybert = False
    
    def extract_attributes(self, keyword: str) -> Dict[str, Any]:
        """
        Extract attributes from a keyword (product type, demographic, price point, etc.).
        
        Args:
            keyword: The keyword to analyze
            
        Returns:
            Dictionary of extracted attributes
        """
        doc = self.nlp(keyword.lower())
        
        # Initial empty attributes
        attributes = {
            "product_type": "",
            "demographic": "",
            "price_point": "",
            "quality": "",
            "other": []
        }
        
        # Extract product type (usually nouns)
        for token in doc:
            if token.pos_ == "NOUN" and not attributes["product_type"]:
                attributes["product_type"] = token.text
                
        # Look for demographics (men, women, kids, etc.)
        demographic_terms = ["men", "women", "kids", "children", "boy", "girl", "teen", "adult", "baby"]
        for term in demographic_terms:
            if term in keyword.lower():
                attributes["demographic"] = term
                break
                
        # Extract price points
        price_pattern = r'under\s*\$(\d+)|(\$\d+)'
        price_matches = re.findall(price_pattern, keyword)
        if price_matches:
            flat_matches = [x for sublist in price_matches for x in sublist if x]
            attributes["price_point"] = flat_matches[0]
            
        # Extract quality indicators
        quality_terms = ["premium", "luxury", "budget", "cheap", "affordable", "high-end", "low-cost"]
        for term in quality_terms:
            if term in keyword.lower():
                attributes["quality"] = term
                break
                
        # Add remaining key terms to "other"
        for token in doc:
            if (token.pos_ in ["ADJ", "ADV"] and 
                token.text not in attributes.values() and 
                token.text not in demographic_terms + quality_terms):
                attributes["other"].append(token.text)
                
        return attributes
    
    def generate_variations(self, 
                           keyword: str, 
                           product_attributes: List[Dict[str, Any]],
                           max_variations: int = 5) -> List[str]:
        """
        Generate variations of a keyword that would match products in catalog.
        
        Args:
            keyword: Original competitor keyword
            product_attributes: List of attribute dictionaries from product catalog
            max_variations: Maximum number of variations to generate per keyword
            
        Returns:
            List of keyword variations
        """
        variations = []
        keyword_attributes = self.extract_attributes(keyword)
        
        # Generate variations by substituting different attributes
        for prod_attr in product_attributes:
            new_variation = keyword
            
            # If keyword has demographic but product has different demographic
            if keyword_attributes["demographic"] and prod_attr.get("demographic") and keyword_attributes["demographic"] != prod_attr["demographic"]:
                new_variation = new_variation.replace(keyword_attributes["demographic"], prod_attr["demographic"])
                variations.append(new_variation)
                
            # If keyword has price point but product is in different range
            if keyword_attributes["price_point"] and prod_attr.get("price_point"):
                price_pattern = r'under\s*\$\d+|\$\d+'
                new_price_term = f"under ${prod_attr['price_point']}" if "under" in keyword else f"${prod_attr['price_point']}"
                new_variation = re.sub(price_pattern, new_price_term, keyword)
                variations.append(new_variation)
            
            # If keyword has quality indicator but product has different quality
            if keyword_attributes["quality"] and prod_attr.get("quality") and keyword_attributes["quality"] != prod_attr["quality"]:
                new_variation = new_variation.replace(keyword_attributes["quality"], prod_attr["quality"])
                variations.append(new_variation)
        
        # Add some generic variations
        if keyword_attributes["product_type"]:
            variations.append(f"best {keyword_attributes['product_type']}")
            variations.append(f"{keyword_attributes['product_type']} on sale")
            variations.append(f"top rated {keyword_attributes['product_type']}")
            
        # Remove duplicates and the original keyword
        variations = list(set(variations))
        if keyword in variations:
            variations.remove(keyword)
            
        # Limit the number of variations
        return variations[:max_variations]
    
    def calculate_keyword_similarity(self, keyword1: str, keyword2: str) -> float:
        """
        Calculate semantic similarity between two keywords.
        
        Args:
            keyword1: First keyword
            keyword2: Second keyword
            
        Returns:
            Similarity score between 0 and 1
        """
        if self.use_sentence_transformers:
            # Use sentence-transformers for better semantic similarity
            embeddings = self.sentence_transformer.encode([keyword1.lower(), keyword2.lower()])
            return cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        else:
            # Fall back to spaCy
            doc1 = self.nlp(keyword1.lower())
            doc2 = self.nlp(keyword2.lower())
            return doc1.similarity(doc2)
    
    def extract_keywords_from_product(self, product: Dict[str, Any], top_n: int = 5) -> List[str]:
        """
        Extract potential keywords from a product description.
        
        Args:
            product: Product dictionary with title and description
            top_n: Number of keywords to extract
            
        Returns:
            List of extracted keywords
        """
        # Combine title and description
        text = f"{product.get('title', '')} {product.get('description', '')}"
        
        if self.use_keybert:
            # Use KeyBERT for keyword extraction
            keywords = self.keybert_model.extract_keywords(
                text, 
                keyphrase_ngram_range=(1, 3), 
                stop_words='english', 
                top_n=top_n
            )
            return [kw[0] for kw in keywords]  # KeyBERT returns (keyword, score) tuples
        else:
            # Fall back to TF-IDF
            try:
                # Fit and transform on the single document
                tfidf_matrix = self.tfidf_vectorizer.fit_transform([text])
                feature_names = self.tfidf_vectorizer.get_feature_names_out()
                
                # Get top N terms by TF-IDF score
                tfidf_scores = zip(feature_names, tfidf_matrix.toarray()[0])
                sorted_scores = sorted(tfidf_scores, key=lambda x: x[1], reverse=True)
                
                return [term for term, score in sorted_scores[:top_n]]
            except Exception:
                # Simple fallback using noun phrases
                doc = self.nlp(text)
                noun_phrases = [chunk.text.lower() for chunk in doc.noun_chunks]
                return noun_phrases[:top_n]
    
    def match_products_to_keywords(self, 
                                  products: List[Dict[str, Any]], 
                                  keywords: List[str],
                                  threshold: float = 0.6) -> Dict[str, List[Dict[str, Any]]]:
        """
        Match products to keywords based on similarity.
        
        Args:
            products: List of product dictionaries
            keywords: List of keywords
            threshold: Minimum similarity score to consider a match
            
        Returns:
            Dictionary mapping keywords to lists of matching products
        """
        # Create product descriptions
        product_descriptions = [
            f"{p.get('title', '')} {p.get('description', '')}"
            for p in products
        ]
        
        if self.use_sentence_transformers:
            # Use sentence-transformers for better semantic matching
            keyword_embeddings = self.sentence_transformer.encode(keywords)
            product_embeddings = self.sentence_transformer.encode(product_descriptions)
            
            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(keyword_embeddings, product_embeddings)
        else:
            # Fall back to TF-IDF
            # Create TF-IDF vectors
            self.tfidf_vectorizer.fit(product_descriptions + keywords)
            product_vectors = self.tfidf_vectorizer.transform(product_descriptions)
            keyword_vectors = self.tfidf_vectorizer.transform(keywords)
            
            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(keyword_vectors, product_vectors)
        
        # Match keywords to products
        keyword_to_products = {}
        for i, keyword in enumerate(keywords):
            matched_indices = np.where(similarity_matrix[i] >= threshold)[0]
            keyword_to_products[keyword] = [products[idx] for idx in matched_indices]
            
        return keyword_to_products
    
    def fuzzy_match_keywords(self, 
                            search_keywords: List[str], 
                            product_keywords: List[str],
                            threshold: int = 80) -> Dict[str, List[str]]:
        """
        Perform fuzzy matching between search keywords and product keywords.
        
        Args:
            search_keywords: Keywords from search/competitors
            product_keywords: Keywords extracted from products
            threshold: Minimum similarity score (0-100) to consider a match
            
        Returns:
            Dictionary mapping search keywords to matching product keywords
        """
        # Import rapidfuzz here to avoid loading it unless needed
        from rapidfuzz import fuzz, process
        
        matches = {}
        
        for search_kw in search_keywords:
            # Find matches using rapidfuzz
            fuzzy_matches = process.extract(
                search_kw,
                product_keywords,
                scorer=fuzz.token_sort_ratio,
                limit=5
            )
            
            # Filter by threshold
            good_matches = [match[0] for match in fuzzy_matches if match[1] >= threshold]
            
            if good_matches:
                matches[search_kw] = good_matches
        
        return matches

class StoreKeywordFetcher:
    """Class for fetching keywords for a store using the existing get_trending_keywords function."""
    
    def __init__(self):
        """Initialize the StoreKeywordFetcher."""
        pass

    async def get_competitors(self, store_id: int) -> List[str]:
        """Get competitors for a store."""
        return await get_competitor_domains(store_id)
    
    async def get_competitor_keywords(self, 
                                    store_id: int,
                                    limit: int = 5) -> List[str]:
        """
        Get keywords that competitors rank for.
        
        Args:
            store_id: ID of the store
            limit: Maximum number of keywords to return
            
        Returns:
            List of keywords
        """
        try:
            # Import directly here to avoid module resolution issues
            import sys
            import os
            
            # Add all possible paths to sys.path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            grandparent_dir = os.path.dirname(parent_dir)
            
            # Add paths if they're not already in sys.path
            for path in [current_dir, parent_dir, grandparent_dir]:
                if path not in sys.path:
                    sys.path.append(path)
            
            # Now try the import with the expanded path
            from app.Utils.get_trending_keywords import KeywordService, Config
            from pathlib import Path
            
            # Initialize config with ruleset file path
            config_path = Path(__file__).parent.parent.parent / 'RankCollections/config/keyword_filter_rulesets.yml'
            config = Config(ruleset_file=config_path)
            service = KeywordService(config)
            
            # Process the keyword request
            result = await service.process_keywords_request(
                store_id=store_id,
                force_new_generation=False,
                generate_based_on_store_domain=False,
                generate_based_on_competitors_domains=True,
                generate_based_on_target_keywords=False,
                limit=limit
            )
            
            # Extract refined_keywords from result
            keywords = []
            
            # Check the actual structure of the result and extract refined_keywords
            if result and isinstance(result, dict):
                if "refined_keywords" in result:
                    # Get the refined keywords
                    keywords = result["refined_keywords"]
                    
                    # If refined_keywords are dictionaries, extract the values
                    if keywords and isinstance(keywords[0], dict) and "refined" in keywords[0]:
                        keywords = [kw.get("refined", "") for kw in keywords]
                elif "keywords" in result:
                    # Fallback to regular keywords if refined_keywords not available
                    keywords = result["keywords"]
                    
                    # If keywords are dictionaries with a 'keyword' field, extract them
                    if keywords and isinstance(keywords[0], dict) and "keyword" in keywords[0]:
                        keywords = [kw.get("keyword", "") for kw in keywords]
            
            return keywords
            
        except Exception:
            return []

class KeywordProductMatcher:
    """Class for matching keywords to products and creating collections."""
    
    def __init__(self, 
                 keyword_expander: KeywordExpander,
                 similarity_threshold: float = 0.6,
                 use_fuzzy_matching: bool = True):
        """
        Initialize the KeywordProductMatcher.
        
        Args:
            keyword_expander: KeywordExpander instance
            similarity_threshold: Threshold for semantic similarity matching
            use_fuzzy_matching: Whether to use fuzzy matching for keywords
        """
        self.keyword_expander = keyword_expander
        self.similarity_threshold = similarity_threshold
        self.use_fuzzy_matching = use_fuzzy_matching
    
    def extract_product_attributes(self, products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract attributes from products for keyword variation generation.
        
        Args:
            products: List of product dictionaries
            
        Returns:
            List of attribute dictionaries
        """
        product_attributes = []
        
        for product in products:
            # Extract text for attribute analysis
            product_text = f"{product.get('title', '')} {product.get('description', '')}"
            
            # Extract attributes using keyword expander
            attributes = self.keyword_expander.extract_attributes(product_text)
            
            # Add product ID for reference
            attributes["product_id"] = product.get("id", "") or product.get("shopify_gid", "")
            
            product_attributes.append(attributes)
        
        return product_attributes
    
    def expand_keywords(self, 
                       keywords: List[str], 
                       product_attributes: List[Dict[str, Any]],
                       max_variations_per_keyword: int = 3) -> List[str]:
        """
        Expand keywords with variations based on product attributes.
        
        Args:
            keywords: Original keywords
            product_attributes: Product attributes for variation generation
            max_variations_per_keyword: Maximum variations per keyword
            
        Returns:
            Expanded list of keywords
        """
        expanded_keywords = keywords.copy()
        
        for keyword in keywords:
            variations = self.keyword_expander.generate_variations(
                keyword, 
                product_attributes,
                max_variations=max_variations_per_keyword
            )
            expanded_keywords.extend(variations)
        
        # Remove duplicates
        expanded_keywords = list(set(expanded_keywords))
        
        return expanded_keywords
    
    def extract_keywords_from_products(self, 
                                      products: List[Dict[str, Any]],
                                      keywords_per_product: int = 3) -> List[str]:
        """
        Extract potential keywords from products.
        
        Args:
            products: List of product dictionaries
            keywords_per_product: Number of keywords to extract per product
            
        Returns:
            List of extracted keywords
        """
        all_keywords = []
        
        for product in products:
            product_keywords = self.keyword_expander.extract_keywords_from_product(
                product,
                top_n=keywords_per_product
            )
            all_keywords.extend(product_keywords)
        
        # Remove duplicates
        unique_keywords = list(set(all_keywords))
        
        return unique_keywords
    
    def create_keyword_product_groups(self, 
                                     products: List[Dict[str, Any]],
                                     keywords: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Create groups of products based on keyword matching.
        
        Args:
            products: List of product dictionaries
            keywords: List of keywords to match
            
        Returns:
            Dictionary mapping keywords to product groups
        """
        # Match products to keywords
        keyword_product_matches = self.keyword_expander.match_products_to_keywords(
            products,
            keywords,
            threshold=self.similarity_threshold
        )
        
        # Extract keywords from products
        product_keywords = self.extract_keywords_from_products(products)
        
        # Enhance matches with fuzzy matching if enabled
        if self.use_fuzzy_matching:
            fuzzy_matches = self.keyword_expander.fuzzy_match_keywords(
                keywords,
                product_keywords
            )
            
            # Add products that match fuzzy-matched keywords
            for search_kw, matched_kws in fuzzy_matches.items():
                if search_kw not in keyword_product_matches:
                    keyword_product_matches[search_kw] = []
                
                for matched_kw in matched_kws:
                    # Find products that contain this keyword
                    for product in products:
                        product_text = f"{product.get('title', '')} {product.get('description', '')}"
                        if matched_kw.lower() in product_text.lower():
                            if product not in keyword_product_matches[search_kw]:
                                keyword_product_matches[search_kw].append(product)
        
        # Format the result as expected by the collection generator
        keyword_product_groups = {}
        
        for keyword, matched_products in keyword_product_matches.items():
            if matched_products:  # Only include keywords with matching products
                keyword_product_groups[keyword] = {
                    "products": matched_products,
                    "metadata": {
                        "group_type": "keyword_match",
                        "keyword": keyword,
                        "product_count": len(matched_products)
                    }
                }
        
        return keyword_product_groups

def extract_product_attributes_from_text(products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Extract product attributes from product text fields.
    
    Args:
        products: List of product dictionaries
        
    Returns:
        List of attribute dictionaries
    """
    try:
        nlp = SpacyModelSingleton().get_model()
        attributes_list = []
        
        for product in products:
            # Combine title and description
            text = f"{product.get('title', '')} {product.get('description', '')}"
            doc = nlp(text.lower())
            
            # Initialize attributes
            attributes = {
                "product_type": "",
                "demographic": "",
                "price_point": "",
                "quality": "",
                "other": []
            }
            
            # Extract product type (usually nouns)
            for token in doc:
                if token.pos_ == "NOUN" and not attributes["product_type"]:
                    attributes["product_type"] = token.text
            
            # Look for demographics
            demographic_terms = ["men", "women", "kids", "children", "boy", "girl", "teen", "adult", "baby"]
            for term in demographic_terms:
                if term in text.lower():
                    attributes["demographic"] = term
                    break
            
            # Extract price points
            price_pattern = r'\$(\d+(\.\d+)?)'
            price_matches = re.findall(price_pattern, text)
            if price_matches:
                attributes["price_point"] = price_matches[0][0]
            
            # Extract quality indicators
            quality_terms = ["premium", "luxury", "budget", "cheap", "affordable", "high-end", "low-cost"]
            for term in quality_terms:
                if term in text.lower():
                    attributes["quality"] = term
                    break
            
            # Add product ID for reference
            attributes["product_id"] = product.get("id", "") or product.get("shopify_gid", "")
            
            attributes_list.append(attributes)
        
        return attributes_list
        
    except Exception:
        return [] 
