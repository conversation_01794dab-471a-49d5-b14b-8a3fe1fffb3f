"""
Utils/collections/product_collection_pipeline.py
Main pipeline for forming product collections using competitor keywords.
"""

import asyncio
import random
from typing import List, Dict, Any, Optional

try:
    from app.Utils.collections.keyword_expansion import KeywordExpander, KeywordProductMatcher, StoreKeywordFetcher
    from app.Utils.collections.product_filter import ProductFilter
    from app.Models.dbHelpers import (
        get_product_titles_and_descriptions,
        get_product_title_and_shopify_gid,
        get_all_product_data_belongs_to_a_store_except_embedding,
        get_product_details_with_only_necessary_fields,
        store_collections_in_database
    )
    from app.Agents.rankCollections.collection_generator import create_collections
    from app.Agents.rankCollections.generic_collection_generator import create_generic_collections
    from app.Logging.logger_config import get_logger
except ImportError:
    from Utils.collections.keyword_expansion import KeywordExpander, KeywordProductMatcher, StoreKeywordFetcher
    from Utils.collections.product_filter import ProductFilter
    from Models.dbHelpers import (
        get_product_titles_and_descriptions,
        get_product_title_and_shopify_gid,
        get_all_product_data_belongs_to_a_store_except_embedding,
        get_product_details_with_only_necessary_fields,
        store_collections_in_database
    )
    from Agents.rankCollections.collection_generator import create_collections
    from Agents.rankCollections.generic_collection_generator import create_generic_collections
    from Logging.logger_config import get_logger

# Configuration constants
DEFAULT_SIMILARITY_THRESHOLD = 0.7
USE_SENTENCE_TRANSFORMERS = True
USE_KEYBERT = True
USE_FUZZY_MATCHING = True
MIN_CLUSTERS = 2
MAX_CLUSTERS = 10
KEYWORDS_COUNT = 5
TRY_FALLBACK_GENERIC_COLLECTIONS = True

logger = get_logger(__name__)

class ProductCollectionPipeline:
    """Main pipeline for forming product collections using competitor keywords."""
    
    def __init__(self, 
                 store_id: int,
                 similarity_threshold: float = DEFAULT_SIMILARITY_THRESHOLD or 0.7,
                 use_sentence_transformers: bool = USE_SENTENCE_TRANSFORMERS or True,
                 use_keybert: bool = USE_KEYBERT or True,
                 use_fuzzy_matching: bool = USE_FUZZY_MATCHING or True,
                 keywords_origin_type: str = 'competitor',
                 attributes: Optional[Dict[str, Dict[str, bool]]] = None,
                 enforce_strict_collection_creation: Optional[bool] = False):
        """
        Initialize the ProductCollectionPipeline.
        
        Args:
            store_id: ID of the store
            similarity_threshold: Threshold for semantic similarity matching
            use_sentence_transformers: Whether to use sentence-transformers for embeddings
            use_keybert: Whether to use KeyBERT for keyword extraction
            use_fuzzy_matching: Whether to use fuzzy matching for keywords
            keywords_origin_type: Source of keywords ('self' or 'competitor')
            attributes: Optional attributes for collection creation
            enforce_strict_collection_creation: Whether to enforce strict collection creation
        """
        self.store_id = store_id
        self.keyword_fetcher = StoreKeywordFetcher()
        self.keywords_origin_type = keywords_origin_type
        self.attributes = attributes
        self.enforce_strict_collection_creation = enforce_strict_collection_creation
        
        # Check if running on macOS with MPS
        import platform
        import torch
        
        is_macos = platform.system() == 'Darwin'
        has_mps = False
        try:
            has_mps = hasattr(torch, 'backends') and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()
        except:
            pass
        
        # Disable sentence transformers and keybert on macOS with MPS to avoid segfault
        if is_macos and has_mps:
            logger.info("Detected macOS with MPS. Disabling SentenceTransformer and KeyBERT to avoid segfault.")
            use_sentence_transformers = False
            use_keybert = False
        
        self.keyword_expander = KeywordExpander(
            use_sentence_transformers=use_sentence_transformers,
            use_keybert=use_keybert
        )
        self.keyword_matcher = KeywordProductMatcher(
            keyword_expander=self.keyword_expander,
            similarity_threshold=similarity_threshold,
            use_fuzzy_matching=use_fuzzy_matching
        )
        self.product_filter = ProductFilter(
            similarity_threshold=similarity_threshold,
            min_clusters=MIN_CLUSTERS,
            max_clusters=MAX_CLUSTERS
        )
        self.similarity_threshold = similarity_threshold
    
    async def fetch_keywords(self,  
                            store_id: int,
                            keywords_count: int = KEYWORDS_COUNT or 5) -> List[str]:
        """
        Fetch keywords from competitors or products.
        
        Args:
            store_id: ID of the store
            keywords_count: Number of keywords to fetch
            
        Returns:
            List of keywords
        """
        try:
            # Try to get keywords based on the specified origin type
            if self.keywords_origin_type == 'competitor':
                logger.info(f"Fetching competitor keywords for store {store_id}")
                keywords = await self.keyword_fetcher.get_competitor_keywords(
                    store_id=store_id,
                    limit=keywords_count
                )
                
                # If no competitor keywords, fall back to product-based keywords
                if not keywords:
                    logger.info("No competitor keywords found, falling back to product-based keywords")
                    keywords = await self._generate_keywords_from_products(
                        store_id=store_id,
                        keywords_count=keywords_count
                    )
            else:
                logger.info(f"Generating keywords from products for store {store_id}")
                keywords = await self._generate_keywords_from_products(
                    store_id=store_id,
                    keywords_count=keywords_count
                )
                
                # If no product-based keywords, fall back to competitor keywords
                if not keywords:
                    logger.info("No product-based keywords found, falling back to competitor keywords")
                    keywords = await self.keyword_fetcher.get_competitor_keywords(
                        store_id=store_id,
                        limit=keywords_count
                    )
            
            # If still no keywords, use generic fallback
            if not keywords:
                logger.warning("No keywords found from any source, using generic fallback")
                keywords = ["products", "collection", "featured", "new", "popular"]
            
            logger.info(f"Found {len(keywords)} keywords: {keywords}")
            return keywords
            
        except Exception as e:
            logger.error(f"Error fetching keywords: {str(e)}", exc_info=True)
            # Return some generic keywords as fallback
            return ["products", "collection", "featured", "new", "popular"]
    
    async def _generate_keywords_from_products(self, store_id: int, keywords_count: int) -> List[str]:
        """
        Generate keywords from product data.
        
        Args:
            store_id: ID of the store
            keywords_count: Number of keywords to extract
            
        Returns:
            List of keywords
        """
        try:
            # Get product data
            products = await get_product_titles_and_descriptions(store_id)
            
            if not products:
                logger.warning(f"No products found for store {store_id}")
                return []
            
            logger.info(f"Generating keywords from {len(products)} products")
            
            # Extract keywords from products
            all_keywords = self.keyword_expander.extract_keywords_from_product(
                {"title": " ".join([p.get("title", "") for p in products]),
                 "description": " ".join([p.get("description", "") for p in products])},
                top_n=keywords_count * 2  # Extract more than needed to filter later
            )
            
            # Filter and limit keywords
            filtered_keywords = []
            for kw in all_keywords:
                # Skip very short keywords
                if len(kw.split()) < 2 and len(kw) < 5:
                    continue
                filtered_keywords.append(kw)
                if len(filtered_keywords) >= keywords_count:
                    break
            
            logger.info(f"Generated {len(filtered_keywords)} keywords from products")

            # Shuffle the keywords
            logger.info(f"Shuffling keywords: {filtered_keywords}")
            random.shuffle(filtered_keywords)
            return filtered_keywords
            
        except Exception as e:
            logger.error(f"Error generating keywords from products: {str(e)}", exc_info=True)
            return []
    
    async def expand_keywords(self, 
                             keywords: List[str], 
                             max_variations_per_keyword: int = 2) -> List[str]:
        """
        Expand keywords with variations.
        
        Args:
            keywords: List of keywords
            max_variations_per_keyword: Maximum variations per keyword
            
        Returns:
            List of expanded keywords
        """
        try:
            # Get product data for attribute extraction
            products = await get_product_titles_and_descriptions(self.store_id)
            
            if not products:
                logger.warning(f"No products found for store {self.store_id}, skipping keyword expansion")
                return keywords
            
            # Extract product attributes
            product_attributes = self.keyword_matcher.extract_product_attributes(products)
            
            # Expand keywords
            expanded_keywords = self.keyword_matcher.expand_keywords(
                keywords,
                product_attributes,
                max_variations_per_keyword=max_variations_per_keyword
            )
            
            logger.info(f"Expanded {len(keywords)} keywords to {len(expanded_keywords)} variations")
            return expanded_keywords
            
        except Exception as e:
            logger.error(f"Error expanding keywords: {str(e)}", exc_info=True)
            return keywords
    
    async def create_keyword_product_groups(self, 
                                          keywords: List[str], 
                                          max_groups: int = 10) -> Dict[str, Dict[str, Any]]:
        """
        Create groups of products based on keyword matching.
        
        Args:
            keywords: List of keywords
            max_groups: Maximum number of groups to create
            
        Returns:
            Dictionary mapping keywords to product groups
        """
        try:
            # Get product data
            products = await get_product_titles_and_descriptions(self.store_id)
            
            if not products:
                logger.warning(f"No products found for store {self.store_id}")
                return {}
            
            # Create keyword-product groups
            keyword_product_groups = self.keyword_matcher.create_keyword_product_groups(
                products,
                keywords
            )
            
            # Limit the number of groups
            if len(keyword_product_groups) > max_groups:
                # Sort groups by product count
                sorted_groups = sorted(
                    keyword_product_groups.items(),
                    key=lambda x: len(x[1]["products"]),
                    reverse=True
                )
                
                # Keep only the top groups
                keyword_product_groups = {
                    k: v for k, v in sorted_groups[:max_groups]
                }
            
            # Ensure each group has at least 3 products
            for keyword, group in keyword_product_groups.items():
                if len(group["products"]) < 3:
                    # Find closest products for this keyword
                    additional_products = await self._find_closest_products_for_keyword(
                        keyword,
                        products,
                        min_products=3
                    )
                    
                    # Add products not already in the group
                    existing_ids = {p.get("id", "") or p.get("shopify_gid", "") for p in group["products"]}
                    for product in additional_products:
                        product_id = product.get("id", "") or product.get("shopify_gid", "")
                        if product_id and product_id not in existing_ids:
                            group["products"].append(product)
                            existing_ids.add(product_id)
                    
                    # Update metadata
                    group["metadata"]["product_count"] = len(group["products"])
            
            logger.info(f"Created {len(keyword_product_groups)} keyword-product groups")
            return keyword_product_groups
            
        except Exception as e:
            logger.error(f"Error creating keyword-product groups: {str(e)}", exc_info=True)
            return {}
    
    async def _find_closest_products_for_keyword(self, keyword: str, products: List[Dict[str, Any]], min_products: int = 3) -> List[Dict[str, Any]]:
        """
        Find products that are closest to a keyword.
        
        Args:
            keyword: Keyword to match
            products: List of products
            min_products: Minimum number of products to return
            
        Returns:
            List of matching products
        """
        # Generate embedding for the keyword
        try:
            # Use direct similarity matching
            matching_products = []
            similarity_scores = {}
            
            # Lower the threshold until we find enough products
            current_threshold = self.similarity_threshold
            while len(matching_products) < min_products and current_threshold >= 0.3:
                for product in products:
                    if product in matching_products:
                        continue
                        
                    # Calculate similarity between keyword and product
                    product_text = f"{product.get('title', '')} {product.get('description', '')}"
                    similarity = self.keyword_expander.calculate_keyword_similarity(keyword, product_text)
                    
                    if similarity >= current_threshold:
                        matching_products.append(product)
                        similarity_scores[product.get("id", "") or product.get("shopify_gid", "")] = similarity
                
                # If we still don't have enough products, lower the threshold
                if len(matching_products) < min_products:
                    current_threshold -= 0.1
            
            # If we still don't have enough products, add random ones
            if len(matching_products) < min_products:
                remaining_products = [p for p in products if p not in matching_products]
                if remaining_products:
                    random_products = random.sample(
                        remaining_products, 
                        min(min_products - len(matching_products), len(remaining_products))
                    )
                    matching_products.extend(random_products)
            
            return matching_products
            
        except Exception as e:
            logger.error(f"Error finding closest products for keyword '{keyword}': {str(e)}", exc_info=True)
            # Return random products as fallback
            return random.sample(products, min(min_products, len(products)))
    
    async def generate_collections(self, 
                                 keywords: List[str],
                                 keyword_product_groups: Dict[str, Dict[str, Any]],
                                 attributes: Optional[Dict[str, Dict[str, bool]]] = None) -> Dict[str, Any]:
        """
        Generate collections using autogen agents.
        
        Args:
            keywords: List of keywords
            keyword_product_groups: Dictionary mapping keywords to product groups
            attributes: Optional attributes for collection creation
            
        Returns:
            Dictionary with collection results
        """
        logger.info("Generating collections")
        try:
            if self.enforce_strict_collection_creation:
                # Extract keywords and all products
                keywords = list(keyword_product_groups.keys())
                all_products = []
                total_products = await get_all_product_data_belongs_to_a_store_except_embedding(self.store_id)
            
                # Get products data
                try:
                    # Use the actual products list from keyword groups
                    for group in keyword_product_groups.values():
                        all_products.extend(group["products"])
                except Exception as e:
                    logger.error(f"Error getting products: {str(e)}", exc_info=True)
                    # Just use the products from keyword groups
                    for group in keyword_product_groups.values():
                        all_products.extend(group["products"])
                
                # Remove duplicate products
                unique_products = []
                seen_ids = set()
                
                for product in all_products:
                    product_id = product.get("id", "") or product.get("shopify_gid", "")
                    if product_id and product_id not in seen_ids:
                        unique_products.append(product)
                        seen_ids.add(product_id)

                # Create collections
                collections = await create_collections(
                    keywords=keywords,
                    products=total_products,
                    store_id=self.store_id,
                    grouped_products=keyword_product_groups,
                    attributes=attributes
                )

            else:
                logger.info("Creating generic collections")
                total_products = await get_product_details_with_only_necessary_fields(self.store_id)

                collections = await create_generic_collections(
                    keywords=keywords,
                    products=total_products,
                    store_id=self.store_id,
                    attributes=attributes
                )
            
            # If the agent didn't return collections in the expected format, log it but don't create fallbacks
            if not collections or not isinstance(collections, dict) or "collections" not in collections:
                logger.warning("Agent didn't return collections in the expected format")
                # Return empty collections instead of creating fallbacks
                return {"collections": []}
            
            return collections
            
        except Exception as e:
            logger.error(f"Error generating collections: {str(e)}", exc_info=True)
            # Return empty collections instead of creating fallbacks
            return {"collections": []}
    
    def _extract_keywords_from_products(self, products: List[Dict[str, Any]], keywords_count: int) -> List[str]:
        """
        Extract keywords from product data when no competitor URLs are provided.
        
        Args:
            products: List of product dictionaries
            keywords_count: Number of keywords to extract
            
        Returns:
            List of keywords
        """
        try:
            # Combine all product titles and descriptions
            combined_text = {
                "title": " ".join([p.get("title", "") for p in products]),
                "description": " ".join([p.get("description", "") for p in products])
            }
            
            # Extract keywords
            keywords = self.keyword_expander.extract_keywords_from_product(
                combined_text,
                top_n=keywords_count
            )
            
            logger.info(f"Extracted {len(keywords)} keywords from products")
            return keywords
            
        except Exception as e:
            logger.error(f"Error extracting keywords from products: {str(e)}", exc_info=True)
            # Return some generic keywords as fallback
            return ["products", "collection", "featured", "new", "popular"]

    async def run(self, 
                 store_id: int,
                 keywords_count: int,
                 max_variations_per_keyword: int,
                 max_product_groups: int) -> Dict[str, Any]:
        """
        Run the full pipeline to create product collections.
        
        Args:
            keywords_count: Number of keywords to fetch per competitor URL
            max_variations_per_keyword: Maximum variations per keyword
            max_product_groups: Maximum number of product groups to create
            
        Returns:
            Dictionary with collection results
        """
        logger.info("Starting product collection pipeline")
        
        try:
            # 1. Fetch keywords (with fallback to product-based keywords if none available)
            keywords = await self.fetch_keywords(
                store_id=store_id,
                keywords_count=keywords_count
            )
            
            if not keywords:
                logger.warning("No keywords available after fallback, using generic keywords")
                keywords = ["products", "collection", "featured", "new", "popular"]
            
            logger.info(f"Using keywords: {keywords}")
            
            # 2. Refine and expand keywords with variations based on product attributes
            expanded_keywords = await self.expand_keywords(
                keywords,
                max_variations_per_keyword=max_variations_per_keyword
            )
            
            if not expanded_keywords:
                logger.warning("No expanded keywords available, using original keywords")
                expanded_keywords = keywords
            
            logger.info(f"Using expanded keywords: {expanded_keywords}")

            if self.enforce_strict_collection_creation:
                # 3. Create keyword-product groups using clustering or similarity
                keyword_product_groups = await self.create_keyword_product_groups(
                    expanded_keywords,
                    max_groups=max_product_groups
                )
       
                if not keyword_product_groups:
                    logger.warning("Failed to create keyword-product groups, falling back to generic approach")
                    # Instead of returning an error, switch to generic approach
                    self.enforce_strict_collection_creation = False
                    keyword_product_groups = await get_product_titles_and_descriptions(self.store_id)
            else:
                logger.info("Creating generic product groups")
                keyword_product_groups = await get_product_titles_and_descriptions(self.store_id)

            # 4. Generate collections using the keyword-product groups
            collections_result = await self.generate_collections(
                keywords=expanded_keywords,
                keyword_product_groups=keyword_product_groups if self.enforce_strict_collection_creation else None,
                attributes=self.attributes if hasattr(self, 'attributes') else None,
            )

            # Fallback to generic collections if no collections are created
            if not collections_result or "collections" not in collections_result:
                if TRY_FALLBACK_GENERIC_COLLECTIONS:
                    logger.warning("No collections created, using generic collections")
                    # Modify self.enforce_strict_collection_creation to False
                    self.enforce_strict_collection_creation = False
                    collections_result = await self.generate_collections(
                        keywords=expanded_keywords,
                        attributes=self.attributes if hasattr(self, 'attributes') else None,
                    )
                else:
                    logger.warning("No collections created")
                    return {
                        "status": "error",
                        "message": "No collections created",
                        "collections": []
                    }
  
            # Ensure collections only include existing product IDs
            await self._validate_product_ids_in_collections(collections_result)
            
            return collections_result
            
        except Exception as e:
            logger.error(f"Error in product collection pipeline: {str(e)}", exc_info=True)
            
            # Return error with any collections that might have been created
            return {
                "status": "error",
                "message": f"Error in product collection pipeline: {str(e)}",
                "collections": []
            }
            
    async def _validate_product_ids_in_collections(self, collections_result: Dict[str, Any]) -> None:
        """
        Ensure collections only include existing product IDs.
        
        Args:
            collections_result: Dictionary with collection results
        """
        if not collections_result or "collections" not in collections_result:
            return
        
        # Get all valid product IDs for the store
        all_products = await get_product_title_and_shopify_gid(self.store_id)
        valid_product_ids = set(p.get("shopify_gid", "") for p in all_products if p.get("shopify_gid"))
        
        logger.info(f"Found {len(valid_product_ids)} valid product IDs")
        
        # Filter collections to only include valid product IDs
        for collection in collections_result["collections"]:
            if "products" in collection:
                # Filter to only include valid product IDs
                valid_products = [pid for pid in collection["products"] if pid in valid_product_ids]
                
                # If we lost too many products, add some random valid ones
                if len(valid_products) < 3 and valid_product_ids:
                    needed = 3 - len(valid_products)
                    # Get product IDs not already in the collection
                    available_ids = list(valid_product_ids - set(valid_products))
                    
                    if available_ids:
                        # Add random valid product IDs
                        valid_products.extend(random.sample(available_ids, min(needed, len(available_ids))))
                
                # Update the collection with only valid products
                collection["products"] = valid_products
                
                logger.info(f"Collection '{collection.get('title', '')}' has {len(valid_products)} valid products")

async def create_collections_from_competitors(
    store_id: int,
    task_id: str,
    similarity_threshold: float,
    keywords_count: int,
    max_variations_per_keyword: int,
    max_product_groups: int,
    attributes: Optional[Dict[str, Dict[str, bool]]] = None,
    keywords_origin_type: str = 'competitor',
    enforce_strict_collection_creation: Optional[bool] = False
) -> Dict[str, Any]:
    """
    Create collections based on competitor keywords.
    
    Args:
        store_id: ID of the store
        task_id: Task ID for tracking
        similarity_threshold: Threshold for semantic similarity matching
        keywords_count: Number of keywords to fetch per competitor URL
        max_variations_per_keyword: Maximum variations per keyword
        max_product_groups: Maximum number of product groups to create
        attributes: Optional attributes for collection creation
        keywords_origin_type: Source of keywords ('self' or 'competitor')
        enforce_strict_collection_creation: Whether to enforce strict collection creation
    Returns:
        Dictionary with collection results
    """
    try:
        # Initialize the pipeline
        pipeline = ProductCollectionPipeline(
            store_id=store_id,
            similarity_threshold=similarity_threshold,
            keywords_origin_type=keywords_origin_type,
            attributes=attributes,
            enforce_strict_collection_creation=enforce_strict_collection_creation
        )
        
        # Run the pipeline
        result = await pipeline.run(
            store_id=store_id,
            keywords_count=keywords_count,
            max_variations_per_keyword=max_variations_per_keyword,
            max_product_groups=max_product_groups
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error in create_collections_from_competitors: {str(e)}", exc_info=True)
        
        return {
            "status": "error",
            "message": f"Error creating collections: {str(e)}",
            "collections": []
        }

# Helper function to run async code in sync context
def run_sync(coro):
    """Run an async coroutine in a synchronous context."""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop.run_until_complete(coro)

# Synchronous wrapper for the async function
def create_collections_from_competitors_sync(
    store_id: int,
    task_id: str,
    attributes: Optional[Dict[str, Dict[str, bool]]] = None,
    keywords_origin_type: str = 'competitor',
    enforce_strict_collection_creation: Optional[bool] = False
) -> Dict[str, Any]:
    """
    Synchronous wrapper for create_collections_from_competitors.
    
    Args:
        store_id: ID of the store
        task_id: Task ID for tracking
        attributes: Optional attributes for collection creation
        keywords_origin_type: Source of keywords ('self' or 'competitor')
        enforce_strict_collection_creation: Whether to enforce strict collection creation
        
    Returns:
        Dictionary with collection results
    """
    return run_sync(
        create_collections_from_competitors(
            store_id=store_id,
            task_id=task_id,
            similarity_threshold=0.7,
            keywords_count=5,
            max_variations_per_keyword=2,
            max_product_groups=10,
            attributes=attributes,
            keywords_origin_type=keywords_origin_type,
            enforce_strict_collection_creation=enforce_strict_collection_creation
        )
    ) 
