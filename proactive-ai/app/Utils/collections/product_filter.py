from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import logging
from collections import defaultdict

try:
    from app.Utils.collections.embedding_creator import generate_embeddings_using_spacy
except ImportError:
    from Utils.collections.embedding_creator import generate_embeddings_using_spacy

logger = logging.getLogger(__name__)

@dataclass
class FilterResult:
    """Data class to hold filtered product results"""
    products_list: List[Dict[str, Any]]
    similarity_scores: Optional[Dict[str, float]] = None
    cluster_labels: Optional[np.ndarray] = None
    cluster_centers: Optional[np.ndarray] = None

class ProductFilter:
    """Combined class for clustering and similarity-based product filtering"""
    
    def __init__(
        self,
        min_clusters: int = 2,
        max_clusters: int = 10,
        similarity_threshold: float = 0.5
    ):
        self.min_clusters = min_clusters
        self.max_clusters = max_clusters
        self.similarity_threshold = similarity_threshold

    def _combine_embeddings(self, product: Dict[str, Any]) -> np.ndarray:
        """Combine different embedding types into a single vector"""
        try:
            # Generate embeddings for text fields if they don't exist
            text_fields = {
                'title': product.get('title', ''),
                'description': product.get('description', ''),
                'handle': product.get('handle', '')
            }
            
            # Filter out empty fields
            text_fields = {k: v for k, v in text_fields.items() if v and isinstance(v, str)}
            
            if not text_fields:
                logger.warning(f"No valid text fields found in product {product.get('id', 'unknown')}")
                # Return zero vector instead of raising error
                return np.zeros(300)  # spaCy's default vector size
            
            # Generate embeddings for each field
            embeddings = {}
            for field, text in text_fields.items():
                try:
                    embedding = generate_embeddings_using_spacy(text, fallback_strategy='mean')
                    if embedding is not None and not np.all(embedding == 0):
                        embeddings[field] = embedding
                except Exception as e:
                    logger.warning(f"Failed to generate embedding for {field}: {e}")
                    continue
            
            if not embeddings:
                logger.warning(f"No valid embeddings generated for product {product.get('id', 'unknown')}")
                # Return zero vector instead of raising error
                return np.zeros(300)
            
            # Combine embeddings with weights
            weights = {'title': 0.5, 'description': 0.3, 'handle': 0.2}
            combined = np.zeros_like(next(iter(embeddings.values())))
            total_weight = 0
            
            for field, embedding in embeddings.items():
                weight = weights.get(field, 0.1)
                combined += embedding * weight
                total_weight += weight
            
            if total_weight > 0:
                combined /= total_weight
            
            return combined
            
        except Exception as e:
            logger.error(f"Error combining embeddings: {e}")
            # Return zero vector as fallback
            return np.zeros(300)

    def _find_optimal_clusters(self, data: np.ndarray) -> int:
        """Find optimal number of clusters using elbow method"""
        if len(data) <= self.min_clusters:
            return self.min_clusters
            
        inertias = []
        n_range = range(self.min_clusters, min(self.max_clusters + 1, len(data)))
        
        for n in n_range:
            kmeans = KMeans(n_clusters=n, random_state=42)
            kmeans.fit(data)
            inertias.append(kmeans.inertia_)
        
        if len(inertias) > 1:
            diffs = np.diff(inertias)
            rates = diffs[1:] / diffs[:-1]
            elbow_idx = np.argmin(np.abs(rates - 1))
            return n_range[elbow_idx]
        
        return self.min_clusters

    def cluster_products(self, products: List[Dict[str, Any]]) -> FilterResult:
        """Filter products using clustering"""
        try:
            # Combine embeddings
            combined_embeddings = np.array([
                self._combine_embeddings(product)
                for product in products
            ])
            
            # Find optimal clusters and perform clustering
            n_clusters = self._find_optimal_clusters(combined_embeddings)
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(combined_embeddings)
            
            # Group products by cluster
            clustered_products = defaultdict(list)
            for product, label in zip(products, cluster_labels):
                clustered_products[int(label)].append(product)
            
            return FilterResult(
                products_list=products,
                cluster_labels=cluster_labels,
                cluster_centers=kmeans.cluster_centers_
            )
            
        except Exception as e:
            logger.error(f"Error in clustering products: {str(e)}")
            raise

    def filter_by_similarity(
        self,
        products: List[Dict[str, Any]],
        keywords: List[np.ndarray]
    ) -> FilterResult:
        """Filter products using direct similarity matching with fallback"""
        try:
            similar_products = []
            similarity_scores = {}
            current_threshold = self.similarity_threshold
            
            while not similar_products and current_threshold >= 0.3:  # Don't go below 0.3
                for product in products:
                    product_embedding = self._combine_embeddings(product)
                    
                    # Calculate maximum similarity with any keyword
                    max_similarity = max(
                        cosine_similarity(
                            product_embedding.reshape(1, -1),
                            keyword.reshape(1, -1)
                        )[0][0]
                        for keyword in keywords
                    )
                    
                    if max_similarity >= current_threshold:
                        similar_products.append(product)
                        similarity_scores[product.get('gid', '')] = float(max_similarity)
                
                if not similar_products:
                    current_threshold -= 0.1  # Reduce threshold and try again
                    logger.info(f"No products found at threshold {current_threshold:.1f}, reducing threshold")
            
            if not similar_products:
                logger.warning("No similar products found even with reduced threshold")
            
            return FilterResult(
                products_list=similar_products,
                similarity_scores=similarity_scores
            )
            
        except Exception as e:
            logger.error(f"Error in similarity filtering: {str(e)}")
            raise

    def filter_products(
        self,
        products: List[Dict[str, Any]],
        keyword_embeddings: List[np.ndarray],
        use_clustering: bool = True
    ) -> FilterResult:
        """Main method to filter products using either clustering or similarity"""
        if use_clustering:
            return self.cluster_products(products)
        return self.filter_by_similarity(products, keyword_embeddings) 
