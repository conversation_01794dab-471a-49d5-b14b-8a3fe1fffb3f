"""
Utils/embedding_creator.py
Module for creating embeddings from text using spaCy with singleton pattern for model management.
"""
import spacy
from typing import List, Union, Optional
import numpy as np
from threading import Lock

try:
    from app.Logging.logger_config import get_logger
except ImportError:
    from Logging.logger_config import get_logger

logger = get_logger(__name__)

class EmbeddingError(Exception):
    """Custom exception for embedding-related errors"""
    pass

class SpacyModelSingleton:
    """Singleton class to manage spaCy model loading and access"""
    _instance = None
    _lock = Lock()
    _model = None

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def get_model(self, model_name: str = 'en_core_web_lg') -> spacy.language.Language:
        """Get or load spaCy model"""
        if self._model is None:
            with self._lock:
                if self._model is None:
                    try:
                        logger.info(f"Loading spaCy model: {model_name}")
                        try:
                            self._model = spacy.load(model_name)
                        except OSError:
                            # Try loading from custom path if default fails
                            from pathlib import Path
                            custom_path = Path('/Users/<USER>/Documents/projects/GeneratorAiModule/GeneratorAI/.venv/lib/python3.12/site-packages/en_core_web_lg/en_core_web_lg-3.4.0')
                            if custom_path.exists():
                                self._model = spacy.load(str(custom_path))
                            else:
                                raise EmbeddingError(f"Failed to load model '{model_name}' from both paths")
                        
                        if not self._model or not hasattr(self._model, 'vocab'):
                            raise EmbeddingError("Model not properly initialized")
                            
                        logger.info(f"Model loaded with vector size: {self._model.vocab.vectors.shape[1]}")
                    except Exception as e:
                        logger.error(f"Model initialization error: {str(e)}")
                        raise EmbeddingError(f"Model loading failed: {e}")
        return self._model

def is_mps_environment():
    """Check if running on macOS with MPS available."""
    try:
        import platform
        import torch
        
        is_macos = platform.system() == 'Darwin'
        has_mps = hasattr(torch, 'backends') and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()
        
        return is_macos and has_mps
    except:
        return False

def generate_embeddings_using_spacy(
    texts: Union[str, List[str]],
    fallback_strategy: str = 'mean'
) -> np.ndarray:
    """Generate embeddings for input texts using spaCy"""
    if not texts:
        raise ValueError("Input texts cannot be empty")
    
    if isinstance(texts, str):
        texts = [texts]
    
    try:
        nlp = SpacyModelSingleton().get_model()
        vector_size = nlp.vocab.vectors.shape[1]
        embeddings = []
        
        # If in MPS environment, force CPU usage
        if is_mps_environment():
            import os
            os.environ["TOKENIZERS_PARALLELISM"] = "false"
            os.environ["CUDA_VISIBLE_DEVICES"] = ""
            os.environ["USE_TORCH"] = "1"
            
            # Use spaCy instead of SentenceTransformer
            return np.array([nlp(text).vector for text in texts])
        
        for idx, text in enumerate(texts):
            if not isinstance(text, str):
                logger.warning(f"Non-string input at index {idx}, converting to string")
                text = str(text)
            
            # Process the entire text as one document
            doc = nlp(text.lower().strip())
            
            # Get vectors for content words (excluding punctuation and stop words)
            content_tokens = [
                token for token in doc 
                if token.has_vector and not token.is_stop and not token.is_punct
            ]
            
            if content_tokens:
                # Extract vectors and weights
                vectors = np.array([token.vector for token in content_tokens])
                
                # Simple mean if no content tokens have probability
                if not any(hasattr(token, 'prob') for token in content_tokens):
                    embedding = np.mean(vectors, axis=0)
                else:
                    # Use uniform weights if token probabilities are not available
                    weights = np.ones(len(content_tokens)) / len(content_tokens)
                    embedding = np.average(vectors, axis=0, weights=weights)
            else:
                logger.warning(f"No valid vectors found for text: '{text[:50]}...'")
                if fallback_strategy == 'zeros':
                    embedding = np.zeros(vector_size)
                elif fallback_strategy == 'mean':
                    # Use mean of all word vectors in vocabulary as fallback
                    embedding = np.zeros(vector_size)  # Default to zeros if no vectors available
                    vocab_vectors = [word.vector for word in nlp.vocab if word.has_vector]
                    if vocab_vectors:
                        embedding = np.mean(vocab_vectors, axis=0)
                else:  # 'skip'
                    continue
            
            embeddings.append(embedding)
        
        if not embeddings:
            logger.error("No embeddings generated for any input text")
            raise ValueError("No valid embeddings generated for any input text")
        
        embeddings_array = np.array(embeddings)
        return embeddings_array[0] if len(texts) == 1 else embeddings_array
        
    except Exception as e:
        logger.error(f"Embedding generation failed: {str(e)}")
        raise EmbeddingError(f"Embedding generation failed: {str(e)}")