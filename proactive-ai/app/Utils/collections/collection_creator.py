from typing import List, Dict, Any, Optional
import logging
import numpy as np
from datetime import datetime
from .product_filter import ProductFilter
from .embedding_creator import generate_embeddings_using_spacy
try:
    from app.RankCollections.config.constants import (
        MIN_CLUSTERS,
        MAX_CLUSTERS,
        DEFAULT_SIMILARITY_THRESHOLD
    )
    from app.Agents.rankCollections.collection_generator import create_collections
except:
    from RankCollections.config.constants import (
        MIN_CLUSTERS,
        MAX_CLUSTERS,
        DEFAULT_SIMILARITY_THRESHOLD
    )
    from Agents.rankCollections.collection_generator import create_collections


logger = logging.getLogger(__name__)

# Default generic keywords to use as fallback
DEFAULT_GENERIC_KEYWORDS = [
    "best-sellers",
    "new-arrivals",
    "trending-items",
    "seasonal-products",
    "featured-products",
    "customer-favorites",
    "special-offers"
]

class CollectionGenerator:
    """Unified collection generator supporting both clustering and similarity methods"""
    
    def __init__(
        self,
        similarity_threshold: float = DEFAULT_SIMILARITY_THRESHOLD,
        min_clusters: int = MIN_CLUSTERS,
        max_clusters: int = MAX_CLUSTERS
    ):
        self.product_filter = ProductFilter(
            min_clusters=min_clusters,
            max_clusters=max_clusters,
            similarity_threshold=similarity_threshold
        )
        self.similarity_threshold = similarity_threshold

    def _get_or_create_embeddings(self, products: List[Dict[str, Any]]) -> Dict[str, np.ndarray]:
        """Get existing embeddings or create new ones for each product field"""
        fields = ['title', 'handle', 'description']
        embeddings = {field: [] for field in fields}
        default_size = 300  # spaCy's default embedding size
        
        try:
            for product in products:
                for field in fields:
                    embedding_key = f"{field}_embedding"
                    embedding = None
                    
                    # Try to get existing embedding
                    if embedding_key in product and product[embedding_key] is not None:
                        if isinstance(product[embedding_key], str):
                            try:
                                # Convert string to numpy array
                                embedding = np.array([
                                    float(x) for x in product[embedding_key].strip('[]').split(',')
                                ], dtype=np.float32)
                            except (ValueError, AttributeError):
                                embedding = None
                        elif isinstance(product[embedding_key], (list, np.ndarray)):
                            embedding = np.array(product[embedding_key], dtype=np.float32)
                    
                    # Generate new embedding if needed
                    if embedding is None:
                        try:
                            embedding = generate_embeddings_using_spacy([product.get(field, '')])[0]
                        except Exception as e:
                            logger.warning(f"Failed to generate embedding for {field} in product {product.get('shopify_gid', 'unknown')}: {str(e)}")
                            embedding = np.zeros(default_size, dtype=np.float32)
                    
                    # Ensure embedding is the correct shape and size
                    if embedding is not None:
                        if embedding.ndim != 1:
                            logger.warning(f"Unexpected embedding shape for {field} in product {product.get('shopify_gid', 'unknown')}")
                            embedding = np.zeros(default_size, dtype=np.float32)
                        elif len(embedding) != default_size:
                            # Pad or truncate to match default size
                            if len(embedding) < default_size:
                                embedding = np.pad(embedding, (0, default_size - len(embedding)), mode='constant')
                            else:
                                embedding = embedding[:default_size]
                    else:
                        embedding = np.zeros(default_size, dtype=np.float32)
                    
                    embeddings[field].append(embedding)
            
            # Convert lists to numpy arrays, ensuring all have the same shape
            return {
                k: np.array(v, dtype=np.float32) 
                for k, v in embeddings.items()
            }
            
        except Exception as e:
            logger.error(f"Error in embedding processing: {str(e)}")
            raise ValueError(f"Failed to process embeddings: {str(e)}")

    async def create_collections(
        self,
        products: List[Dict[str, Any]],
        keywords: List[str],
        store_id: int,
        attributes: Optional[Dict[str, Dict[str, bool]]] = None,
        use_clustering: bool = True
    ) -> Dict[str, Any]:
        """Create collections using pre-filtered groups"""
        try:
            # Validate inputs
            if not products:
                raise ValueError("Products list cannot be empty")
            
            # Handle empty or None keywords
            if not keywords:
                logger.warning("No keywords provided, using default keywords")
                keywords = DEFAULT_GENERIC_KEYWORDS
            
            # Generate embeddings for keywords
            keyword_embeddings = generate_embeddings_using_spacy(keywords)
            
            # Filter products using selected method
            filter_result = self.product_filter.filter_products(
                products=products,
                keyword_embeddings=keyword_embeddings,
                use_clustering=use_clustering
            )
            
            # Prepare products for agent-based collection creation
            grouped_products = {}
            total_products = len(products)
            grouped_product_count = 0
            
            if use_clustering:
                # Group by cluster
                for idx, label in enumerate(filter_result.cluster_labels):
                    cluster_id = int(label)
                    if cluster_id not in grouped_products:
                        grouped_products[cluster_id] = {
                            "products": [],
                            "metadata": {
                                "group_type": "cluster",
                                "group_id": cluster_id
                            }
                        }
                    grouped_products[cluster_id]["products"].append(products[idx])
                    grouped_product_count += 1
            else:
                # Group by similarity threshold
                grouped_products = {
                    "similarity_group": {
                        "products": filter_result.products_list,
                        "metadata": {
                            "group_type": "similarity",
                            "similarity_scores": filter_result.similarity_scores
                        }
                    }
                }
                grouped_product_count = len(filter_result.products_list)
            
            # Check if we have enough groups or products
            min_groups_required = 2
            min_product_percentage = 0.35  # 35% of products should be grouped
            
            if (len(grouped_products) < min_groups_required or 
                grouped_product_count / total_products < min_product_percentage):
                
                logger.warning(
                    f"Insufficient grouping: {len(grouped_products)} groups, {grouped_product_count}/{total_products} products. "
                    f"Falling back to generic keywords."
                )
                
                # Try again with generic keywords
                generic_keyword_embeddings = generate_embeddings_using_spacy(DEFAULT_GENERIC_KEYWORDS)
                
                filter_result = self.product_filter.filter_products(
                    products=products,
                    keyword_embeddings=generic_keyword_embeddings,
                    use_clustering=use_clustering
                )
                
                # Reset and rebuild grouped products
                grouped_products = {}
                grouped_product_count = 0
                
                if use_clustering:
                    for idx, label in enumerate(filter_result.cluster_labels):
                        cluster_id = int(label)
                        if cluster_id not in grouped_products:
                            grouped_products[cluster_id] = {
                                "products": [],
                                "metadata": {
                                    "group_type": "cluster",
                                    "group_id": cluster_id
                                }
                            }
                        grouped_products[cluster_id]["products"].append(products[idx])
                        grouped_product_count += 1
                else:
                    grouped_products = {
                        "similarity_group": {
                            "products": filter_result.products_list,
                            "metadata": {
                                "group_type": "similarity",
                                "similarity_scores": filter_result.similarity_scores
                            }
                        }
                    }
                    grouped_product_count = len(filter_result.products_list)
                
                # Use the generic keywords for collection creation
                keywords = DEFAULT_GENERIC_KEYWORDS
                logger.info(f"After fallback: {len(grouped_products)} groups, {grouped_product_count}/{total_products} products")
            
            # Use agent-based collection creation with grouped products
            collections = await create_collections(
                keywords=keywords,
                products=products,
                store_id=store_id,
                grouped_products=grouped_products,
                attributes=attributes
            )
            
            return {
                "status": "success",
                "collections": collections.get('collections', []),
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "total_products": len(products),
                    "filtered_products": grouped_product_count,
                    "method": "clustering" if use_clustering else "similarity",
                    "keywords_used": keywords,
                    "grouping_metadata": {
                        "num_groups": len(grouped_products),
                        "group_sizes": {
                            gid: len(group["products"]) 
                            for gid, group in grouped_products.items()
                        }
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"Error in collection generation: {str(e)}", exc_info=True)
            raise
