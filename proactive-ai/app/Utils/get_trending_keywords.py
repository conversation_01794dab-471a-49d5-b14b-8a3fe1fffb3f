#app/Utils/get_trending_keywords.py
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime
import random
from .keywords_filtering import RuleEngine
try:
    from Logging.logger_config import get_logger
    from DataForSeo.dataforseo_service import DataForSEOService
    from Models.dbHelpers import (
        get_store_details,
        get_existing_keywords,
        create_keyword_record,
        update_keyword_usage,
        get_filtered_keywords,
        get_existing_keywords_self_origin,
        get_existing_keywords_competitor_origin
    )
except:
    from app.Logging.logger_config import get_logger
    from app.DataForSeo.dataforseo_service import DataForSEOService
    from app.Models.dbHelpers import (
        get_store_details,
        get_existing_keywords,
        create_keyword_record,
        update_keyword_usage,
        get_filtered_keywords,
        get_existing_keywords_self_origin,
        get_existing_keywords_competitor_origin
    )

logger = get_logger(__name__)

@dataclass
class Config:
    KEYWORDS_RULESET_FILE: Path = None

    def __init__(self, ruleset_file: str = None):
        if ruleset_file:
            self.KEYWORDS_RULESET_FILE = Path(ruleset_file)
            logger.info(f"Using ruleset file at: {ruleset_file}")
        else:
            self.KEYWORDS_RULESET_FILE = Path(__file__).parent / '../Config/default_keyword_filter_rulesets.yml'
            logger.info("Using default ruleset file")

        if not self.KEYWORDS_RULESET_FILE.exists():
            raise FileNotFoundError(f"Ruleset file not found at: {self.KEYWORDS_RULESET_FILE}")

class KeywordService:
    def __init__(self, config: Config):
        self.config = config
        self.rule_engine = RuleEngine(config.KEYWORDS_RULESET_FILE)
        self.dataforseo_service = DataForSEOService()

    # main method to process keyword requests based on different parameters
    async def process_keywords_request(
        self,
        store_id: int,
        competitors_domain: Optional[List[str]] = None,
        focus_keywords: Optional[List[str]] = None,
        force_new_generation: bool = False,
        generate_based_on_store_domain: bool = True,
        generate_based_on_competitors_domains: bool = False,
        generate_based_on_target_keywords: bool = False,
        limit: int = 12,
        refine_keywords: bool = True,
        keywords_to_refine: int = 5
    ) -> Dict[str, Any]:
        """Main method to process keyword requests based on different parameters."""
        try:
            logger.info("Processing keyword request with parameters: ", 
                {
                    "store_id": store_id,
                    "competitors_domain": competitors_domain,
                    "focus_keywords": focus_keywords,
                    "force_new_generation": force_new_generation,
                    "generate_based_on_store_domain": generate_based_on_store_domain,
                    "generate_based_on_competitors_domains": generate_based_on_competitors_domains,
                    "generate_based_on_target_keywords": generate_based_on_target_keywords,
                    "limit": limit,
                    "refine_keywords": refine_keywords,
                    "keywords_to_refine": keywords_to_refine
                }
            )
            store_details = await get_store_details(store_id)
            if not store_details:
                raise ValueError(f"Store not found with id: {store_id}")

            print(f"*********Store details for store {store_id}*********", store_details, generate_based_on_competitors_domains)

            # update competitors_domain if not provided
            if not competitors_domain:
                competitors_domain = store_details.get('competitors_domains', [])

            all_keywords = []
            sources_count = sum([
                generate_based_on_store_domain,
                generate_based_on_competitors_domains,
                generate_based_on_target_keywords
            ])
            keywords_per_source = limit // sources_count if sources_count > 0 else limit

            # Process store domain keywords
            if generate_based_on_store_domain:
                print(f"*********Generating keywords based on store domain*********")
                store_keywords = await self._process_store_keywords(
                    store_id,
                    store_details['url'],
                    force_new_generation,
                    keywords_per_source
                )
                all_keywords.extend(store_keywords)

            # Process competitor keywords
            if generate_based_on_competitors_domains and competitors_domain:
                print(f"*********Generating keywords based on competitors domains*********")
                competitor_keywords = await self._process_competitor_keywords(
                    store_id,
                    competitors_domain,
                    force_new_generation,
                    keywords_per_source
                )
                all_keywords.extend(competitor_keywords)

            # Process target keywords
            if generate_based_on_target_keywords and focus_keywords:
                print(f"*********Generating keywords based on target keywords*********")
                target_keywords = await self._process_target_keywords(
                    store_id,
                    focus_keywords,
                    force_new_generation,
                    keywords_per_source
                )
                all_keywords.extend(target_keywords)

            # Ensure we don't exceed the limit while maintaining balance
            final_keywords = self._balance_and_limit_keywords(all_keywords, limit)

            # If keyword refinement is requested
            if refine_keywords and final_keywords:
                from Agents.storeBlog.keyword_refiner import refine_keywords
                
                # Select keywords to refine
                keywords_for_refinement = random.sample(
                    final_keywords, 
                    min(keywords_to_refine, len(final_keywords))
                )
                
                # Get refined versions
                refined_results = await refine_keywords(
                    keywords_for_refinement,
                    required_count=limit
                )
                
                # Update the response to include both original and refined keywords
                return {
                    "keywords": final_keywords,
                    "total_keywords": len(final_keywords),
                    "refined_keywords": refined_results["refined_keywords"],
                    "refinement_metadata": refined_results["metadata"]
                }

            return {
                "keywords": final_keywords,
                "total_keywords": len(final_keywords)
            }

        except Exception as e:
            logger.error("Failed to process keywords request", exc_info=True)
            raise

    async def _process_store_keywords(
        self,
        store_id: int,
        store_url: str,
        force_new_generation: bool,
        limit: int
    ) -> List[str]:
        """Process keywords for store domain."""
        try:
            if not force_new_generation:
                # Check for existing keywords
                existing_keywords = await get_existing_keywords_self_origin(store_id)
                if existing_keywords:
                    available_keywords = existing_keywords[0].get('available_keywords', [])
                    if available_keywords:
                        selected_keywords = random.sample(available_keywords, min(limit, len(available_keywords)))
                        available_keywords = [k for k in available_keywords if k not in selected_keywords]
                        # update used keywords
                        await update_keyword_usage(existing_keywords[0]['id'], selected_keywords, available_keywords)
                        return selected_keywords

            # Fetch new keywords if needed
            results = await self.dataforseo_service.fetch_keywords_using_store_domain(
                store_url,
                [],
                self.config.KEYWORDS_RULESET_FILE
            )
            
            if results.get('keywords'):
                # Store new keywords in database
                await create_keyword_record(
                    store_id=store_id,
                    origin_type='self',
                    keywords=results['keywords'],
                    url=store_url.rstrip('/')
                )
                return random.sample(results['keywords'], min(limit, len(results['keywords'])))

            return []

        except Exception as e:
            logger.error(f"Error processing store keywords: {str(e)}")
            return []

    async def _process_competitor_keywords(
        self,
        store_id: int,
        competitors_domain: List[str],
        force_new_generation: bool,
        limit: int
    ) -> List[str]:
        """Process keywords for competitor domains."""
        logger.info("Processing competitor keywords", 
            {
                "store_id": store_id,
                "competitors_domain": competitors_domain,
                "force_new_generation": force_new_generation,
                "limit": limit
            }
        )
        all_competitor_keywords = []
        keywords_per_competitor = limit // len(competitors_domain) if competitors_domain else 0

        for competitor in competitors_domain:
            try:
                # Normalize URL by removing trailing slash
                normalized_url = competitor.rstrip('/')
                
                # First check database for existing keywords if not forcing new generation
                if not force_new_generation:
                    existing_keywords = await get_existing_keywords_competitor_origin(store_id, normalized_url)
                    logger.info("Database lookup result", 
                              competitor=normalized_url,
                              keywords_found=bool(existing_keywords))
                    
                    if existing_keywords and len(existing_keywords) > 0:
                        available_keywords = existing_keywords[0].get('available_keywords', [])
                        logger.info("Available keywords found", 
                                  count=len(available_keywords),
                                  needed=keywords_per_competitor)
                        
                        if available_keywords and len(available_keywords) >= keywords_per_competitor:
                            # If we have enough available keywords, use them
                            selected = random.sample(available_keywords, keywords_per_competitor)
                            all_competitor_keywords.extend(selected)
                            
                            # Update used keywords in database
                            new_available = [k for k in available_keywords if k not in selected]
                            await update_keyword_usage(
                                existing_keywords[0]['id'], 
                                selected, 
                                new_available
                            )
                            
                            logger.info("Using existing keywords from database", 
                                      selected_count=len(selected),
                                      remaining_count=len(new_available))
                            continue

                        logger.info("Not enough available keywords in database", 
                                  available=len(available_keywords),
                                  needed=keywords_per_competitor)
                    else:
                        logger.info("No existing keywords found in database")

                # Only fetch from DataForSEO if:
                # 1. force_new_generation is True, or
                # 2. No existing keywords found, or
                # 3. Not enough available keywords left
                logger.info("Fetching from DataForSEO", 
                          reason="force_new_generation" if force_new_generation 
                          else "no_existing_keywords" if not existing_keywords 
                          else "insufficient_keywords")
                
                results = await self.dataforseo_service.fetch_keywords_using_competitors_domain(
                    normalized_url,
                    self.config.KEYWORDS_RULESET_FILE
                )

                if results.get('keywords'):
                    await create_keyword_record(
                        store_id=store_id,
                        origin_type='competitor',
                        keywords=results['keywords'],
                        url=normalized_url  # Store normalized URL
                    )
                    selected = random.sample(
                        results['keywords'], 
                        min(keywords_per_competitor, len(results['keywords']))
                    )
                    all_competitor_keywords.extend(selected)

            except Exception as e:
                logger.error(f"Error processing competitor keywords for {competitor}: {str(e)}")
                continue

        return all_competitor_keywords

    async def _process_target_keywords(
        self,
        store_id: int,
        focus_keywords: List[str],
        force_new_generation: bool,
        limit: int
    ) -> List[str]:
        """Process keywords for target keywords."""
        all_target_keywords = []
        keywords_per_target = limit // len(focus_keywords) if focus_keywords else 0

        for keyword in focus_keywords:
            try:
                results = await self.dataforseo_service.fetch_keywords_using_target_keywords(
                    keyword,
                    self.config.KEYWORDS_RULESET_FILE
                )

                if results.get('keywords'):
                    await create_keyword_record(
                        store_id=store_id,
                        origin_type='target-keyword',
                        keywords=results['keywords'],
                        url=keyword.rstrip('/')
                    )
                    selected = random.sample(results['keywords'], min(keywords_per_target, len(results['keywords'])))
                    all_target_keywords.extend(selected)

            except Exception as e:
                logger.error(f"Error processing target keyword {keyword}: {str(e)}")
                continue

        return all_target_keywords

    def _balance_and_limit_keywords(self, keywords: List[str], limit: int) -> List[str]:
        """Balance and limit keywords from different sources."""
        # Remove duplicates while preserving order
        unique_keywords = list(dict.fromkeys(keywords))
        
        # If we have fewer keywords than the limit, return all
        if len(unique_keywords) <= limit:
            return unique_keywords
            
        # Otherwise, return a random sample up to the limit
        return random.sample(unique_keywords, limit)

    # get existing keywords from database
    async def _get_existing_keywords(
        self,
        store_id: int,
        competitors_domain: Optional[List[str]],
        generate_based_on_store_domain: bool,
        generate_based_on_competitors_domains: bool,
        limit: int
    ) -> Dict[str, Any]:
        """Retrieve and filter existing keywords from database."""
        try:
            # get existing keywords for self origin
            if generate_based_on_store_domain:
                existing_records = await get_existing_keywords_self_origin(
                    store_id
                )

            # get existing keywords for competitor origin
            if generate_based_on_competitors_domains:
                existing_records = {}
                for competitor in competitors_domain:
                    competitor_records = await get_existing_keywords_competitor_origin(
                        store_id,
                        competitor
                    )
                    existing_records[competitor] = competitor_records

            if not existing_records:
                return {"keywords": [], "total_keywords": 0}

            selected_keywords, updates_by_record = await get_filtered_keywords(existing_records, limit)

            if selected_keywords:
                # Update used keywords for each affected record
                for record_id, used_keywords in updates_by_record.items():
                    record = next(r for r in existing_records if r['id'] == record_id)
                    new_used = list(set(record['used_keywords'] + used_keywords))
                    new_available = [k for k in record['available_keywords'] if k not in new_used]
                    await update_keyword_usage(record_id, new_used, new_available)

                return {
                    "keywords": selected_keywords,
                    "total_keywords": len(selected_keywords)
                }

            return {"keywords": [], "total_keywords": 0}

        except Exception as e:
            logger.error("Failed to get existing keywords", exc_info=True)
            raise

    # generate new keywords
    async def _generate_new_keywords(
        self,
        store_id: int,
        store_details: Dict[str, Any],
        competitors_domain: Optional[List[str]],
        focus_keywords: Optional[List[str]],
        generate_based_on_store_domain: bool,
        generate_based_on_competitors_domains: bool,
        generate_based_on_target_keywords: bool,
        limit: int
    ) -> Dict[str, Any]:
        """Generate new keywords using DataForSEO service."""
        all_keywords = []
        errors = []

        try:
            if generate_based_on_store_domain and store_details['url']:
                logger.info("Generating keywords based on store domain", store_id=store_id, store_url=store_details['url'])
                store_domain = store_details['url'].replace('https://', '').replace('http://', '')
                store_keywords = await self._get_store_keywords(store_domain)
                if store_keywords:
                    await create_keyword_record(store_id, 'self', store_keywords, url=store_domain.rstrip('/'))
                    all_keywords.extend(store_keywords)

            if generate_based_on_competitors_domains:
                competitor_urls = competitors_domain or store_details.get('competitors_domains', [])
                for competitor in competitor_urls:
                    competitor_keywords = await self._get_competitor_keywords(competitor)
                    if competitor_keywords:
                        await create_keyword_record(store_id, 'competitor', competitor_keywords, url=competitor.rstrip('/'))
                        all_keywords.extend(competitor_keywords)

            if generate_based_on_target_keywords and focus_keywords:
                logger.info("Generating keywords based on target keywords", store_id=store_id, focus_keywords=focus_keywords)
                for keyword in focus_keywords:
                    target_keywords = await self._get_target_keywords(keyword)
                    if target_keywords:
                        await create_keyword_record(store_id, 'target-keyword', target_keywords, url=keyword.rstrip('/'))
                        all_keywords.extend(target_keywords)

            if not all_keywords:
                return {"keywords": [], "total_keywords": 0}

            # Remove duplicates and limit results
            selected_keywords = list(set(all_keywords))[:limit]
            return {
                "keywords": selected_keywords,
                "total_keywords": len(selected_keywords)
            }

        except Exception as e:
            logger.error("Failed to generate new keywords", exc_info=True)
            raise

    async def _get_existing_keywords(
        self,
        store_id: int,
        competitors_domain: Optional[List[str]],
        generate_based_on_store_domain: bool,
        generate_based_on_competitors_domains: bool,
        limit: int
    ) -> Dict[str, Any]:
        """Retrieve and filter existing keywords from database with fallback to new generation."""
        try:
            existing_records = await get_existing_keywords(
                store_id,
                generate_based_on_store_domain,
                generate_based_on_competitors_domains,
                competitors_domain
            )

            if not existing_records:
                logger.info("No existing records found, falling back to new generation", 
                          store_id=store_id)
                return await self._generate_new_keywords_fallback(
                    store_id,
                    competitors_domain,
                    generate_based_on_store_domain,
                    generate_based_on_competitors_domains,
                    limit
                )

            selected_keywords, updates_by_record = await get_filtered_keywords(existing_records, limit)

            if not selected_keywords:
                logger.info("All existing keywords have been used, falling back to new generation", 
                          store_id=store_id)
                return await self._generate_new_keywords_fallback(
                    store_id,
                    competitors_domain,
                    generate_based_on_store_domain,
                    generate_based_on_competitors_domains,
                    limit
                )

            # Update used keywords for each affected record
            for record_id, used_keywords in updates_by_record.items():
                record = next(r for r in existing_records if r['id'] == record_id)
                new_used = list(set(record['used_keywords'] + used_keywords))
                new_available = [k for k in record['available_keywords'] if k not in new_used]
                await update_keyword_usage(record_id, new_used, new_available)

            return {
                "keywords": selected_keywords,
                "total_keywords": len(selected_keywords)
            }

        except Exception as e:
            logger.error("Failed to get existing keywords", exc_info=True)
            raise

    async def _get_store_keywords(self, store_url: str) -> List[str]:
        """Get and filter keywords based on store URL."""
        try:
            logger.info("Fetching keywords using store domain", store_url=store_url)
            results = await self.dataforseo_service.fetch_keywords_using_store_domain(store_url, [])
            logger.info("Filtering store keywords", store_url=store_url, results=results)
            
            # Check if results is a dictionary before trying to access 'keywords'
            if isinstance(results, dict):
                return results.get('keywords', [])
            elif isinstance(results, list):
                return results  # If it's already a list of keywords
            elif isinstance(results, str):
                logger.warning(f"Unexpected string result from dataforseo_service: {results}")
                return []  # Return empty list if results is a string
            else:
                logger.warning(f"Unexpected result type from dataforseo_service: {type(results)}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting store keywords: {str(e)}")
            return []

    # get competitor keywords - competitor origin
    async def _get_competitor_keywords(self, competitor_url: str) -> List[str]:
        """Get and filter keywords based on competitor URL."""
        try:
            logger.info("Fetching keywords using competitor domain", competitor_url=competitor_url)
            results = await self.dataforseo_service.fetch_keywords_using_competitors_domain(competitor_url)
            logger.info("Filtering competitor keywords", competitor_url=competitor_url)
            
            # Check if results is a dictionary before trying to access 'keywords'
            if isinstance(results, dict):
                return results.get('keywords', [])
            elif isinstance(results, list):
                return results  # If it's already a list of keywords
            elif isinstance(results, str):
                logger.warning(f"Unexpected string result from dataforseo_service: {results}")
                return []  # Return empty list if results is a string
            else:
                logger.warning(f"Unexpected result type from dataforseo_service: {type(results)}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting competitor keywords: {str(e)}")
            return []

    # get target keywords - target keyword origin
    async def _get_target_keywords(self, focus_keyword: str) -> List[str]:
        """Get and filter keywords based on target keyword."""
        try:
            logger.info("Fetching keywords using target keyword", focus_keyword=focus_keyword)
            results = await self.dataforseo_service.fetch_keywords_using_target_keywords(focus_keyword)
            logger.info("Filtering target keywords", focus_keyword=focus_keyword)
            
            # Check if results is a dictionary before trying to access 'keywords'
            if isinstance(results, dict):
                return results.get('keywords', [])
            elif isinstance(results, list):
                return results  # If it's already a list of keywords
            elif isinstance(results, str):
                logger.warning(f"Unexpected string result from dataforseo_service: {results}")
                return []  # Return empty list if results is a string
            else:
                logger.warning(f"Unexpected result type from dataforseo_service: {type(results)}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting target keywords: {str(e)}")
            return []