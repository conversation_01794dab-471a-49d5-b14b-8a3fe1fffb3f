import autogen
import yaml
import json
from datetime import datetime
from typing import List, Dict, Any
from Logging.logger_config import get_logger
from Agents.lib.clients.portkey_client import get_portkey_llm_config
from Models.dbHelpers import get_store_uuid
from .sitemap_processor import SitemapProcessor

logger = get_logger()

async def load_prompt_config(file_path: str) -> dict:
    """Load and validate the prompt configuration from YAML file."""
    logger.debug(f"Attempting to load prompt config from: {file_path}")
    try:
        with open(file_path, 'r') as file:
            config = yaml.safe_load(file)
            logger.info("Successfully loaded prompt configuration")
            return config
    except Exception as e:
        logger.error(f"Failed to load prompt configuration: {str(e)}", exc_info=True)
        raise

class CompetitorAnalysisTracker:
    """Utility class to track and log competitor analysis process."""
    
    def __init__(self):
        self.logger = get_logger()
        self.analysis_count = 0
        self.phase_completion = {"phase_1": False, "phase_2": False, "phase_3": False}
        
    def log_message(self, sender: str, recipient: str, message: str):
        """Log a message exchange."""
        self.analysis_count += 1
        self.logger.info(f"Message {self.analysis_count} | From: {sender} -> To: {recipient}")
        self.logger.debug(f"Message content: {message[:500]}...")
        
    def mark_phase_complete(self, phase: int):
        """Mark a specific phase as complete."""
        self.phase_completion[f"phase_{phase}"] = True
        
    def all_phases_complete(self) -> bool:
        """Check if all phases are complete."""
        return all(self.phase_completion.values())
        
    def log_error(self, error: Exception, context: str):
        """Log an error with context."""
        self.logger.error(f"Error in {context}: {str(error)}", exc_info=True)

def extract_json_response(message: str, context: str) -> Dict:
    """Extract and validate JSON from agent response."""
    try:
        if "```json" in message:
            start = message.find('{')
            end = message.rfind('}') + 1
            json_str = message[start:end]
            return json.loads(json_str)
        else:
            raise ValueError(f"No valid JSON data found in {context} response")
    except json.JSONDecodeError:
        logger.error(f"Failed to parse JSON from {context} message")
        raise ValueError(f"Invalid JSON format in {context} response")

def setup_competitor_analysis_groupchat(agents: List[autogen.AssistantAgent], portkey_config: List[Dict[str, Any]], tracker: CompetitorAnalysisTracker):
    """Set up the group chat with analysis workflow."""
    logger.info("Setting up competitor analysis group chat...")
    try:
        groupchat = autogen.GroupChat(
            agents=agents,
            messages=[],
            max_round=10
        )

        def is_termination_msg(message: Dict[str, Any]) -> bool:
            """Detect workflow completion with phase tracking."""
            content = message.get("content", "")
            
            # Track phase completion
            if "PRODUCT_ANALYSIS_COMPLETE" in content.upper():
                tracker.mark_phase_complete(1)
            elif "KEYWORD_GENERATION_COMPLETE" in content.upper():
                tracker.mark_phase_complete(2)
            elif "STRATEGY_CONSOLIDATION_COMPLETE" in content.upper():
                tracker.mark_phase_complete(3)
            
            # Check for final completion
            if "ANALYSIS_WORKFLOW_COMPLETE" in content.upper() and tracker.all_phases_complete():
                try:
                    if "```json" in content:
                        json_start = content.find('```json\n') + 7
                        json_end = content.rfind('```')
                        json_str = content[json_start:json_end].strip()
                        json.loads(json_str)  # Validate JSON
                        return True
                except json.JSONDecodeError:
                    logger.error("Invalid JSON in termination message")
                    return False
            return False
        
        manager = autogen.GroupChatManager(
            groupchat=groupchat,
            llm_config={"config_list": portkey_config},
            is_termination_msg=is_termination_msg
        )
        
        logger.info("Successfully set up competitor analysis group chat and manager")
        return manager, groupchat
        
    except Exception as e:
        logger.error("Failed to set up competitor analysis group chat", exc_info=True)
        raise

async def analyze_competitor(
    competitor_urls: List[str],
    store_id: int = None
) -> Dict[str, Any]:
    """
    Analyze competitor websites using AI agents.
    
    Args:
        competitor_urls: List of competitor website URLs to analyze
        store_id: Optional store ID for tracking
        
    Returns:
        Dictionary containing analysis results and recommendations
    """
    logger = get_logger()
    try:
        # Get store uuid for tracing
        store_uuid = await get_store_uuid(store_id)
        
        # Load configurations
        logger.info("Loading configurations...")
        # prompt = await load_prompt_config('Agents/lib/prompts/storeBlog/keyword_analysis.yml') 
        prompt = await load_prompt_config('Agents/lib/prompts/storeBlog/competitor_analysis.yml') #TO-DO : Change this to dynamic - RC
        portkey_config = get_portkey_llm_config(store_uuid=store_uuid)

        # Create message tracker
        tracker = CompetitorAnalysisTracker()

        # Process sitemaps first
        processor = SitemapProcessor(competitor_urls)
        product_urls = await processor.process_all_sites()

        if not product_urls:
            logger.error("No product URLs found. Please check the sitemap processing.")
            return {"status": "error", "message": "No product URLs found."}
        
           # Convert sets to lists in product_urls
        serializable_urls = {
            domain: list(urls) for domain, urls in product_urls.items()
        }


        # Create all required agents
        product_analyzer = autogen.AssistantAgent(
            name="product_analyzer",
            system_message=prompt['system_message']['Product_Analyzer']['objectives'],
            llm_config={"config_list": portkey_config}
        )

        keyword_generator = autogen.AssistantAgent(
            name="keyword_generator",
            system_message=prompt['system_message']['Keyword_Generator']['objectives'],
            llm_config={"config_list": portkey_config}
        )

        strategy_consolidator = autogen.AssistantAgent(
            name="strategy_consolidator",
            system_message=prompt['system_message']['Strategy_Consolidator']['objectives'],
            llm_config={"config_list": portkey_config}
        )

        analysis_manager = autogen.AssistantAgent(
            name="analysis_manager",
            system_message=prompt['system_message']['Manager']['objectives'],
            llm_config={"config_list": portkey_config}
        )

        # Create agent list for group chat
        agents = [
            product_analyzer,
            keyword_generator,
            strategy_consolidator,
            analysis_manager
        ]

        # Setup group chat with structured workflow
        manager, groupchat = setup_competitor_analysis_groupchat(agents, portkey_config, tracker)

        try:
            # Prepare initial workflow message
            initial_message = {
                "task": "analyze_competitors",
                "workflow_steps": [
                    {
                        "phase": 1,
                        "agent": "product_analyzer",
                        "action": "analyze_products",
                        "input": {
                            "product_urls": serializable_urls,
                        },
                        "output_marker": "PRODUCT_ANALYSIS_COMPLETE"
                    },
                    {
                        "phase": 2,
                        "agent": "keyword_generator",
                        "action": "generate_keywords",
                        "output_marker": "KEYWORD_GENERATION_COMPLETE"
                    },
                    {
                        "phase": 3,
                        "agent": "strategy_consolidator",
                        "action": "consolidate_strategy",
                        "output_marker": "STRATEGY_CONSOLIDATION_COMPLETE"
                    }
                ]
            }

            logger.info("Initiating competitor analysis workflow")
            tracker.log_message("System", "analysis_manager", json.dumps(initial_message))

            # Start the group chat with the workflow message
            chat_reply = manager.initiate_chat(
                analysis_manager,
                message=json.dumps(initial_message)
            )

            # Get the final message from chat history
            messages = groupchat.messages
            if not messages:
                raise ValueError("No messages found in chat history")

            last_message = messages[-1]["content"]
            
            if "```json" in last_message:
                start = last_message.find('{')
                end = last_message.rfind('}') + 1
                json_str = last_message[start:end]
                
                try:
                    analysis_data = json.loads(json_str)
                except json.JSONDecodeError:
                    logger.error("Failed to parse JSON from final message")
                    raise ValueError("Invalid JSON format in completion message")

            return {
                "status": "success",
                "data": analysis_data,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            tracker.log_error(e, "competitor analysis")
            raise

    except Exception as e:
        logger.error("Fatal error in competitor analysis", exc_info=True)
        raise



async def analyze_competitor_for_keyword(
    competitor_urls: List[str],
    store_id: int = None
) -> Dict[str, Any]:
    """
    Analyze competitor websites using AI agents.
    
    Args:
        competitor_urls: List of competitor website URLs to analyze
        store_id: Optional store ID for tracking
        
    Returns:
        Dictionary containing analysis results and recommendations
    """
    logger = get_logger()
    try:
        # Get store uuid for tracing
        store_uuid = await get_store_uuid(store_id)
        
        # Load configurations
        logger.info("Loading configurations...")
        prompt = await load_prompt_config('Agents/lib/prompts/storeBlog/keyword_analysis.yml')  #TO-DO : Change this to dynamic - RC
        # prompt = await load_prompt_config('Agents/lib/prompts/storeBlog/competitor_analysis.yml')
        portkey_config = get_portkey_llm_config(store_uuid=store_uuid)

        # Create message tracker
        tracker = CompetitorAnalysisTracker()

        # Process sitemaps first
        processor = SitemapProcessor(competitor_urls)
        product_urls = await processor.process_all_sites()

        if not product_urls:
            logger.error("No product URLs found. Please check the sitemap processing.")
            return {"status": "error", "message": "No product URLs found."}
        
           # Convert sets to lists in product_urls
        serializable_urls = {
            domain: list(urls) for domain, urls in product_urls.items()
        }


        # Create all required agents
        product_analyzer = autogen.AssistantAgent(
            name="product_analyzer",
            system_message=prompt['system_message']['Product_Analyzer']['objectives'],
            llm_config={"config_list": portkey_config}
        )

        keyword_generator = autogen.AssistantAgent(
            name="keyword_generator",
            system_message=prompt['system_message']['Keyword_Generator']['objectives'],
            llm_config={"config_list": portkey_config}
        )

        strategy_consolidator = autogen.AssistantAgent(
            name="strategy_consolidator",
            system_message=prompt['system_message']['Strategy_Consolidator']['objectives'],
            llm_config={"config_list": portkey_config}
        )

        analysis_manager = autogen.AssistantAgent(
            name="analysis_manager",
            system_message=prompt['system_message']['Manager']['objectives'],
            llm_config={"config_list": portkey_config}
        )

        # Create agent list for group chat
        agents = [
            product_analyzer,
            keyword_generator,
            strategy_consolidator,
            analysis_manager
        ]

        # Setup group chat with structured workflow
        manager, groupchat = setup_competitor_analysis_groupchat(agents, portkey_config, tracker)

        try:
            # Prepare initial workflow message
            initial_message = {
                "task": "analyze_competitors",
                "workflow_steps": [
                    {
                        "phase": 1,
                        "agent": "product_analyzer",
                        "action": "analyze_products",
                        "input": {
                            "product_urls": serializable_urls,
                        },
                        "output_marker": "PRODUCT_ANALYSIS_COMPLETE"
                    },
                    {
                        "phase": 2,
                        "agent": "keyword_generator",
                        "action": "generate_keywords",
                        "output_marker": "KEYWORD_GENERATION_COMPLETE"
                    },
                    {
                        "phase": 3,
                        "agent": "strategy_consolidator",
                        "action": "consolidate_strategy",
                        "output_marker": "STRATEGY_CONSOLIDATION_COMPLETE"
                    }
                ]
            }

            logger.info("Initiating competitor analysis workflow")
            tracker.log_message("System", "analysis_manager", json.dumps(initial_message))

            # Start the group chat with the workflow message
            chat_reply = manager.initiate_chat(
                analysis_manager,
                message=json.dumps(initial_message)
            )

            # Get the final message from chat history
            messages = groupchat.messages
            if not messages:
                raise ValueError("No messages found in chat history")

            last_message = messages[-1]["content"]
            
            if "```json" in last_message:
                start = last_message.find('{')
                end = last_message.rfind('}') + 1
                json_str = last_message[start:end]
                
                try:
                    analysis_data = json.loads(json_str)
                except json.JSONDecodeError:
                    logger.error("Failed to parse JSON from final message")
                    raise ValueError("Invalid JSON format in completion message")

            return {
                "status": "success",
                "data": analysis_data,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            tracker.log_error(e, "competitor analysis")
            raise

    except Exception as e:
        logger.error("Fatal error in competitor analysis", exc_info=True)
        raise