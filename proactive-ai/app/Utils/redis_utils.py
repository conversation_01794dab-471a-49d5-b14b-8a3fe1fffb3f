import redis
try:
    from app.Logging.logger_config import get_logger
except:
    from Logging.logger_config import get_logger


logger = get_logger(__name__)

# Redis Configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0  # For task status tracking

def get_redis_client():
    try:
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            decode_responses=True
        )
        redis_client.ping()
        return redis_client
    except redis.ConnectionError as e:
        logger.error(f"Failed to connect to Redis: {e}")
        logger.error("Task status tracking will be unavailable - Redis DB DOWN")
        return None

redis_client = get_redis_client() 
