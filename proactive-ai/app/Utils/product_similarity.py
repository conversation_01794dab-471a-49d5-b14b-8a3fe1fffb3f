
from typing import List, Dict, Optional, <PERSON><PERSON>
import numpy as np
from Utils.embedding_creator import generate_embeddings_using_spacy

INITIAL_SIMILARITY_THRESHOLD = 0.5
FALLBACK_SIMILARITY_THRESHOLD = 0.25

WEIGHTS = {
    'keywords': 0.4,
    'title': 0.3,
    'article_type': 0.3
}

def calculate_weighted_embedding(
    keywords: List[str],
    article_type: str,
    title: Optional[str] = None
) -> np.ndarray:
    """Calculate weighted embedding from input elements."""
    embeddings = []
    weights = []
    
    if keywords:
        keyword_embedding = generate_embeddings_using_spacy(" ".join(keywords))
        embeddings.append(keyword_embedding)
        weights.append(WEIGHTS['keywords'])
    
    if title:
        title_embedding = generate_embeddings_using_spacy(title)
        embeddings.append(title_embedding)
        weights.append(WEIGHTS['title'])
    
    article_embedding = generate_embeddings_using_spacy(article_type)
    embeddings.append(article_embedding)
    weights.append(WEIGHTS['article_type'])
    
    weights = np.array(weights) / sum(weights)
    return np.average(embeddings, weights=weights, axis=0)

def calculate_similarity(embedding1: np.ndarray, embedding2: np.ndarray) -> float:
    """Calculate cosine similarity between embeddings."""
    norm1 = np.linalg.norm(embedding1)
    norm2 = np.linalg.norm(embedding2)
    
    if norm1 == 0 or norm2 == 0:
        return 0.0
    
    embedding1_normalized = embedding1 / norm1
    embedding2_normalized = embedding2 / norm2
    return float(np.dot(embedding1_normalized, embedding2_normalized))

def generate_missing_embeddings(product: Dict) -> Dict[str, List[float]]:
    """Generate missing embeddings for a product."""
    embeddings = {}
    
    if not product.get('title_embedding') and product.get('title'):
        embeddings['title_embedding'] = generate_embeddings_using_spacy(product['title']).tolist()
        
    if not product.get('description_embedding') and product.get('description'):
        embeddings['description_embedding'] = generate_embeddings_using_spacy(product['description']).tolist()
        
    if not product.get('handle_embedding') and product.get('handle'):
        embeddings['handle_embedding'] = generate_embeddings_using_spacy(product['handle']).tolist()
        
    return embeddings

def find_matches(
    products: List[Dict],
    input_embedding: np.ndarray,
    limit: int
) -> Tuple[List[Dict], bool, int]:
    """Find matching products based on similarity."""
    matches = []
    random_products_added = 0
    threshold = INITIAL_SIMILARITY_THRESHOLD
    
    while len(matches) < limit and threshold >= FALLBACK_SIMILARITY_THRESHOLD:
        for product in products:
            if any(m["gid"] == product['shopify_gid'] for m in matches):
                continue
                
            embeddings = []
            for emb in [product.get('title_embedding'), product.get('description_embedding'), product.get('handle_embedding')]:
                if emb is not None:
                    # Handle string representation of array
                    if isinstance(emb, str):
                        emb = eval(emb)
                    embeddings.append(np.array(emb, dtype=np.float32))
            if not embeddings:
                continue
            product_embedding = np.mean(embeddings, axis=0)
            
            similarity = calculate_similarity(input_embedding, product_embedding)
            if similarity >= threshold:
                matches.append({
                    "gid": product['shopify_gid'],
                    "title": product['title'],
                    "image_url": product.get('image_url'),
                    "product_url": product.get('product_url'),
                    "similarity_score": float(similarity)
                })
                
            if len(matches) >= limit:
                break
                
        threshold -= 0.1

    return matches, len(matches) < limit, random_products_added