# Utils/sync_products.py
import numpy as np
from typing import List, Dict, Any
try:
    from app.Models.dbHelpers import insert_data_into_products_table
    from app.Utils.embedding_creator import generate_embeddings_using_spacy
    from app.Config.feature_flags import product_sync
    from app.Logging.logger_config import get_logger
except ImportError:
    from Models.dbHelpers import insert_data_into_products_table
    from Utils.embedding_creator import generate_embeddings_using_spacy
    from Config.feature_flags import product_sync
    from Logging.logger_config import get_logger

logger = get_logger(__name__)

async def sync_all_products(products: List[Dict[str, Any]], store_id: int) -> Dict[str, Any]:
    """
    Insert product data from payload into products table
    
    Args:
        products (List[Dict]): List of product dictionaries
        store_id (int): ID of the store this product belongs to
    
    Returns:
        Dict[str, Any]: Response containing sync results and products processed
    """
    logger.info(f"Syncing {len(products)} products to database")
    sync_results = []
    embedding_errors = []
    
    for product in products:
        try:
            variants = product.get('variants', [])
            embeddings = {}
            
            if product_sync['create_product_embeddings_while_syncing']:
                for field in ['description', 'handle', 'title']:
                    try:
                        text = product[field]
                        if text and text.strip():
                            embedding = generate_embeddings_using_spacy(text)
                            if isinstance(embedding, np.ndarray):
                                embeddings[f'{field}_embedding'] = embedding.tolist()
                        else:
                            embeddings[f'{field}_embedding'] = None
                    except Exception as e:
                        logger.error(f"Error creating embedding for {field} in product {product['id']}: {str(e)}")
                        embedding_errors.append({
                            'product_id': product['id'],
                            'field': field,
                            'error': str(e)
                        })
                        embeddings[f'{field}_embedding'] = None

            product_data = {
                'title': product['title'],
                'description': product['description'],
                'handle': product['handle'], 
                'tags': product['tags'],
                'vendor': product['vendor'],
                'store_id': store_id,
                'shopify_gid': product['id'],
                'variants': variants,
                'price': float(variants[0]['price']) if variants else None,
                'product_created_at': product['createdAt'],
                'image_url': product.get('image_url'),
                'product_url': product.get('product_url'),
                **embeddings
            }

            result = await insert_data_into_products_table(product_data)
            sync_results.append(result)

        except Exception as e:
            logger.error(f"Error processing product {product.get('id', 'unknown')}: {str(e)}")
            sync_results.append({
                'error': str(e),
                'product_id': product.get('id', 'unknown')
            })

    return {
        "sync_results": sync_results,
        "products_processed": len(products),
        "embedding_errors": embedding_errors if embedding_errors else None
    }