import sys
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import asyncio
import json

# Add project root to Python path
project_root = str(Path(__file__).resolve().parents[2])
if project_root not in sys.path:
    sys.path.append(project_root)

from app.Logging.logger_config import get_logger
from app.Models.dbHelpers import insert_data_into_collections_table

logger = get_logger(__name__)

async def sync_all_collections(collections: List[Dict[str, Any]], store_id: int) -> Dict[str, Any]:
    """
    Sync all collections from Shopify to the database
    
    Args:
        collections: List of collection dictionaries from Shopify
        store_id: Store ID
        
    Returns:
        Dict containing sync results and any errors
    """
    sync_results = []
    errors = []
    
    try:
        logger.info(
            "Starting collection sync process",
            extra={
                "store_id": store_id,
                "total_collections": len(collections)
            }
        )
        
        for collection in collections:
            try:
                # Prepare collection data for database
                collection_data = {
                    'shopify_gid': collection.get('id', ''),
                    'title': collection.get('title', ''),
                    'description': collection.get('description', ''),
                    'products_data': collection.get('products', []),  # Store as a list instead of JSON
                    'store_id': store_id
                }

                # print("collection_data############", collection_data)
                
                # Insert or update collection in database
                result = await insert_data_into_collections_table(collection_data)
                sync_results.append(result)
                
                logger.info(
                    f"Processed collection: {collection.get('title', 'Unknown')}",
                    extra={
                        "store_id": store_id,
                        "collection_id": collection.get('id'),
                        "result": result
                    }
                )
                
            except Exception as e:
                error_msg = f"Error processing collection {collection.get('title', 'Unknown')}: {str(e)}"
                errors.append(error_msg)
                logger.error(
                    error_msg,
                    extra={
                        "store_id": store_id,
                        "collection_id": collection.get('id'),
                        "error": str(e)
                    },
                    exc_info=True
                )
                continue
        
        # Log completion
        successful_syncs = sum(1 for result in sync_results if "Collection Sync Done" in result)
        already_synced = sum(1 for result in sync_results if "Collection Sync Done - Already" in result)
        
        logger.info(
            "Collection sync process completed",
            extra={
                "store_id": store_id,
                "total_collections": len(collections),
                "successful_syncs": successful_syncs,
                "already_synced": already_synced,
                "errors": len(errors)
            }
        )
        
        return {
            "sync_results": sync_results,
            "errors": errors
        }
        
    except Exception as e:
        logger.error(
            "Fatal error in collection sync process",
            extra={
                "store_id": store_id,
                "error": str(e)
            },
            exc_info=True
        )
        raise 