import sys
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import asyncio

# Add project root to Python path
project_root = str(Path(__file__).resolve().parents[2])
if project_root not in sys.path:
    sys.path.append(project_root)

from app.Logging.logger_config import get_logger
from app.Models.dbHelpers import insert_data_into_blogs_table

logger = get_logger(__name__)

async def sync_all_blogs(blogs: List[Dict[str, Any]], store_id: int) -> Dict[str, Any]:
    """
    Sync all blogs from Shopify to the database
    
    Args:
        blogs: List of blog dictionaries from Shopify
        store_id: Store ID
        
    Returns:
        Dict containing sync results and any errors
    """
    sync_results = []
    errors = []
    
    try:
        logger.info(
            "Starting blog sync process",
            extra={
                "store_id": store_id,
                "total_blogs": len(blogs)
            }
        )
        
        for blog in blogs:
            try:
                # Prepare blog data for database
                blog_data = {
                    'shopify_gid': blog['id'],
                    'handle': blog['handle'],
                    'title': blog['title'],
                    'comment_policy': blog.get('comment_policy'),
                    'feed': blog.get('feed'),
                    'template_suffix': blog.get('template_suffix'),
                    'tags': blog.get('tags', []),
                    'blog_created_at': blog.get('blog_created_at'),
                    'blog_updated_at': blog.get('blog_updated_at'),
                    'store_id': store_id
                }
                
                # Insert or update blog in database
                result = await insert_data_into_blogs_table(blog_data)
                sync_results.append(result)
                
                logger.info(
                    f"Processed blog: {blog['title']}",
                    extra={
                        "store_id": store_id,
                        "blog_id": blog['id'],
                        "result": result
                    }
                )
                
            except Exception as e:
                error_msg = f"Error processing blog {blog.get('title', 'Unknown')}: {str(e)}"
                errors.append(error_msg)
                logger.error(
                    error_msg,
                    extra={
                        "store_id": store_id,
                        "blog_id": blog.get('id'),
                        "error": str(e)
                    },
                    exc_info=True
                )
                continue
        
        # Log completion
        successful_syncs = sum(1 for result in sync_results if "Blog Sync Done" in result)
        already_synced = sum(1 for result in sync_results if "Blog Sync Done - Already" in result)
        
        logger.info(
            "Blog sync process completed",
            extra={
                "store_id": store_id,
                "total_blogs": len(blogs),
                "successful_syncs": successful_syncs,
                "already_synced": already_synced,
                "errors": len(errors)
            }
        )
        
        return {
            "sync_results": sync_results,
            "errors": errors
        }
        
    except Exception as e:
        logger.error(
            "Fatal error in blog sync process",
            extra={
                "store_id": store_id,
                "error": str(e)
            },
            exc_info=True
        )
        raise