import sys
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import asyncio
import json

# Add project root to Python path
project_root = str(Path(__file__).resolve().parents[2])
if project_root not in sys.path:
    sys.path.append(project_root)

from app.Logging.logger_config import get_logger
from app.Models.dbHelpers import insert_data_into_articles_table, get_blog_id_by_shopify_gid

logger = get_logger(__name__)

async def sync_all_articles(articles: List[Dict[str, Any]], store_id: int) -> Dict[str, Any]:
    """
    Sync all articles from Shopify to the database
    
    Args:
        articles: List of article dictionaries from Shopify
        store_id: Store ID
        
    Returns:
        Dict containing sync results and any errors
    """
    sync_results = []
    errors = []
    
    try:
        logger.info(
            "Starting article sync process",
            extra={
                "store_id": store_id,
                "total_articles": len(articles)
            }
        )
        
        for article in articles:
            try:
                # Get the blog_id from synced_blogs table
                blog_id = await get_blog_id_by_shopify_gid(article['blog_id'])
                
                if not blog_id:
                    error_msg = f"Blog not found for article {article.get('title', 'Unknown')}"
                    errors.append(error_msg)
                    logger.error(
                        error_msg,
                        extra={
                            "store_id": store_id,
                            "article_id": article.get('id'),
                            "blog_id": article.get('blog_id')
                        }
                    )
                    continue
                
                # Prepare article data for database
                article_data = {
                    'shopify_gid': article['id'],
                    'handle': article['handle'],
                    'title': article['title'],
                    'body': article['body'],
                    'author_name': article['author_name'],
                    'blog_id': blog_id,
                    'article_created_at': article['article_created_at'],
                    'article_published_at': article['article_published_at'],
                    'image_data': article['image_data'],
                    'store_id': store_id
                }
                
                # Insert or update article in database
                result = await insert_data_into_articles_table(article_data)
                sync_results.append(result)
                
                logger.info(
                    f"Processed article: {article['title']}",
                    extra={
                        "store_id": store_id,
                        "article_id": article['id'],
                        "result": result
                    }
                )
                
            except Exception as e:
                error_msg = f"Error processing article {article.get('title', 'Unknown')}: {str(e)}"
                errors.append(error_msg)
                logger.error(
                    error_msg,
                    extra={
                        "store_id": store_id,
                        "article_id": article.get('id'),
                        "error": str(e)
                    },
                    exc_info=True
                )
                continue
        
        # Log completion
        successful_syncs = sum(1 for result in sync_results if "Article Sync Done" in result)
        already_synced = sum(1 for result in sync_results if "Article Sync Done - Already" in result)
        
        logger.info(
            "Article sync process completed",
            extra={
                "store_id": store_id,
                "total_articles": len(articles),
                "successful_syncs": successful_syncs,
                "already_synced": already_synced,
                "errors": len(errors)
            }
        )
        
        return {
            "sync_results": sync_results,
            "errors": errors
        }
        
    except Exception as e:
        logger.error(
            "Fatal error in article sync process",
            extra={
                "store_id": store_id,
                "error": str(e)
            },
            exc_info=True
        )
        raise 