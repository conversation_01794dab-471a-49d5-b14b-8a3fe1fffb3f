# keyword_filtering.py
import yaml
import operator
from typing import Dict, List, Any, Union
from datetime import datetime
from pathlib import Path
from Logging.logger_config import get_logger

logger = get_logger()

class RuleEngineError(Exception):
    pass

class RuleEngine:
    def __init__(self, rules_file: Union[str, Path]):
        self.operators = {
            'greater_than': operator.gt,
            'less_than': operator.lt,
            'equals': operator.eq,
            'not_equals': operator.ne,
            'greater_equals': operator.ge,
            'less_equals': operator.le,
            'contains': lambda x, y: y in x,
            'starts_with': str.startswith,
            'ends_with': str.endswith
        }
        
        try:
            with open(rules_file, 'r') as f:
                self.rules = yaml.safe_load(f)
            logger.info("Successfully loaded rules file", rules_file=str(rules_file))
        except Exception as e:
            logger.error("Failed to load rules file", rules_file=str(rules_file), error=str(e))
            raise RuleEngineError(f"Failed to load rules file: {str(e)}")

    def _get_field_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """Extract nested field value using dot notation."""
        current = data
        for key in field_path.split('.'):
            if isinstance(current, dict):
                current = current.get(key)
            else:
                return None
        return current

    def _convert_value(self, value: str, value_type: str) -> Any:
        """Convert string values to appropriate Python types."""
        try:
            if value_type == 'datetime':
                return datetime.strptime(value, '%Y-%m-%d')
            elif value_type == 'float':
                return float(value)
            elif value_type == 'int':
                return int(value)
            return value
        except ValueError as e:
            logger.error("Value conversion failed", value=value, value_type=value_type, error=str(e))
            raise RuleEngineError(f"Value conversion failed: {str(e)}")

    def _evaluate_condition(self, data: Dict[str, Any], condition: Dict[str, Any]) -> bool:
        """Evaluate a single condition against the data."""
        try:
            field_value = self._get_field_value(data, condition['field'])
            if field_value is None:
                return False
                
            operator_name = condition['operator']
            value = self._convert_value(condition['value'], condition.get('value_type', 'str'))
            
            return self.operators[operator_name](field_value, value)
        except KeyError as e:
            logger.error("Missing required field in condition", condition=condition, error=str(e))
            return False
        except Exception as e:
            logger.error("Error evaluating condition", condition=condition, error=str(e))
            return False

    def _evaluate_group(self, data: Dict[str, Any], group: Dict[str, Any]) -> bool:
        """Evaluate a group of conditions with logical operators."""
        try:
            logger.info("Evaluating group", group=group)
            if 'and' in group:
                return all(self._evaluate_rule(data, rule) for rule in group['and'])
            elif 'or' in group:
                return any(self._evaluate_rule(data, rule) for rule in group['or'])
            return self._evaluate_condition(data, group)
        except Exception as e:
            logger.error("Error evaluating group", group=group, error=str(e))
            return False

    def _evaluate_rule(self, data: Dict[str, Any], rule: Dict[str, Any]) -> bool:
        """Evaluate a rule which can be either a condition or a group."""
        try:
            logger.info("Evaluating rule", rule=rule)
            if 'and' in rule or 'or' in rule:
                return self._evaluate_group(data, rule)
            return self._evaluate_condition(data, rule)
        except Exception as e:
            logger.error("Error evaluating rule", rule=rule, error=str(e))
            return False

    def filter_keywords(self, keywords_data: List[Dict[str, Any]]) -> List[str]:
        """Filter keywords based on configured rules."""
        logger.info("Filtering keywords", keywords_data=keywords_data)
        if not keywords_data:
            return []
            
        filtered_keywords = []
        for item in keywords_data:
            try:
                if self._evaluate_rule(item, self.rules):
                    keyword = item.get('keyword_data', {}).get('keyword', '').strip()
                    if keyword:
                        filtered_keywords.append(keyword)
            except Exception as e:
                logger.error("Keyword filtering failed", keyword_data=item, error=str(e))
                continue
        logger.info("Filtered keywords", filtered_keywords=filtered_keywords)
        return list(set(filtered_keywords))

    async def filter_store_keywords(self, results: List[Dict[str, Any]], store_url: str) -> Dict[str, Any]:
        """Filter store domain keywords."""
        logger.info("Filtering store domain keywords", store_url=store_url)
        filtered = self.filter_keywords(results)
        return {
            "keywords": filtered,
            "metadata": {
                "source": "store",
                "store_url": store_url,
                "count": len(filtered),
                "timestamp": datetime.utcnow().isoformat()
            }
        }

    async def filter_competitor_keywords(self, results: List[Dict[str, Any]], competitor_url: str) -> Dict[str, Any]:
        """Filter keywords for competitor domain."""
        try:
            filtered_keywords = self.filter_keywords(results)
            return {
                "keywords": filtered_keywords,
                "metadata": {
                    "source": "competitor",
                    "competitor_url": competitor_url,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
        except Exception as e:
            logger.error("Error filtering competitor keywords", competitor_url=competitor_url, error=str(e))
            return {"keywords": [], "metadata": {"error": str(e)}}
        
    async def filter_target_keywords(self, results: List[Dict[str, Any]], focus_keyword: str) -> Dict[str, Any]:
        """Filter keywords for target keyword."""
        try:
            filtered_keywords = self.filter_keywords(results)
            return {
                "keywords": filtered_keywords,
                "metadata": {
                    "source": "target",
                    "focus_keyword": focus_keyword,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
        except Exception as e:
            logger.error("Error filtering target keywords", focus_keyword=focus_keyword, error=str(e))
            return {"keywords": [], "metadata": {"error": str(e)}}