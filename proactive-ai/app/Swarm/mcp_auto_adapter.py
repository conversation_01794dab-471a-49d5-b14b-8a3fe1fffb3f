from crewai_tools import MCPServerAdapter
from crewai import Agent, Task, Crew

def main():
    print("[Step 1] Setting server parameters...")
    server_params = {"url": "http://localhost:8080/sse"}

    print("[Step 2] Connecting to MCPServerAdapter...")
    with MCPServerAdapter(server_params) as tools:
        print(f"[Step 3] Available tools from SSE MCP server: {[tool.name for tool in tools]}")

        print("[Step 4] Creating Agent...")
        agent = Agent(
            role="Shopify Graphql query Generator",
            goal="Generate a Shopify Graphql query for a given Shopify store.",
            backstory="An AI assistant specialized in generating Shopify Graphql queries for a given Shopify store.",
            tools=tools,
            verbose=True,
        )

        print("[Step 5] Creating Task...")
        task = Task(
            description="Generate a Shopify Graphql query for a given Shopify store.",
            expected_output="A Shopify Graphql query for a given Shopify store.",
            agent=agent,
        )

        print("[Step 6] Creating Crew...")
        crew = Crew(
            agents=[agent],
            tasks=[task],
            verbose=True,
        )

        print("[Step 7] Kicking off Crew...")
        result = crew.kickoff()
        print("[Step 8] Result:")
        print(result)

if __name__ == "__main__":
    main()