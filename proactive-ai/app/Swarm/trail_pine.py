import os
from dotenv import load_dotenv
from pinecone import Pinecone
import time

# Load environment variables
load_dotenv()

# Initialize a Pinecone client with your API key
pc = Pinecone(api_key="pcsk_58QuXn_PcGNJTFvhivdVERLmKPH4G4WQkeqJGEn5SAM3WXufm1xo3rdfJkuQyuPVAJSPGB")

# Create a dense index with integrated embedding
index_name = "project-tools-index"
if not pc.has_index(index_name):
    pc.create_index_for_model(
        name=index_name,
        cloud="aws",
        region="us-east-1",
        embed={
            "model": "llama-text-embed-v2",
            "field_map": {"text": "text"}
        }
    )

# Target the index
index = pc.Index(index_name)

# Define sample data
sample_data = [
    {
        "id": "proj1",
        "text": "AI Project Management Tool - A comprehensive solution for managing AI development projects with built-in version control and collaboration features."
    },
    {
        "id": "tool1",
        "text": "Code Generator - An intelligent tool that helps developers write code faster using AI-powered suggestions and templates."
    },
    {
        "id": "scope1",
        "text": "Project Scope: Development of an AI-powered code review system with automated testing and deployment capabilities."
    }
]

# Upsert the data to the index
index.upsert_records("text", sample_data)

# Wait for the upserted vectors to be indexed
time.sleep(10)

# View stats for the index
stats = index.describe_index_stats()
print(stats)

# Example query
query_text = "Find AI development tools"

# Search with text using the new API format
results = index.search(
    namespace="text",
    query={
        "inputs": {"text": query_text},
        "top_k": 1
    },
    fields=["text"]  # Specify which fields to return
)

# Display results
print("\nSearch Results:", results)
# for match in results.matches:
#     print(f"ID: {match.id}")
#     print(f"Score: {match.score}")
#     print(f"Text: {match.fields.get('text', '')}")
#     print("---")

# Run this file as a script
if __name__ == "__main__":
    print("Running Pinecone script...")
    print("Index stats:", index.describe_index_stats())
