from crewai_tools import MCPServerAdapter
from crewai import Agent, Task, Crew, Process, LLM

def main():
    print("[INFO] Initializing LLM and server parameters...")
    llm = LLM(
        model="azure/development-proactive-ai",
        api_key="",
        base_url=""
    )

    server_params = {"url": "http://localhost:8000/sse"}

    mcp_server_adapter = None
    try:
        print("[INFO] Starting MCPServerAdapter...")
        mcp_server_adapter = MCPServerAdapter(server_params)
        mcp_server_adapter.start()
        tools = mcp_server_adapter.tools
        print(f"[INFO] Available tools (manual SSE): {[tool.name for tool in tools]}")

        print("[INFO] Creating agent...")
        agent = Agent(
            role="Shopify Graphql query Generator", 
            goal="Generate a Shopify Graphql query for a given Shopify store.", 
            backstory="An AI assistant specialized in generating Shopify Graphql queries for a given Shopify store.", 
            tools=tools,
            verbose=True,
            llm=llm
        )
        
        print("[INFO] Creating task...")
        task = Task(
            description="Generate a Shopify Graphql query for a given Shopify store.",
            expected_output="A Shopify Graphql query for a given Shopify store.",
            agent=agent
        )
        
        print("[INFO] Creating crew...")
        crew = Crew(
            agents=[agent],
            tasks=[task],
            verbose=True,
            process=Process.sequential
        )
        
        print("[INFO] Kicking off crew execution...")
        result = crew.kickoff()
        print("[RESULT]", result)
    except Exception as e:
        print(f"[ERROR] Exception occurred: {e}")
    finally:
        print("[INFO] Stopping SSE MCP server connection (manual)...")
        if mcp_server_adapter:
            mcp_server_adapter.stop() # **Crucial: Ensure stop is called**

default_entry = __name__ == "__main__"
if default_entry:
    main()