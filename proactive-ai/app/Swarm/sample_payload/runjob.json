{"input_data": [{"shopify_gid": "gid://shopify/Product/1234567890", "product_title": "<PERSON><PERSON>das Shoes - men's running shoes", "product_description": "Ad<PERSON>das Shoes - men's running shoes. Perfect for running and everyday use.", "product_category": "Shoes", "product_subcategory": "Running Shoes", "product_tags": "<PERSON><PERSON><PERSON>, <PERSON>, Men, Running", "product_price": "100"}, {"shopify_gid": "gid://shopify/Product/1234567891", "product_title": "Gucci handbags - women's luxury handbags", "product_description": "Gucci handbags - women's luxury handbags. Elegant and stylish, perfect for any occasion.", "product_category": "Handbags", "product_subcategory": "Luxury Handbags", "product_tags": "Gucci, Handbags, Women, Luxury", "product_price": "100"}], "output_structure": [{"display_data": {"product_title": "<product_title>", "product_description": "<product_description>"}, "graphql_string": "mutation productUpdate($input: ProductUpdateInput!) { productUpdate(product: $input) { product { id title descriptionHtml } userErrors { field message } } }", "input": {"id": "<shopify_gid>", "descriptionHtml": "<product_description>"}}], "max_keywords": 20, "keyword_target": "Global", "include_long_tail": true, "writing_style": "Persuasive", "max_word_count": 300, "include_benefits": true, "special_terms": "eco-friendly, sustainable, free shipping", "check_spelling_grammar": true, "customer_focus_rating": 9, "highlighted_keywords": "durable, lightweight, limited edition"}