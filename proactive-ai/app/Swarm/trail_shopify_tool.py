import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = str(Path(__file__).parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from crewai import Agent, Task, Crew, Process, LLM
from app.StoreWorker.Tools.shopify_admin_tools import search_shopify_admin_schema
from app.StoreWorker.logger.logger import info, error
from typing import List, Dict

llm = LLM(
    model="azure/development-proactive-ai",
    api_key="********************************",
    base_url="https://bincha-ai.openai.azure.com"
)

# Create specialized agents
shopify_expert = Agent(
    role='Shopify API Expert',
    goal='Find and provide the exact GraphQL mutation structure for article creation',
    backstory="""You are a Shopify API expert who specializes in the Admin GraphQL API. 
    You know exactly how to structure article creation mutations and can help 
    implement them correctly. Your primary focus is to provide the exact mutation 
    structure that will work with the Shopify Admin API.""",
    tools=[search_shopify_admin_schema],
    verbose=True,
    llm=llm
)

content_writer = Agent(
    role='Content Writer & GraphQL Specialist',
    goal='Create article content that matches the required GraphQL mutation structure',
    backstory="""You are a skilled content writer with expertise in e-commerce and GraphQL. 
    You can create compelling content that not only engages readers but also perfectly 
    matches the required GraphQL mutation structure. You ensure the content is properly 
    formatted for the Shopify Admin API.""",
    verbose=True,
    llm=llm
)

# Define tasks
api_research_task = Task(
    description="""Search the Shopify Admin GraphQL schema to find the exact mutation for article creation.
    Focus on:
    1. Find the exact mutation name for creating an article
    2. List ALL required fields and their exact types
    3. List ALL optional fields that might be useful
    4. Provide a complete example of the mutation structure
    5. Include any specific requirements or constraints
    6. Format the output as a complete, working GraphQL mutation template
    
    The output should be a complete GraphQL mutation that can be used as a template,
    with placeholders for the actual content values.""",
    agent=shopify_expert,
    expected_output="""A complete GraphQL mutation template containing:
    1. The exact mutation name and structure
    2. All required fields with their exact types
    3. Useful optional fields with their types
    4. A complete mutation template with placeholders
    5. Example of how to use the mutation
    Format: A complete, working GraphQL mutation string that can be used as a template"""
)

content_creation_task = Task(
    description="""Using the provided GraphQL mutation structure, create an article about 
    Shopify e-commerce that will work with the mutation. The content must:
    1. Match exactly the required fields from the mutation
    2. Use the correct data types for each field
    3. Include a compelling headline and content
    4. Be between 300-500 words
    5. Be optimized for SEO
    6. Include relevant examples
    
    Most importantly, format the output as a complete, working GraphQL mutation string
    that can be directly used with the Shopify Admin API. Use the exact structure
    provided by the API expert, replacing the placeholders with actual content.""",
    agent=content_writer,
    expected_output="""A complete, working GraphQL mutation string containing:
    1. The exact mutation structure from the API expert
    2. All required fields filled with appropriate content
    3. Useful optional fields filled with appropriate content
    4. Properly formatted content that matches field types
    5. A complete, executable GraphQL mutation
    Format: A complete, working GraphQL mutation string that can be executed"""
)

# Create the crew
article_creation_crew = Crew(
    agents=[shopify_expert, content_writer],
    tasks=[api_research_task, content_creation_task],
    process=Process.sequential,  # Tasks will be executed in sequence
    verbose=True
)

def create_shopify_article() -> Dict:
    """
    Execute the crew to create and prepare an article for Shopify.
    
    Returns:
        Dict containing the API structure and final GraphQL mutation
    """
    try:
        info("Starting article creation process")
        result = article_creation_crew.kickoff()
        
        # Process and structure the results
        final_result = {
            'api_structure': None,
            'final_mutation': None
        }
        
        # The result will be a list of task outputs in sequence
        if isinstance(result, list) and len(result) >= 2:
            final_result['api_structure'] = result[0]  # API structure from expert
            final_result['final_mutation'] = result[1]  # Final GraphQL mutation
        
        info("Article creation process completed successfully")
        return final_result
        
    except Exception as e:
        error(f"Error in article creation process: {str(e)}")
        return {
            'error': str(e),
            'api_structure': None,
            'final_mutation': None
        }

if __name__ == "__main__":
    # Example usage
    result = create_shopify_article()
    
    if 'error' in result:
        print(f"Error occurred: {result['error']}")
    else:
        print("\n=== API Structure ===")
        print(result['api_structure'])
        
        print("\n=== Final GraphQL Mutation ===")
        print(result['final_mutation'])  # This can be used directly with Shopify Admin API
