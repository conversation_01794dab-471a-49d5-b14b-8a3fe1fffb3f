import logging
import sys
import os
import time
from typing import Optional, Dict, Any
from contextvars import Context<PERSON><PERSON>
from datetime import datetime

# Context variables for request tracking
request_id_var = ContextVar('request_id', default=None)
store_id_var = ContextVar('store_id', default=None)

# Configure the logger
def setup_logger(name: str = "StoreWorker", 
                 level: int = logging.INFO, 
                 log_file: Optional[str] = None) -> logging.Logger:
    """
    Set up and configure a logger instance.
    
    Args:
        name: The name of the logger
        level: The logging level (default: logging.INFO)
        log_file: Optional path to a log file
        
    Returns:
        logging.Logger: Configured logger instance
    """
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Create formatter with additional context
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - '
        '[request_id=%(request_id)s] [store_id=%(store_id)s] - %(message)s',
        defaults={'request_id': 'N/A', 'store_id': 'N/A'}
    )
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Add file handler if log_file is specified
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

# Create a default logger instance
logger = setup_logger()

class LogContext:
    """Context manager for structured logging"""
    def __init__(self, request_id: Optional[str] = None, store_id: Optional[int] = None):
        self.request_id = request_id
        self.store_id = store_id
        self.start_time = None
        
    def __enter__(self):
        if self.request_id:
            request_id_var.set(self.request_id)
        if self.store_id:
            store_id_var.set(self.store_id)
        self.start_time = time.time()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            execution_time = time.time() - self.start_time
            logger.info(f"Request completed in {execution_time:.2f} seconds")
        request_id_var.set(None)
        store_id_var.set(None)

def get_log_context() -> Dict[str, Any]:
    """Get current logging context"""
    return {
        'request_id': request_id_var.get(),
        'store_id': store_id_var.get()
    }

# Convenience functions with context
def info(message: str, **kwargs) -> None:
    """Log an info message with context"""
    logger.info(message, extra={**get_log_context(), **kwargs})

def error(message: str, exc_info: bool = False, **kwargs) -> None:
    """Log an error message with context"""
    logger.error(message, exc_info=exc_info, extra={**get_log_context(), **kwargs})

def debug(message: str, **kwargs) -> None:
    """Log a debug message with context"""
    logger.debug(message, extra={**get_log_context(), **kwargs})

def warning(message: str, **kwargs) -> None:
    """Log a warning message with context"""
    logger.warning(message, extra={**get_log_context(), **kwargs})

def critical(message: str, **kwargs) -> None:
    """Log a critical message with context"""
    logger.critical(message, extra={**get_log_context(), **kwargs})

def set_level(level: int) -> None:
    """Set the logging level"""
    logger.setLevel(level)
