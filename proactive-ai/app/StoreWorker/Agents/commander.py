# src/latest_ai_development/crew.py
from crewai import Agent, Task, Crew
from crewai.project import CrewBase, agent, task, crew
from app.StoreWorker.config.llm import azure_llm
from typing import Dict, List
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@CrewBase
class CommanderCrew():
  """Commander crew"""

  agents_config = "config/agents.yml"
  tasks_config = "config/task.yml"

  @agent
  def commander_agent(self) -> Agent:
    try:
      agent = Agent(
        config=self.agents_config['commander_agent'],
        verbose=True,
        max_iter=3,
        llm=azure_llm
      )
      logger.debug(f"Agent created with config: {self.agents_config['commander_agent']}")
      return agent
    except Exception as e:
      logger.error(f"Error creating agent: {str(e)}")
      raise

  # @agent
  # def formatting_agent_for_user(self) -> Agent:
  #   try:
  #     agent = Agent(
  #       config=self.agents_config['formatting_agent_for_user'],
  #       verbose=True,
  #       llm=azure_llm
  #     )
  #     logger.debug(f"Agent created with config: {self.agents_config['formatting_agent_for_user']}")
  #     return agent
  #   except Exception as e:
  #     logger.error(f"Error creating agent: {str(e)}")
  #     raise

  @task
  def commander_task(self) -> Task:
    try:
      task = Task(
        config=self.tasks_config['commander_task'],
        agent=self.commander_agent()  # Assign the agent to the task
      )
      logger.debug(f"Task created with config: {self.tasks_config['commander_task']}")
      return task
    except Exception as e:
      logger.error(f"Error creating task: {str(e)}")
      raise

  # @task
  # def formatting_agents_for_user(self) -> Task:
  #   try:
  #     task = Task(
  #       config=self.tasks_config['formatting_agents_for_user'],
  #       agent=self.formatting_agent_for_user()  # Assign the agent to the task
  #     )
  #     logger.debug(f"Task created with config: {self.tasks_config['formatting_agents_for_user']}")
  #     return task
  #   except Exception as e:
  #     logger.error(f"Error creating task: {str(e)}")
  #     raise

  @crew
  def commander_crew(self) -> Crew:
    try:
      crew = Crew(
        agents=self.agents,
        tasks=self.tasks,
        verbose=True
      )
      logger.debug("Crew created successfully")
      return crew
    except Exception as e:
      logger.error(f"Error creating crew: {str(e)}")
      raise
  
  def form_crew(self, task: Dict, agents: List[Dict], tasks: List[Dict]) -> str:
    try:
      logger.debug(f"Forming crew for task: {task}")
      crew_instance = self.commander_crew()
      logger.debug("Crew instance created")
      result = crew_instance.kickoff(inputs={"task": task, "agents": agents, "tasks": tasks})
      logger.debug("Crew execution completed")
      return result
    except Exception as e:
      logger.error(f"Error during crew formation: {str(e)}")
      raise

if __name__ == "__main__":
  try:
    commander = CommanderCrew()
    #get the query from the user
    task = input("Enter a task: ")
    #validate the query
    result = commander.form_crew(task)
    #print the result
    print(result)
  except Exception as e:
    logger.error(f"Main execution error: {str(e)}")
    raise
