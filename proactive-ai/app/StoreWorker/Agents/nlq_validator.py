# src/latest_ai_development/crew.py
from crewai import Agent, Task, Crew
from crewai.project import Crew<PERSON><PERSON>, agent, task, crew
from crewai_tools import RagTool
from app.StoreWorker.config.llm import azure_llm
import logging
import json
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# # create a RAG tool
# rag_tool = RagTool()

# # add content to the knowledge base
# rag_tool.add(data_type="database", url="https://proactive-ai-dwsftq8.svc.aped-4627-b74a.pinecone.io")


@CrewBase
class NlqValidatorCrew():
  """Nlq Validator crew"""

  agents_config = "config/agents.yml"
  tasks_config = "config/task.yml"

  @agent
  def nlq_validator_agent(self) -> Agent:
    try:
      logger.info("Creating NLQ validator agent")
      start_time = time.time()
      
      agent = Agent(
        config=self.agents_config['nlq_validator_agent'],
        verbose=True,
        max_iter=3,
        llm=azure_llm
      )
      
    #   logger.debug(f"Agent created with config: {json.dumps(self.agents_config['nlq_validator_agent'], indent=2)}")
      logger.info(f"Agent creation completed in {time.time() - start_time:.2f} seconds")
      return agent
    except Exception as e:
      logger.error(f"Error creating agent: {str(e)}", exc_info=True)
      raise

  @task
  def validate_nlq_task(self) -> Task:
    try:
      logger.info("Creating validation task")
      start_time = time.time()
      
      task = Task(
        config=self.tasks_config['nlq_validator_task'],
        agent=self.nlq_validator_agent()
      )
      
    #   logger.debug(f"Task created with config: {json.dumps(self.tasks_config['nlq_validator_task'], indent=2)}")
      logger.info(f"Task creation completed in {time.time() - start_time:.2f} seconds")
      return task
    except Exception as e:
      logger.error(f"Error creating task: {str(e)}", exc_info=True)
      raise

  @crew
  def nlq_validator_crew(self) -> Crew:
    try:
      logger.info("Creating NLQ validator crew")
      start_time = time.time()
      
      crew = Crew(
        agents=self.agents,
        tasks=self.tasks,
        verbose=True,
      )
      
      logger.info(f"Crew creation completed in {time.time() - start_time:.2f} seconds")
      return crew
    except Exception as e:
      logger.error(f"Error creating crew: {str(e)}", exc_info=True)
      raise
  
  def validate_nlq(self, query: str) -> str:
    try:
      logger.info(f"Starting NLQ validation for query: {query}")
      start_time = time.time()
      
      crew_instance = self.nlq_validator_crew()
      logger.debug("Crew instance created successfully")
      
      result = crew_instance.kickoff(inputs={"query": query})
    #   logger.debug(f"Raw crew execution result: {json.dumps(result, indent=2)}")
      
      logger.info(f"NLQ validation completed in {time.time() - start_time:.2f} seconds")
      return result
    except Exception as e:
      logger.error(f"Error during validation: {str(e)}", exc_info=True)
      raise

if __name__ == "__main__":
  try:
    logger.info("Starting NLQ validator in standalone mode")
    nlq_validator = NlqValidatorCrew()
    
    query = input("Enter a query: ")
    logger.info(f"Processing query: {query}")
    
    result = nlq_validator.validate_nlq(query)
    logger.info("Validation completed successfully")
    print(result)
  except Exception as e:
    logger.error(f"Main execution error: {str(e)}", exc_info=True)
    raise
