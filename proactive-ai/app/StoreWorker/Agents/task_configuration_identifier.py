from crewai import Agent, Task, Crew
from crewai.project import CrewBase, agent, task, crew
from app.StoreWorker.config.llm import azure_llm
import logging
import json
import time
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@CrewBase
class TaskConfigurationIdentifierCrew():
  """Task Configuration Identifier crew"""

  agents_config = "config/agents.yml"
  tasks_config = "config/task.yml"

  @agent
  def task_configuration_agent(self) -> Agent:
    try:
      logger.info("Creating Task Configuration Identifier agent")
      start_time = time.time()
      
      agent = Agent(
        config=self.agents_config['task_configuration_agent'],
        verbose=True,
        max_iter=3,
        llm=azure_llm
      )
      
      logger.info(f"Agent creation completed in {time.time() - start_time:.2f} seconds")
      return agent
    except Exception as e:
      logger.error(f"Error creating agent: {str(e)}", exc_info=True)
      raise

  @task
  def identify_configuration_task(self) -> Task:
    try:
      logger.info("Creating configuration identification task")
      start_time = time.time()
      
      task = Task(
        config=self.tasks_config['task_configuration_task'],
        agent=self.task_configuration_agent()
      )
      
      logger.info(f"Task creation completed in {time.time() - start_time:.2f} seconds")
      return task
    except Exception as e:
      logger.error(f"Error creating task: {str(e)}", exc_info=True)
      raise

  @crew
  def task_configuration_crew(self) -> Crew:
    try:
      logger.info("Creating Task Configuration Identifier crew")
      start_time = time.time()
      
      crew = Crew(
        agents=self.agents,
        tasks=self.tasks,
        verbose=True,
      )
      
      logger.info(f"Crew creation completed in {time.time() - start_time:.2f} seconds")
      return crew
    except Exception as e:
      logger.error(f"Error creating crew: {str(e)}", exc_info=True)
      raise
  
  def identify_configuration(self, task_data: dict) -> str:
    try:
      logger.info(f"Starting configuration identification for task: {task_data}")
      start_time = time.time()
      
      crew_instance = self.task_configuration_crew()
      logger.debug("Crew instance created successfully")
      
      result = crew_instance.kickoff(inputs={"task": json.dumps(task_data)})
      
      logger.info(f"Configuration identification completed in {time.time() - start_time:.2f} seconds")
      return result
    except Exception as e:
      logger.error(f"Error during configuration identification: {str(e)}", exc_info=True)
      raise

if __name__ == "__main__":
  try:
    logger.info("Starting Task Configuration Identifier in standalone mode")
    config_identifier = TaskConfigurationIdentifierCrew()
    
    # Example task data
    task_data = {
      "task": "Create Blog Post",
      "workers": [
        {
          "id": "writer",
          "title": "Writer",
          "description": "Creates engaging content with proper structure and style",
          "icon": "✏️",
          "iconColor": "success",
          "selected": True
        },
        {
          "id": "seo-pro",
          "title": "SEO Pro",
          "description": "Optimizes content for search engines and keyword ranking",
          "icon": "🔍",
          "iconColor": "highlight",
          "selected": True
        }
      ]
    }
    
    result = config_identifier.identify_configuration(task_data)
    logger.info("Configuration identification completed successfully")
    print(result)
  except Exception as e:
    logger.error(f"Main execution error: {str(e)}", exc_info=True)
    raise