# src/latest_ai_development/crew.py
from crewai import Agent, Task, Crew
from crewai.project import Crew<PERSON><PERSON>, agent, task, crew
from crewai_tools import RagTool
from app.StoreWorker.config.llm import azure_llm
import logging
import json
import time
from typing import List
# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@CrewBase
class ScriptFormatterCrew():
  """Script Formatter crew"""

  agents_config = "config/agents.yml"
  tasks_config = "config/task.yml"

  @agent
  def script_formatter_agent(self) -> Agent:
    try:
      logger.info("Creating Script Formatter agent")
      start_time = time.time()
      
      agent = Agent(
        config=self.agents_config['script_formatter_agent'],
        verbose=True,
        max_iter=3,
        llm=azure_llm
      )
      
    #   logger.debug(f"Agent created with config: {json.dumps(self.agents_config['script_formatter_agent'], indent=2)}")
      logger.info(f"Agent creation completed in {time.time() - start_time:.2f} seconds")
      return agent
    except Exception as e:
      logger.error(f"Error creating agent: {str(e)}", exc_info=True)
      raise

  @task
  def script_formatter_task(self) -> Task:
    try:
      logger.info("Creating script formatter task")
      start_time = time.time()
      
      task = Task(
        config=self.tasks_config['script_formatter_task'],
        agent=self.script_formatter_agent()
      )
      
    #   logger.debug(f"Task created with config: {json.dumps(self.tasks_config['script_formatter_task'], indent=2)}")
      logger.info(f"Task creation completed in {time.time() - start_time:.2f} seconds")
      return task
    except Exception as e:
      logger.error(f"Error creating task: {str(e)}", exc_info=True)
      raise

  @crew
  def script_formatter_crew(self) -> Crew:
    try:
      logger.info("Creating Script Formatter crew")
      start_time = time.time()
      
      crew = Crew(
        agents=self.agents,
        tasks=self.tasks,
        verbose=True,
      )
      
      logger.info(f"Crew creation completed in {time.time() - start_time:.2f} seconds")
      return crew
    except Exception as e:
      logger.error(f"Error creating crew: {str(e)}", exc_info=True)
      raise
  
  def script_formatter(self, spec: str, parameters: List[str]) -> str:
    try:
      logger.info(f"Starting Script Formatter for spec: {spec} and parameters: {parameters}")
      start_time = time.time()
      
      crew_instance = self.script_formatter_crew()
      logger.debug("Crew instance created successfully")
      
      result = crew_instance.kickoff(inputs={"spec": spec, "parameters": parameters})
    #   logger.debug(f"Raw crew execution result: {json.dumps(result, indent=2)}")
      
      logger.info(f"Script Formatter completed in {time.time() - start_time:.2f} seconds")
      return result
    except Exception as e:
      logger.error(f"Error during Script Formatter: {str(e)}", exc_info=True)
      raise

if __name__ == "__main__":
  try:
    logger.info("Starting Script Formatter in standalone mode")
    script_formatter = ScriptFormatterCrew()
    
    spec = input("Enter a spec: ")
    parameters = input("Enter parameters: ")
    logger.info(f"Processing spec: {spec} and parameters: {parameters}")
    
    result = script_formatter.script_formatter(spec, parameters)
    logger.info("Script Formatter completed successfully")
    print(result)
  except Exception as e:
    logger.error(f"Main execution error: {str(e)}", exc_info=True)
    raise
