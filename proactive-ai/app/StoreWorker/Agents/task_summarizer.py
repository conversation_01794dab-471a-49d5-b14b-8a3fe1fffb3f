from crewai import Agent, Task, Crew
from crewai.project import CrewBase, agent, task, crew
from app.StoreWorker.config.llm import azure_llm
import logging
import json
import time
import uuid
from uuid import uuid4

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
SINGLE_TASK_MODE = "single"
MULTIPLE_TASKS_MODE = "multiple"
DEFAULT_MODE = SINGLE_TASK_MODE

@CrewBase
class TaskSummarizerCrew():
  """Task Summarizer crew"""

  agents_config = "config/agents.yml"
  tasks_config = "config/task.yml"

  @agent
  def task_summarizer_agent(self) -> Agent:
    try:
      logger.info("Creating Task Summarizer agent")
      start_time = time.time()
      
      agent = Agent(
        config=self.agents_config['task_summarizer_agent'],
        verbose=True,
        max_iter=3,
        llm=azure_llm
      )
      
      logger.info(f"Agent creation completed in {time.time() - start_time:.2f} seconds")
      return agent
    except Exception as e:
      logger.error(f"Error creating agent: {str(e)}", exc_info=True)
      raise

  @task
  def summarize_task(self) -> Task:
    try:
      logger.info("Creating task summarization task")
      start_time = time.time()
      
      task = Task(
        config=self.tasks_config['task_summarizer_task'],
        agent=self.task_summarizer_agent()
      )
      
      logger.info(f"Task creation completed in {time.time() - start_time:.2f} seconds")
      return task
    except Exception as e:
      logger.error(f"Error creating task: {str(e)}", exc_info=True)
      raise

  @crew
  def task_summarizer_crew(self) -> Crew:
    try:
      logger.info("Creating Task Summarizer crew")
      start_time = time.time()
      
      crew = Crew(
        agents=self.agents,
        tasks=self.tasks,
        verbose=True,
      )
      
      logger.info(f"Crew creation completed in {time.time() - start_time:.2f} seconds")
      return crew
    except Exception as e:
      logger.error(f"Error creating crew: {str(e)}", exc_info=True)
      raise
  
  def summarize(self, task_data: dict, mode: str = DEFAULT_MODE) -> str:
    try:
      logger.info(f"Starting task summarization for task: {task_data}")
      start_time = time.time()
      
      crew_instance = self.task_summarizer_crew()
      logger.debug("Crew instance created successfully")
      
      if mode == SINGLE_TASK_MODE:
        result = crew_instance.kickoff(inputs={"task": json.dumps(task_data)})
        # Format the result as JSON
        return f"```json\n{json.dumps([{
          'task_name': task_data['task'],
          'summary': [{
            'name': worker['title'],
            'description': worker['description']
          } for worker in task_data['workers']],
          'task_id': str(uuid4())
        }], indent=2)}\n```"
      elif mode == MULTIPLE_TASKS_MODE:
        # Handle multiple tasks
        results = []
        for task in task_data:
          result = crew_instance.kickoff(inputs={"task": json.dumps(task)})
          # Format each result as JSON
          results.append({
            'task_name': task['task'],
            'summary': [{
              'name': worker['title'],
              'description': worker['description']
            } for worker in task['workers']],
            'task_id': str(uuid4())
          })
        return f"```json\n{json.dumps(results, indent=2)}\n```"
      else:
        raise ValueError(f"Invalid mode: {mode}. Supported modes are {SINGLE_TASK_MODE} and {MULTIPLE_TASKS_MODE}")
      
      logger.info(f"Task summarization completed in {time.time() - start_time:.2f} seconds")
    except Exception as e:
      logger.error(f"Error during task summarization: {str(e)}", exc_info=True)
      raise

if __name__ == "__main__":
  try:
    logger.info("Starting Task Summarizer in standalone mode")
    summarizer = TaskSummarizerCrew()
    
    # Example task data
    task_data = {
      "task": "Create Blog Post",
      "workers": [
        {
          "id": "writer",
          "title": "Writer",
          "description": "Creates engaging content with proper structure and style",
          "icon": "✏️",
          "iconColor": "success",
          "selected": True
        },
        {
          "id": "seo-pro",
          "title": "SEO Pro",
          "description": "Optimizes content for search engines and keyword ranking",
          "icon": "🔍",
          "iconColor": "highlight",
          "selected": True
        }
      ]
    }
    
    # Example usage for single task mode
    result = summarizer.summarize(task_data)
    logger.info("Task summarization completed successfully")
    print(result)
    
    # Example usage for multiple tasks mode
    # tasks_data = [task_data, task_data]  # Example with multiple tasks
    # result = summarizer.summarize(tasks_data, mode=MULTIPLE_TASKS_MODE)
    # print(result)
    
  except Exception as e:
    logger.error(f"Main execution error: {str(e)}", exc_info=True)
    raise