# This file contains the tasks for the crew

# Nlq Validator Task
nlq_validator_task:
  description: >
    Validate the NLQ query - {query} using the Nlq Validator crew. 
    Accept only shopify, e-commerce, product, collection, blog, article related queries. Don't need to look for those keywords in the query. Just look into the context of the query and if it fits the context, then accept the query.
    If the query is accpeted, while trying to forming the task, follow the below rules:
      - Make sure the task too broad.
      -Example1: "query": "Create a product collection using my products" - then the task should be "Creating a product collection"
      -Example2: "query": "Create a blog post about the latest trends in e-commerce" - then the task should be "Creating a blog post"
      -Example3: "query": "Create a product collection using my products and write a blog post about it" - then the task should be "Creating a product collection and writing a blog post"
      - Make sure the task doesn't include any framework or our code specific keywords (like crew, agent, task, nlq - validator, etc.)
    If the query is not related to shopify, but related to any of the above topics, then return the response variations with including shopify valid variations.
      While returing the valid variations, follow the below rules:
      - Make sure the variations would work for sure if used as a query.
      - Make sure the variations are valid shopify queries.
      - Make sure the variations are not too broad or too specific.
      - Make sure the variations doesn't include any framework or our code specific keywords (like crew, agent, task, nlq - validator, etc.)
      
  expected_output: >
    - Present results in a clear, structured manner
    - Generate a unique task_id (uuid) for each task
    - Return the response in the following format, if the query is not valid:
      ```json
      {
        "response": "The query is not valid. Please rephrase it in a way that can be processed by the system.",
        "valid_variations": ["valid_variation_one", "valid_variation_two", "valid_variation_three"]
      }
      ```
    - Return the response in the following format, if the query is valid:
      ```json
      {
        "response": "The query is valid. Please proceed to the next step.",
        "workflow_name": "workflow_name",
        "tasks": [
          {
            "task_id": "uuid",
            "name": "sub_task_one",
            "description": "sub_task_one_description",
            "parameters": {
              "parameter_one": "parameter_one_description",
              "parameter_two": "parameter_two_description"
            }
          },
          {
            "name": "sub_task_two",
            "description": "sub_task_two_description",
            "parameters": {
              "parameter_one": "parameter_one_description",
              "parameter_two": "parameter_two_description"
            }
          }
        ]
      }
      ```

commander_task:
  description: >
    Form a crew of agents to complete the task. The crew should be optimized to complete the task in the best way possible.
    input task: {task}.
    You would also be provided with the list of agents and tasks that are already available, you can reuse the existing agents and tasks to complete the task. you can also add new agents and tasks to the crew.
    the crew should be optimized to complete the task in the best way possible with least number of agents and tasks.
    input list of agents: {agents}
    input list of tasks: {tasks}

  expected_output: >
    - Present results in a clear, structured JSON format.
    - Format the output of the crew ai - agents in a way that is easy to understand for the user.
    - Needed both the syntactical output from the crew ai and the formatted output for the user.
    - Return the response in the following format (a sample response is given below):
      ```json
      {
        "task_id": "given task id",
        "Name": <given task name>,
        "Description": <given task description>,
        "Agents": [
          {
            "uuid": "uuid", (generate a uuid for new agent / existing agent uuid)
            "Name": "Research Coordinator",
            "Role": "Research Project Manager",
            "Goal": "Goal of the agent",
            "Backstory": "backstory_of_the_agent",
            "Tools": ["Tool1", "Tool2", "Tool3"]
          },
          {
            "uuid": "uuid", (generate a uuid for new agent / existing agent uuid)
            "Name": "Data Researcher",
            "Role": "Information Gathering Specialist",
            "Goal": "Goal of the agent",
            "Backstory": "backstory_of_the_agent",
            "Tools": ["Tool1", "Tool2", "Tool3"]
          },
          {
            "uuid": "uuid", (generate a uuid for new agent / existing agent uuid)
            "Name": "Content Analyst",
            "Role": "Content Analysis Expert",
            "Goal": "Goal of the agent",
            "Backstory": "backstory_of_the_agent",
            "Tools": ["Tool1", "Tool2", "Tool3"]
          }
        ],
        "Tasks": [
          {
            "uuid": "uuid", (generate a uuid for new task / existing task uuid)
            "Name": "Define Research Scope",
            "Description": "Establish the research objectives and methodology",
            "Output": "Detailed research plan",
            "Agent": ["Research Coordinator"],
            "Dependencies": "None"
          },
          {
            "uuid": "uuid", (generate a uuid for new task / existing task uuid)
            "Name": "Gather Information",
            "Description": "Collect relevant data from multiple sources",
            "Output": "Compiled research data",
            "Dependencies": "Define Research Scope",
            "Agent": ["Data Researcher"]
          },
          {
            "uuid": "uuid", (generate a uuid for new task / existing task uuid)
            "Name": "Analyze Findings",
            "Description": "Analyze collected data and identify key insights",
            "Output": "Analysis report",
            "Dependencies": "Gather Information",
            "Agent": ["Content Analyst"]
          },
          {
            "uuid": "uuid", (generate a uuid for new task / existing task uuid)
            "Name": "Create Final Report",
            "Description": "Compile findings into a comprehensive report",
            "Output": "Final research report",
            "Dependencies": "Analyze Findings",
            "Agent": ["Research Coordinator"]
          }
        ],
        "Crew": {
          "Name": "Research Team",
          "Description": "A collaborative team for comprehensive research and analysis",
          "Agents": ["Research Coordinator", "Data Researcher", "Content Analyst"],
          "Tasks": ["Define Research Scope", "Gather Information", "Analyze Findings", "Create Final Report"]
        },
        "Tools": [
          {
            "Name": "Task Management",
            "Description": "Tool for managing and tracking research tasks",
            "Parameters": {
              "task_name": "string",
              "status": "string",
              "assignee": "string"
            }
          },
          {
            "Name": "Web Search",
            "Description": "Tool for web-based research",
            "Parameters": {
              "query": "string",
              "max_results": "integer"
            }
          },
          {
            "Name": "Text Analysis",
            "Description": "Tool for analyzing text content",
            "Parameters": {
              "text": "string",
              "analysis_type": "string"
            }
          }
        ],
        "formatted_agents": [
           {
            "uuid": "uuid", (uuid of that particular agent)
            "id": "serial_number",
            "title": "agent_title",
            "description": "agent_description",
            "icon": "agent_icon", (a emoji)
            "iconColor": "agent_icon_color", (a color)
            "selected": true
          }
        ]
      }
      ```

task_configuration_task:
  description: >
    Identify configurable items for the task - {task} and its workers. Identify and include only the following inputs types:
    - text
    - number
    - boolean
    - text_area
    - dropdown
    Identify the category of the input (product, collection, article) and its numbers (single, multiple)
    
  expected_output: >
    - Present results in a clear, structured JSON format
    - Return the response in the following format:
      ```json
      {
        "task_id": "uuid",
        "name": "task_name",
        "type": "task_type",
        "workers": [
          {
            "id": "worker_id",
            "title": "worker_title",
            "description": "worker_description",
            "icon": "worker_icon",
            "iconColor": "worker_icon_color",
            "selected": true
          }
        ],
        "configurations": [
          {
            "agent_name": "worker_title",
            "name": "configuration_name",
            "description": "configuration_description",
            "fields": [
              {
                "key": "field_key",
                "type": "field_type",
                "description": "field_description",
                "display_name": "field_display_name",
                "placeholder": "field_placeholder",
                "component": "ui_component",
                "required": true/false, (for boolean, it should be false)
                "options": ["option1", "option2"] // if applicable
              }
            ]
          }
        ],
        "store_data": {
          "category": "product/collection/article",
          "type": "single/multiple"
        }
      }
      ```

task_summarizer_task:
  description: >
    Summarize the task - {task} and its workers
  expected_output: >
    - Present results in a clear, structured array format
    - Return the response in the following format:
      ```json
      [
        {
          "summary": [
            {
              "name": "sub_task_name",
              "description": "summary_of_the_sub_task"
            }
          ]
        }
      ]
      ``` 

script_formatter_task:
  description: >
    Format the inputs given to you according to crew AI documentation. (inline definitions).
    You would be provided with all necessary attributes and dynamic key-value parameters for the input.
    input: spec - {spec}
    input: parameters - {parameters}
    Include the actual keys (mentioned in the parameters) in the task description and goal or any other place where it is applicable.
    In this way , we can add dynamic inputs to the task and in turn make the task more flexible and reusable.
  expected_output: >
    - Present results in a clear, structured JSON format
    - Return the response in the following format: ( sample response is given below)
      ```json
      {
        "name": "Name of the crew",
        "description": "Description of the crew",
        "azure_config": {
            "llm_model": "azure/<development>", (leave it as it is)
            "api_key": "", (leave it blank)
            "base_url": "", (leave it blank)
            "function_name": "Name of the crew",
            "auth_level": "function"
        },
        "agents": [
          {
              "name": "Name of the agent",
              "role": "Role of the agent" (with the input key as a template placeholder if applicable),
              "goal": "Goal of the agent" (with the input key as a template placeholder if applicable),
              "backstory": "Backstory of the agent"
          }
        ],
        "tasks": [
          {
              "description": "Description of the task" (with the input key as a template placeholder if applicable),
              "expected_output": "Expected output of the task",
              "agent": "Name of the agent/agents" 
          }
        ],
        "crew": {
          "process": "sequential",
          "verbose": true,
          "memory": false,
          "cache": true
        },
      "configuration_keys": [
          "input_key1",
          "input_key2"
        ]
      } 
      ```