from typing import List, Dict, Optional
from crewai.tools import BaseTool
from pydantic import BaseModel, <PERSON>
from pinecone import Pinecone
import time

# Initialize Pinecone client
pc = Pinecone(api_key="pcsk_58QuXn_PcGNJTFvhivdVERLmKPH4G4WQkeqJGEn5SAM3WXufm1xo3rdfJkuQyuPVAJSPGB")

# Input schemas for each tool
class CreateIndexInput(BaseModel):
    """Input schema for creating a Pinecone index."""
    index_name: str = Field(..., description="Name of the index to create")
    region: str = Field(default="us-east-1", description="AWS region for the index")

class CreateNamespaceInput(BaseModel):
    """Input schema for creating a namespace in Pinecone index."""
    index_name: str = Field(..., description="Name of the index")
    namespace: str = Field(..., description="Name of the namespace to create")

class UpsertDataInput(BaseModel):
    """Input schema for upserting data to Pinecone."""
    index_name: str = Field(..., description="Name of the index")
    namespace: str = Field(..., description="Name of the namespace")
    records: List[Dict] = Field(..., description="List of records to upsert")

class QueryDataInput(BaseModel):
    """Input schema for querying data from Pinecone."""
    index_name: str = Field(..., description="Name of the index")
    namespace: str = Field(..., description="Name of the namespace")
    query_vector: List[float] = Field(..., description="Query vector")
    top_k: int = Field(default=5, description="Number of results to return")
    filter: Optional[Dict] = Field(default=None, description="Filter criteria for the query")

# Custom tools
class CreatePineconeIndexTool(BaseTool):
    name: str = "Create Pinecone Index"
    description: str = "Creates a new Pinecone index with Llama text embedding model"
    args_schema: type[BaseModel] = CreateIndexInput

    def _run(self, index_name: str, region: str = "us-east-1") -> str:
        try:
            if pc.has_index(index_name):
                return f"Index '{index_name}' already exists"
            
            pc.create_index_for_model(
                name=index_name,
                cloud="aws",
                region=region,
                embed={
                    "model": "llama-text-embed-v2",
                    "field_map": {"text": "text"}
                }
            )
            return f"Successfully created index '{index_name}'"
        except Exception as e:
            return f"Error creating index: {str(e)}"

class CreateNamespaceTool(BaseTool):
    name: str = "Create Pinecone Namespace"
    description: str = "Creates a new namespace in a Pinecone index"
    args_schema: type[BaseModel] = CreateNamespaceInput

    def _run(self, index_name: str, namespace: str) -> str:
        try:
            index = pc.Index(index_name)
            # Namespace creation is implicit in Pinecone, we just need to verify the index exists
            if not pc.has_index(index_name):
                return f"Index '{index_name}' does not exist"
            return f"Namespace '{namespace}' is ready to use in index '{index_name}'"
        except Exception as e:
            return f"Error creating namespace: {str(e)}"

class UpsertDataTool(BaseTool):
    name: str = "Upsert Data to Pinecone"
    description: str = "Upserts data to a specific namespace in a Pinecone index"
    args_schema: type[BaseModel] = UpsertDataInput

    def _run(self, index_name: str, namespace: str, records: List[Dict]) -> str:
        try:
            index = pc.Index(index_name)
            index.upsert_records(namespace, records)
            time.sleep(10)  # Wait for indexing
            return f"Successfully upserted {len(records)} records to namespace '{namespace}'"
        except Exception as e:
            return f"Error upserting data: {str(e)}"

class QueryDataTool(BaseTool):
    name: str = "Query Pinecone Data"
    description: str = "Queries data from a specific namespace in a Pinecone index"
    args_schema: type[BaseModel] = QueryDataInput

    def _run(self, index_name: str, namespace: str, query_vector: List[float], 
             top_k: int = 5, filter: Optional[Dict] = None) -> str:
        try:
            index = pc.Index(index_name)
            results = index.query(
                namespace=namespace,
                vector=query_vector,
                top_k=top_k,
                filter=filter
            )
            return str(results)
        except Exception as e:
            return f"Error querying data: {str(e)}"

# Export the tools
__all__ = [
    'CreatePineconeIndexTool',
    'CreateNamespaceTool',
    'UpsertDataTool',
    'QueryDataTool'
] 