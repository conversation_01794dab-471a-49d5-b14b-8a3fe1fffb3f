# Import the Pinecone library
from pinecone import Pinecone

# Initialize a Pinecone client with your API key
pc = Pinecone(api_key="pcsk_58QuXn_PcGNJTFvhivdVERLmKPH4G4WQkeqJGEn5SAM3WXufm1xo3rdfJkuQyuPVAJSPGB")

# Create a dense index with integrated embedding
index_name = "proactive-ai"
if not pc.has_index(index_name):
    pc.create_index_for_model(
        name=index_name,
        cloud="aws",
        region="us-east-1",
        embed={
            "model":"llama-text-embed-v2",
            "field_map":{"scope_of_the_project": "scope_of_the_project"}
        }
    )

# target the index
index = pc.Index(index_name)

#define the scope of the project
text = '''Building a Agentic AI System - for Product Marketing in Shopify e-commerce.
The system should be able to handle the following tasks:
- Product Marketing (Content Creation, Social Media Marketing, Email Marketing, SEO, SEM  etc.)
- Mainly focussing on
    - Blog Creation (SEO friendly, product based blog, category based blog, etc.)
    - Product Description (Product based description, category based description, etc.)
    - Collection Creation (Category based collection, product based collection, etc.)
    - SEO (Keyword Research, On Page SEO, Off Page SEO, etc.)
'''

category = "Scope"

#upsert the data to the index
records = [
    {
        "id": "1",
        "text": text,
        "category": category
    }
]

index.upsert_records("scope_of_the_project", records)

# Wait for the upserted vectors to be indexed
import time
time.sleep(10)

# View stats for the index
stats = index.describe_index_stats()
print(stats)

# run this file as a script
if __name__ == "__main__":
    # Test the script functionality
    print("Running Pinecone script...")
    pc = Pinecone(api_key="pcsk_58QuXn_PcGNJTFvhivdVERLmKPH4G4WQkeqJGEn5SAM3WXufm1xo3rdfJkuQyuPVAJSPGB")
    index = pc.Index(index_name)
    print("Pinecone client initialized and index targeted.")
    print("Index stats:", index.describe_index_stats())
