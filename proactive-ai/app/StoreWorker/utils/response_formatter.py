import logging
from typing import Any, Dict, List, Optional, Union
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from ..logger.logger import info, error as log_error

# Configure logging
logger = logging.getLogger(__name__)

class ResponseStatus:
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    FAILURE = "failure"
    UNAUTHORIZED = "unauthorized"

class StandardResponse(BaseModel):
    status_code: int
    status: str
    message: str
    data: Optional[Any] = None
    error: Optional[str] = None
    apology: Optional[str] = None

def format_success_response(
    message: str,
    data: Any = None, 
    status_code: int = 200
) -> JSONResponse:
    """
    Format a successful response with the provided data.
    
    Args:
        message: A success message
        data: The data to include in the response
        status_code: HTTP status code (default: 200)
        
    Returns:
        JSONResponse: A formatted success response
    """
    response = StandardResponse(
        status_code=status_code,
        status=ResponseStatus.SUCCESS,
        message=message,
        data=data
    )
    
    info(f"Success response: {message}")
    return JSONResponse(content=response.dict(exclude_none=True), status_code=status_code)

def format_failure_response(
    message: str,
    error: Optional[str] = None,
    status_code: int = 400
) -> JSONResponse:
    """
    Format a failure response with details about the failure.
    
    Args:
        message: The failure message
        error: Optional error details
        status_code: HTTP status code (default: 400)
        
    Returns:
        JSONResponse: A formatted failure response
    """
    response = StandardResponse(
        status_code=status_code,
        status=ResponseStatus.FAILURE,
        message=message,
        error=error,
        apology="We apologize for the inconvenience. Please try again or contact support if the issue persists."
    )
    
    log_error(f"Failure response: {message}, error: {error}")
    return JSONResponse(content=response.dict(exclude_none=True), status_code=status_code)

def format_error_response(
    message: str,
    error: Optional[str] = None,
    status_code: int = 500
) -> JSONResponse:
    """
    Format an error response with details about the error.
    
    Args:
        message: The error message
        error: Optional error details
        status_code: HTTP status code (default: 500)
        
    Returns:
        JSONResponse: A formatted error response
    """
    response = StandardResponse(
        status_code=status_code,
        status=ResponseStatus.ERROR,
        message=message,
        error=error,
        apology="We apologize for this unexpected error. Our team has been notified and is working to resolve it."
    )
    
    log_error(f"Error response: {message}, error: {error}")
    return JSONResponse(content=response.dict(exclude_none=True), status_code=status_code)

def format_unauthorized_response(
    message: str = "Unauthorized access",
    status_code: int = 401
) -> JSONResponse:
    """
    Format an unauthorized response.
    
    Args:
        message: The unauthorized message
        status_code: HTTP status code (default: 401)
        
    Returns:
        JSONResponse: A formatted unauthorized response
    """
    response = StandardResponse(
        status_code=status_code,
        status=ResponseStatus.UNAUTHORIZED,
        message=message,
        error="Authentication required",
        apology="Access denied. Please ensure you are properly authenticated and have the necessary permissions."
    )
    
    log_error(f"Unauthorized response: {message}")
    return JSONResponse(content=response.dict(exclude_none=True), status_code=status_code)

def format_exception_response(
    exception: Exception,
    status_code: int = 500
) -> JSONResponse:
    """
    Format a response for an exception.
    
    Args:
        exception: The exception that was raised
        status_code: HTTP status code (default: 500)
        
    Returns:
        JSONResponse: A formatted exception response
    """
    # Handle HTTPException specially to preserve its status code
    if isinstance(exception, HTTPException):
        status_code = exception.status_code
        detail = exception.detail
    else:
        detail = str(exception)
    
    response = StandardResponse(
        status_code=status_code,
        status=ResponseStatus.ERROR,
        message="An unexpected error occurred",
        error=detail,
        apology="We apologize for this unexpected error. Our team has been notified and is working to resolve it."
    )
    
    log_error(f"Exception response: {detail}", exc_info=True)
    return JSONResponse(content=response.dict(exclude_none=True), status_code=status_code)

def handle_response(func):
    """
    Decorator to handle responses and exceptions in route handlers.
    
    Args:
        func: The route handler function to decorate
        
    Returns:
        A wrapped function that handles formatting responses
    """
    async def wrapper(*args, **kwargs):
        try:
            result = await func(*args, **kwargs)
            # If the result is already a Response object, return it as is
            if isinstance(result, JSONResponse):
                return result
            return format_success_response(message=result)
        except Exception as e:
            return format_exception_response(e)
    
    return wrapper
