from fastapi import APIRouter, Request, Depends
from fastapi.responses import JSONResponse
from typing import Dict, Any
import logging
from .low_stock_report_gsheets import run_low_stock_report_workflow

router = APIRouter()

# Mocked function to get user/entity info from DB (replace with real logic)
def get_user_context(request: Request) -> Dict[str, Any]:
    # In production, extract user_id/entity_id from token/session/db
    # Here, we mock it for demonstration
    return {
        "user_id": "user-123",
        "entity_id": "entity-123"
    }

@router.post("/run-mcp-low-stock-workflow")
async def run_mcp_low_stock_workflow(request: Request):
    """
    Endpoint to run the low stock to Google Sheets workflow using MCP and user context.
    Receives user input and workflow parameters in the body.
    """
    try:
        body = await request.json()
        # Extract user/entity context (mocked for now)
        user_context = get_user_context(request)
        # Option 1: Fetch all connected accounts for the user (mocked)
        fetch_connected_accounts = body.get("fetch_connected_accounts", False)
        connected_accounts = None
        if fetch_connected_accounts:
            # TODO: Replace with real call to composio to fetch connected accounts
            connected_accounts = [
                {"integration_name": "google_sheets", "id": "gsheets-acc-1"},
                {"integration_name": "shopify", "id": "shopify-acc-1"}
            ]
        # Option 2: Pass entity_id and let the workflow handle connection
        # Prepare workflow input
        workflow_input = {
            "entity_id": user_context["entity_id"],
            "user_id": user_context["user_id"],
            "connected_accounts": connected_accounts,
            "workflow_params": body.get("workflow_params", {})
        }
        # Call the real workflow script
        result = run_low_stock_report_workflow(workflow_input)
        return JSONResponse(content=result)
    except Exception as e:
        logging.error(f"[run-mcp-low-stock-workflow] Error: {str(e)}", exc_info=True)
        return JSONResponse(content={"status": "error", "message": str(e)}, status_code=500) 