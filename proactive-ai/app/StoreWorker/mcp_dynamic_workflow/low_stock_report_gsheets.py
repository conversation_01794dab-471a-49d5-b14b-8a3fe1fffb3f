import logging
from typing import Dict, Any, Optional
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# --- Utility: Dynamic Multi-Server Adapter ---
def get_mcp_tools(entity_id: str):
    MCP_SERVER_PARAMS_LIST = [
        {
            "url": "https://mcp.composio.dev/composio/server/257b86f1-aa58-4cc8-9a11-91ae55383d61/sse",
            "transport": "sse",
            "entity_id": entity_id
        },
        {
            "url": "https://mcp.shopify.chakril.site/sse",
            "transport": "sse",
            "entity_id": entity_id
        }
    ]
    return MCPServerAdapter(MCP_SERVER_PARAMS_LIST)

# --- CrewAI-based Orchestration ---
def run_low_stock_report_workflow(context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Runs the low stock to Google Sheets workflow using CrewAI and MCP best practices.
    context: dict with keys entity_id, user_id, connected_accounts, workflow_params
    """
    entity_id = context.get("entity_id")
    connected_accounts = context.get("connected_accounts") or []
    workflow_params = context.get("workflow_params") or {}
    # Extract connected_account_ids for Google Sheets and Shopify
    gsheets_account_id = None
    shopify_account_id = None
    for acc in connected_accounts:
        if acc["integration_name"] == "google_sheets":
            gsheets_account_id = acc["id"]
        if acc["integration_name"] == "shopify":
            shopify_account_id = acc["id"]
    llm = LLM(
        model="azure/development-proactive-ai",
        api_key=os.environ.get("AZURE_OPENAI_API_KEY", "demo-key"),
        base_url=os.environ.get("AZURE_OPENAI_BASE_URL", "https://bincha-ai.openai.azure.com")
    )
    with get_mcp_tools(entity_id) as tools:
        # Dynamically select tools from MCP
        admin_schema_tools = [t for t in tools if "admin_schema" in t.name.lower() or "schema" in t.name.lower()]
        shopify_tools = [t for t in tools if "shopify" in t.name.lower()]
        gsheets_tools = [t for t in tools if "google" in t.name.lower() or "sheet" in t.name.lower()]
        # --- AGENTS ---
        agents = {
            "fetcher": Agent(
                role="Shopify Product Fetcher",
                goal="Fetch the first 10 products from Shopify using the admin_schema tool and Shopify tools.",
                backstory="Expert in retrieving product data from Shopify using dynamic tools.",
                tools=admin_schema_tools + shopify_tools,
                verbose=True,
                llm=llm
            ),
            "filter": Agent(
                role="Low Stock Filter",
                goal="Filter products with inventory less than a threshold.",
                backstory="Specialist in inventory analysis.",
                verbose=True,
                llm=llm
            ),
            "sheet_writer": Agent(
                role="Google Sheets Writer",
                goal="Write low stock products to a Google Sheet.",
                backstory="Expert in Google Sheets automation.",
                tools=gsheets_tools,
                verbose=True,
                llm=llm
            ),
            "summary": Agent(
                role="Summary Agent",
                goal="Summarize the process and output a human-readable summary.",
                backstory="Provides clear summaries for business users.",
                verbose=True,
                llm=llm
            )
        }
        # --- TASKS ---
        tasks = [
            Task(
                description=(
                    "Fetch the first 10 products from Shopify using the admin_schema tool to get the GraphQL string, "
                    "then use the Shopify tools to fetch the products. Use the following credentials: "
                    f"entity_id: {entity_id}, shopify_connected_account_id: {shopify_account_id}. "
                    "Return the raw JSON response from Shopify."
                ),
                expected_output="Raw JSON response containing the first 10 products from Shopify.",
                agent=agents["fetcher"],
                inputs={}
            ),
            Task(
                description=(
                    f"Filter the products from the previous step to only include those with inventory_quantity (or totalInventory) less than a threshold. "
                    "Return the filtered list as raw JSON."
                ),
                expected_output="Raw JSON list of low stock products.",
                agent=agents["filter"]
            ),
            Task(
                description=(
                    "Write the filtered low stock products to a Google Sheet using the available Google Sheets tool. "
                    f"entity_id: {entity_id}, gsheets_connected_account_id: {gsheets_account_id}. Return the confirmation or result as raw JSON."
                ),
                expected_output="Raw JSON confirmation of rows added to the sheet.",
                agent=agents["sheet_writer"]
            ),
            Task(
                description=(
                    "Summarize the process: how many products were fetched, how many were low stock, and confirmation of writing to the sheet. "
                    "Return a human-readable summary."
                ),
                expected_output="A summary string.",
                agent=agents["summary"]
            )
        ]
        crew = Crew(
            agents=list(agents.values()),
            tasks=tasks,
            process=Process.sequential,
            verbose=True,
            memory=False,
            cache=True
        )
        result = crew.kickoff(inputs={})
    return {
        "status": "success",
        "result": result
    } 