# Admin Endpoints Documentation

This document provides comprehensive documentation for the admin endpoints in the StoreWorker API.

## Overview

The admin endpoints provide elevated access for managing template details and configurations in the database. These endpoints are designed for administrative operations and require special authentication.

## Authentication

All admin endpoints require the following header for authentication:

```
store-worker-admin: bincha@dmin
```

The admin token value is configurable via the `STORE_WORKER_ADMIN_TOKEN` environment variable.

## Base URL

All admin endpoints are available under the `/admin` prefix:

```
http://localhost:8000/api/v1/admin/
```

## Endpoints

### 1. GET /admin/templates

Retrieve all templates with full details and admin fields.

**Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/admin/templates" \
  -H "store-worker-admin: bincha@dmin"
```

**Response:**
```json
{
  "status_code": 200,
  "status": "success",
  "message": "Templates retrieved successfully",
  "data": {
    "templates": [
      {
        "id": 1,
        "uuid": "template-uuid-123",
        "title": "Sample Template",
        "description": "A sample template",
        "emoji": "📝",
        "template_type": "content",
        "category": "blogging",
        "tags": ["blog", "content"],
        "summary": "Template for creating blog content",
        "base_configuration": [...],
        "store_data_type": {...},
        "store_data": [...],
        "include_metadata": true,
        "is_visible": true,
        "is_restricted_viewing_enabled": false,
        "store_ids_with_access": [],
        "marked_for_deletion": false,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "created_by": null,
        "updated_by": null
      }
    ]
  }
}
```

### 2. GET /admin/templates/{template_uuid}

Retrieve a specific template by UUID with full details and admin fields.

**Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/admin/templates/template-uuid-123" \
  -H "store-worker-admin: bincha@dmin"
```

**Response:**
```json
{
  "status_code": 200,
  "status": "success",
  "message": "Template retrieved successfully",
  "data": {
    "id": 1,
    "uuid": "template-uuid-123",
    "title": "Sample Template",
    // ... full template details
  }
}
```

### 3. POST /admin/templates

Create a new template with configurations.

**Request:**
```bash
curl -X POST "http://localhost:8000/api/v1/admin/templates" \
  -H "store-worker-admin: bincha@dmin" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "New Template",
    "description": "A new template created via admin API",
    "emoji": "🆕",
    "template_type": "content",
    "category": "general",
    "tags": ["new", "template"],
    "summary": "New template summary",
    "base_configuration": [
      {
        "name": "title",
        "type": "string",
        "required": true,
        "description": "Template title"
      }
    ],
    "store_data_type": {"category": "general", "type": "single"},
    "store_data": [],
    "include_metadata": true,
    "is_visible": true,
    "is_restricted_viewing_enabled": false
  }'
```

**Response:**
```json
{
  "status_code": 200,
  "status": "success",
  "message": "Template created successfully",
  "data": {
    "id": 2,
    "uuid": "new-template-uuid-456",
    "title": "New Template",
    // ... full created template details
  }
}
```

### 4. PUT /admin/templates/{template_uuid}

Update template details and/or configurations. Auto-detects update type based on provided fields.

**Request:**
```bash
curl -X PUT "http://localhost:8000/api/v1/admin/templates/template-uuid-123" \
  -H "store-worker-admin: bincha@dmin" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated Template Title",
    "description": "Updated description",
    "tags": ["updated", "template"],
    "is_visible": false
  }'
```

**Response:**
```json
{
  "status_code": 200,
  "status": "success",
  "message": "Template updated successfully",
  "data": {
    "id": 1,
    "uuid": "template-uuid-123",
    "title": "Updated Template Title",
    // ... full updated template details
  }
}
```

### 5. DELETE /admin/templates/{template_uuid}

Soft delete a template by UUID.

**Request:**
```bash
curl -X DELETE "http://localhost:8000/api/v1/admin/templates/template-uuid-123" \
  -H "store-worker-admin: bincha@dmin"
```

**Response:**
```json
{
  "status_code": 200,
  "status": "success",
  "message": "Template deleted successfully",
  "data": {
    "template_uuid": "template-uuid-123",
    "deleted": true
  }
}
```

## Error Responses

### 403 Forbidden (Missing Admin Access)
```json
{
  "detail": "Admin access denied. Valid admin credentials required."
}
```

### 404 Not Found (Template Not Found)
```json
{
  "status_code": 404,
  "status": "error",
  "message": "Template not found",
  "error": "No template found with the specified UUID"
}
```

### 500 Internal Server Error
```json
{
  "status_code": 500,
  "status": "error",
  "message": "Failed to create template",
  "error": "Database insert operation failed"
}
```

## Field Descriptions

### Template Fields

- **id**: Internal database ID (auto-generated)
- **uuid**: Unique identifier for the template (auto-generated if not provided)
- **title**: Template title (required for creation)
- **description**: Template description
- **emoji**: Emoji icon for the template
- **template_type**: Type/category of template
- **category**: Template category
- **tags**: Array of tags for categorization
- **summary**: Brief summary of the template
- **base_configuration**: Base configuration schema
- **store_data_type**: Data type configuration
- **store_data**: Associated store data
- **include_metadata**: Whether to include metadata
- **is_visible**: Template visibility flag
- **is_restricted_viewing_enabled**: Access restriction flag
- **store_ids_with_access**: Array of store IDs with access (when restricted)
- **marked_for_deletion**: Soft deletion flag
- **created_at**: Creation timestamp
- **updated_at**: Last update timestamp
- **created_by**: Creator identifier
- **updated_by**: Last updater identifier

## Update Logic

The PUT endpoint auto-detects the update type based on provided fields:

- **Template Details**: Fields like title, description, emoji, etc.
- **Configuration**: Fields like base_configuration, store_data_type, etc.
- **Both**: When both types of fields are provided

All fields are optional for updates, allowing for partial updates.

## Testing

Use the provided test script at `app/StoreWorker/Tools/test_admin_endpoints.py` to test all admin endpoints:

```bash
python app/StoreWorker/Tools/test_admin_endpoints.py
```
