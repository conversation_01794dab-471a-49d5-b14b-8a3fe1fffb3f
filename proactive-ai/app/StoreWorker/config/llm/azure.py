from crewai import LLM
import os
from portkey_ai import createHeaders, PORTKEY_GATEWAY_URL


# Azure OpenAI API key
# AZURE_OPENAI_API_KEY = os.environ["AZURE_OPENAI_API_KEY"]
AZURE_OPENAI_API_KEY = "********************************"

# Azure OpenAI API endpoint
# AZURE_OPENAI_ENDPOINT = os.environ["AZURE_OPENAI_ENDPOINT"]
AZURE_OPENAI_ENDPOINT = "https://bincha-ai.openai.azure.com/openai/deployments/development-proactive-ai/chat/completions?api-version=2024-08-01-preview"

# plain_azure_llm = LLM(
#     model="azure/development-proactive-ai",
#     base_url=AZURE_OPENAI_ENDPOINT,
#     api_key=AZURE_OPENAI_API_KEY
# )

PORTKEY_API_KEY = os.environ["PORTKEY_API_KEY"]
PORTKEY_VIRTUAL_KEY = os.environ["PORTKEY_VIRTUAL_KEY"]
PORTKEY_GATEWAY_URL = os.environ["PORTKEY_BASE_URL"]

azure_llm = LLM(
    model="gpt-4o",
    base_url=PORTKEY_GATEWAY_URL,
    api_key="dummy", # We are using Virtual key
    extra_headers=createHeaders(
        api_key=PORTKEY_API_KEY,
        virtual_key=PORTKEY_VIRTUAL_KEY, # Enter your OpenAI Virtual key from Portkey
        trace_id="llm1"
    )
)

# Export the LLM instance from azure.py
__all__ = ['azure_llm']