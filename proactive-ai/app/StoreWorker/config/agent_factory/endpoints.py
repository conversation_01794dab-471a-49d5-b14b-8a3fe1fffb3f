# baseEndpoint = "https://stage.agent-factory.chakril.site"
baseEndpoint = "http://localhost:6001"
api_version = "api/v1"
azure_function_endpoint = "azure"
create_subroute = "create"
main_health_check_endpoint = f"{baseEndpoint}/health"
azure_health_check_endpoint = f"{baseEndpoint}/{api_version}/{azure_function_endpoint}/health"
azure_create_function_endpoint = f"{baseEndpoint}/{api_version}/{azure_function_endpoint}/{create_subroute}/crew-function"
azure_create_multi_crew_function_endpoint = f"{baseEndpoint}/{api_version}/{azure_function_endpoint}/{create_subroute}/multi-crew-function"
azure_create_template_crew_function_endpoint = f"{baseEndpoint}/{api_version}/{azure_function_endpoint}/{create_subroute}/template-crew-function"
