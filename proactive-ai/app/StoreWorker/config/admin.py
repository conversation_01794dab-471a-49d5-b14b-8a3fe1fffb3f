"""
Admin configuration for StoreWorker API
Contains admin-specific settings and constants
"""

import os
from typing import Dict, Any

# Admin authentication configuration
ADMIN_HEADER_KEY = "store-worker-admin"
ADMIN_HEADER_VALUE = os.getenv("STORE_WORKER_ADMIN_TOKEN", "bincha@dmin")

# Admin access configuration
ADMIN_CONFIG: Dict[str, Any] = {
    "header_key": ADMIN_HEADER_KEY,
    "header_value": ADMIN_HEADER_VALUE,
    "require_admin_for_endpoints": [
        "/admin/templates",
        "/admin/templates/{template_uuid}",
    ],
    "admin_permissions": {
        "create_templates": True,
        "update_templates": True,
        "delete_templates": True,
        "view_all_templates": True,
        "view_deleted_templates": True,
        "manage_template_visibility": True,
        "manage_template_access": True,
    }
}

def get_admin_config() -> Dict[str, Any]:
    """
    Get the admin configuration dictionary
    
    Returns:
        Dict[str, Any]: Admin configuration settings
    """
    return ADMIN_CONFIG

def is_admin_endpoint(endpoint_path: str) -> bool:
    """
    Check if an endpoint requires admin access
    
    Args:
        endpoint_path: The endpoint path to check
        
    Returns:
        bool: True if endpoint requires admin access
    """
    return endpoint_path.startswith("/admin/")

def get_admin_header_config() -> tuple[str, str]:
    """
    Get the admin header key and value
    
    Returns:
        tuple: (header_key, header_value)
    """
    return ADMIN_HEADER_KEY, ADMIN_HEADER_VALUE
