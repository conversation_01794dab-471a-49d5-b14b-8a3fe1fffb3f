from pydantic import BaseModel, HttpUrl
from typing import List, Optional, Dict, Any
from datetime import datetime

# Common/Shared Models
class Worker(BaseModel):
    id: str
    title: str
    description: str
    icon: str
    iconColor: str
    selected: bool

class TaskSummary(BaseModel):
    name: str
    description: str

# Configuration Related Models
class ConfigurationField(BaseModel):
    key: str
    type: str
    component: str
    required: bool
    display_name: str
    placeholder: Optional[str] = None
    description: str
    options: Optional[List[str]] = None

class WorkerConfiguration(BaseModel):
    agent_name: str
    name: str
    description: str
    fields: List[ConfigurationField]

# Task/Worker Related Models
class Agent(BaseModel):
    uuid: str
    id: int
    title: str
    description: str
    icon: str
    iconColor: str
    selected: bool

class TaskData(BaseModel):
    task_id: str
    task: str
    agents: List[Agent]

class TaskDetails(BaseModel):
    task_id: str
    name: str
    description: str

class CommanderRequest(BaseModel):
    team_id: str
    store_id: Optional[int] = None
    tasks: List[TaskDetails]

class ListWorkersRequest(BaseModel):
    tasks: List[CommanderRequest]

class CrewAgent(BaseModel):
    Name: str
    Role: str
    Goal: List[str]
    Tools: Optional[dict] = None

class CrewTask(BaseModel):
    uuid: Optional[str] = None
    Name: str
    Description: str
    Output: str
    Agent: List[str]
    Dependencies: Optional[str] = None

class CrewCrew(BaseModel):
    Name: str
    Description: str
    Agents: List[str]
    Tasks: List[str]

class Tool(BaseModel):
    Name: str
    Description: str
    Parameters: Dict[str, Any]

class FormattedAgent(BaseModel):
    uuid: Optional[str] = None
    id: int
    title: str
    description: str
    icon: str
    iconColor: str
    selected: bool

class CommanderResponse(BaseModel):
    task_id: str
    Name: str
    Description: str
    Agents: List[Agent]
    Tasks: List[CrewTask]
    Crew: CrewCrew
    Tools: List[Tool]
    formatted_agents: List[FormattedAgent]

class SummarizerRequest(BaseModel):
    team_id: str
    tasks: List[TaskData]

class SummarizerResponse(BaseModel):
    task_id: str
    task_name: str
    summary: List[TaskSummary]

class SummarizerResponseBody(BaseModel):
    team_id: str
    tasks: List[SummarizerResponse]

class ConfigurationRequest(BaseModel):
    team_id: str
    tasks: List[TaskData]

class ConfigurationResponse(BaseModel):
    task_id: str
    name: str
    type: str
    agents: List[Agent]
    configurations: List[WorkerConfiguration]
    store_data: Optional[dict] = None

class ConfigurationResponseBody(BaseModel):
    team_id: str
    tasks: List[ConfigurationResponse]

# Validation Related Models
class ValidationRequest(BaseModel):
    query: str

class ValidationResult(BaseModel):
    team_id: str
    workflow_name: str
    tasks: List[dict] = []
    valid_variations: List[str] = []

# NLQ Task Approval Request Model
class NlqTaskApprovalRequest(BaseModel):
    team_id: str
    selected_tasks: List[dict]

# Define request body models for products sync
class ProductVariantNode(BaseModel):
    id: str
    title: str
    price: str
    position: int
    displayName: str

class ProductVariant(BaseModel):
    cursor: Optional[str] = None
    node: ProductVariantNode

class Product(BaseModel):
    id: str
    title: str
    handle: str
    totalInventory: Optional[int]
    tags: List[str]
    vendor: str
    description: str
    createdAt: datetime
    variants: List[ProductVariant]
    image_url: Optional[str]
    product_url: Optional[str]

# Define request body models for competitor domains
class CompetitorDomains(BaseModel):
    competitors_domain: List[HttpUrl]

# Define request body models for collection scheduling
class UserMetadata(BaseModel):
    domain: Optional[str] = None
    payment_status: Optional[str] = None
    plan_type: Optional[str] = None
    plan_name: Optional[str] = None

class ScheduleSettings(BaseModel):
    frequency: Optional[str] = None
    count: Optional[int] = 3
    impact_type: Optional[str] = None
    start_date: Optional[str] = None

class CollectionSchedulingRequest(BaseModel):
    user_metadata: Optional[UserMetadata] = None
    attributes: dict  # Using dict to allow flexible attribute categories
    schedule_settings: Optional[ScheduleSettings] = None

# Update your ProductVariant model to include callback_url
class ProductSyncRequest(BaseModel):
    products: List[Product]
    callback_url: Optional[str] = None

# Workflow Selection Models
class WorkerSelection(BaseModel):
    worker_uuid: str
    selected: bool
    task_id: Optional[str] = None
    configuration: Optional[dict] = None

class WorkflowSelectionRequest(BaseModel):
    team_id: str
    store_id: int
    selected_workers: List[WorkerSelection]
    manually_added_workers: Optional[List[WorkerSelection]] = None
    modified_by: Optional[str] = None

# Workflow State Models
class WorkerState(BaseModel):
    worker_uuid: str
    name: str
    role: str
    status: str
    task_id: Optional[str] = None
    configuration: Optional[dict] = None
    selected: bool = True

class WorkflowStateResponse(BaseModel):
    team_id: str
    store_id: int
    workflow_data: dict
    selected_workers: List[WorkerState]
    manually_added_workers: Optional[List[WorkerState]] = None
    status: str
    created_at: datetime
    updated_at: datetime

class TeamsResponseBody(BaseModel):
    team_id: str
    store_id: int
    name: str
    description: str
    status: str
    current_running_status: str | None = None
    created_at: str | None = None

class TeamDetailsResponse(BaseModel):
    team_id: Optional[str] = None
    store_id: Optional[int] = None
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    current_running_status: Optional[str] = None
    created_at: Optional[str] = None
    agents: Optional[List[Agent]] = None
    current_configuration: Optional[List[WorkerConfiguration]] = None
    current_key_values: Optional[Dict[str, Any]] = None

# New models for approved-task-configuration endpoint
class ApprovedTaskConfiguration(BaseModel):
    configurations: List[WorkerConfiguration]
    values: Dict[str, Any]
    task_id: Optional[str] = None
    name: Optional[str] = None
    type: Optional[str] = None
    store_data: Optional[dict] = None


class ApprovedTaskConfigurationRequest(BaseModel):
    team_id: str
    tasks: List[ApprovedTaskConfiguration]

class ApprovedTaskConfigurationResponse(BaseModel):
    task_id: Optional[str] = None
    name: Optional[str] = None
    type: Optional[str] = None
    configurations: List[WorkerConfiguration]
    values: Dict[str, Any]

class ApprovedTaskConfigurationResponseBody(BaseModel):
    team_id: str
    tasks: List[ApprovedTaskConfigurationResponse]

# New models for list-workers-for-task endpoint
class CrewAgentResponse(BaseModel):
    uuid: str
    Name: str
    Role: str
    Goal: str
    Backstory: Optional[str] = None
    Tools: List[str]

class CrewTaskResponse(BaseModel):
    uuid: str
    Name: str
    Description: str
    Output: str
    Agent: List[str]
    Dependencies: Optional[Any] = None

class CrewCrewResponse(BaseModel):
    Name: str
    Description: str
    Agents: List[str]
    Tasks: List[str]

class CommanderResponseBody(BaseModel):
    task_id: str
    Name: str
    Description: str
    Agents: List[CrewAgentResponse]
    Tasks: List[CrewTaskResponse]
    Crew: CrewCrewResponse
    Tools: List[Tool]
    formatted_agents: List[FormattedAgent]

class CommanderResponseList(BaseModel):
    team_id: str
    tasks: List[Dict[str, Any]]

# New model for store workers endpoint
class StoreWorkerAgent(BaseModel):
    uuid: str
    name: str
    role: str
    goal: str
    backstory: Optional[str] = None
    tools: Optional[List[str]] = None

class CreateWorkflowRequest(BaseModel):
    team_id: str
    callback_url: Optional[str] = None

class FormatAgentFactoryInputRequest(BaseModel):
    team_id: str
    keys: Optional[List[str]] = None

class FormatAgentFactoryInputResponse(BaseModel):
    result: dict

class TemplateSummary(BaseModel):
    uuid: str
    title: str
    description: str | None = None
    emoji: str | None = None
    template_type: str | None = None
    category: str | None = None
    tags: list[str] | None = None
    summary: str | None = None

class TemplateSummaryList(BaseModel):
    templates: list[TemplateSummary]

class TemplateJobRun(BaseModel):
    job_id: str
    job_status: str
    job_duration: Optional[str] = None
    created_at: Optional[str] = None
    job_title: Optional[str] = None
    # Add more fields if needed (e.g., errors, output_data)

class StoreDataBlock(BaseModel):
    store_data_type: Optional[dict] = None
    store_data: Optional[list] = None
    include_metadata: Optional[bool] = None

class TemplateDetails(BaseModel):
    uuid: str
    title: str
    description: str | None = None
    emoji: str | None = None
    template_type: str | None = None
    category: str | None = None
    tags: list[str] | None = None
    agents_list: list[dict] | None = None
    base_configuration: list[dict] | None = None
    store_data_type: dict | None = None
    include_metadata: bool | None = None
    summary: str | None = None
    is_visible: bool | None = None
    is_restricted_viewing_enabled: bool | None = None
    store_ids_with_access: list[int] | None = None
    marked_for_deletion: bool | None = None
    created_at: str | None = None
    updated_at: str | None = None
    current_configuration: list[dict] | None = None
    current_key_values: dict | None = None
    latest_job_runs: Optional[List[TemplateJobRun]] = None
    store_data: Optional[StoreDataBlock] = None

class TemplateDetailsResponse(BaseModel):
    template: TemplateDetails

class StoreTemplateConfigurationRequest(BaseModel):
    template_uuid: str
    configurations: list[dict] | None = None
    values: dict | None = None
    additional_inputs: dict | None = None
    store_data_type: dict | None = None
    store_data: list[dict] | None = None
    include_metadata: bool | None = None
    configuration_history: list[dict] | None = None
    config_update: bool = False

class StoreTemplateConfigurationResponse(BaseModel):
    success: bool
    config: dict | None = None

class StoreTemplateValuesRequest(BaseModel):
    template_uuid: str
    current_key_values: dict

class StoreTemplateValuesResponse(BaseModel):
    success: bool
    config: dict | None = None

# Admin-specific models for template management
class AgentAdmin(BaseModel):
    name: str
    role: str
    goal: str
    backstory: Optional[str] = None

class AdminTemplateDetails(BaseModel):
    """Complete template details including all admin fields"""
    id: Optional[int] = None
    uuid: str
    title: str
    description: Optional[str] = None
    emoji: Optional[str] = None
    template_type: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    summary: Optional[str] = None
    base_configuration: Optional[List[dict]] = None
    store_data_type: Optional[dict] = None
    store_data: Optional[List[dict]] = None
    include_metadata: Optional[bool] = None
    is_visible: Optional[bool] = None
    is_restricted_viewing_enabled: Optional[bool] = None
    store_ids_with_access: Optional[List[int]] = None
    marked_for_deletion: Optional[bool] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    agents_list: Optional[List[AgentAdmin]] = None

class AdminTemplateResponse(BaseModel):
    """Response model for single template admin operations"""
    template: AdminTemplateDetails

class AdminTemplateListResponse(BaseModel):
    """Response model for admin template list operations"""
    templates: List[AdminTemplateDetails]

class AdminCreateTemplateRequest(BaseModel):
    """Request model for creating a new template via admin"""
    title: str
    description: Optional[str] = None
    emoji: Optional[str] = None
    template_type: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    summary: Optional[str] = None
    base_configuration: Optional[List[dict]] = None
    store_data_type: Optional[dict] = None
    store_data: Optional[List[dict]] = None
    include_metadata: Optional[bool] = True
    is_visible: Optional[bool] = True
    is_restricted_viewing_enabled: Optional[bool] = False
    store_ids_with_access: Optional[List[int]] = None
    agents_list: Optional[List[AgentAdmin]] = None

class AdminUpdateTemplateRequest(BaseModel):
    """Request model for updating template via admin - all fields optional for partial updates"""
    title: Optional[str] = None
    description: Optional[str] = None
    emoji: Optional[str] = None
    template_type: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    summary: Optional[str] = None
    base_configuration: Optional[List[dict]] = None
    store_data_type: Optional[dict] = None
    store_data: Optional[List[dict]] = None
    include_metadata: Optional[bool] = None
    is_visible: Optional[bool] = None
    is_restricted_viewing_enabled: Optional[bool] = None
    store_ids_with_access: Optional[List[int]] = None
    agents_list: Optional[List[AgentAdmin]] = None

class AdminCreateDeploymentRequest(BaseModel):
    """Request model for creating a new deployment record via admin"""
    template_id: int
    url: str
    function_name: Optional[str] = None
    endpoint: Optional[str] = None
    last_deployed_on: Optional[str] = None  # ISO format string
    deployment_history: Optional[list] = None
    deploy_status: Optional[str] = None
    errors: Optional[str] = None
    function_app_name: Optional[str] = None
    function_app_id: Optional[int] = None

class TemplateAnalyticsResponse(BaseModel):
    total_templates: int
    total_time_saved_minutes: float
    total_time_saved_hours: float
    success_rate: float
    failure_rate: float
    recent_activity: list[TemplateJobRun]
    average_run_duration_seconds: float
    templates_by_category: dict
    most_used_templates: list[dict]
    average_time_saved_per_template_minutes: float
    top_performing_templates: list[dict]
    templates_with_no_usage: list[dict]

