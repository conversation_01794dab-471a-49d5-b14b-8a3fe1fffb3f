# routes.py
import os
from fastapi import APIRouter
from uuid import uuid4
from typing import List
from pydantic import BaseModel
from StoreWorker.api.v1.sync import router as sync_router
from StoreWorker.api.v1.workflow import router as workflow_router
from StoreWorker.api.v1.resources import router as resources_router
from StoreWorker.api.v1.dashboard import router as dashboard_router
from StoreWorker.api.v1.startup import router as startup_router
from StoreWorker.api.v1.fetch_data import router as fetch_data_router
from StoreWorker.api.v1.update_data import router as update_data_router
from StoreWorker.api.v1.template import router as template_router
from StoreWorker.api.v1.admin import router as admin_router

# Import Composio testing router (optional)
try:
    from StoreWorker.api.v1.composio_test import router as composio_test_router
    COMPOSIO_TEST_AVAILABLE = True
except ImportError:
    COMPOSIO_TEST_AVAILABLE = False

# Import Composio testing router (optional)
try:
    from StoreWorker.api.v1.composio_test import router as composio_test_router
    COMPOSIO_TEST_AVAILABLE = True
except ImportError:
    COMPOSIO_TEST_AVAILABLE = False

router = APIRouter()

router.include_router(sync_router, prefix="/sync", tags=["Sync store Data"])
router.include_router(workflow_router, prefix="/workflow", tags=["Workflow"])
router.include_router(resources_router, prefix="/resources", tags=["Resources"])
router.include_router(dashboard_router, prefix="/dashboard", tags=["Dashboard"])
router.include_router(startup_router, prefix="/startup", tags=["Startup"])
router.include_router(fetch_data_router, prefix="/fetch", tags=["Fetch Data"])
router.include_router(update_data_router, prefix="/update-data", tags=["Update Data"])
router.include_router(template_router, prefix="/templates", tags=["Template"])
router.include_router(admin_router, prefix="/admin", tags=["Admin"])

# Include Composio testing router if available
if COMPOSIO_TEST_AVAILABLE:
    router.include_router(composio_test_router, prefix="/composio", tags=["Composio Testing"])

# Include Composio testing router if available
if COMPOSIO_TEST_AVAILABLE:
    router.include_router(composio_test_router, prefix="/composio", tags=["Composio Testing"])

class ValidationResult(BaseModel):
    task_id: str
    sub_tasks: List[dict] = []
    variations: List[str] = []
    workflow_name: str

@router.get("/")
async def root():
    return {"message": "API - Store Worker RUNNING"}
