"""
Test script for admin endpoints
This script provides examples and test cases for the new admin endpoints
"""

import requests
import json
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000/api/v1/admin"
ADMIN_HEADER = {"store-worker-admin": "bincha@dmin"}

def test_get_all_templates():
    """Test GET /admin/templates endpoint"""
    print("Testing GET /admin/templates...")
    
    try:
        response = requests.get(f"{BASE_URL}/templates", headers=ADMIN_HEADER)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_get_template_by_uuid(template_uuid: str):
    """Test GET /admin/templates/{template_uuid} endpoint"""
    print(f"Testing GET /admin/templates/{template_uuid}...")
    
    try:
        response = requests.get(f"{BASE_URL}/templates/{template_uuid}", headers=ADMIN_HEADER)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_create_template():
    """Test POST /admin/templates endpoint"""
    print("Testing POST /admin/templates...")
    
    template_data = {
        "title": "Test Admin Template",
        "description": "A test template created via admin API",
        "emoji": "🧪",
        "template_type": "test",
        "category": "testing",
        "tags": ["test", "admin", "api"],
        "summary": "This is a test template for admin functionality",
        "base_configuration": [
            {
                "name": "test_config",
                "type": "string",
                "required": True,
                "description": "Test configuration field"
            }
        ],
        "store_data_type": {"category": "test", "type": "single"},
        "store_data": [{"id": "test-001", "name": "Test Data"}],
        "include_metadata": True,
        "is_visible": True,
        "is_restricted_viewing_enabled": False
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/templates", 
            headers={**ADMIN_HEADER, "Content-Type": "application/json"},
            json=template_data
        )
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_update_template(template_uuid: str):
    """Test PUT /admin/templates/{template_uuid} endpoint"""
    print(f"Testing PUT /admin/templates/{template_uuid}...")
    
    update_data = {
        "title": "Updated Test Admin Template",
        "description": "Updated description via admin API",
        "tags": ["test", "admin", "api", "updated"],
        "summary": "This template has been updated via admin API"
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/templates/{template_uuid}",
            headers={**ADMIN_HEADER, "Content-Type": "application/json"},
            json=update_data
        )
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_delete_template(template_uuid: str):
    """Test DELETE /admin/templates/{template_uuid} endpoint"""
    print(f"Testing DELETE /admin/templates/{template_uuid}...")
    
    try:
        response = requests.delete(f"{BASE_URL}/templates/{template_uuid}", headers=ADMIN_HEADER)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_unauthorized_access():
    """Test endpoints without admin header"""
    print("Testing unauthorized access...")
    
    try:
        response = requests.get(f"{BASE_URL}/templates")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    except Exception as e:
        print(f"Error: {e}")
        return None

def run_full_test_suite():
    """Run complete test suite for admin endpoints"""
    print("=" * 50)
    print("ADMIN ENDPOINTS TEST SUITE")
    print("=" * 50)
    
    # Test unauthorized access
    print("\n1. Testing unauthorized access...")
    test_unauthorized_access()
    
    # Test get all templates
    print("\n2. Testing get all templates...")
    templates_response = test_get_all_templates()
    
    # Test create template
    print("\n3. Testing create template...")
    create_response = test_create_template()
    
    if create_response and create_response.get('data', {}).get('uuid'):
        template_uuid = create_response['data']['uuid']
        
        # Test get specific template
        print(f"\n4. Testing get template by UUID: {template_uuid}...")
        test_get_template_by_uuid(template_uuid)
        
        # Test update template
        print(f"\n5. Testing update template: {template_uuid}...")
        test_update_template(template_uuid)
        
        # Test delete template
        print(f"\n6. Testing delete template: {template_uuid}...")
        test_delete_template(template_uuid)
    else:
        print("\nSkipping UUID-based tests due to create failure")
    
    print("\n" + "=" * 50)
    print("TEST SUITE COMPLETED")
    print("=" * 50)

if __name__ == "__main__":
    run_full_test_suite()
