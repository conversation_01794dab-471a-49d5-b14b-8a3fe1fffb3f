# Composio Testing Endpoints

This document provides comprehensive testing instructions for the Composio integration endpoints.

## Base URL

All endpoints are available under the `/composio` prefix:
```
http://localhost:8000/api/v1/composio/
```

## Authentication

All endpoints require authentication. Include your auth token in the request headers:
```
Authorization: Bearer <your_token>
```

## Available Endpoints

### 1. **GET /composio/status**
Check Composio integration status and environment.

**Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/composio/status" \
  -H "Authorization: Bearer <your_token>"
```

**Response:**
```json
{
  "success": true,
  "message": "Composio status retrieved successfully",
  "data": {
    "composio_available": true,
    "environment_valid": {
      "composio_api_key": true,
      "openai_api_key": true
    },
    "supported_apps_count": 300
  }
}
```

### 2. **GET /composio/apps**
Get list of all available Composio apps.

**Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/composio/apps" \
  -H "Authorization: Bearer <your_token>"
```

**Response:**
```json
{
  "success": true,
  "message": "Available apps retrieved successfully",
  "data": {
    "total_apps": 300,
    "all_apps": ["gmail", "github", "slack", "..."],
    "categories": {
      "productivity": ["gmail", "googlecalendar", "notion"],
      "development": ["github", "jira", "linear"],
      "communication": ["slack", "discord", "microsoftteams"]
    }
  }
}
```

### 3. **POST /composio/tools**
Get Composio tools for specific apps.

**Request:**
```bash
curl -X POST "http://localhost:8000/api/v1/composio/tools" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "apps": ["github", "gmail"],
    "tags": ["important"],
    "entity_id": "store_123"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Tools retrieved successfully",
  "data": {
    "entity_id": "store_123",
    "apps": ["github", "gmail"],
    "tags": ["important"],
    "tools_count": 15,
    "tools": ["<tool_1>", "<tool_2>", "..."]
  }
}
```

### 4. **POST /composio/actions**
Get tools for specific actions.

**Request:**
```bash
curl -X POST "http://localhost:8000/api/v1/composio/actions" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "actions": ["GITHUB_GET_THE_AUTHENTICATED_USER", "GMAIL_SEND_EMAIL"],
    "entity_id": "store_123"
  }'
```

### 5. **POST /composio/execute**
Execute a Composio action directly.

**Request:**
```bash
curl -X POST "http://localhost:8000/api/v1/composio/execute" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "GITHUB_GET_THE_AUTHENTICATED_USER",
    "params": {},
    "entity_id": "store_123"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Action executed successfully",
  "data": {
    "entity_id": "store_123",
    "action": "GITHUB_GET_THE_AUTHENTICATED_USER",
    "params": {},
    "result": {
      "successful": true,
      "data": {
        "login": "username",
        "id": 12345,
        "name": "User Name"
      }
    }
  }
}
```

### 6. **GET /composio/connections/{entity_id}**
Get connected accounts for an entity.

**Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/composio/connections/store_123" \
  -H "Authorization: Bearer <your_token>"
```

### 7. **POST /composio/connect**
Initiate connection for an app (OAuth flow).

**Request:**
```bash
curl -X POST "http://localhost:8000/api/v1/composio/connect" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "app_name": "github",
    "entity_id": "store_123",
    "timeout": 120
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Connection initiated successfully",
  "data": {
    "entity_id": "store_123",
    "app_name": "github",
    "connection_initiated": true,
    "oauth_required": true,
    "redirect_url": "https://github.com/login/oauth/authorize?...",
    "message": "Please visit the redirect URL to complete OAuth for github"
  }
}
```

### 8. **POST /composio/quick-crew**
Create and optionally execute a quick CrewAI crew.

**Request:**
```bash
curl -X POST "http://localhost:8000/api/v1/composio/quick-crew" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "apps": ["github"],
    "task_description": "List my GitHub repositories and create a summary",
    "entity_id": "store_123",
    "execute": false
  }'
```

### 9. **POST /composio/search-tools**
Search for tools using natural language.

**Request:**
```bash
curl -X POST "http://localhost:8000/api/v1/composio/search-tools" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "use_case": "create a GitHub issue",
    "apps": ["github"],
    "advanced": false,
    "entity_id": "store_123"
  }'
```

### 10. **GET /composio/test-connection/{app_name}**
Test connection for a specific app.

**Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/composio/test-connection/github?entity_id=store_123" \
  -H "Authorization: Bearer <your_token>"
```

### 11. **GET /composio/actions/{app_name}**
List all available actions for an app.

**Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/composio/actions/github" \
  -H "Authorization: Bearer <your_token>"
```

### 12. **GET /composio/schema/{action_name}**
Get schema for a specific action.

**Request:**
```bash
curl -X GET "http://localhost:8000/api/v1/composio/schema/GITHUB_CREATE_AN_ISSUE" \
  -H "Authorization: Bearer <your_token>"
```

## Testing Workflow

### Step 1: Check Status
```bash
curl -X GET "http://localhost:8000/api/v1/composio/status" \
  -H "Authorization: Bearer <your_token>"
```

### Step 2: List Available Apps
```bash
curl -X GET "http://localhost:8000/api/v1/composio/apps" \
  -H "Authorization: Bearer <your_token>"
```

### Step 3: Test Connection (before connecting)
```bash
curl -X GET "http://localhost:8000/api/v1/composio/test-connection/github" \
  -H "Authorization: Bearer <your_token>"
```

### Step 4: Connect App (if needed)
```bash
curl -X POST "http://localhost:8000/api/v1/composio/connect" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{"app_name": "github", "entity_id": "store_123"}'
```

### Step 5: Get Tools
```bash
curl -X POST "http://localhost:8000/api/v1/composio/tools" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{"apps": ["github"]}'
```

### Step 6: Execute Action
```bash
curl -X POST "http://localhost:8000/api/v1/composio/execute" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "GITHUB_GET_THE_AUTHENTICATED_USER",
    "params": {}
  }'
```

### Step 7: Create Quick Crew
```bash
curl -X POST "http://localhost:8000/api/v1/composio/quick-crew" \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "apps": ["github"],
    "task_description": "Get my GitHub profile information",
    "execute": true
  }'
```

## Error Responses

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

Common error codes:
- **503**: Composio not available (missing dependencies)
- **400**: Invalid request parameters
- **401**: Authentication failed
- **404**: Resource not found
- **500**: Internal server error

## Prerequisites

1. **Install Composio packages:**
   ```bash
   pip install composio_crewai composio_core
   ```

2. **Set environment variables:**
   ```bash
   export COMPOSIO_API_KEY="your_composio_api_key"
   export OPENAI_API_KEY="your_openai_api_key"
   ```

3. **Connect apps via CLI:**
   ```bash
   composio login
   composio add github
   composio add gmail
   ```

## Troubleshooting

- **503 errors**: Check if Composio packages are installed
- **Connection failures**: Ensure apps are connected via `composio add <app>`
- **Authentication errors**: Verify COMPOSIO_API_KEY is set
- **Tool retrieval failures**: Check if entity has connected accounts
