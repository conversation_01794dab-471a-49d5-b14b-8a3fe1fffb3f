#!/usr/bin/env python3
"""
Test script for Composio endpoints
==================================

This script tests the Composio integration endpoints to ensure they work correctly
both with and without Composio packages installed.

Usage:
    python test_endpoints.py [--host localhost] [--port 8000] [--token your_token]
"""

import requests
import json
import argparse
import sys
from typing import Dict, Any


def test_endpoint(base_url: str, endpoint: str, method: str = "GET", 
                 data: Dict[str, Any] = None, headers: Dict[str, str] = None) -> Dict[str, Any]:
    """Test a single endpoint and return the response."""
    url = f"{base_url}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers, timeout=10)
        else:
            return {"error": f"Unsupported method: {method}"}
        
        return {
            "status_code": response.status_code,
            "success": response.status_code < 400,
            "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
        }
    except requests.exceptions.RequestException as e:
        return {"error": str(e), "success": False}


def main():
    parser = argparse.ArgumentParser(description="Test Composio endpoints")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", default="8000", help="Server port")
    parser.add_argument("--token", help="Auth token (optional)")
    
    args = parser.parse_args()
    
    base_url = f"http://{args.host}:{args.port}/api/v1/storeworker/composio"
    
    # Set up headers
    headers = {"Content-Type": "application/json"}
    if args.token:
        headers["Authorization"] = f"Bearer {args.token}"
    
    print(f"Testing Composio endpoints at: {base_url}")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        {
            "name": "Status Check",
            "endpoint": "/status",
            "method": "GET",
            "description": "Check if Composio is available and environment is valid"
        },
        {
            "name": "Available Apps",
            "endpoint": "/apps", 
            "method": "GET",
            "description": "Get list of available Composio apps"
        },
        {
            "name": "Get Tools for Apps",
            "endpoint": "/tools",
            "method": "POST",
            "data": {"apps": ["github", "gmail"]},
            "description": "Get tools for specific apps"
        },
        {
            "name": "Test Connection",
            "endpoint": "/test-connection/github",
            "method": "GET",
            "description": "Test GitHub connection"
        },
        {
            "name": "List GitHub Actions",
            "endpoint": "/actions/github",
            "method": "GET", 
            "description": "List available GitHub actions"
        },
        {
            "name": "Get Action Schema",
            "endpoint": "/schema/GITHUB_GET_THE_AUTHENTICATED_USER",
            "method": "GET",
            "description": "Get schema for GitHub user action"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        print(f"   Description: {test_case['description']}")
        print(f"   Endpoint: {test_case['method']} {test_case['endpoint']}")
        
        result = test_endpoint(
            base_url=base_url,
            endpoint=test_case["endpoint"],
            method=test_case["method"],
            data=test_case.get("data"),
            headers=headers
        )
        
        results.append({
            "test": test_case["name"],
            "result": result
        })
        
        if result["success"]:
            print(f"   ✅ Status: {result['status_code']} - SUCCESS")
            if isinstance(result["response"], dict):
                if result["response"].get("success"):
                    print(f"   📊 Response: {result['response'].get('message', 'No message')}")
                else:
                    print(f"   ⚠️  API Error: {result['response'].get('message', 'Unknown error')}")
            else:
                print(f"   📄 Response: {result['response'][:100]}...")
        else:
            print(f"   ❌ FAILED: {result.get('error', f'Status {result.get(\"status_code\", \"unknown\")}')}")
            if "response" in result and isinstance(result["response"], dict):
                print(f"   📄 Error Details: {result['response'].get('message', 'No details')}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    successful = sum(1 for r in results if r["result"]["success"])
    total = len(results)
    
    print(f"✅ Successful: {successful}/{total}")
    print(f"❌ Failed: {total - successful}/{total}")
    
    if successful == total:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print(f"\n⚠️  {total - successful} test(s) failed")
        
        # Show failed tests
        failed_tests = [r for r in results if not r["result"]["success"]]
        for test in failed_tests:
            print(f"   - {test['test']}: {test['result'].get('error', 'Unknown error')}")
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
