# Composio Integration for CrewAI

This directory contains comprehensive helper functions for integrating Composio tools with CrewAI agents in your StoreWorker application.

## Overview

Composio provides access to 300+ external tools and APIs with built-in authentication. This integration allows your CrewAI agents to interact with services like GitHub, Gmail, Slack, Shopify, and many more.

## Installation

### 1. Install Required Packages

```bash
pip install composio_crewai composio_core crewai langchain_openai
```

### 2. Set Environment Variables

```bash
export COMPOSIO_API_KEY="your_composio_api_key"
export OPENAI_API_KEY="your_openai_api_key"  # Optional, for LLM usage
```

### 3. Get Composio API Key

1. Visit [Composio Dashboard](https://app.composio.dev/developers)
2. Sign up/login and get your API key
3. Set it as an environment variable

## Quick Start

### Basic Setup

```python
from app.StoreWorker.Tools import ComposioManager, CrewAIComposioIntegration

# Initialize Composio manager
manager = ComposioManager(entity_id="user_123")

# Get tools for specific apps
github_tools = manager.get_tools_for_apps(['github'])
gmail_tools = manager.get_tools_for_apps(['gmail'])

# Create CrewAI integration
crew_integration = CrewAIComposioIntegration(manager)
```

### Connect Apps

Before using tools, you need to connect the apps:

```bash
# Login to Composio
composio login

# Connect apps (this will open OAuth flows)
composio add github
composio add gmail
composio add slack
```

### Create Agent with Tools

```python
from langchain_openai import ChatOpenAI

# Initialize LLM
llm = ChatOpenAI(model="gpt-4o-mini")

# Create agent with GitHub tools
agent = crew_integration.create_agent_with_tools(
    tools=github_tools,
    role="GitHub Assistant",
    goal="Help with GitHub operations",
    backstory="You are an AI assistant specialized in GitHub workflows.",
    llm=llm
)

# Create task
task = crew_integration.create_task(
    description="List my GitHub repositories and create a summary",
    agent=agent,
    expected_output="A summary of GitHub repositories"
)

# Create and execute crew
crew = crew_integration.create_crew(agents=[agent], tasks=[task])
result = crew.kickoff()
```

## Available Files

### `composio_helpers.py`
Main integration module with core classes:
- `ComposioManager`: Main class for managing Composio operations
- `CrewAIComposioIntegration`: Helper for CrewAI integration
- `ComposioToolsPresets`: Predefined tool collections

### `composio_config.py`
Configuration and constants:
- `SupportedApps`: Enum of supported applications
- `AGENT_TEMPLATES`: Predefined agent configurations
- `APP_CATEGORIES`: Grouped app collections

### `composio_examples.py`
Comprehensive examples showing:
- Basic setup and tool retrieval
- Connection management
- CrewAI integration
- Direct action execution
- Async operations

## Common Use Cases

### 1. Productivity Assistant

```python
from app.StoreWorker.Tools import ComposioToolsPresets

manager = ComposioManager(entity_id="productivity_user")
presets = ComposioToolsPresets(manager)

# Get productivity tools (Gmail, Calendar, Notion, etc.)
tools = presets.get_productivity_tools()
```

### 2. Development Workflow

```python
# Get development tools (GitHub, Jira, Slack)
dev_tools = presets.get_development_tools()

# Create development assistant
agent = crew_integration.create_agent_with_tools(
    tools=dev_tools,
    role="Development Assistant",
    goal="Manage development workflows",
    backstory="You help with code management and project coordination."
)
```

### 3. E-commerce Management

```python
# Get e-commerce tools (Shopify, Stripe)
ecommerce_tools = presets.get_ecommerce_tools()

# Set up for specific store
store_manager = setup_composio_for_store(store_id=123)
```

### 4. Quick Crew Creation

```python
from app.StoreWorker.Tools import create_quick_crew

# Create a crew quickly for specific apps
crew = create_quick_crew(
    apps=['github', 'slack'],
    task_description="Check GitHub issues and notify team on Slack",
    entity_id="team_lead"
)

result = crew.kickoff()
```

## Direct Action Execution

For simple operations without CrewAI:

```python
# Execute GitHub action directly
result = manager.execute_action_direct(
    action="GITHUB_GET_THE_AUTHENTICATED_USER",
    params={},
    entity_id="user_123"
)

if result.get("successful"):
    user_info = result.get("data")
    print(f"GitHub user: {user_info.get('login')}")
```

## Store Integration

For store-specific workflows:

```python
from app.StoreWorker.Tools import setup_composio_for_store

# Set up Composio for a specific store
store_id = 123
manager = setup_composio_for_store(store_id)

# This creates entity_id as "store_123"
# All operations will be scoped to this store
```

## Error Handling

The helpers include comprehensive error handling:

```python
# Test connection before using
is_connected = test_composio_connection("github", "user_123")
if not is_connected:
    print("GitHub not connected. Run: composio add github")

# Check available apps
apps = manager.get_available_apps()
print(f"Available: {len(apps)} apps")

# Validate environment
from app.StoreWorker.Tools import validate_environment
env_status = validate_environment()
if not env_status["composio_api_key"]:
    print("COMPOSIO_API_KEY not set")
```

## Async Operations

For async workflows:

```python
from app.StoreWorker.Tools import async_execute_action

# Execute action asynchronously
result = await async_execute_action(
    composio_manager=manager,
    action="GITHUB_LIST_REPOSITORIES_FOR_THE_AUTHENTICATED_USER",
    params={},
    entity_id="user_123"
)
```

## Supported Apps

The integration supports 300+ apps including:

**Productivity**: Gmail, Google Calendar, Notion, Google Docs, Todoist
**Development**: GitHub, Jira, Linear, Bitbucket
**Communication**: Slack, Discord, Microsoft Teams, Zoom  
**Marketing**: Twitter, LinkedIn, HubSpot, Mailchimp
**E-commerce**: Shopify, Stripe, Square
**CRM**: Salesforce, Pipedrive
**Project Management**: Asana, Trello, Monday, ClickUp

## Troubleshooting

### Common Issues

1. **Import Error**: Install required packages
   ```bash
   pip install composio_crewai composio_core
   ```

2. **API Key Error**: Set environment variable
   ```bash
   export COMPOSIO_API_KEY="your_key"
   ```

3. **No Tools Found**: Connect the app first
   ```bash
   composio add <app_name>
   ```

4. **Connection Failed**: Check OAuth flow completion
   ```bash
   composio apps  # List connected apps
   ```

### Getting Help

- Check the examples in `composio_examples.py`
- Review configuration in `composio_config.py`
- Visit [Composio Documentation](https://docs.composio.dev)
- Check [Composio Discord](https://dub.composio.dev/discord)

## Contributing

When adding new functionality:
1. Update the appropriate helper class
2. Add examples to `composio_examples.py`
3. Update configuration if needed
4. Test with multiple apps
5. Update this README
