"""
Slack Integration for StoreWorker Tools

This module provides Slack notification integration for StoreWorker operations,
following the existing Tools directory pattern.
"""

import os
import sys
import logging
from typing import Dict, Any, Optional
from datetime import datetime

# Add the root directory to the path to import slack_utils
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from slack_notifications.slack_utils import (
    SlackNotifier,
    send_store_worker_notification,
    send_rank_collections_notification,
    send_store_blogs_notification,
    send_installation_notification
)

# Set up logging
logger = logging.getLogger(__name__)


class StoreWorkerSlackNotifier:
    """
    Slack notifier specifically for StoreWorker operations.
    Integrates with the existing StoreWorker Tools pattern.
    """
    
    def __init__(self, project_name: str = None):
        """
        Initialize the StoreWorker Slack notifier.
        
        Args:
            project_name: Default project name for notifications
        """
        self.project_name = project_name
        self.notifier = SlackNotifier()
        
    def notify_job_started(
        self,
        job_type: str,
        job_id: str,
        workflow_name: str = None,
        project_name: str = None
    ) -> bool:
        """Notify that a StoreWorker job has started."""
        return send_store_worker_notification(
            project_name=project_name or self.project_name,
            job_type=job_type,
            status='started',
            details={
                'job_id': job_id,
                'workflow_name': workflow_name,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )
    
    def notify_job_completed(
        self,
        job_type: str,
        job_id: str,
        execution_time: str,
        products_processed: int,
        success_count: int,
        error_count: int = 0,
        project_name: str = None
    ) -> bool:
        """Notify that a StoreWorker job has completed."""
        return send_store_worker_notification(
            project_name=project_name or self.project_name,
            job_type=job_type,
            status='completed',
            details={
                'job_id': job_id,
                'execution_time': execution_time,
                'products_processed': products_processed,
                'success_count': success_count,
                'error_count': error_count,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )
    
    def notify_job_failed(
        self,
        job_type: str,
        job_id: str,
        error_message: str,
        project_name: str = None
    ) -> bool:
        """Notify that a StoreWorker job has failed."""
        return send_store_worker_notification(
            project_name=project_name or self.project_name,
            job_type=job_type,
            status='failed',
            details={
                'job_id': job_id,
                'error_message': error_message,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )
    
    def notify_workflow_deployment(
        self,
        workflow_name: str,
        status: str,
        version: str = None,
        project_name: str = None
    ) -> bool:
        """Notify about workflow deployment."""
        return send_store_worker_notification(
            project_name=project_name or self.project_name,
            job_type='workflow_deployment',
            status=status,
            details={
                'workflow_name': workflow_name,
                'version': version,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )


class RankCollectionsSlackNotifier:
    """
    Slack notifier specifically for RankCollections operations.
    """
    
    def __init__(self, project_name: str = None):
        self.project_name = project_name
        self.notifier = SlackNotifier()
    
    def notify_collection_creation(
        self,
        collection_name: str,
        status: str,
        keywords_count: int = None,
        products_count: int = None,
        ranking_score: float = None,
        processing_time: str = None,
        project_name: str = None
    ) -> bool:
        """Notify about collection creation."""
        return send_rank_collections_notification(
            project_name=project_name or self.project_name,
            collection_name=collection_name,
            status=status,
            details={
                'keywords_count': keywords_count,
                'products_count': products_count,
                'ranking_score': ranking_score,
                'processing_time': processing_time,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )


class StoreBlogsSlackNotifier:
    """
    Slack notifier specifically for StoreBlogs operations.
    """
    
    def __init__(self, project_name: str = None):
        self.project_name = project_name
        self.notifier = SlackNotifier()
    
    def notify_article_creation(
        self,
        article_title: str,
        status: str,
        word_count: int = None,
        seo_score: float = None,
        author: str = None,
        project_name: str = None
    ) -> bool:
        """Notify about article creation."""
        return send_store_blogs_notification(
            project_name=project_name or self.project_name,
            article_title=article_title,
            status=status,
            details={
                'word_count': word_count,
                'seo_score': seo_score,
                'author': author,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )


class InstallationSlackNotifier:
    """
    Slack notifier specifically for Installation operations.
    """
    
    def __init__(self, project_name: str = None):
        self.project_name = project_name
        self.notifier = SlackNotifier()
    
    def notify_installation(
        self,
        installation_type: str,
        status: str,
        user_info: Dict[str, Any],
        version: str = None,
        features_enabled: list = None,
        project_name: str = None
    ) -> bool:
        """Notify about installation."""
        return send_installation_notification(
            project_name=project_name or self.project_name,
            user_info=user_info,
            status=status,
            details={
                'installation_type': installation_type,
                'version': version,
                'features_enabled': features_enabled,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )


# Convenience functions for quick integration
def notify_store_worker_job(
    project_name: str,
    job_type: str,
    status: str,
    **kwargs
) -> bool:
    """Quick function to send StoreWorker notifications."""
    notifier = StoreWorkerSlackNotifier(project_name)
    
    if status == 'started':
        return notifier.notify_job_started(
            job_type=job_type,
            job_id=kwargs.get('job_id', 'unknown'),
            workflow_name=kwargs.get('workflow_name')
        )
    elif status == 'completed':
        return notifier.notify_job_completed(
            job_type=job_type,
            job_id=kwargs.get('job_id', 'unknown'),
            execution_time=kwargs.get('execution_time', '0s'),
            products_processed=kwargs.get('products_processed', 0),
            success_count=kwargs.get('success_count', 0),
            error_count=kwargs.get('error_count', 0)
        )
    elif status == 'failed':
        return notifier.notify_job_failed(
            job_type=job_type,
            job_id=kwargs.get('job_id', 'unknown'),
            error_message=kwargs.get('error_message', 'Unknown error')
        )
    else:
        # Use generic notification
        return send_store_worker_notification(
            project_name=project_name,
            job_type=job_type,
            status=status,
            details=kwargs
        )


def notify_collection_job(
    project_name: str,
    collection_name: str,
    status: str,
    **kwargs
) -> bool:
    """Quick function to send RankCollections notifications."""
    notifier = RankCollectionsSlackNotifier(project_name)
    return notifier.notify_collection_creation(
        collection_name=collection_name,
        status=status,
        **kwargs
    )


def notify_blog_job(
    project_name: str,
    article_title: str,
    status: str,
    **kwargs
) -> bool:
    """Quick function to send StoreBlogs notifications."""
    notifier = StoreBlogsSlackNotifier(project_name)
    return notifier.notify_article_creation(
        article_title=article_title,
        status=status,
        **kwargs
    )


def notify_installation_job(
    project_name: str,
    installation_type: str,
    status: str,
    user_info: Dict[str, Any],
    **kwargs
) -> bool:
    """Quick function to send Installation notifications."""
    notifier = InstallationSlackNotifier(project_name)
    return notifier.notify_installation(
        installation_type=installation_type,
        status=status,
        user_info=user_info,
        **kwargs
    )


# Decorator for automatic Slack notifications
def slack_notify(operation_type: str, project_name: str = None):
    """
    Decorator to automatically send Slack notifications for function execution.
    
    Usage:
        @slack_notify('storeWorker', 'my-project')
        def my_job_function():
            # Your job logic here
            pass
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_name = func.__name__
            start_time = datetime.now()
            
            # Send started notification
            if operation_type == 'storeWorker':
                notify_store_worker_job(
                    project_name=project_name or 'default',
                    job_type=func_name,
                    status='started',
                    job_id=f"{func_name}_{start_time.strftime('%Y%m%d_%H%M%S')}"
                )
            
            try:
                # Execute the function
                result = func(*args, **kwargs)
                
                # Calculate execution time
                end_time = datetime.now()
                execution_time = str(end_time - start_time)
                
                # Send completed notification
                if operation_type == 'storeWorker':
                    notify_store_worker_job(
                        project_name=project_name or 'default',
                        job_type=func_name,
                        status='completed',
                        job_id=f"{func_name}_{start_time.strftime('%Y%m%d_%H%M%S')}",
                        execution_time=execution_time
                    )
                
                return result
                
            except Exception as e:
                # Send failed notification
                if operation_type == 'storeWorker':
                    notify_store_worker_job(
                        project_name=project_name or 'default',
                        job_type=func_name,
                        status='failed',
                        job_id=f"{func_name}_{start_time.strftime('%Y%m%d_%H%M%S')}",
                        error_message=str(e)
                    )
                
                raise e
        
        return wrapper
    return decorator
