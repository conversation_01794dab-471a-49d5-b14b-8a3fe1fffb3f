"""
Composio Integration Helper Functions for CrewAI
=================================================

This module provides comprehensive helper functions for integrating Composio tools
with CrewAI agents. It includes functionality for:

- Tool fetching and management
- Authentication and connection handling
- Direct tool execution
- Entity and user management
- Error handling and logging

Requirements:
- pip install composio_crewai composio_core
- COMPOSIO_API_KEY environment variable
- Individual app integrations as needed

Usage:
    from app.StoreWorker.Tools.composio_helpers import ComposioManager

    manager = ComposioManager()
    tools = manager.get_tools_for_apps(['github', 'gmail'])
    agent = manager.create_crewai_agent_with_tools(tools, role="Assistant")
"""

import os
import logging
import asyncio
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta

try:
    from composio_crewai import ComposioToolSet, App, Action
    from composio import ComposioToolSet as BaseComposioToolSet
    from crewai import Agent, Task, Crew
    COMPOSIO_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Composio not available: {e}")
    COMPOSIO_AVAILABLE = False
    # Create dummy classes for type hints when Composio is not available
    class App:
        pass
    class Action:
        pass
    class ComposioToolSet:
        pass
    class BaseComposioToolSet:
        pass
    class Agent:
        pass
    class Task:
        pass
    class Crew:
        pass

# Set up logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
formatter = logging.Formatter('[%(asctime)s] %(levelname)s %(name)s: %(message)s')
handler.setFormatter(formatter)
if not logger.hasHandlers():
    logger.addHandler(handler)


class ComposioManager:
    """
    Main class for managing Composio integrations with CrewAI.
    Provides high-level methods for tool management, authentication, and execution.
    """

    def __init__(self, api_key: Optional[str] = None, entity_id: str = "default"):
        """
        Initialize ComposioManager.

        Args:
            api_key: Composio API key (defaults to COMPOSIO_API_KEY env var)
            entity_id: Default entity ID for user context
        """
        if not COMPOSIO_AVAILABLE:
            raise ImportError("Composio packages not installed. Run: pip install composio_crewai composio_core")

        self.api_key = api_key or os.getenv('COMPOSIO_API_KEY')
        if not self.api_key:
            raise ValueError("COMPOSIO_API_KEY environment variable or api_key parameter required")

        self.entity_id = entity_id
        self.toolset = ComposioToolSet(api_key=self.api_key)
        self.base_toolset = BaseComposioToolSet(api_key=self.api_key)

        logger.info(f"ComposioManager initialized with entity_id: {entity_id}")

    def get_available_apps(self) -> List[str]:
        """
        Get list of all available Composio apps.

        Returns:
            List of app names
        """
        try:
            # Get all App enum values with safe attribute access
            apps = []
            for app in App:
                if hasattr(app, 'value'):
                    apps.append(app.value)
                elif hasattr(app, 'name'):
                    apps.append(app.name)
                else:
                    apps.append(str(app))
            logger.info(f"Found {len(apps)} available apps")
            return apps
        except Exception as e:
            logger.error(f"Error getting available apps: {e}")
            return []

    def get_tools_for_apps(self, apps: List[Union[str, App]], tags: Optional[List[str]] = None) -> List[Any]:
        """
        Get tools for specific apps with optional tag filtering.

        Args:
            apps: List of app names or App enums
            tags: Optional list of tags to filter tools

        Returns:
            List of tool definitions for CrewAI
        """
        try:
            # Convert string app names to App enums
            app_enums = []
            for app in apps:
                if isinstance(app, str):
                    try:
                        app_enums.append(App(app.upper()))
                    except ValueError:
                        logger.warning(f"Unknown app: {app}")
                        continue
                else:
                    app_enums.append(app)

            if not app_enums:
                logger.warning("No valid apps provided")
                return []

            # Get tools with optional tag filtering
            kwargs = {'apps': app_enums}
            if tags:
                kwargs['tags'] = tags

            tools = self.toolset.get_tools(**kwargs)

            # Safe logging of app names
            app_names = []
            for app in app_enums:
                if hasattr(app, 'value'):
                    app_names.append(app.value)
                elif hasattr(app, 'name'):
                    app_names.append(app.name)
                else:
                    app_names.append(str(app))

            logger.info(f"Retrieved {len(tools)} tools for apps: {app_names}")
            return tools

        except Exception as e:
            logger.error(f"Error getting tools for apps {apps}: {e}")
            return []

    def get_tools_for_actions(self, actions: List[Union[str, Action]]) -> List[Any]:
        """
        Get tools for specific actions.

        Args:
            actions: List of action names or Action enums

        Returns:
            List of tool definitions for CrewAI
        """
        try:
            # Convert string action names to Action enums
            action_enums = []
            for action in actions:
                if isinstance(action, str):
                    try:
                        action_enums.append(Action(action.upper()))
                    except ValueError:
                        logger.warning(f"Unknown action: {action}")
                        continue
                else:
                    action_enums.append(action)

            if not action_enums:
                logger.warning("No valid actions provided")
                return []

            tools = self.toolset.get_tools(actions=action_enums)
            logger.info(f"Retrieved {len(tools)} tools for {len(action_enums)} actions")
            return tools

        except Exception as e:
            logger.error(f"Error getting tools for actions {actions}: {e}")
            return []

    def find_tools_by_use_case(self, use_case: str, apps: Optional[List[Union[str, App]]] = None,
                              advanced: bool = False) -> List[Any]:
        """
        Find tools using natural language description (experimental).

        Args:
            use_case: Natural language description of the task
            apps: Optional list of apps to scope the search
            advanced: Use advanced search for complex queries

        Returns:
            List of tool definitions for CrewAI
        """
        try:
            # Convert apps to enums if provided
            app_enums = None
            if apps:
                app_enums = []
                for app in apps:
                    if isinstance(app, str):
                        try:
                            app_enums.append(App(app.upper()))
                        except ValueError:
                            logger.warning(f"Unknown app: {app}")
                            continue
                    else:
                        app_enums.append(app)

            # Find relevant actions
            kwargs = {'use_case': use_case, 'advanced': advanced}
            if app_enums:
                kwargs['apps'] = app_enums

            relevant_actions = self.toolset.find_actions_by_use_case(**kwargs)

            if relevant_actions:
                tools = self.toolset.get_tools(actions=relevant_actions)
                logger.info(f"Found {len(tools)} tools for use case: '{use_case}'")
                return tools
            else:
                logger.info(f"No tools found for use case: '{use_case}'")
                return []

        except Exception as e:
            logger.error(f"Error finding tools by use case '{use_case}': {e}")
            return []

    def execute_action_direct(self, action: Union[str, Action], params: Dict[str, Any],
                             entity_id: Optional[str] = None,
                             connected_account_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute a Composio action directly without LLM involvement.

        Args:
            action: Action name or Action enum
            params: Parameters for the action
            entity_id: Entity ID (defaults to instance default)
            connected_account_id: Specific connection ID (optional)

        Returns:
            Execution result dictionary
        """
        try:
            # Convert string to Action enum if needed
            if isinstance(action, str):
                try:
                    action = Action(action.upper())
                except ValueError:
                    logger.error(f"Unknown action: {action}")
                    return {"successful": False, "error": f"Unknown action: {action}"}

            # Use provided entity_id or default
            entity_id = entity_id or self.entity_id

            # Execute action
            kwargs = {
                'action': action,
                'params': params,
                'entity_id': entity_id
            }
            if connected_account_id:
                kwargs['connected_account_id'] = connected_account_id

            result = self.toolset.execute_action(**kwargs)

            # Safe logging of action name
            action_name = action
            if hasattr(action, 'value'):
                action_name = action.value
            elif hasattr(action, 'name'):
                action_name = action.name

            logger.info(f"Executed action {action_name} for entity {entity_id}")
            return result

        except Exception as e:
            logger.error(f"Error executing action {action}: {e}")
            return {"successful": False, "error": str(e)}

    def get_entity(self, entity_id: Optional[str] = None):
        """
        Get entity object for user management.

        Args:
            entity_id: Entity ID (defaults to instance default)

        Returns:
            Entity object
        """
        try:
            entity_id = entity_id or self.entity_id
            entity = self.toolset.get_entity(id=entity_id)
            logger.info(f"Retrieved entity: {entity_id}")
            return entity
        except Exception as e:
            logger.error(f"Error getting entity {entity_id}: {e}")
            return None

    def initiate_connection(self, app_name: Union[str, App], entity_id: Optional[str] = None):
        """
        Initiate connection for an app and entity.

        Args:
            app_name: App name or App enum
            entity_id: Entity ID (defaults to instance default)

        Returns:
            Connection request object
        """
        try:
            # Store original app name for logging
            original_app_name = app_name

            # Convert string to App enum if needed
            if isinstance(app_name, str):
                try:
                    app_name = App(app_name.upper())
                except ValueError:
                    logger.error(f"Unknown app: {original_app_name}")
                    return None

            entity = self.get_entity(entity_id)
            if not entity:
                logger.error(f"Failed to get entity for entity_id: {entity_id}")
                return None

            connection_request = entity.initiate_connection(app_name=app_name)

            # Use original app name for logging if app_name doesn't have value attribute
            app_display_name = original_app_name
            if hasattr(app_name, 'value'):
                app_display_name = app_name.value
            elif hasattr(app_name, 'name'):
                app_display_name = app_name.name

            logger.info(f"Initiated connection for {app_display_name} and entity {entity.id}")
            return connection_request

        except Exception as e:
            logger.error(f"Error initiating connection for {original_app_name}: {e}")
            return None

    def wait_for_connection(self, connection_request, timeout: int = 120):
        """
        Wait for connection to become active (for OAuth flows).

        Args:
            connection_request: Connection request object from initiate_connection
            timeout: Timeout in seconds

        Returns:
            Active connection object or None
        """
        try:
            if not connection_request:
                return None

            if connection_request.redirectUrl:
                logger.info(f"Please visit: {connection_request.redirectUrl}")
                logger.info(f"Waiting for connection to become active (timeout: {timeout}s)...")

                active_connection = connection_request.wait_until_active(
                    client=self.toolset.client,
                    timeout=timeout
                )
                logger.info(f"Connection successful! ID: {active_connection.id}")
                return active_connection
            else:
                # Non-OAuth flow
                logger.info("Connection established (non-OAuth flow)")
                return connection_request

        except Exception as e:
            logger.error(f"Error waiting for connection: {e}")
            return None

    def get_connected_accounts(self, entity_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all connected accounts for an entity.

        Args:
            entity_id: Entity ID (defaults to instance default)

        Returns:
            List of connected account dictionaries
        """
        try:
            logger.info(f"Getting connected accounts for entity {entity_id} in composio_helpers.py")
            entity_id = entity_id or self.entity_id
            entity = self.get_entity(entity_id)
            if not entity:
                logger.error(f"No entity found for entity_id: {entity_id}")
                return []

            logger.info(f"Entity found for entity_id: {entity_id} - {entity}")

            # Try different methods to get connected accounts
            accounts = []

            # Method 1: Try using entity.client.connections.list() (from documentation)
            try:
                if hasattr(entity, 'client') and hasattr(entity.client, 'connections'):
                    connections_obj = entity.client.connections
                    if hasattr(connections_obj, 'list'):
                        accounts = connections_obj.list()
                        logger.info(f"Method 1 success: Found {len(accounts)} connections using entity.client.connections.list()")
                    else:
                        logger.warning(f"entity.client.connections methods: {dir(connections_obj)}")
            except Exception as method1_error:
                logger.warning(f"Method 1 (entity.client.connections.list) failed: {method1_error}")

            # Method 2: Try using the toolset client directly
            if not accounts:
                try:
                    if hasattr(self.toolset.client, 'connected_accounts'):
                        connected_accounts_obj = self.toolset.client.connected_accounts

                        # Try different method names
                        if hasattr(connected_accounts_obj, 'list'):
                            accounts = connected_accounts_obj.list(entity_id=entity_id)
                        elif hasattr(connected_accounts_obj, 'get'):
                            accounts = connected_accounts_obj.get(entity_id=entity_id)
                        elif hasattr(connected_accounts_obj, 'find'):
                            accounts = connected_accounts_obj.find(entity_id=entity_id)
                        elif callable(connected_accounts_obj):
                            accounts = connected_accounts_obj(entity_id=entity_id)
                        else:
                            logger.warning(f"Connected accounts object methods: {dir(connected_accounts_obj)}")

                except Exception as method2_error:
                    logger.warning(f"Method 2 (toolset.client.connected_accounts) failed: {method2_error}")

            # Method 3: Try using entity directly
            if not accounts:
                try:
                    if hasattr(entity, 'get_connections'):
                        accounts = entity.get_connections()
                    elif hasattr(entity, 'connections'):
                        accounts = entity.connections
                    elif hasattr(entity, 'connected_accounts'):
                        accounts = entity.connected_accounts
                except Exception as method3_error:
                    logger.warning(f"Method 3 (entity methods) failed: {method3_error}")

            # Method 4: Try using base toolset
            if not accounts:
                try:
                    if hasattr(self.base_toolset, 'get_connected_accounts'):
                        accounts = self.base_toolset.get_connected_accounts(entity_id=entity_id)
                except Exception as method4_error:
                    logger.warning(f"Method 4 (base_toolset) failed: {method4_error}")

            # Convert to list if needed
            if accounts and not isinstance(accounts, list):
                if hasattr(accounts, '__iter__'):
                    accounts = list(accounts)
                else:
                    accounts = [accounts]

            logger.info(f"Found {len(accounts) if accounts else 0} connected accounts for entity {entity_id}")
            return accounts or []

        except Exception as e:
            logger.error(f"Error getting connected accounts: {e}")
            return []


class CrewAIComposioIntegration:
    """
    Helper class for integrating Composio tools with CrewAI agents, tasks, and crews.
    """

    def __init__(self, composio_manager: ComposioManager):
        """
        Initialize with a ComposioManager instance.

        Args:
            composio_manager: ComposioManager instance
        """
        self.composio_manager = composio_manager
        logger.info("CrewAIComposioIntegration initialized")

    def create_agent_with_tools(self, tools: List[Any], role: str, goal: str,
                               backstory: str, llm=None, verbose: bool = True,
                               **kwargs) -> Optional[Agent]:
        """
        Create a CrewAI agent with Composio tools.

        Args:
            tools: List of Composio tools
            role: Agent role
            goal: Agent goal
            backstory: Agent backstory
            llm: Language model instance
            verbose: Enable verbose logging
            **kwargs: Additional Agent parameters

        Returns:
            CrewAI Agent instance or None
        """
        try:
            if not COMPOSIO_AVAILABLE:
                logger.error("CrewAI not available")
                return None

            agent = Agent(
                role=role,
                goal=goal,
                backstory=backstory,
                tools=tools,
                llm=llm,
                verbose=verbose,
                **kwargs
            )
            logger.info(f"Created agent '{role}' with {len(tools)} Composio tools")
            return agent

        except Exception as e:
            logger.error(f"Error creating agent: {e}")
            return None

    def create_task(self, description: str, agent: Agent, expected_output: str,
                   **kwargs) -> Optional[Task]:
        """
        Create a CrewAI task.

        Args:
            description: Task description
            agent: Agent to assign the task
            expected_output: Expected output description
            **kwargs: Additional Task parameters

        Returns:
            CrewAI Task instance or None
        """
        try:
            if not COMPOSIO_AVAILABLE:
                logger.error("CrewAI not available")
                return None

            task = Task(
                description=description,
                agent=agent,
                expected_output=expected_output,
                **kwargs
            )
            logger.info(f"Created task: {description[:50]}...")
            return task

        except Exception as e:
            logger.error(f"Error creating task: {e}")
            return None

    def create_crew(self, agents: List[Agent], tasks: List[Task], **kwargs) -> Optional[Crew]:
        """
        Create a CrewAI crew.

        Args:
            agents: List of agents
            tasks: List of tasks
            **kwargs: Additional Crew parameters

        Returns:
            CrewAI Crew instance or None
        """
        try:
            if not COMPOSIO_AVAILABLE:
                logger.error("CrewAI not available")
                return None

            crew = Crew(
                agents=agents,
                tasks=tasks,
                **kwargs
            )
            logger.info(f"Created crew with {len(agents)} agents and {len(tasks)} tasks")
            return crew

        except Exception as e:
            logger.error(f"Error creating crew: {e}")
            return None

    def execute_crew(self, crew: Crew) -> Optional[Any]:
        """
        Execute a CrewAI crew.

        Args:
            crew: CrewAI Crew instance

        Returns:
            Execution result or None
        """
        try:
            if not crew:
                logger.error("No crew provided")
                return None

            logger.info("Starting crew execution...")
            result = crew.kickoff()
            logger.info("Crew execution completed")
            return result

        except Exception as e:
            logger.error(f"Error executing crew: {e}")
            return None


class ComposioToolsPresets:
    """
    Predefined tool configurations for common use cases.
    """

    def __init__(self, composio_manager: ComposioManager):
        """
        Initialize with a ComposioManager instance.

        Args:
            composio_manager: ComposioManager instance
        """
        self.composio_manager = composio_manager
        logger.info("ComposioToolsPresets initialized")

    def get_productivity_tools(self) -> List[Any]:
        """
        Get tools for productivity tasks (Gmail, Calendar, Notion, etc.).

        Returns:
            List of productivity tools
        """
        apps = ['gmail', 'googlecalendar', 'notion', 'googledocs', 'googlesheets']
        return self.composio_manager.get_tools_for_apps(apps)

    def get_development_tools(self) -> List[Any]:
        """
        Get tools for development tasks (GitHub, Jira, Slack, etc.).

        Returns:
            List of development tools
        """
        apps = ['github', 'jira', 'slack', 'linear']
        return self.composio_manager.get_tools_for_apps(apps)

    def get_marketing_tools(self) -> List[Any]:
        """
        Get tools for marketing tasks (Twitter, LinkedIn, HubSpot, etc.).

        Returns:
            List of marketing tools
        """
        apps = ['twitter', 'linkedin', 'hubspot', 'mailchimp']
        return self.composio_manager.get_tools_for_apps(apps)

    def get_ecommerce_tools(self) -> List[Any]:
        """
        Get tools for e-commerce tasks (Shopify, Stripe, etc.).

        Returns:
            List of e-commerce tools
        """
        apps = ['shopify', 'stripe', 'square']
        return self.composio_manager.get_tools_for_apps(apps)

    def get_communication_tools(self) -> List[Any]:
        """
        Get tools for communication (Slack, Discord, Teams, etc.).

        Returns:
            List of communication tools
        """
        apps = ['slack', 'discord', 'microsoftteams', 'zoom']
        return self.composio_manager.get_tools_for_apps(apps)


# Utility functions for common operations
def setup_composio_for_store(store_id: int, entity_id: Optional[str] = None) -> ComposioManager:
    """
    Set up Composio manager for a specific store.

    Args:
        store_id: Store ID
        entity_id: Entity ID (defaults to store_id)

    Returns:
        ComposioManager instance
    """
    entity_id = entity_id or f"store_{store_id}"
    return ComposioManager(entity_id=entity_id)


def get_action_schema(action_name: str, api_key: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get schema for a specific action without requiring connection.

    Args:
        action_name: Action name
        api_key: Composio API key

    Returns:
        Action schema dictionary or None
    """
    try:
        if not COMPOSIO_AVAILABLE:
            logger.error("Composio not available")
            return None

        api_key = api_key or os.getenv('COMPOSIO_API_KEY')
        if not api_key:
            logger.error("COMPOSIO_API_KEY required")
            return None

        base_toolset = BaseComposioToolSet(api_key=api_key)

        # Convert string to Action enum
        try:
            action = Action(action_name.upper())
        except ValueError:
            logger.error(f"Unknown action: {action_name}")
            return None

        schemas = base_toolset.get_action_schemas(
            actions=[action],
            check_connected_accounts=False
        )

        if schemas:
            return schemas[0].model_dump()
        return None

    except Exception as e:
        logger.error(f"Error getting action schema for {action_name}: {e}")
        return None


async def async_execute_action(composio_manager: ComposioManager, action: Union[str, Action],
                              params: Dict[str, Any], entity_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Async wrapper for executing Composio actions.

    Args:
        composio_manager: ComposioManager instance
        action: Action name or Action enum
        params: Action parameters
        entity_id: Entity ID

    Returns:
        Execution result
    """
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(
        None,
        composio_manager.execute_action_direct,
        action,
        params,
        entity_id
    )


def create_quick_crew(apps: List[str], task_description: str, entity_id: str = "default",
                     llm=None) -> Optional[Crew]:
    """
    Quick helper to create a crew with tools for specific apps.

    Args:
        apps: List of app names
        task_description: Description of the task
        entity_id: Entity ID for Composio
        llm: Language model instance

    Returns:
        CrewAI Crew instance or None
    """
    try:
        if not COMPOSIO_AVAILABLE:
            logger.error("Composio/CrewAI not available")
            return None

        # Set up Composio
        manager = ComposioManager(entity_id=entity_id)
        tools = manager.get_tools_for_apps(apps)

        if not tools:
            logger.error(f"No tools found for apps: {apps}")
            return None

        # Set up CrewAI integration
        crew_integration = CrewAIComposioIntegration(manager)

        # Create agent
        agent = crew_integration.create_agent_with_tools(
            tools=tools,
            role="Multi-App Assistant",
            goal=f"Complete tasks using {', '.join(apps)} tools",
            backstory=f"You are an AI assistant capable of working with {', '.join(apps)} to help users accomplish their goals.",
            llm=llm
        )

        if not agent:
            return None

        # Create task
        task = crew_integration.create_task(
            description=task_description,
            agent=agent,
            expected_output="Successful completion of the requested task with detailed results"
        )

        if not task:
            return None

        # Create crew
        crew = crew_integration.create_crew(
            agents=[agent],
            tasks=[task]
        )

        return crew

    except Exception as e:
        logger.error(f"Error creating quick crew: {e}")
        return None


# Example usage and testing functions
def test_composio_connection(app_name: str, entity_id: str = "test_entity") -> bool:
    """
    Test Composio connection for a specific app.

    Args:
        app_name: App name to test
        entity_id: Entity ID for testing

    Returns:
        True if connection successful, False otherwise
    """
    try:
        manager = ComposioManager(entity_id=entity_id)

        # Check if there are any connected accounts for this specific app
        accounts = manager.get_connected_accounts()
        logger.info(f"Found {len(accounts)} total connected accounts for entity {entity_id}")

        # Check if any account is for the specific app
        app_connected = False
        for account in accounts:
            if isinstance(account, dict):
                # Check different possible field names for app identification
                account_app = account.get('appName') or account.get('app_name') or account.get('appUniqueId')
                account_status = account.get('status', '').upper()

                logger.info(f"Checking account: app={account_app}, status={account_status}, full_account={account}")

                if account_app and account_app.lower() == app_name.lower() and account_status == 'ACTIVE':
                    app_connected = True
                    logger.info(f"✓ Found active connection for {app_name}: {account.get('id')}")
                    break
            else:
                logger.info(f"Account is not a dict: {type(account)} - {account}")
                # Try to handle non-dict account objects
                if hasattr(account, 'appName') or hasattr(account, 'app_name') or hasattr(account, 'appUniqueId'):
                    account_app = getattr(account, 'appName', None) or getattr(account, 'app_name', None) or getattr(account, 'appUniqueId', None)
                    account_status = getattr(account, 'status', '').upper()

                    logger.info(f"Checking object account: app={account_app}, status={account_status}")

                    if account_app and account_app.lower() == app_name.lower() and account_status == 'ACTIVE':
                        app_connected = True
                        logger.info(f"✓ Found active connection for {app_name}: {getattr(account, 'id', 'unknown')}")
                        break

        if not app_connected:
            logger.info(f"No active connection found for {app_name}")

            # Try to get tools for the app as a secondary check
            tools = manager.get_tools_for_apps([app_name])
            if tools:
                logger.info(f"Tools available for {app_name} ({len(tools)} tools), but no active connection found")
            else:
                logger.error(f"No tools found for {app_name}")

            return False

        logger.info(f"✓ Connection test passed for {app_name}")
        return True

    except Exception as e:
        logger.error(f"Error testing connection for {app_name}: {e}")
        return False


def list_available_actions_for_app(app_name: str) -> List[str]:
    """
    List all available actions for a specific app.

    Args:
        app_name: App name

    Returns:
        List of action names
    """
    try:
        if not COMPOSIO_AVAILABLE:
            return []

        # Get all Action enum values and filter by app
        actions = []
        app_prefix = app_name.upper() + "_"

        for action in Action:
            action_value = None
            if hasattr(action, 'value'):
                action_value = action.value
            elif hasattr(action, 'name'):
                action_value = action.name
            else:
                action_value = str(action)

            if action_value and action_value.startswith(app_prefix):
                actions.append(action_value)

        logger.info(f"Found {len(actions)} actions for {app_name}")
        return actions

    except Exception as e:
        logger.error(f"Error listing actions for {app_name}: {e}")
        return []
