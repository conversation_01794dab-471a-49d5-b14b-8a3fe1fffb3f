"""
Composio Configuration and Constants
====================================

This module contains configuration settings, constants, and mappings
for Composio integration with CrewAI.

Includes:
- App configurations
- Common action mappings
- Default settings
- Error handling configurations
"""

import os
from typing import Dict, List, Any, Optional
from enum import Enum

# Check if Composio is available
try:
    import composio_crewai
    COMPOSIO_AVAILABLE = True
except ImportError:
    COMPOSIO_AVAILABLE = False


class ComposioConfig:
    """Configuration settings for Composio integration."""

    # API Configuration
    DEFAULT_TIMEOUT = 120  # seconds
    DEFAULT_ENTITY_ID = "default"
    MAX_RETRIES = 3

    # Environment variables
    API_KEY_ENV = "COMPOSIO_API_KEY"
    OPENAI_API_KEY_ENV = "OPENAI_API_KEY"

    # Logging
    LOG_LEVEL = "INFO"
    LOG_FORMAT = '[%(asctime)s] %(levelname)s %(name)s: %(message)s'


class SupportedApps(Enum):
    """Enum of commonly used Composio apps."""

    # Productivity
    GMAIL = "gmail"
    GOOGLE_CALENDAR = "googlecalendar"
    GOOGLE_DOCS = "googledocs"
    GOOGLE_SHEETS = "googlesheets"
    GOOGLE_DRIVE = "googledrive"
    NOTION = "notion"
    TODOIST = "todoist"

    # Development
    GITHUB = "github"
    JIRA = "jira"
    LINEAR = "linear"
    BITBUCKET = "bitbucket"

    # Communication
    SLACK = "slack"
    DISCORD = "discord"
    MICROSOFT_TEAMS = "microsoftteams"
    ZOOM = "zoom"

    # Marketing
    TWITTER = "twitter"
    LINKEDIN = "linkedin"
    HUBSPOT = "hubspot"
    MAILCHIMP = "mailchimp"

    # E-commerce
    SHOPIFY = "shopify"
    STRIPE = "stripe"
    SQUARE = "square"

    # CRM
    SALESFORCE = "salesforce"
    PIPEDRIVE = "pipedrive"

    # Project Management
    ASANA = "asana"
    TRELLO = "trello"
    MONDAY = "monday"
    CLICKUP = "clickup"


# Common action patterns for different apps
COMMON_ACTIONS = {
    "github": [
        "GITHUB_GET_THE_AUTHENTICATED_USER",
        "GITHUB_LIST_REPOSITORIES_FOR_THE_AUTHENTICATED_USER",
        "GITHUB_CREATE_AN_ISSUE",
        "GITHUB_LIST_ISSUES",
        "GITHUB_CREATE_A_PULL_REQUEST",
        "GITHUB_STAR_A_REPOSITORY_FOR_THE_AUTHENTICATED_USER"
    ],
    "gmail": [
        "GMAIL_SEND_EMAIL",
        "GMAIL_LIST_EMAILS",
        "GMAIL_READ_EMAIL",
        "GMAIL_CREATE_DRAFT",
        "GMAIL_SEARCH_EMAILS"
    ],
    "slack": [
        "SLACK_SEND_MESSAGE",
        "SLACK_LIST_CHANNELS",
        "SLACK_CREATE_CHANNEL",
        "SLACK_GET_USER_INFO",
        "SLACK_UPLOAD_FILE"
    ],
    "notion": [
        "NOTION_CREATE_PAGE",
        "NOTION_UPDATE_PAGE",
        "NOTION_SEARCH_PAGES",
        "NOTION_CREATE_DATABASE",
        "NOTION_QUERY_DATABASE"
    ],
    "shopify": [
        "SHOPIFY_GET_PRODUCTS",
        "SHOPIFY_CREATE_PRODUCT",
        "SHOPIFY_UPDATE_PRODUCT",
        "SHOPIFY_GET_ORDERS",
        "SHOPIFY_CREATE_ORDER"
    ],
    "googlecalendar": [
        "GOOGLECALENDAR_LIST_EVENTS",
        "GOOGLECALENDAR_CREATE_EVENT",
        "GOOGLECALENDAR_UPDATE_EVENT",
        "GOOGLECALENDAR_DELETE_EVENT",
        "GOOGLECALENDAR_LIST_CALENDARS"
    ]
}

# App categories for easy grouping
APP_CATEGORIES = {
    "productivity": [
        SupportedApps.GMAIL.value,
        SupportedApps.GOOGLE_CALENDAR.value,
        SupportedApps.GOOGLE_DOCS.value,
        SupportedApps.GOOGLE_SHEETS.value,
        SupportedApps.NOTION.value,
        SupportedApps.TODOIST.value
    ],
    "development": [
        SupportedApps.GITHUB.value,
        SupportedApps.JIRA.value,
        SupportedApps.LINEAR.value,
        SupportedApps.BITBUCKET.value
    ],
    "communication": [
        SupportedApps.SLACK.value,
        SupportedApps.DISCORD.value,
        SupportedApps.MICROSOFT_TEAMS.value,
        SupportedApps.ZOOM.value
    ],
    "marketing": [
        SupportedApps.TWITTER.value,
        SupportedApps.LINKEDIN.value,
        SupportedApps.HUBSPOT.value,
        SupportedApps.MAILCHIMP.value
    ],
    "ecommerce": [
        SupportedApps.SHOPIFY.value,
        SupportedApps.STRIPE.value,
        SupportedApps.SQUARE.value
    ],
    "crm": [
        SupportedApps.SALESFORCE.value,
        SupportedApps.PIPEDRIVE.value
    ],
    "project_management": [
        SupportedApps.ASANA.value,
        SupportedApps.TRELLO.value,
        SupportedApps.MONDAY.value,
        SupportedApps.CLICKUP.value
    ]
}

# Default agent configurations for different use cases
AGENT_TEMPLATES = {
    "productivity_assistant": {
        "role": "Productivity Assistant",
        "goal": "Help users manage their productivity tools and workflows efficiently",
        "backstory": "You are an AI assistant specialized in productivity tools like Gmail, Calendar, Notion, and Google Workspace. You help users organize their work and personal life.",
        "apps": APP_CATEGORIES["productivity"]
    },
    "development_assistant": {
        "role": "Development Assistant",
        "goal": "Assist with software development workflows and project management",
        "backstory": "You are an AI assistant specialized in development tools like GitHub, Jira, and Linear. You help developers manage code, issues, and project workflows.",
        "apps": APP_CATEGORIES["development"]
    },
    "marketing_assistant": {
        "role": "Marketing Assistant",
        "goal": "Help with marketing campaigns and social media management",
        "backstory": "You are an AI assistant specialized in marketing tools like Twitter, LinkedIn, and HubSpot. You help create and manage marketing campaigns.",
        "apps": APP_CATEGORIES["marketing"]
    },
    "ecommerce_manager": {
        "role": "E-commerce Manager",
        "goal": "Manage e-commerce operations and customer transactions",
        "backstory": "You are an AI assistant specialized in e-commerce platforms like Shopify and payment systems like Stripe. You help manage online stores and transactions.",
        "apps": APP_CATEGORIES["ecommerce"]
    },
    "communication_coordinator": {
        "role": "Communication Coordinator",
        "goal": "Facilitate team communication and collaboration",
        "backstory": "You are an AI assistant specialized in communication tools like Slack, Discord, and Teams. You help coordinate team communications and meetings.",
        "apps": APP_CATEGORIES["communication"]
    }
}

# Error handling configurations
ERROR_HANDLING = {
    "max_retries": 3,
    "retry_delay": 1.0,  # seconds
    "timeout": 30.0,  # seconds
    "ignore_errors": [
        "PGRST116",  # No rows found
        "connection_timeout",
        "rate_limit_exceeded"
    ]
}

# Default LLM configurations
LLM_CONFIGS = {
    "openai": {
        "model": "gpt-4o-mini",
        "temperature": 0.1,
        "max_tokens": 2000
    },
    "azure_openai": {
        "model": "gpt-4",
        "temperature": 0.1,
        "max_tokens": 2000
    }
}

# Webhook and trigger configurations
WEBHOOK_CONFIG = {
    "default_timeout": 30,
    "max_payload_size": 10 * 1024 * 1024,  # 10MB
    "supported_events": [
        "github.push",
        "github.pull_request",
        "slack.message",
        "gmail.new_email",
        "shopify.order_created"
    ]
}


def get_apps_for_category(category: str) -> List[str]:
    """
    Get list of apps for a specific category.

    Args:
        category: Category name

    Returns:
        List of app names
    """
    return APP_CATEGORIES.get(category, [])


def get_common_actions_for_app(app_name: str) -> List[str]:
    """
    Get common actions for a specific app.

    Args:
        app_name: App name

    Returns:
        List of action names
    """
    return COMMON_ACTIONS.get(app_name.lower(), [])


def get_agent_template(template_name: str) -> Optional[Dict[str, Any]]:
    """
    Get agent template configuration.

    Args:
        template_name: Template name

    Returns:
        Agent template dictionary or None
    """
    return AGENT_TEMPLATES.get(template_name)


def validate_environment() -> Dict[str, bool]:
    """
    Validate required environment variables.

    Returns:
        Dictionary with validation results
    """
    return {
        "composio_api_key": bool(os.getenv(ComposioConfig.API_KEY_ENV)),
        "openai_api_key": bool(os.getenv(ComposioConfig.OPENAI_API_KEY_ENV))
    }


def get_default_entity_id(store_id: Optional[int] = None, user_id: Optional[str] = None) -> str:
    """
    Generate default entity ID based on store or user.

    Args:
        store_id: Store ID
        user_id: User ID

    Returns:
        Entity ID string
    """
    if store_id:
        return f"store_{store_id}"
    elif user_id:
        return f"user_{user_id}"
    else:
        return ComposioConfig.DEFAULT_ENTITY_ID


# Export commonly used configurations
__all__ = [
    'ComposioConfig',
    'SupportedApps',
    'COMMON_ACTIONS',
    'APP_CATEGORIES',
    'AGENT_TEMPLATES',
    'ERROR_HANDLING',
    'LLM_CONFIGS',
    'WEBHOOK_CONFIG',
    'get_apps_for_category',
    'get_common_actions_for_app',
    'get_agent_template',
    'validate_environment',
    'get_default_entity_id'
]
