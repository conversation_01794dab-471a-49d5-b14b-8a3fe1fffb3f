"""
Composio Integration Examples for CrewAI
========================================

This module provides practical examples of how to use the Composio helper functions
with CrewAI for various use cases.

Examples include:
- Setting up connections
- Creating agents with tools
- Executing tasks
- Common workflows

Requirements:
- pip install composio_crewai composio_core crewai langchain_openai
- COMPOSIO_API_KEY environment variable
- OPENAI_API_KEY environment variable (for examples)
"""

import os
import asyncio
from typing import List, Dict, Any, Optional

try:
    from langchain_openai import ChatOpenAI
    from .composio_helpers import (
        ComposioManager, 
        CrewAIComposioIntegration, 
        ComposioToolsPresets,
        setup_composio_for_store,
        create_quick_crew,
        test_composio_connection,
        list_available_actions_for_app
    )
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"Dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False


def example_basic_setup():
    """
    Example: Basic Composio setup and tool retrieval.
    """
    print("=== Basic Composio Setup Example ===")
    
    if not DEPENDENCIES_AVAILABLE:
        print("Required dependencies not installed")
        return
    
    try:
        # Initialize Composio manager
        manager = ComposioManager(entity_id="example_user")
        
        # Get available apps
        apps = manager.get_available_apps()
        print(f"Available apps: {len(apps)} total")
        print(f"First 10 apps: {apps[:10]}")
        
        # Get tools for specific apps
        github_tools = manager.get_tools_for_apps(['github'])
        print(f"GitHub tools: {len(github_tools)}")
        
        # Get tools for specific actions
        specific_tools = manager.get_tools_for_actions(['GITHUB_GET_THE_AUTHENTICATED_USER'])
        print(f"Specific action tools: {len(specific_tools)}")
        
        # Find tools by use case
        search_tools = manager.find_tools_by_use_case("create a GitHub issue")
        print(f"Tools found by use case: {len(search_tools)}")
        
    except Exception as e:
        print(f"Error in basic setup: {e}")


def example_connection_setup():
    """
    Example: Setting up connections for apps.
    """
    print("\n=== Connection Setup Example ===")
    
    if not DEPENDENCIES_AVAILABLE:
        print("Required dependencies not installed")
        return
    
    try:
        manager = ComposioManager(entity_id="connection_example")
        
        # Check existing connections
        accounts = manager.get_connected_accounts()
        print(f"Existing connected accounts: {len(accounts)}")
        
        # Initiate connection (this would require user interaction)
        print("To connect GitHub:")
        print("1. Run: composio add github")
        print("2. Follow the OAuth flow")
        
        # Test connection
        is_connected = test_composio_connection("github", "connection_example")
        print(f"GitHub connection test: {'✓' if is_connected else '✗'}")
        
    except Exception as e:
        print(f"Error in connection setup: {e}")


def example_crewai_integration():
    """
    Example: Creating CrewAI agents with Composio tools.
    """
    print("\n=== CrewAI Integration Example ===")
    
    if not DEPENDENCIES_AVAILABLE:
        print("Required dependencies not installed")
        return
    
    try:
        # Initialize components
        manager = ComposioManager(entity_id="crewai_example")
        crew_integration = CrewAIComposioIntegration(manager)
        
        # Get tools for development workflow
        tools = manager.get_tools_for_apps(['github', 'slack'])
        
        if not tools:
            print("No tools available - ensure apps are connected")
            return
        
        # Initialize LLM (requires OPENAI_API_KEY)
        llm = None
        if os.getenv('OPENAI_API_KEY'):
            llm = ChatOpenAI(model="gpt-4o-mini")
        
        # Create agent
        agent = crew_integration.create_agent_with_tools(
            tools=tools,
            role="Development Assistant",
            goal="Help with GitHub and Slack operations",
            backstory="You are an AI assistant specialized in development workflows using GitHub and Slack.",
            llm=llm
        )
        
        if agent:
            print(f"✓ Created agent with {len(tools)} tools")
            
            # Create task
            task = crew_integration.create_task(
                description="Check my GitHub repositories and send a summary to Slack",
                agent=agent,
                expected_output="A summary of GitHub repositories posted to Slack"
            )
            
            if task:
                print("✓ Created task")
                
                # Create crew
                crew = crew_integration.create_crew(
                    agents=[agent],
                    tasks=[task]
                )
                
                if crew:
                    print("✓ Created crew")
                    print("Note: Execute with crew.kickoff() when ready")
                else:
                    print("✗ Failed to create crew")
            else:
                print("✗ Failed to create task")
        else:
            print("✗ Failed to create agent")
            
    except Exception as e:
        print(f"Error in CrewAI integration: {e}")


def example_tool_presets():
    """
    Example: Using predefined tool presets.
    """
    print("\n=== Tool Presets Example ===")
    
    if not DEPENDENCIES_AVAILABLE:
        print("Required dependencies not installed")
        return
    
    try:
        manager = ComposioManager(entity_id="presets_example")
        presets = ComposioToolsPresets(manager)
        
        # Get different tool categories
        productivity_tools = presets.get_productivity_tools()
        development_tools = presets.get_development_tools()
        marketing_tools = presets.get_marketing_tools()
        ecommerce_tools = presets.get_ecommerce_tools()
        communication_tools = presets.get_communication_tools()
        
        print(f"Productivity tools: {len(productivity_tools)}")
        print(f"Development tools: {len(development_tools)}")
        print(f"Marketing tools: {len(marketing_tools)}")
        print(f"E-commerce tools: {len(ecommerce_tools)}")
        print(f"Communication tools: {len(communication_tools)}")
        
    except Exception as e:
        print(f"Error in tool presets: {e}")


def example_direct_execution():
    """
    Example: Direct action execution without CrewAI.
    """
    print("\n=== Direct Execution Example ===")
    
    if not DEPENDENCIES_AVAILABLE:
        print("Required dependencies not installed")
        return
    
    try:
        manager = ComposioManager(entity_id="execution_example")
        
        # Example: Get GitHub user info
        result = manager.execute_action_direct(
            action="GITHUB_GET_THE_AUTHENTICATED_USER",
            params={},
            entity_id="execution_example"
        )
        
        if result.get("successful"):
            user_data = result.get("data", {})
            username = user_data.get("login", "Unknown")
            print(f"✓ GitHub user: {username}")
        else:
            print(f"✗ GitHub execution failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"Error in direct execution: {e}")


def example_quick_crew():
    """
    Example: Using the quick crew helper function.
    """
    print("\n=== Quick Crew Example ===")
    
    if not DEPENDENCIES_AVAILABLE:
        print("Required dependencies not installed")
        return
    
    try:
        # Initialize LLM if available
        llm = None
        if os.getenv('OPENAI_API_KEY'):
            llm = ChatOpenAI(model="gpt-4o-mini")
        
        # Create a quick crew for GitHub operations
        crew = create_quick_crew(
            apps=['github'],
            task_description="List my GitHub repositories and create a summary",
            entity_id="quick_crew_example",
            llm=llm
        )
        
        if crew:
            print("✓ Quick crew created successfully")
            print("Execute with: result = crew.kickoff()")
        else:
            print("✗ Failed to create quick crew")
            
    except Exception as e:
        print(f"Error in quick crew: {e}")


def example_store_integration():
    """
    Example: Integration with store-specific workflows.
    """
    print("\n=== Store Integration Example ===")
    
    if not DEPENDENCIES_AVAILABLE:
        print("Required dependencies not installed")
        return
    
    try:
        store_id = 123
        
        # Set up Composio for a specific store
        manager = setup_composio_for_store(store_id)
        print(f"✓ Set up Composio for store {store_id}")
        
        # Get e-commerce tools for the store
        presets = ComposioToolsPresets(manager)
        ecommerce_tools = presets.get_ecommerce_tools()
        
        print(f"E-commerce tools available: {len(ecommerce_tools)}")
        
        # Example: Create a Shopify management crew
        if ecommerce_tools:
            crew_integration = CrewAIComposioIntegration(manager)
            
            llm = None
            if os.getenv('OPENAI_API_KEY'):
                llm = ChatOpenAI(model="gpt-4o-mini")
            
            agent = crew_integration.create_agent_with_tools(
                tools=ecommerce_tools,
                role="E-commerce Manager",
                goal="Manage Shopify store operations",
                backstory="You are an AI assistant specialized in e-commerce store management.",
                llm=llm
            )
            
            if agent:
                print(f"✓ Created e-commerce agent for store {store_id}")
            else:
                print("✗ Failed to create e-commerce agent")
        
    except Exception as e:
        print(f"Error in store integration: {e}")


async def example_async_execution():
    """
    Example: Asynchronous action execution.
    """
    print("\n=== Async Execution Example ===")
    
    if not DEPENDENCIES_AVAILABLE:
        print("Required dependencies not installed")
        return
    
    try:
        from .composio_helpers import async_execute_action
        
        manager = ComposioManager(entity_id="async_example")
        
        # Execute action asynchronously
        result = await async_execute_action(
            composio_manager=manager,
            action="GITHUB_GET_THE_AUTHENTICATED_USER",
            params={},
            entity_id="async_example"
        )
        
        if result.get("successful"):
            print("✓ Async execution successful")
        else:
            print(f"✗ Async execution failed: {result.get('error')}")
            
    except Exception as e:
        print(f"Error in async execution: {e}")


def run_all_examples():
    """
    Run all examples in sequence.
    """
    print("Running Composio Integration Examples")
    print("=" * 50)
    
    example_basic_setup()
    example_connection_setup()
    example_crewai_integration()
    example_tool_presets()
    example_direct_execution()
    example_quick_crew()
    example_store_integration()
    
    # Run async example
    try:
        asyncio.run(example_async_execution())
    except Exception as e:
        print(f"Error running async example: {e}")
    
    print("\n" + "=" * 50)
    print("Examples completed!")


if __name__ == "__main__":
    run_all_examples()
