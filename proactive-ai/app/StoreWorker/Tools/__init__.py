"""
StoreWorker Tools Package
========================

This package contains various tools and integrations for the StoreWorker system,
including Shopify admin tools and Composio integrations for CrewAI.

Available modules:
- shopify_admin_tools: Shopify Admin API integration
- composio_helpers: Composio integration for CrewAI
- composio_examples: Example usage of Composio tools
- composio_config: Configuration and constants for Composio
"""

# Import Shopify tools (existing)
try:
    from .shopify_admin_tools import *
except ImportError:
    pass

# Import Composio tools (new)
try:
    from .composio_helpers import (
        ComposioManager,
        CrewAIComposioIntegration,
        ComposioToolsPresets,
        setup_composio_for_store,
        create_quick_crew,
        test_composio_connection,
        list_available_actions_for_app,
        get_action_schema,
        async_execute_action
    )
    from .composio_config import (
        ComposioConfig,
        SupportedApps,
        COMMON_ACTIONS,
        APP_CATEGORIES,
        AGENT_TEMPLATES,
        get_apps_for_category,
        get_common_actions_for_app,
        get_agent_template,
        validate_environment,
        get_default_entity_id
    )
    COMPOSIO_AVAILABLE = True
except ImportError as e:
    # Create dummy functions/classes when Composio is not available
    COMPOSIO_AVAILABLE = False

    def ComposioManager(*args, **kwargs):
        raise ImportError("Composio packages not installed. Run: pip install composio_crewai composio_core")

    def CrewAIComposioIntegration(*args, **kwargs):
        raise ImportError("Composio packages not installed. Run: pip install composio_crewai composio_core")

    def ComposioToolsPresets(*args, **kwargs):
        raise ImportError("Composio packages not installed. Run: pip install composio_crewai composio_core")

    def setup_composio_for_store(*args, **kwargs):
        raise ImportError("Composio packages not installed. Run: pip install composio_crewai composio_core")

    def create_quick_crew(*args, **kwargs):
        raise ImportError("Composio packages not installed. Run: pip install composio_crewai composio_core")

    def test_composio_connection(*args, **kwargs):
        return False

    def list_available_actions_for_app(*args, **kwargs):
        return []

    def get_action_schema(*args, **kwargs):
        return None

    async def async_execute_action(*args, **kwargs):
        raise ImportError("Composio packages not installed. Run: pip install composio_crewai composio_core")

    # Dummy config objects
    class ComposioConfig:
        pass

    class SupportedApps:
        pass

    COMMON_ACTIONS = {}
    APP_CATEGORIES = {}
    AGENT_TEMPLATES = {}

    def get_apps_for_category(*args, **kwargs):
        return []

    def get_common_actions_for_app(*args, **kwargs):
        return []

    def get_agent_template(*args, **kwargs):
        return None

    def validate_environment(*args, **kwargs):
        return {"composio_api_key": False, "openai_api_key": False}

    def get_default_entity_id(*args, **kwargs):
        return "default"

__all__ = [
    # Composio classes
    'ComposioManager',
    'CrewAIComposioIntegration',
    'ComposioToolsPresets',

    # Composio utility functions
    'setup_composio_for_store',
    'create_quick_crew',
    'test_composio_connection',
    'list_available_actions_for_app',
    'get_action_schema',
    'async_execute_action',

    # Composio configuration
    'ComposioConfig',
    'SupportedApps',
    'COMMON_ACTIONS',
    'APP_CATEGORIES',
    'AGENT_TEMPLATES',
    'get_apps_for_category',
    'get_common_actions_for_app',
    'get_agent_template',
    'validate_environment',
    'get_default_entity_id',

    # Availability flag
    'COMPOSIO_AVAILABLE'
]
