from crewai.tools import tool
from typing import List, Optional, Dict, Any
import os
import json
import gzip
from pathlib import Path
from ..logger.logger import error, info

# Get schema file path from environment variable or use default
SCHEMA_FILE_PATH = os.getenv(
    'SHOPIFY_ADMIN_SCHEMA_PATH',
    '/Users/<USER>/Documents/projects/GeneratorAiModule/GeneratorAI/app/StoreWorker/docs/admin_schema_2025-01.json'
)

# Maximum number of fields to extract from an object
MAX_FIELDS_TO_SHOW = 50

def load_schema_content(schema_path: str) -> str:
    """Load schema content, handling decompression if needed"""
    gzipped_schema_path = f"{schema_path}.gz"

    # If uncompressed file doesn't exist but gzipped does, decompress it
    if not os.path.exists(schema_path) and os.path.exists(gzipped_schema_path):
        error(f"Decompressing GraphQL schema from {gzipped_schema_path}")
        with gzip.open(gzipped_schema_path, 'rb') as f:
            schema_content = f.read().decode('utf-8')

        # Save the uncompressed content to disk
        with open(schema_path, 'w', encoding='utf-8') as f:
            f.write(schema_content)
        info(f"Saved uncompressed schema to {schema_path}")
        return schema_content

    info(f"Reading GraphQL schema from {schema_path}")
    with open(schema_path, 'r', encoding='utf-8') as f:
        return f.read()

def filter_and_sort_items(items: List[Dict], search_term: str, max_items: int) -> Dict[str, Any]:
    """Helper function to filter, sort, and truncate schema items"""
    # Filter items based on search term
    filtered = [item for item in items if search_term in item.get('name', '').lower()]

    # Sort filtered items by name length (shorter names first)
    filtered.sort(key=lambda x: len(x.get('name', '')))

    # Return truncation info and limited items
    return {
        'was_truncated': len(filtered) > max_items,
        'items': filtered[:max_items]
    }

def format_type(type_info: Dict) -> str:
    """Helper function to format GraphQL type"""
    if not type_info:
        return "null"

    if type_info.get('kind') == "NON_NULL":
        return f"{format_type(type_info['ofType'])}!"
    elif type_info.get('kind') == "LIST":
        return f"[{format_type(type_info['ofType'])}]"
    else:
        return type_info.get('name', '')

def format_arg(arg: Dict) -> str:
    """Helper function to format GraphQL argument"""
    result = f"{arg['name']}: {format_type(arg['type'])}"
    if arg.get('defaultValue') is not None:
        result += f" = {arg['defaultValue']}"
    return result

def format_field(field: Dict) -> str:
    """Helper function to format GraphQL field"""
    result = f"  {field['name']}"

    # Add arguments if present
    if field.get('args') and len(field['args']) > 0:
        result += f"({', '.join(format_arg(arg) for arg in field['args'])})"

    result += f": {format_type(field['type'])}"

    # Add deprecation info if present
    if field.get('isDeprecated'):
        result += " @deprecated"
        if field.get('deprecationReason'):
            result += f" ({field['deprecationReason']})"

    return result

def format_schema_type(item: Dict) -> str:
    """Helper function to format GraphQL schema type"""
    result = f"{item['kind']} {item['name']}"

    if item.get('description'):
        # Truncate description if too long
        max_desc_length = 150
        desc = item['description'].replace('\n', ' ')
        result += f"\n  Description: {desc[:max_desc_length] + '...' if len(desc) > max_desc_length else desc}"

    # Add interfaces if present
    if item.get('interfaces') and len(item['interfaces']) > 0:
        result += f"\n  Implements: {', '.join(i['name'] for i in item['interfaces'])}"

    # For INPUT_OBJECT types, use inputFields instead of fields
    if item.get('kind') == "INPUT_OBJECT" and item.get('inputFields'):
        result += "\n  Input Fields:"
        fields_to_show = item['inputFields'][:MAX_FIELDS_TO_SHOW]
        for field in fields_to_show:
            result += f"\n{format_field(field)}"
        if len(item['inputFields']) > MAX_FIELDS_TO_SHOW:
            result += f"\n  ... and {len(item['inputFields']) - MAX_FIELDS_TO_SHOW} more input fields"
    # For regular object types, use fields
    elif item.get('fields'):
        result += "\n  Fields:"
        fields_to_show = item['fields'][:MAX_FIELDS_TO_SHOW]
        for field in fields_to_show:
            result += f"\n{format_field(field)}"
        if len(item['fields']) > MAX_FIELDS_TO_SHOW:
            result += f"\n  ... and {len(item['fields']) - MAX_FIELDS_TO_SHOW} more fields"

    return result

def format_graphql_operation(query: Dict) -> str:
    """Helper function to format GraphQL operation"""
    result = query['name']

    if query.get('description'):
        # Truncate description if too long
        max_desc_length = 100
        desc = query['description'].replace('\n', ' ')
        result += f"\n  Description: {desc[:max_desc_length] + '...' if len(desc) > max_desc_length else desc}"

    # Add arguments if present
    if query.get('args') and len(query['args']) > 0:
        result += "\n  Arguments:"
        for arg in query['args']:
            result += f"\n    {format_arg(arg)}"

    # Add return type
    result += f"\n  Returns: {format_type(query['type'])}"

    return result

@tool
def search_shopify_admin_schema(query: str, filter_options: Optional[List[str]] = None) -> str:
    """
    Search the Shopify Admin GraphQL schema for types, queries, and mutations.
    
    Args:
        query: The search term to filter the schema
        filter_options: List of strings to filter results (can be "all", "types", "queries", "mutations")
    
    Returns:
        A formatted string containing matching schema items
    """
    if filter_options is None:
        filter_options = ["all"]

    try:
        schema_content = load_schema_content(SCHEMA_FILE_PATH)
        schema_json = json.loads(schema_content)

        # If a query is provided, filter the schema
        result_schema = schema_json
        was_truncated = False
        queries_were_truncated = False
        mutations_were_truncated = False

        if query and query.strip():
            # Normalize search term: remove trailing 's' and remove all spaces
            normalized_query = query.strip()
            if normalized_query.endswith('s'):
                normalized_query = normalized_query[:-1]
            normalized_query = normalized_query.replace(' ', '')

            error(f"Filtering schema with query: {query} (normalized: {normalized_query})")

            search_term = normalized_query.lower()

            if schema_json.get('data', {}).get('__schema', {}).get('types'):
                MAX_RESULTS = 10

                # Process types
                processed_types = filter_and_sort_items(
                    schema_json['data']['__schema']['types'],
                    search_term,
                    MAX_RESULTS
                )
                was_truncated = processed_types['was_truncated']
                limited_types = processed_types['items']

                # Find the Query and Mutation types
                query_type = next(
                    (t for t in schema_json['data']['__schema']['types'] if t['name'] == "QueryRoot"),
                    None
                )
                mutation_type = next(
                    (t for t in schema_json['data']['__schema']['types'] if t['name'] == "Mutation"),
                    None
                )

                # Process queries if available
                matching_queries = []
                if query_type and query_type.get('fields') and ("all" in filter_options or "queries" in filter_options):
                    processed_queries = filter_and_sort_items(
                        query_type['fields'],
                        search_term,
                        MAX_RESULTS
                    )
                    queries_were_truncated = processed_queries['was_truncated']
                    matching_queries = processed_queries['items']

                # Process mutations if available
                matching_mutations = []
                if mutation_type and mutation_type.get('fields') and ("all" in filter_options or "mutations" in filter_options):
                    processed_mutations = filter_and_sort_items(
                        mutation_type['fields'],
                        search_term,
                        MAX_RESULTS
                    )
                    mutations_were_truncated = processed_mutations['was_truncated']
                    matching_mutations = processed_mutations['items']

                # Create a modified schema that includes matching types
                result_schema = {
                    'data': {
                        '__schema': {
                            **schema_json['data']['__schema'],
                            'types': limited_types,
                            'matchingQueries': matching_queries,
                            'matchingMutations': matching_mutations,
                        }
                    }
                }

        # Create the response text with truncation message if needed
        response_text = ""

        if "all" in filter_options or "types" in filter_options:
            response_text += "## Matching GraphQL Types:\n"
            if was_truncated:
                response_text += "(Results limited to 10 items. Refine your search for more specific results.)\n\n"

            if result_schema['data']['__schema']['types']:
                response_text += "\n\n".join(format_schema_type(t) for t in result_schema['data']['__schema']['types']) + "\n\n"
            else:
                response_text += "No matching types found.\n\n"

        if "all" in filter_options or "queries" in filter_options:
            response_text += "## Matching GraphQL Queries:\n"
            if queries_were_truncated:
                response_text += "(Results limited to 10 items. Refine your search for more specific results.)\n\n"

            if result_schema['data']['__schema'].get('matchingQueries'):
                response_text += "\n\n".join(format_graphql_operation(q) for q in result_schema['data']['__schema']['matchingQueries']) + "\n\n"
            else:
                response_text += "No matching queries found.\n\n"

        if "all" in filter_options or "mutations" in filter_options:
            response_text += "## Matching GraphQL Mutations:\n"
            if mutations_were_truncated:
                response_text += "(Results limited to 10 items. Refine your search for more specific results.)\n\n"

            if result_schema['data']['__schema'].get('matchingMutations'):
                response_text += "\n\n".join(format_graphql_operation(m) for m in result_schema['data']['__schema']['matchingMutations'])
            else:
                response_text += "No matching mutations found."

        return response_text

    except Exception as e:
        error(f"Error processing GraphQL schema: {str(e)}")
        return f"Error: {str(e)}"

def cache_func(args: tuple, result: str) -> bool:
    """
    Custom caching function for the search_shopify_admin_schema tool.
    Only cache successful searches (those that don't start with 'Error:').
    
    Args:
        args: Tuple of arguments passed to the tool
        result: The result returned by the tool
    
    Returns:
        bool: True if the result should be cached, False otherwise
    """
    return not result.startswith('Error:')

# Set the cache function for the tool
search_shopify_admin_schema.cache_function = cache_func 