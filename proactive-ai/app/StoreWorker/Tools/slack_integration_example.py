"""
Slack Integration Example for StoreWorker Tools

This example shows how to integrate Slack notifications into your existing
StoreWorker operations and workflows.
"""

import time
import random
from slack_integration import (
    StoreWorkerSlackNotifier,
    RankCollectionsSlackNotifier,
    StoreBlogsSlackNotifier,
    InstallationSlackNotifier,
    slack_notify,
    notify_store_worker_job,
    notify_collection_job,
    notify_blog_job,
    notify_installation_job
)


# Example 1: Using the StoreWorker notifier class
def example_store_worker_class():
    """Example using StoreWorkerSlackNotifier class."""
    print("=== StoreWorker Class Example ===")
    
    # Initialize notifier for a specific project
    notifier = StoreWorkerSlackNotifier(project_name='ecommerce-store-1')
    
    # Simulate a job workflow
    job_id = 'job_12345'
    
    # 1. Notify job started
    notifier.notify_job_started(
        job_type='product_sync',
        job_id=job_id,
        workflow_name='daily_product_sync'
    )
    
    # 2. Simulate some work
    print("Simulating product sync work...")
    time.sleep(2)  # Simulate work
    
    # 3. Notify job completed
    notifier.notify_job_completed(
        job_type='product_sync',
        job_id=job_id,
        execution_time='2m 15s',
        products_processed=150,
        success_count=148,
        error_count=2
    )
    
    print("StoreWorker notifications sent!")


# Example 2: Using convenience functions
def example_convenience_functions():
    """Example using convenience functions."""
    print("\n=== Convenience Functions Example ===")
    
    # StoreWorker job
    notify_store_worker_job(
        project_name='fashion-boutique',
        job_type='inventory_update',
        status='completed',
        job_id='inv_67890',
        products_processed=75,
        success_count=75,
        execution_time='1m 30s'
    )
    
    # RankCollections job
    notify_collection_job(
        project_name='electronics-shop',
        collection_name='Gaming Accessories',
        status='completed',
        keywords_count=20,
        products_count=45,
        ranking_score=0.82,
        processing_time='3m 10s'
    )
    
    # StoreBlogs job
    notify_blog_job(
        project_name='sports-equipment',
        article_title='Best Basketball Shoes 2024',
        status='published',
        word_count=1400,
        seo_score=89.5,
        author='Sports Writer'
    )
    
    # Installation job
    notify_installation_job(
        project_name='new-store',
        installation_type='new_user_installation',
        status='completed',
        user_info={
            'name': 'John Smith',
            'email': '<EMAIL>',
            'company': 'New Store Inc.'
        },
        version='v3.2.1',
        features_enabled=['analytics', 'seo_tools', 'inventory_management']
    )
    
    print("Convenience function notifications sent!")


# Example 3: Using the decorator
@slack_notify('storeWorker', 'home-decor-store')
def automated_product_import():
    """Example function with automatic Slack notifications."""
    print("Starting automated product import...")
    
    # Simulate some work
    time.sleep(1)
    
    # Simulate random success/failure
    if random.random() > 0.2:  # 80% success rate
        print("Product import completed successfully!")
        return {'status': 'success', 'products_imported': 25}
    else:
        raise Exception("Database connection failed")


def example_decorator():
    """Example using the decorator."""
    print("\n=== Decorator Example ===")
    
    try:
        result = automated_product_import()
        print(f"Function result: {result}")
    except Exception as e:
        print(f"Function failed: {e}")
    
    print("Decorator notifications sent automatically!")


# Example 4: Integration with existing workflow
def example_workflow_integration():
    """Example showing integration with existing workflow."""
    print("\n=== Workflow Integration Example ===")
    
    # Initialize notifiers for different operations
    sw_notifier = StoreWorkerSlackNotifier('multi-store-project')
    rc_notifier = RankCollectionsSlackNotifier('multi-store-project')
    sb_notifier = StoreBlogsSlackNotifier('multi-store-project')
    
    # Simulate a complex workflow
    workflow_steps = [
        {
            'step': 'Product Sync',
            'notifier': sw_notifier,
            'method': 'notify_job_started',
            'args': {'job_type': 'product_sync', 'job_id': 'workflow_001'}
        },
        {
            'step': 'Collection Creation',
            'notifier': rc_notifier,
            'method': 'notify_collection_creation',
            'args': {'collection_name': 'Trending Products', 'status': 'started'}
        },
        {
            'step': 'Blog Article Generation',
            'notifier': sb_notifier,
            'method': 'notify_article_creation',
            'args': {'article_title': 'New Product Spotlight', 'status': 'started'}
        }
    ]
    
    for step in workflow_steps:
        print(f"Executing: {step['step']}")
        
        # Send notification
        method = getattr(step['notifier'], step['method'])
        method(**step['args'])
        
        # Simulate work
        time.sleep(0.5)
    
    # Complete the workflow
    sw_notifier.notify_job_completed(
        job_type='product_sync',
        job_id='workflow_001',
        execution_time='5m 45s',
        products_processed=200,
        success_count=195,
        error_count=5
    )
    
    rc_notifier.notify_collection_creation(
        collection_name='Trending Products',
        status='completed',
        keywords_count=30,
        products_count=85,
        ranking_score=0.88,
        processing_time='3m 20s'
    )
    
    sb_notifier.notify_article_creation(
        article_title='New Product Spotlight',
        status='published',
        word_count=950,
        seo_score=85.0,
        author='AI Content Generator'
    )
    
    print("Workflow integration notifications sent!")


# Example 5: Error handling and fallbacks
def example_error_handling():
    """Example showing error handling."""
    print("\n=== Error Handling Example ===")
    
    notifier = StoreWorkerSlackNotifier('test-project')
    
    # Try to send a notification that might fail
    try:
        success = notifier.notify_job_failed(
            job_type='critical_operation',
            job_id='critical_001',
            error_message='System overload detected - immediate attention required'
        )
        
        if success:
            print("Critical error notification sent successfully")
        else:
            print("Failed to send critical error notification - using fallback")
            # Implement fallback notification method here
            # e.g., email, SMS, or logging
            
    except Exception as e:
        print(f"Notification system error: {e}")
        # Implement emergency notification method here


def main():
    """Run all examples."""
    print("Slack Integration Examples for StoreWorker Tools")
    print("=" * 60)
    
    # Run examples
    example_store_worker_class()
    example_convenience_functions()
    example_decorator()
    example_workflow_integration()
    example_error_handling()
    
    print("\n" + "=" * 60)
    print("All examples completed!")
    print("\nTo use with real Slack notifications:")
    print("1. Set your SLACK_BOT_TOKEN environment variable")
    print("2. Update channel IDs in config/slack_channels.py")
    print("3. Customize templates in config/templates/ as needed")


if __name__ == "__main__":
    main()
