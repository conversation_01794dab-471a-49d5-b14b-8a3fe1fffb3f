import logging
import os
from typing import Dict, Any, List
import requests
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from dotenv import load_dotenv
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from datetime import datetime

# Load environment variables
load_dotenv()

# MCP SSE server configuration (add more as needed)
def get_mcp_server_params(user_id=None, connected_account_id=None):
    """
    Generate MCP server parameters with optional user/account binding

    Args:
        user_id: User identifier from your application
        connected_account_id: Specific Composio connected account ID
    """
    base_url = "https://mcp.composio.dev/composio/server/5269b1ac-7c7e-426b-af79-2ba75fa77994/sse"

    # Build query parameters
    query_params = ["include_composio_helper_actions=true"]

    if user_id:
        query_params.append(f"user_id={user_id}")

    if connected_account_id:
        query_params.append(f"connected_account_id={connected_account_id}")

    url_with_params = f"{base_url}?{'&'.join(query_params)}"

    return [
        {
            "url": url_with_params,
            "transport": "sse"
        },
        {
            "url": "https://mcp.shopify.chakril.site/sse",
            "transport": "sse"
        }
    ]

# Default configuration for backward compatibility
MCP_SERVER_PARAMS_LIST = get_mcp_server_params()

# Static demo data for Shopify and Google Sheets
STATIC_SHOPIFY_DATA = {
    "shop_url": "https://wayne-enterprises-1.myshopify.com",
    "access_token": "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"
}
STATIC_SHEET_DATA = {
    "sheet_title": f"Low_Stock_Products_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
}
LOW_STOCK_THRESHOLD = 25

# --- Custom Tool for Shopify Admin API ---
class ShopifyAdminToolInput(BaseModel):
    shop_url: str = Field(..., description="Shopify store URL (e.g., https://yourstore.myshopify.com)")
    access_token: str = Field(..., description="Shopify Admin API access token")
    graphql_query: str = Field(..., description="GraphQL query or mutation string")

class ShopifyAdminTool(BaseTool):
    name: str = "SHOPIFY_ADMIN_GRAPHQL"
    description: str = "Post a GraphQL query or mutation to the Shopify Admin API and return the raw JSON response."
    args_schema = ShopifyAdminToolInput

    def _run(self, shop_url: str, access_token: str, graphql_query: str) -> dict:
        endpoint = f"{shop_url}/admin/api/2023-10/graphql.json"
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": access_token
        }
        try:
            response = requests.post(endpoint, headers=headers, json={"query": graphql_query})
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}

# --- Utility: Dynamic Multi-Server Adapter ---
def get_mcp_tools(server_params_list: List[dict]):
    """Connect to multiple MCP servers and aggregate their tools."""
    return MCPServerAdapter(server_params_list)

def get_connected_accounts_for_user(user_id: str, app_name: str = "gmail"):
    """
    Get connected accounts for a specific user and app.
    This is a placeholder - you'll need to implement this using Composio SDK/API

    Args:
        user_id: Your application's user identifier
        app_name: The app name (e.g., 'gmail')

    Returns:
        List of connected account IDs for the user
    """
    # TODO: Implement using Composio SDK
    # Example:
    # from composio import Composio
    # client = Composio(api_key=os.getenv("COMPOSIO_API_KEY"))
    # accounts = client.get_connected_accounts(user_id=user_id, app=app_name)
    # return [account.id for account in accounts if account.status == "ACTIVE"]

    # For now, return empty list - you'll need to implement this
    return []

def extract_connected_account_ids(connections_response: dict, app_name: str = "gmail"):
    """
    Extract connected account IDs from Composio connections API response.

    Args:
        connections_response: The response from Composio connections API
        app_name: The app name to filter by (e.g., 'gmail', 'googlesheets')

    Returns:
        List of connected account IDs for the specified app
    """
    if not connections_response.get("data", {}).get("accounts"):
        return []

    accounts = connections_response["data"]["accounts"]
    filtered_accounts = [
        account["id"]
        for account in accounts
        if account.get("appName") == app_name and account.get("status") == "ACTIVE"
    ]

    return filtered_accounts

def get_account_info_from_response(connections_response: dict):
    """
    Extract account information from connections response for easy reference.

    Args:
        connections_response: The response from Composio connections API

    Returns:
        Dictionary with app names as keys and account info as values
    """
    if not connections_response.get("data", {}).get("accounts"):
        return {}

    accounts = connections_response["data"]["accounts"]
    account_info = {}

    for account in accounts:
        if account.get("status") == "ACTIVE":
            app_name = account.get("appName")
            if app_name not in account_info:
                account_info[app_name] = []

            account_info[app_name].append({
                "id": account["id"],
                "created_at": account["createdAt"],
                "updated_at": account["updatedAt"],
                "client_user_id": account.get("clientUniqueUserId"),
                "entity_id": account.get("entityId")
            })

    return account_info

def run_for_specific_user(user_id: str, user_email: str = None, connected_account_id: str = None):
    """
    Run the low stock report for a specific user's Gmail account.

    Args:
        user_id: Your application's user identifier
        user_email: Optional email address for logging/identification
        connected_account_id: Optional specific connected account ID to use

    Returns:
        Result of the crew execution
    """
    print(f"Running low stock report for user: {user_id}")
    if user_email:
        print(f"User email: {user_email}")
    if connected_account_id:
        print(f"Using connected account: {connected_account_id}")

    request_data = {**STATIC_SHOPIFY_DATA, **STATIC_SHEET_DATA}

    # Create crew with user-specific parameters
    crew = ShopifyLowStockCrew(
        request_data=request_data,
        user_id=user_id,
        connected_account_id=connected_account_id
    )

    return crew.run()

def run_for_multiple_users(user_configs: List[Dict[str, str]]):
    """
    Run the low stock report for multiple users.

    Args:
        user_configs: List of user configurations, each containing:
            - user_id: Your application's user identifier
            - user_email: Optional email address
            - connected_account_id: Optional specific connected account ID

    Example:
        user_configs = [
            {"user_id": "user123", "user_email": "<EMAIL>", "connected_account_id": "conn_123"},
            {"user_id": "user456", "user_email": "<EMAIL>", "connected_account_id": "conn_456"}
        ]
    """
    results = {}

    for config in user_configs:
        user_id = config.get("user_id")
        user_email = config.get("user_email")
        connected_account_id = config.get("connected_account_id")

        try:
            print(f"\n{'='*50}")
            result = run_for_specific_user(user_id, user_email, connected_account_id)
            results[user_id] = {
                "status": "success",
                "result": result
            }
        except Exception as e:
            print(f"Error processing user {user_id}: {str(e)}")
            results[user_id] = {
                "status": "error",
                "error": str(e)
            }

    return results

# --- CrewAI-based Orchestration ---
class ShopifyLowStockCrew:
    def __init__(self, request_data: Dict[str, Any], user_id: str = None, connected_account_id: str = None):
        self.request_data = request_data
        self.user_id = user_id
        self.connected_account_id = connected_account_id
        self.llm = LLM(
            model="azure/development-proactive-ai",
            api_key="********************************",
            base_url="https://bincha-ai.openai.azure.com"
        )

    def run(self):
        try:
            # Get MCP server parameters with user/account binding
            mcp_params = get_mcp_server_params(
                user_id=self.user_id,
                connected_account_id=self.connected_account_id
            )

            with get_mcp_tools(mcp_params) as tools:
                admin_schema_tools = [t for t in tools if "admin_schema" in t.name.lower() or "schema" in t.name.lower()]
                shopify_tool = ShopifyAdminTool()
                sheets_tools = [t for t in tools if t.name.startswith("GOOGLESHEETS")]
                mailing_tools = [t for t in tools if t.name.startswith("GMAIL")]

                agents = {
                    "fetcher": Agent(
                        role="Shopify Product Fetcher",
                        goal="Fetch the first 10 products from Shopify using the admin_schema tool and custom ShopifyAdminTool.",
                        backstory="Expert in retrieving product data from Shopify using dynamic tools.",
                        tools=admin_schema_tools + [shopify_tool],
                        verbose=True,
                        llm=self.llm
                    ),
                    "filter": Agent(
                        role="Low Stock Filter",
                        goal=f"Filter products with inventory less than {LOW_STOCK_THRESHOLD}.",
                        backstory="Specialist in inventory analysis.",
                        verbose=True,
                        llm=self.llm
                    ),
                    # "sheet_writer": Agent(
                    #     role="Google Sheets Writer",
                    #     goal="Write low stock products to a Google Sheet.",
                    #     backstory="Expert in Google Sheets automation.",
                    #     tools=sheets_tools,
                    #     verbose=True,
                    #     llm=self.llm
                    # ),
                    "mailing_agent": Agent(
                        role="Mailing Agent",
                        goal="Send an email to the user with the low stock products.",
                        backstory="Expert in sending emails.",
                        tools=mailing_tools,
                        verbose=True,
                        llm=self.llm
                    ),
                    # "reorder_agent": Agent(
                    #     role="Reorder Agent",
                    #     goal="Reorder the low stock products.",
                    #     backstory="Expert in reordering products.",
                    #     verbose=True,
                    #     llm=self.llm
                    # ),
                    "summary": Agent(
                        role="Summary Agent",
                        goal="Summarize the process and output a human-readable summary.",
                        backstory="Provides clear summaries for business users.",
                        verbose=True,
                        llm=self.llm
                    )
                }

                tasks = [
                    Task(
                        description=(
                            "Fetch the first 10 products with all inventory_quantity data for all variants from Shopify using the admin_schema tool to get the GraphQL string, "
                            "then use the custom ShopifyAdminTool to fetch the products. Use the following credentials: "
                            f"shop_url: {STATIC_SHOPIFY_DATA['shop_url']}, access_token: {STATIC_SHOPIFY_DATA['access_token']}. "
                            "Return the raw JSON response from Shopify."
                        ),
                        expected_output="Raw JSON response containing the first 10 products from Shopify.",
                        agent=agents["fetcher"],
                        inputs={}
                    ),
                    Task(
                        description=(
                            f"Filter the products from the previous step to only include those with inventory_quantity (or totalInventory) less than {LOW_STOCK_THRESHOLD}. "
                            "Return the filtered list as raw JSON."
                        ),
                        expected_output="Raw JSON list of low stock products.",
                        agent=agents["filter"]
                    ),
                    # Task(
                    #     description=(
                    #         "Write the filtered low stock products - title, id, variant_id, inventory_quantity, timestamp - Convert to string and add to a Google Sheet using the available Google Sheets tool. "
                    #         f"Sheet title: {STATIC_SHEET_DATA['sheet_title']}. Return the confirmation or result as raw JSON."
                    #     ),
                    #     expected_output="Raw JSON confirmation of rows added to the sheet.",
                    #     agent=agents["sheet_writer"]
                    # ),
                    Task(
                        description=(
                            "Send an email from user's mail account to Recipient Email - <EMAIL>, with the body as low stock products and store name"
                            "Return the confirmation or result as raw JSON."
                        ),
                        expected_output="Raw JSON confirmation of email sent.",
                        agent=agents["mailing_agent"]
                    ),
                    # Task(
                    #     description=(
                    #         "Reorder the low stock products.",
                    #         "Return the confirmation or result as raw JSON."
                    #     ),
                    #     expected_output="Raw JSON confirmation of reorder.",
                    #     agent=agents["reorder_agent"]
                    # ),
                    Task(
                        description=(
                            "Summarize the process: how many products were fetched, how many were low stock, and confirmation of writing to the sheet. "
                            "Return a human-readable summary."
                        ),
                        expected_output="A summary string.",
                        agent=agents["summary"]
                    )
                ]

                crew = Crew(
                    agents=list(agents.values()),
                    tasks=tasks,
                    process=Process.sequential,
                    verbose=True,
                    memory=False,
                    cache=True
                )
                result = crew.kickoff(inputs=self.request_data)
            return {
                "status": "success",
                "result": result
            }
        except Exception as exc:
            print(f"Error running crew: {str(exc)}")
            return {
                "status": "error",
                "message": str(exc)
            }

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    print("Starting ShopifyLowStockToSheets script with CrewAI agents and tasks...")
    logging.info("Script started.")

    # Example usage with specific user/account selection
    request_data = {**STATIC_SHOPIFY_DATA, **STATIC_SHEET_DATA}

    # Extract Gmail connected account ID from your connections response
    # This is the Gmail account ID from your connections API response
    gmail_connected_account_id = "f0959713-db46-4db2-a3f1-9aa06d74a445"

    print(f"Using Gmail connected account ID: {gmail_connected_account_id}")

    # Create crew with the specific Gmail account
    crew = ShopifyLowStockCrew(
        request_data=request_data,
        connected_account_id=gmail_connected_account_id
    )

    result = crew.run()
    print("\nOutput:")
    print(result)

    print("\n" + "="*60)
    print("EXAMPLE: Using helper functions with your connections response")
    print("="*60)

    # Example of how to use the helper functions with your actual connections response
    sample_connections_response = {
        "status_code": 200,
        "status": "success",
        "message": "Connected accounts retrieved successfully",
        "data": {
            "entity_id": "wayne-enterprises-1.myshopify.com",
            "accounts_count": 3,
            "accounts": [
                {
                    "id": "f0959713-db46-4db2-a3f1-9aa06d74a445",
                    "status": "ACTIVE",
                    "createdAt": "2025-06-04T19:53:24.144Z",
                    "updatedAt": "2025-06-04T19:54:19.025Z",
                    "appUniqueId": "gmail",
                    "appName": "gmail",
                    "integrationId": "1960771b-f540-4a50-ae1f-6ec219159db3",
                    "clientUniqueUserId": "wayne-enterprises-1.myshopify.com",
                    "entityId": "default"
                },
                {
                    "id": "e93c923b-9892-405f-88c4-44d3523dc3ef",
                    "status": "ACTIVE",
                    "appName": "googlesheets",
                    "clientUniqueUserId": "wayne-enterprises-1.myshopify.com",
                    "entityId": "default"
                }
            ]
        }
    }

    # Extract Gmail accounts
    gmail_accounts = extract_connected_account_ids(sample_connections_response, "gmail")
    print(f"Gmail accounts found: {gmail_accounts}")

    # Get all account info
    all_accounts = get_account_info_from_response(sample_connections_response)
    print(f"All connected accounts: {all_accounts}")

    # Example: Running for multiple users with different Gmail accounts
    # Uncomment and modify the following to test with multiple users:
    """
    user_configs = [
        {
            "user_id": "user123",
            "user_email": "<EMAIL>",
            "connected_account_id": "conn_account_123"
        },
        {
            "user_id": "user456",
            "user_email": "<EMAIL>",
            "connected_account_id": "conn_account_456"
        }
    ]

    print("\n" + "="*60)
    print("RUNNING FOR MULTIPLE USERS")
    print("="*60)

    multi_user_results = run_for_multiple_users(user_configs)

    for user_id, result in multi_user_results.items():
        print(f"\nUser {user_id}: {result['status']}")
        if result['status'] == 'error':
            print(f"Error: {result['error']}")
    """