import logging
import os
from typing import Dict, Any, List
import requests
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from dotenv import load_dotenv
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from datetime import datetime

# Load environment variables
load_dotenv()

# MCP SSE server configuration (add more as needed)
MCP_SERVER_PARAMS_LIST = [
    {
        "url": "https://mcp.composio.dev/composio/server/257b86f1-aa58-4cc8-9a11-91ae55383d61/sse",
        "transport": "sse"
    },
    {
        "url": "https://mcp.shopify.chakril.site/sse",
        "transport": "sse"
    }
]

# Static demo data for Shopify and Google Sheets
STATIC_SHOPIFY_DATA = {
    "shop_url": "https://wayne-enterprises-1.myshopify.com",
    "access_token": "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"
}
STATIC_SHEET_DATA = {
    "spread_sheet_id": "1kiycNgt9la-j7-VgzuksgogGrQorTQ94kE8B0PLZxEc",
    "spread_sheet_title": "Low_Stock_Products_20250603_134052"
}

# --- Custom Tool for Shopify Admin API ---
class ShopifyAdminToolInput(BaseModel):
    shop_url: str = Field(..., description="Shopify store URL (e.g., https://yourstore.myshopify.com)")
    access_token: str = Field(..., description="Shopify Admin API access token")
    graphql_query: str = Field(..., description="GraphQL query or mutation string")

class ShopifyAdminTool(BaseTool):
    name: str = "SHOPIFY_ADMIN_GRAPHQL"
    description: str = "Post a GraphQL query or mutation to the Shopify Admin API and return the raw JSON response."
    args_schema = ShopifyAdminToolInput

    def _run(self, shop_url: str, access_token: str, graphql_query: str) -> dict:
        endpoint = f"{shop_url}/admin/api/2023-10/graphql.json"
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": access_token
        }
        try:
            response = requests.post(endpoint, headers=headers, json={"query": graphql_query})
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}

# --- Utility: Dynamic Multi-Server Adapter ---
def get_mcp_tools(server_params_list: List[dict]):
    """Connect to multiple MCP servers and aggregate their tools."""
    return MCPServerAdapter(server_params_list)

# --- CrewAI-based Orchestration ---
class ShopifyInventoryUpdateCrew:
    def __init__(self, request_data: Dict[str, Any]):
        self.request_data = request_data
        self.llm = LLM(
            model="azure/development-proactive-ai",
            api_key="********************************",
            base_url="https://bincha-ai.openai.azure.com"
        )

    def run(self):
        try:
            with get_mcp_tools(MCP_SERVER_PARAMS_LIST) as tools:
                # Get relevant tools
                admin_schema_tools = [t for t in tools if "admin_schema" in t.name.lower() or "schema" in t.name.lower()]
                shopify_tool = ShopifyAdminTool()
                sheets_tools = [t for t in tools if t.name.startswith("GOOGLESHEETS")]

                # --- AGENTS ---
                agents = {
                    "sheet_reader": Agent(
                        role="Google Sheets Reader",
                        goal="Read product inventory data from a Google Sheet.",
                        backstory="Expert in extracting structured data from Google Sheets for further processing.",
                        tools=sheets_tools,
                        verbose=True,
                        llm=self.llm
                    ),
                    "shopify_updater": Agent(
                        role="Shopify Inventory Updater",
                        goal="Update the inventory quantity for each product in Shopify using the admin_schema tool and custom ShopifyAdminTool.",
                        backstory="Expert in updating Shopify product data using dynamic tools.",
                        tools=admin_schema_tools + [shopify_tool],
                        verbose=True,
                        llm=self.llm
                    ),
                    "summary": Agent(
                        role="Summary Agent",
                        goal="Summarize the process and output a human-readable summary.",
                        backstory="Provides clear summaries for business users.",
                        verbose=True,
                        llm=self.llm
                    )
                }

                # --- TASKS ---
                tasks = [
                    Task(
                        description=(
                            "Read all rows from the Google Sheet titled '"
                            f"{STATIC_SHEET_DATA['spread_sheet_title']}', {STATIC_SHEET_DATA['spread_sheet_id']} and extract product id, variant id, and new inventory quantity for each row. "
                            "Return the extracted data as raw JSON."
                        ),
                        expected_output="Raw JSON list of products with id, variant id, and new inventory quantity.",
                        agent=agents["sheet_reader"],
                        inputs={}
                    ),
                    Task(
                        description=(
                            "For each product from the previous step, use the admin_schema tool to get the GraphQL mutation string for updating inventory, "
                            "then use the custom ShopifyAdminTool to update the inventory quantity in Shopify. Use the following credentials: "
                            f"shop_url: {STATIC_SHOPIFY_DATA['shop_url']}, access_token: {STATIC_SHOPIFY_DATA['access_token']}. "
                            "Return the raw JSON response from Shopify for each update."
                        ),
                        expected_output="Raw JSON response for each inventory update in Shopify.",
                        agent=agents["shopify_updater"]
                    ),
                    Task(
                        description=(
                            "Summarize the process: how many products were read from the sheet, how many inventory updates were attempted, and confirmation of updates. "
                            "Return a human-readable summary."
                        ),
                        expected_output="A summary string.",
                        agent=agents["summary"]
                    )
                ]

                crew = Crew(
                    agents=list(agents.values()),
                    tasks=tasks,
                    process=Process.sequential,
                    verbose=True,
                    memory=False,
                    cache=True
                )
                result = crew.kickoff(inputs=self.request_data)
            return {
                "status": "success",
                "result": result
            }
        except Exception as exc:
            print(f"Error running crew: {str(exc)}")
            return {
                "status": "error",
                "message": str(exc)
            }

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    print("Starting ShopifyInventoryUpdateCrew script with CrewAI agents and tasks...")
    logging.info("Script started.")

    # No manual filtering or tool calls; just pass static data to the crew
    request_data = {**STATIC_SHOPIFY_DATA, **STATIC_SHEET_DATA}
    crew = ShopifyInventoryUpdateCrew(request_data=request_data)
    result = crew.run()
    print("\nOutput:")
    print(result) 