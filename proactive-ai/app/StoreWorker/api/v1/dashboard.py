from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from uuid import uuid4
from typing import List, Optional, Dict, Any    
import logging
import json
import time
from datetime import datetime
from StoreWorker.models.types import (
    TeamsResponseBody,
    TeamDetailsResponse,
    Agent,
    StoreWorkerAgent
)
from Middleware.auth_check import verify_tokens
from Utils.Helpers.validate_token import validate_auth_data
from StoreWorker.utils.response_formatter import (
    format_success_response, format_error_response, format_failure_response
)
from StoreWorker.logger.logger import LogContext, info, error, debug
from app.Models.dbHelpers import get_teams_by_store_id, get_team_details, get_all_workers_for_store, get_dashboard_total_workers, get_dashboard_total_teams, get_dashboard_current_running_teams, get_dashboard_recent_teams, get_dashboard_workflow_status_rate

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constant for toggling execution time tracking
TRACK_EXECUTION_TIME = True

router = APIRouter()

def convert_datetime_to_iso(dt_value: Any) -> str:
    """
    Convert datetime value to ISO format string.
    
    Args:
        dt_value: The datetime value to convert
        
    Returns:
        str: ISO format string or empty string if conversion fails
    """
    try:
        if isinstance(dt_value, datetime):
            return dt_value.isoformat()
        elif isinstance(dt_value, str):
            return dt_value
        elif dt_value is None:
            return ""
        else:
            return str(dt_value)
    except Exception as e:
        print(f"Error converting datetime: {str(e)}")
        return ""

@router.get("/get-dashboard-data")
async def get_dashboard_data(
    request: Request,
    auth_data: dict = Depends(verify_tokens),
    recent_limit: int = 5
):
    """
    Dashboard endpoint to return total workers, total workflows, current running workflows, recent workflows, and workflow status rate for the authenticated store.
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        if not store_id:
            return format_error_response("Missing store_id in authentication data.")

        # Gather all metrics concurrently
        import asyncio
        (
            total_workers,
            total_teams,
            current_running_teams,
            recent_workflows,
            workflow_status_rate
        ) = await asyncio.gather(
            get_dashboard_total_workers(store_id),
            get_dashboard_total_teams(store_id),
            get_dashboard_current_running_teams(store_id),
            get_dashboard_recent_teams(store_id, recent_limit),
            get_dashboard_workflow_status_rate(store_id)
        )

        response = {
            "total_workers": total_workers,
            "total_workflows": total_teams,
            "current_running_workflows": current_running_teams,
            "recent_workflows": recent_workflows,
            "workflow_status_rate": workflow_status_rate
        }
        return format_success_response(data=response, message="Dashboard data fetched successfully")
    except Exception as e:
        logger.error(f"Error in get_dashboard_data: {str(e)}")
        return format_error_response(str(e))

  
