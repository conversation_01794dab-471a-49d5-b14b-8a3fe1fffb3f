from fastapi import APIRouter, Depends, HTTPException, Request, BackgroundTasks
from pydantic import BaseModel, Field,  HttpUrl
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
from fastapi.encoders import jsonable_encoder

# Import auth middleware
from Middleware.auth_check import verify_tokens
from Auth.api.v1.auth import AuthManager
from Utils import sync_products
from Logging.logger_config import get_logger
from Utils.Helpers.response_formater import success_response, failure_response, error_response, unauthorized_response
from Utils.Helpers.validate_token import validate_auth_data
from Models.dbHelpers import store_competitor_domains, store_collection_schedule, insert_product_sync_job, get_product_sync_job, insert_blog_sync_job, get_blog_sync_job, insert_article_sync_job, get_article_sync_job, insert_collection_sync_job, get_collection_sync_job

# Import necessary modules for Celery
from app.celery_sync.celery_config_products import sync_products_task
from app.celery_sync.celery_config_blogs import sync_blogs_task
from app.celery_sync.celery_config_articles import sync_articles_task
from app.celery_sync.celery_config_collections import sync_collections_task
from app.StoreWorker.models.types import Product

router = APIRouter()

logger = get_logger(__name__)

# Define API endpoint for products sync
@router.post("/products", response_description="Sync all products from Shopify to Supabase database.")
async def sync_all_products(
    request: Request,
    products: List[Product],
    callback_url: Optional[str] = None,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start of sync operation with all relevant details
        logger.info(
            "Starting products sync by StoreBlog",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "product_count": len(products)
            }
        )

        # Prepare products for the sync function
        products_data = []
        for product in products:
            product_dict = {
                'id': product.id,
                'title': product.title,
                'handle': product.handle,
                'totalInventory': product.totalInventory,
                'tags': product.tags,
                'vendor': product.vendor,
                'description': product.description,
                'createdAt': product.createdAt.isoformat(),
                'variants': [variant.node.dict() for variant in product.variants],
                'image_url': product.image_url,
                'product_url': product.product_url
            }
            products_data.append(product_dict)

        # Submit the task to Celery
        task = sync_products_task.delay(
            products=products_data,
            store_id=store_id,
            callback_url=callback_url
        )
        
        # Store task information in database
        await insert_product_sync_job(
            task_id=task.id,
            store_id=store_id,
            product_count=len(products_data),
            status='QUEUED'
        )
        
        # Log successful queue
        logger.info(
            "Products sync task queued successfully",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "task_id": task.id,
                "product_count": len(products_data)
            }
        )
        
        return success_response(
            message="Products sync task started successfully",
            data={
                "task_id": task.id,
                "products_count": len(products_data),
                "status": "queued"
            }
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in sync_all_products",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to sync products",
            error=str(e)
        )

# Add a new endpoint to check the status of a sync task
@router.get("/products/status/{task_id}", response_description="Get the status of a product sync task.")
async def get_product_sync_status(
    task_id: str,
    request: Request,
):
    # Possible statuses: QUEUED, PROCESSING, COMPLETED, FAILED
    try:
        # Get task status from database
        task_info = await get_product_sync_job(task_id)
        
        if not task_info:
            return error_response(
                message="Task not found",
                error="No task found with the specified ID"
            )
            
        return success_response(
            message="Task status retrieved successfully",
            data=task_info
        )
        
    except Exception as e:
        return error_response(
            message="Failed to get task status",
            error=str(e)
        )


class Blog(BaseModel):
    id: str
    handle: str
    title: str
    commentPolicy: Optional[str] = None
    feed: Optional[str] = None
    templateSuffix: Optional[str] = None
    tags: Optional[List[str]] = None
    createdAt: Optional[datetime] = None
    updatedAt: Optional[datetime] = None

class BlogSyncRequest(BaseModel):
    blogs: List[Blog]

@router.post("/blogs", response_description="Sync all blogs from Shopify to Supabase database.")
async def sync_all_blogs(
    request: Request,
    blog_request: BlogSyncRequest,
    callback_url: Optional[str] = None,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start of sync operation
        logger.info(
            "Starting blogs sync",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "blog_count": len(blog_request.blogs)
            }
        )

        # Prepare blogs for the sync function
        blogs_data = []
        for blog in blog_request.blogs:
            blog_dict = {
                'id': blog.id,
                'handle': blog.handle,
                'title': blog.title,
                'comment_policy': blog.commentPolicy,
                'feed': blog.feed,
                'template_suffix': blog.templateSuffix,
                'tags': blog.tags,
                'blog_created_at': blog.createdAt.isoformat() if blog.createdAt else None,
                'blog_updated_at': blog.updatedAt.isoformat() if blog.updatedAt else None
            }
            blogs_data.append(blog_dict)

        # Submit the task to Celery
        task = sync_blogs_task.delay(
            blogs=blogs_data,
            store_id=store_id,
            callback_url=callback_url
        )
        
        # Store task information in database
        await insert_blog_sync_job(
            task_id=task.id,
            store_id=store_id,
            blog_count=len(blogs_data),
            status='QUEUED'
        )
        
        # Log successful queue
        logger.info(
            "Blogs sync task queued successfully",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "task_id": task.id,
                "blog_count": len(blogs_data)
            }
        )
        
        return success_response(
            message="Blogs sync task started successfully",
            data={
                "task_id": task.id,
                "blogs_count": len(blogs_data),
                "status": "queued"
            }
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in sync_all_blogs",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to sync blogs",
            error=str(e)
        )

@router.get("/blogs/status/{task_id}", response_description="Get the status of a blog sync task.")
async def get_blog_sync_status(
    task_id: str,
    request: Request,
):
    try:
        # Get task status from database
        task_info = await get_blog_sync_job(task_id)
        
        if not task_info:
            return error_response(
                message="Task not found",
                error="No task found with the specified ID"
            )
            
        return success_response(
            message="Task status retrieved successfully",
            data=task_info
        )
        
    except Exception as e:
        return error_response(
            message="Failed to get task status",
            error=str(e)
        )

class ArticleImage(BaseModel):
    id: str
    url: str

class ArticleAuthor(BaseModel):
    name: str

class ArticleBlog(BaseModel):
    id: str
    title: str

class Article(BaseModel):
    id: str
    handle: str
    title: str
    createdAt: datetime
    publishedAt: Optional[datetime] = None
    body: str
    author: ArticleAuthor
    blog: ArticleBlog
    image: Optional[ArticleImage] = None

class ArticleSyncRequest(BaseModel):
    articles: List[Article]

@router.post("/articles", response_description="Sync all articles from Shopify to Supabase database.")
async def sync_all_articles(
    request: Request,
    article_request: ArticleSyncRequest,
    callback_url: Optional[str] = None,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start of sync operation
        logger.info(
            "Starting articles sync",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "article_count": len(article_request.articles)
            }
        )

        # Prepare articles for the sync function
        articles_data = []
        for article in article_request.articles:
            article_dict = {
                'id': article.id,
                'handle': article.handle,
                'title': article.title,
                'body': article.body,
                'author_name': article.author.name,
                'blog_id': article.blog.id,
                'article_created_at': article.createdAt.isoformat() if article.createdAt else None,
                'article_published_at': article.publishedAt.isoformat() if article.publishedAt else None,
                'image_data': json.dumps(article.image.dict()) if article.image else json.dumps({}),
                'store_id': store_id
            }
            articles_data.append(article_dict)

        # Submit the task to Celery
        task = sync_articles_task.delay(
            articles=articles_data,
            store_id=store_id,
            callback_url=callback_url
        )
        
        # Store task information in database
        await insert_article_sync_job(
            task_id=task.id,
            store_id=store_id,
            article_count=len(articles_data),
            status='QUEUED'
        )
        
        # Log successful queue
        logger.info(
            "Articles sync task queued successfully",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "task_id": task.id,
                "article_count": len(articles_data)
            }
        )
        
        return success_response(
            message="Articles sync task started successfully",
            data={
                "task_id": task.id,
                "articles_count": len(articles_data),
                "status": "queued"
            }
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in sync_all_articles",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to sync articles",
            error=str(e)
        )

@router.get("/articles/status/{task_id}", response_description="Get the status of an article sync task.")
async def get_article_sync_status(
    task_id: str,
    request: Request,
):
    try:
        # Get task status from database
        task_info = await get_article_sync_job(task_id)
        
        if not task_info:
            return error_response(
                message="Task not found",
                error="No task found with the specified ID"
            )
            
        return success_response(
            message="Task status retrieved successfully",
            data=task_info
        )
        
    except Exception as e:
        return error_response(
            message="Failed to get task status",
            error=str(e)
        )

class CollectionProduct(BaseModel):
    id: str
    title: str
    description: Optional[str] = None
    priceRangeV2: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    vendor: Optional[str] = None
    media: Optional[Dict[str, Any]] = None

class Collection(BaseModel):
    title: str
    description: Optional[str] = None
    products: List[CollectionProduct]

class CollectionSyncRequest(BaseModel):
    collections: List[Collection]

@router.post("/collections", response_description="Sync all collections from Shopify to Supabase database.")
async def sync_all_collections(
    request: Request,
    collection_request: CollectionSyncRequest,
    callback_url: Optional[str] = None,
    auth_data: dict = Depends(verify_tokens)
):
    try:
        # Validate token details using reusable function
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        # Log the start of sync operation
        logger.info(
            "Starting collections sync",
            extra={
                "origin": request.client.host,
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "collection_count": len(collection_request.collections)
            }
        )

        # Prepare collections for the sync function
        collections_data = []
        for collection in collection_request.collections:
            collection_dict = {
                'id': f"gid://shopify/Collection/{collection.title}",  # Generate a unique ID
                'title': collection.title,
                'description': collection.description,
                'products': [product.dict() for product in collection.products]
            }
            collections_data.append(collection_dict)

        # Submit the task to Celery
        task = sync_collections_task.delay(
            collections=collections_data,
            store_id=store_id,
            callback_url=callback_url
        )
        
        # Store task information in database
        await insert_collection_sync_job(
            task_id=task.id,
            store_id=store_id,
            collection_count=len(collections_data),
            status='QUEUED'
        )
        
        # Log successful queue
        logger.info(
            "Collections sync task queued successfully",
            extra={
                "store_id": store_id,
                "app_id": app_id,
                "request_id": request_id,
                "task_id": task.id,
                "collection_count": len(collections_data)
            }
        )
        
        return success_response(
            message="Collections sync task started successfully",
            data={
                "task_id": task.id,
                "collections_count": len(collections_data),
                "status": "queued"
            }
        )
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(
            "Error in sync_all_collections",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            }
        )
        
        return error_response(
            message="Failed to sync collections",
            error=str(e)
        )

@router.get("/collections/status/{task_id}", response_description="Get the status of a collection sync task.")
async def get_collection_sync_status(
    task_id: str,
    request: Request,
):
    try:
        # Get task status from database
        task_info = await get_collection_sync_job(task_id)
        
        if not task_info:
            return error_response(
                message="Task not found",
                error="No task found with the specified ID"
            )
            
        return success_response(
            message="Task status retrieved successfully",
            data=task_info
        )
        
    except Exception as e:
        return error_response(
            message="Failed to get task status",
            error=str(e)
        )