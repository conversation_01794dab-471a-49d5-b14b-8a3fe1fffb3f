"""
Composio Testing Endpoints
==========================

FastAPI endpoints for testing Composio integration functions.
These endpoints allow you to test various Composio operations through HTTP requests.
"""

from fastapi import APIRouter, Request, Depends, Query, Body, HTTPException
from fastapi.params import Path
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field, RootModel
import logging
import asyncio
import json
import os

# Import Composio helpers
try:
    from app.StoreWorker.Tools import (
        ComposioManager,
        CrewAIComposioIntegration,
        ComposioToolsPresets,
        setup_composio_for_store,
        create_quick_crew,
        test_composio_connection,
        list_available_actions_for_app,
        get_action_schema,
        async_execute_action,
        validate_environment,
        COMPOSIO_AVAILABLE
    )
    from app.StoreWorker.Tools.composio_config import SupportedApps, get_apps_for_category
except ImportError as e:
    logging.warning(f"Composio tools not available: {e}")
    COMPOSIO_AVAILABLE = False

from app.StoreWorker.utils.response_formatter import format_success_response, format_error_response
from app.Middleware.auth_check import verify_tokens
from app.Utils.Helpers.validate_token import validate_auth_data
from app.Models.dbHelpers import get_store_name

router = APIRouter()
logger = logging.getLogger(__name__)

# Pydantic models for request/response validation
class ComposioStatusResponse(BaseModel):
    composio_available: bool
    environment_valid: Dict[str, bool]
    supported_apps_count: int

class ToolsRequest(BaseModel):
    apps: List[str] = Field(..., description="List of app names")
    tags: Optional[List[str]] = Field(None, description="Optional tags to filter tools")
    entity_id: Optional[str] = Field(None, description="Entity ID (defaults to store-based)")

class ActionsRequest(BaseModel):
    actions: List[str] = Field(..., description="List of action names")
    entity_id: Optional[str] = Field(None, description="Entity ID (defaults to store-based)")

class DirectExecutionRequest(BaseModel):
    action: str = Field(..., description="Action name to execute")
    params: Dict[str, Any] = Field(default_factory=dict, description="Parameters for the action")
    entity_id: Optional[str] = Field(None, description="Entity ID (defaults to store-based)")
    connected_account_id: Optional[str] = Field(None, description="Specific connection ID")

class ConnectionRequest(BaseModel):
    app_name: str = Field(..., description="App name to connect")
    entity_id: Optional[str] = Field(None, description="Entity ID (defaults to store-based)")
    timeout: int = Field(120, description="Timeout in seconds for OAuth flow")
    redirect_url: Optional[str] = Field(None, description="Redirect URL for OAuth callback (white-labelling)")
    integration_id: Optional[str] = Field(None, description="Integration ID for white-labelled OAuth (optional)")

class QuickCrewRequest(BaseModel):
    apps: List[str] = Field(..., description="List of app names")
    task_description: str = Field(..., description="Description of the task")
    entity_id: Optional[str] = Field(None, description="Entity ID (defaults to store-based)")
    execute: bool = Field(False, description="Whether to execute the crew immediately")

class UseCaseSearchRequest(BaseModel):
    use_case: str = Field(..., description="Natural language description of the task")
    apps: Optional[List[str]] = Field(None, description="Optional list of apps to scope search")
    advanced: bool = Field(False, description="Use advanced search for complex queries")
    entity_id: Optional[str] = Field(None, description="Entity ID (defaults to store-based)")

class IntegrationCreateRequest(RootModel[dict]):
    """Accept any fields (forward all to Composio API)"""
    pass


# ---------used endpoints---------

@router.post("/connect")
async def initiate_connection(
    request: ConnectionRequest,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Initiate connection for an app (OAuth flow), supporting white-labelling via integration_id and redirect_url.
    """
    try:
        logger.info(f"[initiate_connection] Initiating connection for app: {request.app_name}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        # get the store name from the store_id
        store_name = await get_store_name(store_id)

        # Fallback to store_id if store_name is None
        if not store_name:
            store_name = f"store_{store_id}"
            logger.warning(f"[initiate_connection] Store name not found for store_id {store_id}, using fallback: {store_name}")

        # Set up entity_id
        entity_id = request.entity_id or store_name

        # Initialize Composio manager
        try:
            manager = ComposioManager(entity_id=entity_id)
        except ImportError as e:
            return format_error_response(
                message="Composio packages not installed",
                error=str(e)
            )

        connection_request = None
        # White-labelled flow if integration_id is provided
        if request.integration_id:
            try:
                logger.info(f"[initiate_connection] Using WHITE-LABELLED flow (integration_id: {request.integration_id})")
                # Use ComposioToolSet.initiate_connection for white-labelling
                kwargs = {
                    "integration_id": request.integration_id,
                    "entity_id": entity_id
                }
                if request.redirect_url:
                    kwargs["redirect_url"] = request.redirect_url
                    logger.info(f"[initiate_connection] Custom redirect_url provided: {request.redirect_url}")
                connection_request = manager.toolset.initiate_connection(**kwargs)
            except Exception as e:
                logger.error(f"[initiate_connection] White-labelled flow error: {e}", exc_info=True)
                return format_error_response(
                    message="Failed to initiate white-labelled connection",
                    error=str(e)
                )
        else:
            # Old flow (default composio integration)
            logger.info(f"[initiate_connection] Using DEFAULT flow (no integration_id)")
            connection_request = manager.initiate_connection(request.app_name, entity_id)

        if not connection_request:
            return format_error_response(
                message="Failed to initiate connection",
                error=f"Could not initiate connection for {request.app_name}"
            )

        # Try to serialize the connection_request object
        def safe_serialize(obj):
            try:
                if hasattr(obj, 'model_dump'):
                    return obj.model_dump()
                elif hasattr(obj, '__dict__'):
                    return obj.__dict__
                else:
                    return str(obj)
            except Exception:
                return str(obj)

        response_data = {
            "entity_id": entity_id,
            "app_name": request.app_name,
            "connection_request": safe_serialize(connection_request)
        }

        logger.info(f"[initiate_connection] Connection initiated for {request.app_name}, integration_id: {request.integration_id}")

        return format_success_response(
            message="Connection initiated successfully",
            data=response_data
        )

    except Exception as e:
        logger.error(f"[initiate_connection] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to initiate connection",
            error=str(e)
        )

@router.get("/test-connection/{app_name}")
async def test_app_connection(
    app_name: str = Path(..., description="App name to test"),
    entity_id: Optional[str] = Query(None, description="Entity ID (optional)"),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Test connection for a specific app.
    """
    try:
        logger.info(f"[test_app_connection] Testing connection for app: {app_name}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth
        store_id, _, _ = await validate_auth_data(auth_data)

        # Set up entity_id
        test_entity_id = entity_id or f"store_{store_id}"

        # Test connection
        is_connected = test_composio_connection(app_name, test_entity_id)

        logger.info(f"[test_app_connection] Connection test for {app_name}: {'✓' if is_connected else '✗'}")

        return format_success_response(
            message=f"Connection test completed for {app_name}",
            data={
                "entity_id": test_entity_id,
                "app_name": app_name,
                "is_connected": is_connected,
                "status": "connected" if is_connected else "not_connected"
            }
        )

    except Exception as e:
        logger.error(f"[test_app_connection] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message=f"Failed to test connection for {app_name}",
            error=str(e)
        )

@router.get("/connections/{entity_id}")
async def get_connected_accounts(
    entity_id: str = Path(..., description="Entity ID"),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get all connected accounts for an entity.
    """
    try:
        logger.info(f"[get_connected_accounts] Getting connections for entity: {entity_id}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth for validation
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        # Initialize Composio manager
        try:
            manager = ComposioManager(entity_id=entity_id)
        except ImportError as e:
            return format_error_response(
                message="Composio packages not installed",
                error=str(e)
            )

        # Get connected accounts
        accounts = manager.get_connected_accounts(entity_id)

        logger.info(f"[get_connected_accounts] Found {len(accounts)} connected accounts for entity: {entity_id}")

        return format_success_response(
            message="Connected accounts retrieved successfully",
            data={
                "entity_id": entity_id,
                "accounts_count": len(accounts),
                "accounts": accounts
            }
        )

    except Exception as e:
        logger.error(f"[get_connected_accounts] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to get connected accounts",
            error=str(e)
        )


# ---------unused endpoints---------

@router.get("/status", response_model=ComposioStatusResponse)
async def get_composio_status():
    """
    Get the status of Composio integration and environment.
    """
    try:
        logger.info("[get_composio_status] Checking Composio status")

        if not COMPOSIO_AVAILABLE:
            return format_success_response(
                message="Composio not available",
                data={
                    "composio_available": False,
                    "environment_valid": {},
                    "supported_apps_count": 0
                }
            )

        # Validate environment
        env_status = validate_environment()

        # Count supported apps
        apps_count = len([app.value for app in SupportedApps])

        logger.info(f"[get_composio_status] Status checked - available: {COMPOSIO_AVAILABLE}, apps: {apps_count}")

        return format_success_response(
            message="Composio status retrieved successfully",
            data={
                "composio_available": COMPOSIO_AVAILABLE,
                "environment_valid": env_status,
                "supported_apps_count": apps_count
            }
        )

    except Exception as e:
        logger.error(f"[get_composio_status] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to get Composio status",
            error=str(e)
        )

@router.get("/apps")
async def get_available_apps():
    """
    Get list of all available Composio apps.
    """
    try:
        logger.info("[get_available_apps] Fetching available apps")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get apps by category
        categories = {
            "productivity": get_apps_for_category("productivity"),
            "development": get_apps_for_category("development"),
            "communication": get_apps_for_category("communication"),
            "marketing": get_apps_for_category("marketing"),
            "ecommerce": get_apps_for_category("ecommerce"),
            "crm": get_apps_for_category("crm"),
            "project_management": get_apps_for_category("project_management")
        }

        # Get all apps
        all_apps = [app.value for app in SupportedApps]

        logger.info(f"[get_available_apps] Found {len(all_apps)} total apps")

        return format_success_response(
            message="Available apps retrieved successfully",
            data={
                "total_apps": len(all_apps),
                "all_apps": all_apps,
                "categories": categories
            }
        )

    except Exception as e:
        logger.error(f"[get_available_apps] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to get available apps",
            error=str(e)
        )

@router.post("/tools")
async def get_tools_for_apps(
    request: ToolsRequest,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get Composio tools for specific apps.
    """
    try:
        logger.info(f"[get_tools_for_apps] Getting tools for apps: {request.apps}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        # Set up entity_id
        entity_id = request.entity_id or f"store_{store_id}"

        # Initialize Composio manager
        try:
            manager = ComposioManager(entity_id=entity_id)
        except ImportError as e:
            return format_error_response(
                message="Composio packages not installed",
                error=str(e)
            )

        # Get tools
        tools = manager.get_tools_for_apps(request.apps, request.tags)

        logger.info(f"[get_tools_for_apps] Retrieved {len(tools)} tools for {len(request.apps)} apps")

        return format_success_response(
            message="Tools retrieved successfully",
            data={
                "entity_id": entity_id,
                "apps": request.apps,
                "tags": request.tags,
                "tools_count": len(tools),
                "tools": [str(tool) for tool in tools]  # Convert to string for JSON serialization
            }
        )

    except Exception as e:
        logger.error(f"[get_tools_for_apps] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to get tools for apps",
            error=str(e)
        )

@router.post("/actions")
async def get_tools_for_actions(
    request: ActionsRequest,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get Composio tools for specific actions.
    """
    try:
        logger.info(f"[get_tools_for_actions] Getting tools for actions: {request.actions}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        # Set up entity_id
        entity_id = request.entity_id or f"store_{store_id}"

        # Initialize Composio manager
        try:
            manager = ComposioManager(entity_id=entity_id)
        except ImportError as e:
            return format_error_response(
                message="Composio packages not installed",
                error=str(e)
            )

        # Get tools for actions
        tools = manager.get_tools_for_actions(request.actions)

        logger.info(f"[get_tools_for_actions] Retrieved {len(tools)} tools for {len(request.actions)} actions")

        return format_success_response(
            message="Tools for actions retrieved successfully",
            data={
                "entity_id": entity_id,
                "actions": request.actions,
                "tools_count": len(tools),
                "tools": [str(tool) for tool in tools]
            }
        )

    except Exception as e:
        logger.error(f"[get_tools_for_actions] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to get tools for actions",
            error=str(e)
        )

@router.post("/execute")
async def execute_action_direct(
    request: DirectExecutionRequest,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Execute a Composio action directly without CrewAI.
    """
    try:
        logger.info(f"[execute_action_direct] Executing action: {request.action}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        # Set up entity_id
        entity_id = request.entity_id or f"store_{store_id}"

        # Initialize Composio manager
        try:
            manager = ComposioManager(entity_id=entity_id)
        except ImportError as e:
            return format_error_response(
                message="Composio packages not installed",
                error=str(e)
            )

        # Execute action
        result = manager.execute_action_direct(
            action=request.action,
            params=request.params,
            entity_id=entity_id,
            connected_account_id=request.connected_account_id
        )

        logger.info(f"[execute_action_direct] Action executed: {request.action}, success: {result.get('successful', False)}")

        return format_success_response(
            message="Action executed successfully",
            data={
                "entity_id": entity_id,
                "action": request.action,
                "params": request.params,
                "result": result
            }
        )

    except Exception as e:
        logger.error(f"[execute_action_direct] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to execute action",
            error=str(e)
        )

@router.post("/quick-crew")
async def create_and_execute_quick_crew(
    request: QuickCrewRequest,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Create and optionally execute a quick CrewAI crew with Composio tools.
    """
    try:
        logger.info(f"[create_and_execute_quick_crew] Creating crew for apps: {request.apps}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth
        store_id, _, _ = await validate_auth_data(auth_data)

        # Set up entity_id
        entity_id = request.entity_id or f"store_{store_id}"

        # Create quick crew
        try:
            crew = create_quick_crew(
                apps=request.apps,
                task_description=request.task_description,
                entity_id=entity_id
            )
        except ImportError as e:
            return format_error_response(
                message="Composio packages not installed",
                error=str(e)
            )

        if not crew:
            return format_error_response(
                message="Failed to create crew",
                error="Could not create crew with the specified apps and task"
            )

        response_data = {
            "entity_id": entity_id,
            "apps": request.apps,
            "task_description": request.task_description,
            "crew_created": True,
            "executed": False
        }

        # Execute crew if requested
        if request.execute:
            try:
                logger.info(f"[create_and_execute_quick_crew] Executing crew")
                result = crew.kickoff()
                response_data["executed"] = True
                response_data["execution_result"] = str(result)
                logger.info(f"[create_and_execute_quick_crew] Crew executed successfully")
            except Exception as exec_error:
                logger.error(f"[create_and_execute_quick_crew] Execution error: {exec_error}")
                response_data["execution_error"] = str(exec_error)

        logger.info(f"[create_and_execute_quick_crew] Crew created for {len(request.apps)} apps")

        return format_success_response(
            message="Quick crew created successfully",
            data=response_data
        )

    except Exception as e:
        logger.error(f"[create_and_execute_quick_crew] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to create quick crew",
            error=str(e)
        )

@router.post("/search-tools")
async def search_tools_by_use_case(
    request: UseCaseSearchRequest,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Search for tools using natural language description (experimental).
    """
    try:
        logger.info(f"[search_tools_by_use_case] Searching tools for use case: {request.use_case}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth
        store_id, _, _ = await validate_auth_data(auth_data)

        # Set up entity_id
        entity_id = request.entity_id or f"store_{store_id}"

        # Initialize Composio manager
        try:
            manager = ComposioManager(entity_id=entity_id)
        except ImportError as e:
            return format_error_response(
                message="Composio packages not installed",
                error=str(e)
            )

        # Search for tools
        tools = manager.find_tools_by_use_case(
            use_case=request.use_case,
            apps=request.apps,
            advanced=request.advanced
        )

        logger.info(f"[search_tools_by_use_case] Found {len(tools)} tools for use case")

        return format_success_response(
            message="Tools found by use case",
            data={
                "entity_id": entity_id,
                "use_case": request.use_case,
                "apps": request.apps,
                "advanced": request.advanced,
                "tools_count": len(tools),
                "tools": [str(tool) for tool in tools]
            }
        )

    except Exception as e:
        logger.error(f"[search_tools_by_use_case] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to search tools by use case",
            error=str(e)
        )

@router.get("/actions/{app_name}")
async def list_app_actions(
    app_name: str = Path(..., description="App name"),
    auth_data: dict = Depends(verify_tokens)
):
    """
    List all available actions for a specific app.
    """
    try:
        logger.info(f"[list_app_actions] Listing actions for app: {app_name}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth for validation
        store_id, _, _ = await validate_auth_data(auth_data)

        # List actions
        actions = list_available_actions_for_app(app_name)

        logger.info(f"[list_app_actions] Found {len(actions)} actions for {app_name}")

        return format_success_response(
            message=f"Actions listed for {app_name}",
            data={
                "app_name": app_name,
                "actions_count": len(actions),
                "actions": actions
            }
        )

    except Exception as e:
        logger.error(f"[list_app_actions] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message=f"Failed to list actions for {app_name}",
            error=str(e)
        )


@router.get("/schema/{action_name}")
async def get_action_schema_endpoint(
    action_name: str = Path(..., description="Action name"),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get schema for a specific action.
    """
    try:
        logger.info(f"[get_action_schema_endpoint] Getting schema for action: {action_name}")

        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # Get store_id from auth for validation
        store_id, _, _ = await validate_auth_data(auth_data)

        # Get action schema
        schema = get_action_schema(action_name)

        if not schema:
            return format_error_response(
                message=f"Schema not found for action {action_name}",
                error="Action not found or invalid"
            )

        logger.info(f"[get_action_schema_endpoint] Retrieved schema for {action_name}")

        return format_success_response(
            message=f"Schema retrieved for {action_name}",
            data={
                "action_name": action_name,
                "schema": schema
            }
        )

    except Exception as e:
        logger.error(f"[get_action_schema_endpoint] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message=f"Failed to get schema for {action_name}",
            error=str(e)
        )

@router.post("/create-integration")
async def create_integration(
    request: IntegrationCreateRequest,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Create a new Composio integration (protected endpoint).
    Forwards all fields to Composio's integration creation API.
    """
    try:
        if not COMPOSIO_AVAILABLE:
            raise HTTPException(status_code=503, detail="Composio not available")

        # You may need to import the relevant composio SDK/API for integration creation
        # For example, using composio_core or composio_crewai
        # This is a placeholder for the actual call
        from composio import ComposioClient
        api_key = os.getenv('COMPOSIO_API_KEY')
        client = ComposioClient(api_key=api_key)
        # Forward all fields
        integration_data = request.__root__
        integration = client.integrations.create(**integration_data)
        # Try to serialize
        def safe_serialize(obj):
            try:
                if hasattr(obj, 'model_dump'):
                    return obj.model_dump()
                elif hasattr(obj, '__dict__'):
                    return obj.__dict__
                else:
                    return str(obj)
            except Exception:
                return str(obj)
        return format_success_response(
            message="Integration created successfully",
            data={
                "integration": safe_serialize(integration)
            }
        )
    except Exception as e:
        logger.error(f"[create_integration] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to create integration",
            error=str(e)
        )
