from fastapi import APIRouter, HTTPException, Request, Depends, Body
from typing import Dict, Any
import logging

from Middleware.auth_check import verify_tokens
from Utils.Helpers.validate_token import validate_auth_data
from StoreWorker.utils.response_formatter import (
    format_success_response, format_error_response, format_failure_response
)
from app.Models.dbHelpers import (
    update_product_by_id_and_store_id,
    update_collection_by_id_and_store_id,
    update_blog_by_id_and_store_id,
    update_article_by_id_and_store_id
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/products/{product_id}")
async def update_product(
    product_id: str,
    request: Request,
    update_payload: Dict[str, Any] = Body(...),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Endpoint to update a specific product for the authenticated store.
    The ID can be an internal ID or a shopify_gid.
    
    Request Body Example:
    {
        "title": "Updated Product Title",
        "description": "Updated product description",
        "price": 29.99,
        "tags": ["tag1", "tag2"],
        "vendor": "Updated Vendor"
    }
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        if not store_id:
            return format_error_response("Missing store_id in authentication data.", status_code=401)

        logger.info(f"Attempting update for product_id: {product_id}, store_id: {store_id}, request_id: {request_id}")
        
        success = await update_product_by_id_and_store_id(store_id, product_id, update_payload)

        if success:
            return format_success_response(
                data={"product_id": product_id, "updated": True}, 
                message="Product updated successfully"
            )
        else:
            return format_failure_response(
                message=f"Failed to update product with ID {product_id}. It may not exist or the update operation failed.", 
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error in update_product (product_id: {product_id}, store_id: {store_id if 'store_id' in locals() else 'unknown'}): {str(e)}")
        return format_error_response(f"An unexpected error occurred: {str(e)}")

@router.post("/collections/{collection_id}")
async def update_collection(
    collection_id: str,
    request: Request,
    update_payload: Dict[str, Any] = Body(...),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Endpoint to update a specific collection for the authenticated store.
    The ID can be an internal ID or a shopify_gid.
    
    Request Body Example:
    {
        "title": "Updated Collection Title",
        "description": "Updated collection description",
        "products_data": [{"id": "product1"}, {"id": "product2"}]
    }
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        if not store_id:
            return format_error_response("Missing store_id in authentication data.", status_code=401)
        
        logger.info(f"Attempting update for collection_id: {collection_id}, store_id: {store_id}, request_id: {request_id}")

        success = await update_collection_by_id_and_store_id(store_id, collection_id, update_payload)

        if success:
            return format_success_response(
                data={"collection_id": collection_id, "updated": True}, 
                message="Collection updated successfully"
            )
        else:
            return format_failure_response(
                message=f"Failed to update collection with ID {collection_id}. It may not exist or the update operation failed.", 
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error in update_collection (collection_id: {collection_id}, store_id: {store_id if 'store_id' in locals() else 'unknown'}): {str(e)}")
        return format_error_response(f"An unexpected error occurred: {str(e)}")

@router.post("/blogs/{blog_id}")
async def update_blog(
    blog_id: str,
    request: Request,
    update_payload: Dict[str, Any] = Body(...),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Endpoint to update a specific blog for the authenticated store.
    The ID can be an internal ID or a shopify_gid.
    
    Request Body Example:
    {
        "title": "Updated Blog Title",
        "content": "Updated blog content",
        "author": "Updated Author Name",
        "tags": ["tag1", "tag2"]
    }
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        if not store_id:
            return format_error_response("Missing store_id in authentication data.", status_code=401)

        logger.info(f"Attempting update for blog_id: {blog_id}, store_id: {store_id}, request_id: {request_id}")
        
        success = await update_blog_by_id_and_store_id(store_id, blog_id, update_payload)

        if success:
            return format_success_response(
                data={"blog_id": blog_id, "updated": True}, 
                message="Blog updated successfully"
            )
        else:
            return format_failure_response(
                message=f"Failed to update blog with ID {blog_id}. It may not exist or the update operation failed.", 
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error in update_blog (blog_id: {blog_id}, store_id: {store_id if 'store_id' in locals() else 'unknown'}): {str(e)}")
        return format_error_response(f"An unexpected error occurred: {str(e)}")

@router.post("/articles/{article_id}")
async def update_article(
    article_id: str,
    request: Request,
    update_payload: Dict[str, Any] = Body(...),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Endpoint to update a specific article for the authenticated store.
    The ID can be an internal ID or a shopify_gid.
    
    Request Body Example:
    {
        "title": "Updated Article Title",
        "content": "Updated article content",
        "author": "Updated Author Name",
        "tags": ["tag1", "tag2"],
        "meta_description": "Updated meta description"
    }
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        if not store_id:
            return format_error_response("Missing store_id in authentication data.", status_code=401)

        logger.info(f"Attempting update for article_id: {article_id}, store_id: {store_id}, request_id: {request_id}")

        success = await update_article_by_id_and_store_id(store_id, article_id, update_payload)

        if success:
            return format_success_response(
                data={"article_id": article_id, "updated": True}, 
                message="Article updated successfully"
            )
        else:
            return format_failure_response(
                message=f"Failed to update article with ID {article_id}. It may not exist or the update operation failed.", 
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error in update_article (article_id: {article_id}, store_id: {store_id if 'store_id' in locals() else 'unknown'}): {str(e)}")
        return format_error_response(f"An unexpected error occurred: {str(e)}") 