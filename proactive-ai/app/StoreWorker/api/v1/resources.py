from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from uuid import uuid4
from typing import List, Optional, Dict, Any    
import logging
import json
import time
from datetime import datetime
from StoreWorker.models.types import (
    TeamsResponseBody,
    TeamDetailsResponse,
    Agent,
    StoreWorkerAgent
)
from Middleware.auth_check import verify_tokens
from Utils.Helpers.validate_token import validate_auth_data
from StoreWorker.utils.response_formatter import (
    format_success_response, format_error_response, format_failure_response
)
from StoreWorker.logger.logger import LogContext, info, error, debug
from app.Models.dbHelpers import get_teams_by_store_id, get_team_details, get_all_workers_for_store, get_latest_team_run_jobs_for_team_and_store

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constant for toggling execution time tracking
TRACK_EXECUTION_TIME = True

router = APIRouter()

def convert_datetime_to_iso(dt_value: Any) -> str:
    """
    Convert datetime value to ISO format string.
    
    Args:
        dt_value: The datetime value to convert
        
    Returns:
        str: ISO format string or empty string if conversion fails
    """
    try:
        if isinstance(dt_value, datetime):
            return dt_value.isoformat()
        elif isinstance(dt_value, str):
            return dt_value
        elif dt_value is None:
            return ""
        else:
            return str(dt_value)
    except Exception as e:
        print(f"Error converting datetime: {str(e)}")
        return ""

@router.get("/teams", response_model=List[TeamsResponseBody])
async def get_user_teams(
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get all teams that belong to a particular user.
    
    Args:
        auth_data: Authentication data
        
    Returns:
        List[TeamsResponseBody]: List of teams belonging to the user
    """
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)
        
        with LogContext(request_id=request_id, store_id=store_id):
            info(f"Starting to fetch teams for store_id: {store_id}")
            debug(f"Auth data: {auth_data}")
            
            # Get teams from database
            info(f"Calling get_teams_by_store_id for store_id: {store_id}")
            teams = await get_teams_by_store_id(store_id)
            info(f"Retrieved {len(teams) if teams else 0} teams from database")
            
            if not teams:
                info(f"No teams found for store_id: {store_id}")
                return format_success_response(
                    message="No teams found for user",
                    data=[]
                )
            
            #print the teams
            print("@@@@@@@@@@@@@@@@@@@@teams", teams)
            # Convert teams to TeamsResponseBody format
            info("Starting to process teams into TeamsResponseBody format")
            workflow_states = []
            for index, team in enumerate(teams, 1):
                try:
                    debug(f"Processing team {index}/{len(teams)}: {team.get('team_id', 'Unknown')}")
                    
                    # Create workflow state
                    workflow_state = TeamsResponseBody(
                        team_id=team['team_id'],
                        store_id=team['store_id'],
                        name=team['name'],
                        description=team['description'],
                        status=team['status'],
                        current_running_status=team.get('current_running_status', 'pending'),
                        created_at=team['created_at']
                    )
                    workflow_states.append(workflow_state)
                    debug(f"Successfully processed team {index}/{len(teams)}")
                    
                except Exception as team_error:
                    error(
                        f"Error processing team {index}/{len(teams)}: {str(team_error)}",
                        extra={
                            "team_id": team.get('team_id', 'Unknown'),
                            "store_id": store_id,
                            "error": str(team_error)
                        },
                        exc_info=True
                    )
                    continue
            
            info(f"Successfully processed {len(workflow_states)} out of {len(teams)} teams")
            
            if TRACK_EXECUTION_TIME:
                execution_time = time.time() - start_time
                info(f"Teams fetch completed in {execution_time:.2f} seconds")
            
            return format_success_response(
                message="Teams fetched successfully",
                data=workflow_states
            )
            
    except Exception as e:
        error(
            f"Error fetching teams: {str(e)}",
            extra={
                "store_id": store_id if 'store_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "error": str(e)
            },
            exc_info=True
        )
        return format_error_response(
            message="Failed to fetch teams",
            error=str(e)
        )

@router.get("/team-details/{team_id}", response_model=TeamDetailsResponse)
async def get_team_details_api(
    team_id: str,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get detailed information about a team including agents and configurations.
    
    Args:
        team_id: The ID of the team
        auth_data: Authentication data
        
    Returns:
        TeamDetailsResponse: Detailed team information
    """
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)
        
        with LogContext(request_id=request_id, store_id=store_id):
            info(f"Fetching details for team_id: {team_id}")
            
            # Get team details from database
            team_details = await get_team_details(team_id)
            
            if not team_details:
                info(f"No team found for team_id: {team_id}")
                return format_error_response(
                    message="Team not found",
                    error="The specified team ID does not exist",
                    status_code=404
                )
            
            # Get latest job runs for this team and store
            latest_job_runs_raw = await get_latest_team_run_jobs_for_team_and_store(team_id, store_id, limit=5, team_title=team_details.get('name'))
            # Filter job fields
            latest_job_runs = [
                {
                    'team_id': job.get('team_id'),
                    'job_id': job.get('job_id'),
                    'job_status': job.get('job_status'),
                    'job_duration': job.get('job_duration'),
                    'created_at': job.get('created_at'),
                    'job_title': job.get('job_title')
                }
                for job in latest_job_runs_raw
            ]
            # Get store_data fields from team_details if present (top-level fields)
            store_data_obj = None
            store_data_type = team_details.get('store_data_type')
            store_data = team_details.get('store_data')
            include_metadata = team_details.get('include_metadata')
            if store_data_type is not None or store_data is not None or include_metadata is not None:
                store_data_obj = {
                    'store_data_type': store_data_type,
                    'store_data': store_data,
                    'include_metadata': include_metadata
                }
            #print the team details
            print("@@@@@@@@@@@@@@@@@@@@team_details", team_details)
            print("@@@@@@@@@@@@@@@@@@@@latest_job_runs", latest_job_runs)
            
            # Convert to response model
            try:
                snake_case_dict = {
                    'team_id': team_details['team_id'],
                    'store_id': team_details['store_id'],
                    'name': team_details['name'],
                    'description': team_details['description'],
                    'status': team_details['status'],
                    'current_running_status': team_details.get('current_running_status'),
                    'created_at': team_details['created_at'],
                    'agents': [
                        {
                            'uuid': agent['uuid'],
                            'name': agent['Name'],
                            'role': agent['Role'],
                            'goal': agent['Goal']
                        } for agent in team_details['agents']
                    ] if team_details.get('agents') else None,
                    'current_configuration': team_details.get('current_configuration'),
                    'current_key_values': team_details.get('current_key_values'),
                    'latest_job_runs': latest_job_runs,
                    'store_data': store_data_obj
                }
                
                if TRACK_EXECUTION_TIME:
                    execution_time = time.time() - start_time
                    info(f"Team details fetch completed in {execution_time:.2f} seconds")
                
                return format_success_response(
                    message="Team details fetched successfully",
                    data=snake_case_dict
                )
                
            except Exception as model_error:
                error(
                    f"Error converting team details to response model: {str(model_error)}",
                    extra={
                        "team_id": team_id,
                        "store_id": store_id,
                        "error": str(model_error)
                    },
                    exc_info=True
                )
                return format_error_response(
                    message="Failed to process team details",
                    error=str(model_error)
                )
            
    except Exception as e:
        error(
            f"Error fetching team details: {str(e)}",
            extra={
                "team_id": team_id,
                "store_id": store_id if 'store_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "error": str(e)
            },
            exc_info=True
        )
        return format_error_response(
            message="Failed to fetch team details",
            error=str(e)
        )

@router.get("/list-workers", response_model=List[StoreWorkerAgent])
async def get_store_workers(
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get all workers (agents) for a store, including public workers from other stores.
    
    Args:
        auth_data: Authentication data
        
    Returns:
        List[StoreWorkerAgent]: List of workers/agents
    """
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)
        
        with LogContext(request_id=request_id, store_id=store_id):
            info(f"Fetching workers for store_id: {store_id}")
            
            # Get workers from database
            workers = await get_all_workers_for_store(store_id)
            
            if not workers:
                info(f"No workers found for store_id: {store_id}")
                return format_success_response(
                    message="No workers found",
                    data=[]
                )
            
            # Convert to StoreWorkerAgent model
            try:
                agent_list = []
                for worker in workers:
                    agent = StoreWorkerAgent(
                        uuid=worker.get('uuid'),
                        name=worker.get('name'),
                        role=worker.get('role'),
                        goal=worker.get('goal')
                    )
                    agent_list.append(agent)
                
                if TRACK_EXECUTION_TIME:
                    execution_time = time.time() - start_time
                    info(f"Workers fetch completed in {execution_time:.2f} seconds")
                
                return format_success_response(
                    message="Workers fetched successfully",
                    data=agent_list
                )
                
            except Exception as model_error:
                error(
                    f"Error converting workers to response model: {str(model_error)}",
                    extra={
                        "store_id": store_id,
                        "error": str(model_error)
                    },
                    exc_info=True
                )
                return format_error_response(
                    message="Failed to process workers",
                    error=str(model_error)
                )
            
    except Exception as e:
        error(
            f"Error fetching workers: {str(e)}",
            extra={
                "store_id": store_id,
                "request_id": request_id if 'request_id' in locals() else None,
                "error": str(e)
            },
            exc_info=True
        )
        return format_error_response(
            message="Failed to fetch workers",
            error=str(e)
        )
