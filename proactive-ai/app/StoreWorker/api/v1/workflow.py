from fastapi import APIRouter, HTTPException, Request, Depends, Body, Response
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from uuid import uuid4
from typing import List, Optional, Dict, Any    
import logging
import json
import time
from StoreWorker.Agents.nlq_validator import NlqValidator<PERSON><PERSON>
from StoreWorker.Agents.commander import Commander<PERSON><PERSON>
from StoreWorker.Agents.task_summarizer import TaskSummarizerCrew
from StoreWorker.Agents.task_configuration_identifier import TaskConfigurationIdentifierCrew
from StoreWorker.Agents.crew_script_formatter import Script<PERSON><PERSON>atter<PERSON>rew
from StoreWorker.models.types import (
    ValidationRequest, ValidationResult, CommanderRequest, CommanderResponse,
    TaskData, SummarizerResponse, ConfigurationResponse, Agent, Worker,
    WorkerConfiguration, ConfigurationField, TaskSummary, NlqTaskApprovalRequest,
    ListWorkersRequest, CrewTask, ConfigurationRequest, SummarizerRequest, SummarizerResponseBody,
    WorkflowSelectionRequest, WorkerSelection, WorkflowStateResponse, WorkerState, ConfigurationResponseBody,
    CommanderResponseList, CommanderResponseBody, CrewCrewResponse, CrewTaskResponse, CrewAgentResponse,
    FormattedAgent, Tool, ApprovedTaskConfigurationRequest, ApprovedTaskConfigurationResponseBody,
    CreateWorkflowRequest, FormatAgentFactoryInputRequest, FormatAgentFactoryInputResponse
)
from Middleware.auth_check import verify_tokens
from Utils.Helpers.validate_token import validate_auth_data
from StoreWorker.utils.response_formatter import (
    format_success_response, format_error_response, format_failure_response
)
from StoreWorker.logger.logger import LogContext, info, error, debug
from StoreWorker.config.agent_factory.endpoints import azure_create_function_endpoint, azure_create_multi_crew_function_endpoint, azure_create_template_crew_function_endpoint
try:
    from app.Models.dbHelpers import (
        store_nlq_workflow, update_nlq_workflow_approval,
        store_approved_workers, store_task_configuration,
        check_task_configuration_exists, store_created_team, get_team_details,
        update_team_summary, update_team_configuration_id, get_all_agents_and_tasks_for_store,
        update_task_configuration, get_agents_tasks_crew_for_team, get_task_configuration_keys_for_team,
        get_function_app_name_for_store, get_store_name_for_id,
        insert_team_deploy_status, update_team_deploy_status,
        get_team_deploy_status_by_team_id, get_azure_function_app_details_by_store_id,
        update_team_table_status,
        update_team_deploy_function_details, update_team_name,
        insert_team_run_job, update_team_running_status_and_history, supabase, StoreWorkersTeamRunJobsTable,
        update_team_run_job_status
    )
except ImportError:
    from Models.dbHelpers import (
        store_nlq_workflow, update_nlq_workflow_approval,
        store_approved_workers, store_task_configuration,
        check_task_configuration_exists, store_created_team, get_all_agents_and_tasks_for_store,
        update_task_configuration, get_agents_tasks_crew_for_team, get_task_configuration_keys_for_team,
        get_function_app_name_for_store, get_store_name_for_id,
        insert_team_deploy_status, update_team_deploy_status,
        get_team_deploy_status_by_team_id, get_azure_function_app_details_by_store_id,
        update_team_table_status,
        update_team_deploy_function_details, update_team_name,
        insert_team_run_job, update_team_running_status_and_history, update_team_run_job_status
    )
import requests
import ast
import re
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constant for toggling execution time tracking
TRACK_EXECUTION_TIME = True

router = APIRouter()

@router.post("/validate-nlq", response_model=List[ValidationResult])
async def validate_nlq(
    request: Request,
    validation_request: ValidationRequest,
    auth_data: dict = Depends(verify_tokens)
):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        # Validate auth data and get store_id
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        
        with LogContext(request_id=request_id, store_id=store_id):
            # Log the start of validation
            info(
                "Starting NLQ validation",
                extra={
                    "origin": request.client.host,
                    "app_id": app_id,
                    "query": validation_request.query
                }
            )
            
            validator = NlqValidatorCrew()
            debug("Initialized NlqValidatorCrew")
            
            result = validator.validate_nlq(validation_request.query)
            debug(f"Raw validation result: {result}")

            # Generate team_id at the start
            team_id = str(uuid4())
            
            # Process the result and structure it as required
            validation_results = []
            
            # Parse the JSON response from the agent
            try:
                result_str = str(result)
                # Extract the JSON string from the response
                json_str = result_str.split("```json")[1].split("```")[0].strip()
                parsed_result = json.loads(json_str)
                
                # Create validation result from parsed JSON
                validation_result = ValidationResult(
                    team_id=team_id,  # Use the generated team_id
                    workflow_name=parsed_result.get("workflow_name", ""),
                    tasks=parsed_result.get("tasks", []),
                    valid_variations=parsed_result.get("valid_variations", [])
                )
                validation_results.append(validation_result)
                
                # Store the result in the database
                await store_nlq_workflow(
                    team_id=team_id,
                    store_id=store_id,
                    query=validation_request.query,
                    status='generated',
                    workflow_name=parsed_result.get("workflow_name"),
                    tasks=parsed_result.get("tasks", []),
                    valid_variations=parsed_result.get("valid_variations", [])
                )
                
                info(
                    "Successfully processed validation results",
                    extra={
                        "app_id": app_id,
                        "team_id": team_id,
                        "task_count": len(validation_results)
                    }
                )
                
                if TRACK_EXECUTION_TIME:
                    execution_time = time.time() - start_time
                    info(f"NLQ validation completed in {execution_time:.2f} seconds")
                
                return format_success_response(
                    message="NLQ validation completed successfully",
                    data=validation_results
                )
                
            except (IndexError, json.JSONDecodeError) as e:
                error(
                    "Error parsing agent response",
                    extra={
                        "error": str(e),
                        "app_id": app_id
                    },
                    exc_info=True
                )
                return format_error_response(
                    message="Failed to parse agent response",
                    error=str(e)
                )
                
    except Exception as e:
        error(
            "Error during NLQ validation",
            extra={
                "error": str(e),
                "store_id": store_id if 'store_id' in locals() else None,
                "app_id": app_id if 'app_id' in locals() else None,
                "request_id": request_id if 'request_id' in locals() else None,
                "origin": request.client.host
            },
            exc_info=True
        )
        return format_error_response(
            message="Failed to validate NLQ",
            error=str(e)
        )

@router.post("/approved-nlq-task")
async def approve_nlq_task(request: NlqTaskApprovalRequest):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        with LogContext(request_id=request.team_id):
            info(f"Received NLQ task approval request for team_id: {request.team_id}")
            
            # Update the workflow with approved tasks
            result = await update_nlq_workflow_approval(
                team_id=request.team_id,
                selected_tasks=request.selected_tasks
            )
            
            if not result:
                error(f"Task not found for team_id: {request.team_id}")
                return format_error_response(
                    message="Task not found",
                    error="The specified team ID does not exist",
                    status_code=404
                )
            
            if TRACK_EXECUTION_TIME:
                execution_time = time.time() - start_time
                info(f"NLQ task approval completed in {execution_time:.2f} seconds")
            
            return format_success_response(
                message="Task approved successfully",
                data=result
            )
            
    except Exception as e:
        error(f"Error during NLQ task approval: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to approve task",
            error=str(e)
        )

@router.post("/list-workers-for-task", response_model=CommanderResponseList)
async def list_workers_for_task(request: CommanderRequest, auth_data: dict = Depends(verify_tokens)):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)

        with LogContext(request_id=request_id, store_id=store_id):
            info(f"Received crew formation request for team {request.team_id} with {len(request.tasks)} tasks")


            
            commander = CommanderCrew()
            debug("Initialized CommanderCrew")

            # get all agents and tasks for the store
            agents, tasks = await get_all_agents_and_tasks_for_store(store_id)
            
            responses = []
            for task in request.tasks:
                try:
                    # Convert task to dictionary format expected by form_crew
                    task_dict = {
                        "task_id": task.task_id,
                        "task_name": task.name,
                        "task_description": task.description
                    }
                    result = commander.form_crew(task=task_dict, agents=agents, tasks=tasks)
                    debug(f"Raw commander result for task {task.task_id}: {result}")
                    
                    # Parse the JSON response from the agent
                    try:
                        result_str = str(result)
                        # Extract the JSON string from the response
                        json_str = result_str.split("```json")[1].split("```")[0].strip()
                        parsed_result = json.loads(json_str)
                        
                        # Create response with the new structure
                        response = CommanderResponseBody(
                            task_id=task.task_id,
                            Name=task.name,
                            Description=task.description,
                            Agents=[CrewAgentResponse(**agent) for agent in parsed_result.get("Agents", [])],
                            Tasks=[CrewTaskResponse(**task) for task in parsed_result.get("Tasks", [])],
                            Crew=CrewCrewResponse(**parsed_result.get("Crew", {})),
                            Tools=[Tool(**tool) for tool in parsed_result.get("Tools", [])],
                            formatted_agents=[FormattedAgent(**agent) for agent in parsed_result.get("formatted_agents", [])]
                        )
                        responses.append(response)
                        
                    except (IndexError, json.JSONDecodeError) as e:
                        error(f"Error parsing commander response for task {task.task_id}: {str(e)}", exc_info=True)
                        # Continue with next task
                        continue
                        
                except Exception as e:
                    error(f"Error processing task {task.task_id}: {str(e)}", exc_info=True)
                    # Continue with next task
                    continue
            
            info(f"Successfully processed {len(responses)} out of {len(request.tasks)} tasks for team {request.team_id}")
            
            # Prepare commander output
            commander_output = {
                "team_id": request.team_id,
                "tasks": responses if responses else [{}]  # Ensure there's at least one task
            }
            
            # Store workflow
            try:
                workflow_result = await store_created_team(
                    team_id=request.team_id,
                    store_id=store_id,
                    commander_output=commander_output
                )
                
                if workflow_result:
                    info(f"Successfully stored workflow for team {request.team_id}")
                else:
                    error(f"Failed to store workflow for team {request.team_id}")
            except Exception as e:
                error(f"Error storing workflow: {str(e)}", exc_info=True)
            
            if TRACK_EXECUTION_TIME:
                execution_time = time.time() - start_time
                info(f"Crew formation completed in {execution_time:.2f} seconds")
            
            response_body = CommanderResponseList(
                team_id=request.team_id,
                tasks=[{
                    "task_id": task.task_id,
                    "name": task.Name,
                    "description": task.Description,
                    "formatted_agents": task.formatted_agents
                } for task in responses]
            )
            
            return format_success_response(
                message="Crew formation completed successfully",
                data=response_body.model_dump()  # Use model_dump instead of dict
            )
                
    except Exception as e:
        error(f"Error during crew formation: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to form crew",
            error=str(e)
        )

@router.post("/summarize-tasks", response_model=SummarizerResponseBody)
async def summarize_tasks(request: SummarizerRequest):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        with LogContext():
            info(f"Received task summarization request for team {request.team_id} with {len(request.tasks)} tasks")
            
            summarizer = TaskSummarizerCrew()
            debug("Initialized TaskSummarizerCrew")
            
            responses = []
            for task in request.tasks:
                try:
                    # Convert task to the format expected by the summarizer
                    task_data = {
                        "task": task.task,
                        "workers": [agent.model_dump() for agent in task.agents]
                    }
                    result = summarizer.summarize(task_data, mode="single")
                    debug(f"Raw summarization result for task {task.task_id}: {result}")
                    
                    # Parse the JSON response from the agent
                    try:
                        result_str = str(result)
                        # Extract the JSON string from the response
                        json_str = result_str.split("```json")[1].split("```")[0].strip()
                        parsed_result = json.loads(json_str)
                        debug(f"Parsed result: {json.dumps(parsed_result, indent=2)}")
                        
                        # Create response with the new structure
                        response = SummarizerResponse(
                            task_id=task.task_id,
                            task_name=task.task,
                            summary=parsed_result[0].get("summary", []) if parsed_result else []
                        )
                        responses.append(response)
                        
                    except (IndexError, json.JSONDecodeError) as e:
                        error(f"Error parsing summarizer response for task {task.task_id}: {str(e)}", exc_info=True)
                        # Continue with next task
                        continue
                        
                except Exception as e:
                    error(f"Error processing task {task.task_id}: {str(e)}", exc_info=True)
                    # Continue with next task
                    continue
            
            info(f"Successfully processed {len(responses)} out of {len(request.tasks)} tasks for team {request.team_id}")
            
            # Store the summary in the database
            try:
                # Extract all summaries from responses and convert to dictionaries
                all_summaries = []
                for response in responses:
                    # Convert each summary item to a dictionary
                    summary_dicts = []
                    for summary_item in response.summary:
                        if hasattr(summary_item, 'model_dump'):
                            summary_dicts.append(summary_item.model_dump())
                        else:
                            summary_dicts.append(summary_item)
                    all_summaries.extend(summary_dicts)
                
                # Update the team's summary
                await update_team_summary(request.team_id, all_summaries)
                info(f"Successfully stored summary for team {request.team_id}")
                
            except Exception as db_error:
                error(f"Error storing summary in database: {str(db_error)}", exc_info=True)
                # Continue with response even if database update fails
            
            if TRACK_EXECUTION_TIME:
                execution_time = time.time() - start_time
                info(f"Task summarization completed in {execution_time:.2f} seconds")
            
            return_body = SummarizerResponseBody(
                team_id=request.team_id,
                tasks=responses
            )
            
            return format_success_response(
                message="Task summarization completed successfully",
                data=return_body
            )
                
    except Exception as e:
        error(f"Error during task summarization: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to summarize tasks",
            error=str(e)
        )

@router.post("/identify-configurations", response_model=ConfigurationResponseBody)
async def identify_configurations(request: ConfigurationRequest):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        with LogContext():
            info(f"Received configuration identification request for team {request.team_id} with {len(request.tasks)} tasks")
            
            config_identifier = TaskConfigurationIdentifierCrew()
            debug("Initialized TaskConfigurationIdentifierCrew")
            
            responses = []
            for task in request.tasks:
                try:
                    # Convert task to dictionary format expected by identify_configuration
                    task_dict = {
                        "task_id": task.task_id,
                        "name": task.task,
                        "agents": [agent.model_dump() for agent in task.agents]
                    }
                    
                    result = config_identifier.identify_configuration(task_dict)
                    debug(f"Raw configuration result for task {task.task_id}: {result}")
                    
                    # Parse the JSON response from the agent
                    try:
                        result_str = str(result)
                        # Extract the JSON string from the response
                        json_str = result_str.split("```json")[1].split("```")[0].strip()
                        parsed_result = json.loads(json_str)
                        debug(f"Parsed result: {json.dumps(parsed_result, indent=2)}")
                        
                        # Create response with the new structure
                        response = ConfigurationResponse(
                            task_id=task.task_id,
                            name=task.task,
                            type=parsed_result.get("type", "content_creation_task"),
                            agents=task.agents,
                            configurations=parsed_result.get("configurations", []),
                            store_data=parsed_result.get("store_data", {})
                        )
                        responses.append(response)
                        
                    except (IndexError, json.JSONDecodeError) as e:
                        error(f"Error parsing configuration response for task {task.task_id}: {str(e)}", exc_info=True)
                        # Continue with next task
                        continue
                        
                except Exception as e:
                    error(f"Error processing task {task.task_id}: {str(e)}", exc_info=True)
                    # Continue with next task
                    continue
            
            info(f"Successfully processed {len(responses)} out of {len(request.tasks)} tasks for team {request.team_id}")
            
            if TRACK_EXECUTION_TIME:
                execution_time = time.time() - start_time
                info(f"Configuration identification completed in {execution_time:.2f} seconds")
            
            response_body = ConfigurationResponseBody(
                team_id=request.team_id,
                tasks=responses
            )
            
            return format_success_response(
                message="Configuration identification completed successfully",
                data=response_body
            )
                
    except Exception as e:
        error(f"Error during configuration identification: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to identify configurations",
            error=str(e)
        )

@router.post("/approved-workers")
async def store_approved_workers_endpoint(
    team_id: str,
    store_id: int,
    agents: List[dict],
    operation: str = 'create',
    created_by: Optional[str] = None,
    modified_by: Optional[str] = None
):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        with LogContext(request_id=team_id, store_id=store_id):
            info(f"Received approved workers request for team_id: {team_id}")
            
            # Validate operation type
            if operation not in ['create', 'update']:
                error(f"Invalid operation type: {operation}")
                return format_error_response(
                    message="Invalid operation type",
                    error="Operation must be 'create' or 'update'",
                    status_code=400
                )
                
            # Store or update the approved workers
            result = await store_approved_workers(
                team_id=team_id,
                store_id=store_id,
                agents=agents,
                operation=operation,
                created_by=created_by,
                modified_by=modified_by
            )
            
            if not result:
                error(f"Failed to store approved workers for team_id: {team_id}")
                return format_error_response(
                    message="Failed to store approved workers",
                    error="The operation could not be completed",
                    status_code=404
                )
            
            if TRACK_EXECUTION_TIME:
                execution_time = time.time() - start_time
                info(f"Approved workers storage completed in {execution_time:.2f} seconds")
            
            return format_success_response(
                message=f"Approved workers {operation}d successfully",
                data=result
            )
            
    except Exception as e:
        error(f"Error during approved workers storage: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to store approved workers",
            error=str(e)
        )

@router.post("/approved-task-configuration", response_model=ApprovedTaskConfigurationResponseBody)
async def store_task_configuration_endpoint(request: ApprovedTaskConfigurationRequest):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    
    try:
        with LogContext():
            info(f"Received task configuration request for team {request.team_id} with {len(request.tasks)} tasks")
            
            responses = []
            for task in request.tasks:
                try:
                    _, operation = await check_task_configuration_exists(request.team_id)
                    configurations_dict = [config.model_dump() for config in task.configurations]
                    # Extract store_data fields from task.store_data if present
                    store_data_type = None
                    store_data = None
                    include_metadata = None
                    if task.store_data:
                        store_data_type = task.store_data.get('store_data_type')
                        store_data = task.store_data.get('store_data')
                        include_metadata = task.store_data.get('include_metadata')
                    if (operation == 'create'):
                        result = await store_task_configuration(
                            team_id=request.team_id,
                            name=task.name or None,
                            type=task.type or None,
                            current_configuration=configurations_dict,
                            current_key_values=task.values,
                            operation=operation,
                            store_data_type=store_data_type,
                            store_data=store_data,
                            include_metadata=include_metadata
                        )
                    elif (operation == 'update'):
                        result = await update_task_configuration(
                            team_id=request.team_id,
                            current_configuration=configurations_dict,
                            current_key_values=task.values,
                            store_data_type=store_data_type,
                            store_data=store_data,
                            include_metadata=include_metadata
                        )
                    team = await update_team_configuration_id(
                        team_id=request.team_id,
                        configuration_id=result['id']
                    )
                    if not team:
                        error(f"Failed to update team configuration_id for task {task.task_id}")
                        continue
                    if not result:
                        error(f"Failed to store task configuration for task {task.task_id}")
                        continue
                    response = {
                        "task_id": task.task_id if task.task_id else None,
                        "name": result.get("name", task.name),
                        "type": result.get("type", task.type),
                        "configurations": result.get("current_configuration", configurations_dict),
                        "values": result.get("current_key_values", task.values),
                        "store_data": {
                            "store_data_type": result.get("store_data_type", store_data_type),
                            "store_data": result.get("store_data", store_data),
                            "include_metadata": result.get("include_metadata", include_metadata)
                        }
                    }
                    responses.append(response)
                    
                except Exception as e:
                    error(f"Error processing task {task.task_id}: {str(e)}", exc_info=True)
                    # Continue with next task
                    continue
            
            info(f"Successfully processed {len(responses)} out of {len(request.tasks)} tasks for team {request.team_id}")
            
            if TRACK_EXECUTION_TIME:
                execution_time = time.time() - start_time
                info(f"Task configuration storage completed in {execution_time:.2f} seconds")
            
            response_body = ApprovedTaskConfigurationResponseBody(
                team_id=request.team_id,
                tasks=responses
            )
            
            return format_success_response(
                message="Task configuration storage completed successfully",
                data=response_body.model_dump()
            )
                
    except Exception as e:
        error(f"Error during task configuration storage: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to store task configuration",
            error=str(e)
        )

@router.post("/create-workflow")
async def create_workflow(request: Request):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    try:
        body = await request.json()
        team_id = body.get("team_id")
        workflow_name = body.get("workflow_name")
        configuration = body.get("configuration")
        callback_url = body.get("callback_url")
        if not callback_url:
            callback_url = os.environ.get("PROACTIVE_SERVER_CALLBACK_URL_FOR_AZURE_FUNCTION_DEPLOYMENT")
        if not team_id:
            return format_error_response(message="team_id is required", error="Missing team_id", status_code=400)
        with LogContext():
            info(f"Received create workflow request for team {team_id}")
            if workflow_name:
                #update sw_teams table with workflow_name
                await update_team_name(team_id, workflow_name)
            # Update team table status to processing
            update_team_table_status(team_id, "processing")
            # Insert initial deploy status
            team_details = await get_team_details(team_id)
            if not team_details:
                error(f"No team details found for team_id: {team_id}")
                # Use store_id as None if not found
                insert_team_deploy_status(team_id, None, deploy_status="failed", errors="Team details not found")
                update_team_table_status(team_id, "failed")
                return format_error_response(
                    message="Team details not found",
                    error="The specified team ID does not exist",
                    status_code=404
                )
            store_id = team_details.get("store_id")
            if not store_id:
                insert_team_deploy_status(team_id, None, deploy_status="failed", errors="No store_id for team")
                update_team_table_status(team_id, "failed")
                return format_error_response(message="store_id not found for team", error="No store_id", status_code=400)
            insert_team_deploy_status(team_id, store_id, deploy_status="started")
            # Get function_app_name and store_name
            function_app_name = await get_function_app_name_for_store(store_id)
            store_name = await get_store_name_for_id(store_id)
            if not function_app_name or not store_name:
                update_team_deploy_status(team_id, store_id, deploy_status="failed", errors="function_app_name or store_name not found")
                update_team_table_status(team_id, "failed")
                return format_error_response(message="Missing function_app_name or store_name", error="function_app_name or store_name not found", status_code=400)
            # Format agent factory input
            keys = list(configuration.keys()) if configuration else None
            try:
                obj = await get_agents_tasks_crew_for_team(team_id)
                if not keys:
                    try:
                        keys = await get_task_configuration_keys_for_team(team_id)
                    except Exception:
                        keys = None
                script_formatter = ScriptFormatterCrew()
                result = script_formatter.script_formatter(obj, keys)
                result_str = str(result)
                json_str = result_str.split("```json")[1].split("```", 1)[0].strip()
                crew_spec = json.loads(json_str)
                update_team_deploy_status(team_id, store_id, deploy_status="input_formatted")
            except Exception as e:
                error(f"Error formatting agent factory input: {str(e)}", exc_info=True)
                update_team_deploy_status(team_id, store_id, deploy_status="failed", errors=str(e))
                return format_error_response(message="Failed to format agent factory input", error=str(e))
            # Call agent factory endpoint asynchronously with callback_url
            update_team_table_status(team_id, "processing")
            # Do not wait for deployment result, just trigger the process
            _ = await call_agent_factory_create_function(function_app_name, store_name, crew_spec, team_id=team_id, deploy=True, callback_url=callback_url)
            # Immediately return started response
            if TRACK_EXECUTION_TIME:
                execution_time = time.time() - start_time
                info(f"Workflow creation and agent factory call (async) started in {execution_time:.2f} seconds")
            return format_success_response(
                message="Deployment started. You will receive the result at the callback URL.",
                data={"status": "Deployment started"}
            )
    except Exception as e:
        error(f"Error during workflow creation: {str(e)}", exc_info=True)
        # Try to update status to failed
        try:
            body = await request.json()
            team_id = body.get("team_id")
            team_details = await get_team_details(team_id) if team_id else None
            store_id = team_details.get("store_id") if team_details else None
            if team_id:
                update_team_deploy_status(team_id, store_id, deploy_status="failed", errors=str(e))
                update_team_table_status(team_id, "failed")
        except Exception:
            pass
        return format_error_response(
            message="Failed to create workflow",
            error=str(e)
        )

@router.post("/format-agent-factory-input", response_model=FormatAgentFactoryInputResponse)
async def format_agent_factory_input(request: FormatAgentFactoryInputRequest):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    try:
        with LogContext(request_id=request.team_id):
            info(f"Received format-agent-factory-input request for team_id: {request.team_id}")
            # Fetch agents, tasks, crew
            try:
                obj = await get_agents_tasks_crew_for_team(request.team_id)
            except Exception as e:
                error(f"Error fetching agents/tasks/crew: {str(e)}", exc_info=True)
                return format_error_response(
                    message="Failed to fetch agents, tasks, or crew for the team.",
                    error=str(e)
                )
            # Determine keys
            keys = request.keys
            if not keys:
                try:
                    keys = await get_task_configuration_keys_for_team(request.team_id)
                except Exception as e:
                    error(f"Error fetching task configuration keys: {str(e)}", exc_info=True)
                    return format_error_response(
                        message="Failed to fetch task configuration keys for the team.",
                        error=str(e)
                    )
                
            script_formatter = ScriptFormatterCrew()
            debug("Initialized ScriptFormatterCrew")
            try:
                result = script_formatter.script_formatter(obj, keys)
                result_str = str(result)
                # Extract the JSON string from the response
                json_str = result_str.split("```json")[1].split("```", 1)[0].strip()
                parsed_result = json.loads(json_str)
            except Exception as e:
                error(f"Error parsing script_formatter_agent response: {str(e)}", exc_info=True)
                return format_error_response(
                    message="Failed to format agent factory input.",
                    error=str(e)
                )
            if TRACK_EXECUTION_TIME:
                execution_time = time.time() - start_time
                info(f"Agent factory input formatting completed in {execution_time:.2f} seconds")
            return format_success_response(
                message="Agent factory input formatted successfully",
                data={"result": parsed_result}
            )
    except Exception as e:
        error(f"Error during format-agent-factory-input: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to format agent factory input",
            error=str(e)
        )

@router.post("/run-workflow")
async def run_workflow(request: Request, auth_data: dict = Depends(verify_tokens)):
    """
    Run a workflow by calling the deployed Azure Function endpoint for a team.
    Input: team_id (str), configurations (JSON object)
    """
    try:
        body = await request.json()
        team_id = body.get("team_id")
        configurations = body.get("configurations")
        if not team_id or not configurations:
            error(f"run-workflow: Missing input. team_id: {team_id}, configurations: {configurations}")
            return format_error_response(message="team_id and configurations are required", error="Missing input", status_code=400)
        # 1. Get store_id from token
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        info(f"run-workflow: Received request for team_id={team_id}, store_id={store_id}")
        # 2. Get url and endpoint from sw_team_deploy_status
        deploy_status = await get_team_deploy_status_by_team_id(team_id)
        if not deploy_status or not deploy_status.get("url") or not deploy_status.get("endpoint"):
            error(f"run-workflow: No url/endpoint found for team_id={team_id}. Deploy status: {deploy_status}")
            return format_error_response(message="No url/endpoint found for team_id", error="Missing url/endpoint", status_code=404)
        url = deploy_status["url"]
        endpoint = deploy_status["endpoint"]
        # 3. Get api_token from sw_azure_functions_app_details
        azure_details = await get_azure_function_app_details_by_store_id(store_id)
        if not azure_details or not azure_details.get("api_key"):
            error(f"run-workflow: No api_key found for store_id={store_id}. Azure details: {azure_details}")
            return format_error_response(message="No api_key found for store_id", error="Missing api_key", status_code=404)
        api_token = azure_details["api_key"]
        # 4. POST to url+endpoint
        full_url = url.rstrip("/")
        headers = {"x-functions-key": api_token, "Content-Type": "application/json"}
        info(f"run-workflow: Hitting endpoint: {full_url}")
        debug(f"run-workflow: Headers: {headers}")
        debug(f"run-workflow: Configurations: {configurations}")
        job_id = str(uuid4())
        job_status = "running"
        error_messages = None
        output = None
        job_duration = None
        job_title = None
        start_time = time.time()
        # Insert job record as 'running' before making the request
        await insert_team_run_job(
            job_id=job_id,
            team_id=team_id,
            run_by_store_id=store_id,
            input_request=body,
            output=None,
            job_status=job_status,
            error_messages=None,
            job_duration=None,
            job_title=None
        )
        try:
            response = requests.post(full_url, headers=headers, json=configurations)
            info(f"run-workflow: Response status code: {response.status_code}")
            debug(f"run-workflow: Raw response text: {response.text}")
            response.raise_for_status()
            output = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            job_status = "completed"
            # Extract job_title from output if possible
            if isinstance(output, dict):
                job_title = output.get('title')
            error_messages = None
        except Exception as e:
            response_obj = getattr(e, 'response', None)
            # Detailed logging for error and response
            error_msg = f"run-workflow: Failed to call workflow endpoint {full_url}. Error: {str(e)}."
            if response_obj is not None:
                try:
                    response_content = response_obj.json()
                    debug(f"run-workflow: Error response JSON: {json.dumps(response_content, indent=2)}")
                except Exception:
                    response_content = response_obj.text
                    debug(f"run-workflow: Error response text: {response_content}")
                error_msg += f" Response status code: {response_obj.status_code}. Response content: {response_content}"
            else:
                response_content = str(e)
                debug(f"run-workflow: No response object. Exception: {response_content}")
            error(error_msg)
            error_messages = str(e)
            output = response_content
            job_status = "failed"
        job_duration = time.time() - start_time
        # Update job record with final status and output
        await update_team_run_job_status(
            job_id=job_id,
            output=output,
            job_status=job_status,
            error_messages=error_messages,
            job_duration=job_duration,
            job_title=job_title
        )
        # Update team running status and history
        await update_team_running_status_and_history(
            team_id=team_id,
            new_status=job_status,
            job_id=job_id,
            result_status=job_status,
            run_by_store_id=store_id
        )
        # Format the response
        if job_status == "completed":
            result_data = output if isinstance(output, dict) else {"result": output}
            # --- SANITIZE result if needed ---
            if isinstance(result_data.get("result"), str):
                result_str = result_data["result"].strip()
                # Remove 'Output:' prefix
                if result_str.startswith("Output:"):
                    result_str = result_str[len("Output:"):].strip()
                # Remove any code block markers (```json, ```python, or just ```)
                code_block_pattern = r"^```(?:json|python)?\\n|^```|```$"
                result_str = re.sub(code_block_pattern, "", result_str, flags=re.MULTILINE).strip()
                # Try to parse as JSON
                parsed_result = None
                try:
                    parsed_result = json.loads(result_str)
                except Exception:
                    # If JSON parsing fails, try ast.literal_eval for Python-style lists/dicts
                    try:
                        parsed_result = ast.literal_eval(result_str)
                    except Exception:
                        parsed_result = result_str
                result_data["result"] = parsed_result
                output = result_data
            result_data["team_id"] = team_id
            result_data["job_id"] = job_id
            return format_success_response(
                message="Workflow run completed successfully",
                data=result_data
            )
        else:
            # Log job_id and team_id for debugging, but do not send in response
            error(f"run-workflow: job_id={job_id}, team_id={team_id}")
            return format_error_response(
                message="Failed to call workflow endpoint",
                error=error_messages
            )
    except Exception as e:
        error(f"run-workflow: Failed to run workflow. Error: {str(e)}")
        return format_error_response(message="Failed to run workflow", error=str(e))

async def call_agent_factory_create_function(function_app_name, store_name, crew_spec, team_id, deploy=True, callback_url=None):
    payload = {
        "team_id": team_id,
        "function_app_name": function_app_name,
        "store_name": store_name,
        "crew_spec": crew_spec,
        "deploy": deploy
    }
    if callback_url:
        payload["callback_url"] = callback_url
    try:
        response = requests.post(azure_create_template_crew_function_endpoint, json=payload, timeout=480)
        print("Agent Factory Response (async trigger):", response.json())
        return response.json()
    except Exception as e:
        print(f"Error calling agent factory endpoint: {str(e)}")
        return {"error": str(e)}



