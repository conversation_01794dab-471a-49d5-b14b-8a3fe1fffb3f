from fastapi import APIRouter, Request, Depends, Query, Body, BackgroundTasks
from fastapi.params import Path
from StoreWorker.Agents.commander import <PERSON><PERSON><PERSON>
from StoreWorker.models.types import CommanderRequest, CommanderResponseList, CrewAgentResponse, CrewTaskResponse, CrewCrewResponse, Tool, FormattedAgent, CommanderResponseBody, ApprovedTaskConfigurationRequest, ApprovedTaskConfigurationResponseBody, TemplateSummaryList, TemplateDetailsResponse, StoreTemplateConfigurationRequest, StoreTemplateConfigurationResponse, StoreTemplateValuesRequest, StoreTemplateValuesResponse, TemplateAnalyticsResponse, TemplateJobRun
from StoreWorker.utils.response_formatter import format_success_response, format_error_response
from Models.dbHelpers_templates import store_template_agents, store_template_tasks, store_template_crew, store_template_team, store_template_task_configuration, get_all_sw_templates, get_sw_template_details_by_uuid, get_sw_template_id_by_uuid, get_current_sw_template_configuration, get_sw_templates_mapped_to_this_store, store_template_configuration_for_store, update_template_configuration_values, get_sw_template_deployment_by_template_id, create_sw_template_run_job, update_sw_template_run_job, get_azure_function_app_details_by_function_app_name, get_sw_template_run_jobs, get_sw_templates_full_for_store, get_sw_template_run_jobs_for_store
from Middleware.auth_check import verify_tokens
from Utils.Helpers.validate_token import validate_auth_data
import json
import logging
import time
import requests
import httpx
import re
from uuid import uuid4
from datetime import datetime
import ast
from collections import defaultdict
from StoreWorker.config import templates as templates_config
from slack_notifications.slack_script import send_slack_webhook
import base64
from httpx import TimeoutException

router = APIRouter()
logger = logging.getLogger(__name__)
"""
@router.post("/form-script-skeleton", response_model=CommanderResponseList)
async def form_script_skeleton(request: CommanderRequest):
    try:
        store_id = request.store_id if request.store_id is not None else 1
        logger.info(f"[form-script-skeleton] Received request for team_id={request.team_id}, store_id={store_id}")
        commander = CommanderCrew()
        responses = []
        agents = []
        tasks = []
        for task in request.tasks:
            try:
                logger.info(f"[form-script-skeleton] Forming crew for task_id={task.task_id}")
                task_dict = {
                    "task_id": task.task_id,
                    "task_name": task.name,
                    "task_description": task.description
                }
                result = commander.form_crew(task=task_dict, agents=agents, tasks=tasks)
                result_str = str(result)
                json_str = result_str.split("```json")[1].split("```", 1)[0].strip()
                parsed_result = json.loads(json_str)
                response = CommanderResponseBody(
                    task_id=task.task_id,
                    Name=task.name,
                    Description=task.description,
                    Agents=[CrewAgentResponse(**agent) for agent in parsed_result.get("Agents", [])],
                    Tasks=[CrewTaskResponse(**t) for t in parsed_result.get("Tasks", [])],
                    Crew=CrewCrewResponse(**parsed_result.get("Crew", {})),
                    Tools=[Tool(**tool) for tool in parsed_result.get("Tools", [])],
                    formatted_agents=[FormattedAgent(**agent) for agent in parsed_result.get("formatted_agents", [])]
                )
                responses.append(response)
                logger.info(f"[form-script-skeleton] Crew formed for task_id={task.task_id}")
            except Exception as e:
                logger.error(f"[form-script-skeleton] Error forming crew for task_id={task.task_id}: {str(e)}", exc_info=True)
                continue
        response_body = CommanderResponseList(
            team_id=request.team_id,
            tasks=[{
                "task_id": task.task_id,
                "name": task.Name,
                "description": task.Description,
                "formatted_agents": task.formatted_agents
            } for task in responses]
        )
        logger.info(f"[form-script-skeleton] Successfully processed {len(responses)} out of {len(request.tasks)} tasks for team_id={request.team_id}")
        return format_success_response(
            message="Script skeleton formed successfully",
            data=response_body.model_dump()
        )
    except Exception as e:
        logger.error(f"[form-script-skeleton] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to form script skeleton",
            error=str(e)
        )

@router.post("/create-templated-workers")
async def create_templated_workers(request: CommanderResponseList):
    try:
        store_id = getattr(request, 'store_id', None) or 1
        logger.info(f"[create-templated-workers] Received request for team_id={request.team_id}, store_id={store_id}")
        # Extract agents, tasks, crew, and team from the request.tasks
        all_agents = []
        all_tasks = []
        crew_obj = None
        team_obj = {
            "team_id": request.team_id,
            "name": f"Template Team {request.team_id}",
            "description": "Templated team created via API",
            "workflow_data": {},
            "status": "template"
        }
        for task in request.tasks:
            # formatted_agents is a list of FormattedAgent dicts
            agents = task.get("formatted_agents", [])
            all_agents.extend([agent if isinstance(agent, dict) else agent.model_dump() for agent in agents])
            # Tasks is a list of CrewTaskResponse dicts
            tasks_list = task.get("Tasks", [])
            all_tasks.extend([t if isinstance(t, dict) else t.model_dump() for t in tasks_list])
            # Crew is a dict
            if not crew_obj and task.get("Crew"):
                crew_obj = task["Crew"] if isinstance(task["Crew"], dict) else task["Crew"].model_dump()
        logger.info(f"[create-templated-workers] Extracted {len(all_agents)} agents, {len(all_tasks)} tasks, crew: {bool(crew_obj)}")
        # Save agents
        saved_agents = await store_template_agents(all_agents, store_id, request.team_id)
        logger.info(f"[create-templated-workers] Saved {len(saved_agents)} agents")
        # Save tasks
        saved_tasks = await store_template_tasks(all_tasks, store_id, request.team_id)
        logger.info(f"[create-templated-workers] Saved {len(saved_tasks)} tasks")
        # Save crew
        saved_crew = None
        if crew_obj:
            saved_crew = await store_template_crew(crew_obj, store_id, request.team_id)
            logger.info(f"[create-templated-workers] Saved crew")
        # Save team
        saved_team = await store_template_team(team_obj, store_id, request.team_id)
        logger.info(f"[create-templated-workers] Saved team")
        return format_success_response(
            message="Templated workers created successfully",
            data={
                "agents": saved_agents,
                "tasks": saved_tasks,
                "crew": saved_crew,
                "team": saved_team
            }
        )
    except Exception as e:
        logger.error(f"[create-templated-workers] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to create templated workers",
            error=str(e)
        )

@router.post("/add-task-configurations", response_model=ApprovedTaskConfigurationResponseBody)
async def add_task_configurations(request: ApprovedTaskConfigurationRequest):
    try:
        store_id = getattr(request, 'store_id', None) or 1
        logger.info(f"[add-task-configurations] Received request for team_id={request.team_id}, store_id={store_id}")
        responses = []
        for task in request.tasks:
            try:
                logger.info(f"[add-task-configurations] Storing configuration for task_id={getattr(task, 'task_id', None)}")
                config_dict = {
                    "name": getattr(task, 'name', None),
                    "type": getattr(task, 'type', None),
                    "current_configuration": [c.model_dump() for c in getattr(task, 'configurations', [])],
                    "current_key_values": getattr(task, 'values', {}),
                    "store_data_type": task.store_data.get('store_data_type') if getattr(task, 'store_data', None) else None,
                    "store_data": task.store_data.get('store_data') if getattr(task, 'store_data', None) else None,
                    "include_metadata": task.store_data.get('include_metadata') if getattr(task, 'store_data', None) else None
                }
                result = await store_template_task_configuration(config_dict, request.team_id)
                response = {
                    "task_id": getattr(task, 'task_id', None),
                    "name": result.get("name", getattr(task, 'name', None)),
                    "type": result.get("type", getattr(task, 'type', None)),
                    "configurations": result.get("current_configuration", config_dict["current_configuration"]),
                    "values": result.get("current_key_values", config_dict["current_key_values"]),
                    "store_data": {
                        "store_data_type": result.get("store_data_type", config_dict["store_data_type"]),
                        "store_data": result.get("store_data", config_dict["store_data"]),
                        "include_metadata": result.get("include_metadata", config_dict["include_metadata"])
                    }
                }
                responses.append(response)
                logger.info(f"[add-task-configurations] Stored configuration for task_id={getattr(task, 'task_id', None)}")
            except Exception as e:
                logger.error(f"[add-task-configurations] Error storing configuration for task_id={getattr(task, 'task_id', None)}: {str(e)}", exc_info=True)
                continue
        response_body = ApprovedTaskConfigurationResponseBody(
            team_id=request.team_id,
            tasks=responses
        )
        logger.info(f"[add-task-configurations] Successfully processed {len(responses)} out of {len(request.tasks)} tasks for team_id={request.team_id}")
        return format_success_response(
            message="Task configurations added successfully",
            data=response_body.model_dump()
        )
    except Exception as e:
        logger.error(f"[add-task-configurations] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to add task configurations",
            error=str(e)
        )
"""
# -------------------
# SAMPLE REQUEST PAYLOADS FOR TESTING
# -------------------

"""
1. /form-script-skeleton
POST /form-script-skeleton
{
  "team_id": "uuid-001",
  "store_id": 1,
  "tasks": [
    {
      "task_id": "task-001",
      "name": "Create Blog Post",
      "description": "Generate a blog post about the latest e-commerce trends."
    },
    {
      "task_id": "task-002",
      "name": "Product Collection",
      "description": "Create a new product collection for summer sale."
    }
  ]
}

2. /create-templated-workers
POST /create-templated-workers
{
  "team_id": "template-team-123",
  "tasks": [
    {
      "task_id": "task-001",
      "name": "Create Blog Post",
      "description": "Generate a blog post about the latest e-commerce trends.",
      "formatted_agents": [
        {
          "uuid": "agent-001",
          "id": 1,
          "title": "Writer",
          "description": "Writes engaging blog content.",
          "icon": "✏️",
          "iconColor": "success",
          "selected": true
        },
        {
          "uuid": "agent-002",
          "id": 2,
          "title": "SEO Specialist",
          "description": "Optimizes content for search engines.",
          "icon": "🔍",
          "iconColor": "highlight",
          "selected": true
        }
      ],
      "Tasks": [
        {
          "uuid": "task-001",
          "Name": "Draft Blog",
          "Description": "Draft the main content of the blog post.",
          "Output": "Drafted blog text.",
          "Agent": ["Writer"],
          "Dependencies": null
        },
        {
          "uuid": "task-002",
          "Name": "SEO Review",
          "Description": "Review and optimize the blog for SEO.",
          "Output": "SEO-optimized blog post.",
          "Agent": ["SEO Specialist"],
          "Dependencies": "Draft Blog"
        }
      ],
      "Crew": {
        "Name": "Blog Crew",
        "Description": "Handles blog creation and SEO.",
        "Agents": ["Writer", "SEO Specialist"],
        "Tasks": ["Draft Blog", "SEO Review"]
      }
    }
  ]
}

3. /add-task-configurations
POST /add-task-configurations
{
  "team_id": "template-team-123",
  "tasks": [
    {
      "task_id": "task-001",
      "name": "Create Blog Post",
      "type": "content_creation_task",
      "configurations": [
        {
          "agent_name": "Writer",
          "name": "Blog Title",
          "description": "Title for the blog post.",
          "fields": [
            {
              "key": "title",
              "type": "string",
              "component": "input",
              "required": true,
              "display_name": "Blog Title",
              "placeholder": "Enter the blog title",
              "description": "The main title of the blog post."
            }
          ]
        }
      ],
      "values": {
        "title": "Top 10 E-commerce Trends in 2024"
      },
      "store_data": {
        "store_data_type": {
          "category": "blog",
          "type": "single"
        },
        "store_data": [
          {"id": "blog-001", "title": "Sample Blog"}
        ],
        "include_metadata": true
      }
    }
  ]
}
"""


# ----------------New Endpoints-------------
# 1. /home - get all templates (template summary info for home page)
@router.get("/home", response_model=TemplateSummaryList)
async def get_templates_home(auth_data: dict = Depends(verify_tokens)):
    """
    Get all templates (summary info for home page)
    """
    try:
        logger.info("[get_templates_home] Starting template home endpoint")
        # Get store_id from token
        store_id, app_id, request_id = await validate_auth_data(auth_data)
        logger.info(f"[get_templates_home] Authenticated store_id={store_id}, app_id={app_id}, request_id={request_id}")
        # Get templates mapped to this store (via helper)
        logger.info(f"[get_templates_home] Fetching templates for store_id={store_id}")
        templates = await get_sw_templates_mapped_to_this_store(store_id)
        logger.info(f"[get_templates_home] Found {len(templates)} templates for store_id={store_id}")
        # Get recent activity (latest 10 jobs for this store)
        jobs = await get_sw_template_run_jobs_for_store(store_id)
        recent_activity = [TemplateJobRun(
            job_id=job.get('uuid'),
            job_status=job.get('status'),
            job_duration=str(job.get('job_duration')) if job.get('job_duration') is not None else None,
            created_at=job.get('created_at'),
            job_title=job.get('job_title')
        ) for job in jobs[:10]]
        logger.info("[get_templates_home] Successfully fetched templates and recent activity")
        return format_success_response(
            message="Templates fetched successfully",
            data={
                "templates": templates,
                "recent_activity": [ra.model_dump() for ra in recent_activity]
            }
        )
    except Exception as e:
        logger.error(f"[get_templates_home] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to fetch templates",
            error=str(e)
        )

# 2. /template-details - get all details for a template by uuid
@router.get("/template-details/{template_uuid}", response_model=TemplateDetailsResponse)
async def get_template_details(
    template_uuid: str = Path(..., description="UUID of the template"),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get all details for a template by uuid, including current configuration for the current store if present, and latest job runs.
    """
    try:
        logger.info(f"[get_template_details] Starting template details endpoint for template_uuid={template_uuid}")
        # Get store_id from token
        store_id, app_id, request_id = await validate_auth_data(auth_data)
        logger.info(f"[get_template_details] Authenticated store_id={store_id}, app_id={app_id}, request_id={request_id}")
        # Get template details by uuid (via helper)
        logger.info(f"[get_template_details] Fetching details for template_uuid={template_uuid}")
        template = await get_sw_template_details_by_uuid(template_uuid)
        if not template:
            logger.warning(f"[get_template_details] Template not found for uuid={template_uuid}")
            return format_error_response(message="Template not found", error="No template with this uuid", status_code=404)
        # Get template_id for config lookup (via helper)
        logger.info(f"[get_template_details] Getting template_id for uuid={template_uuid}")
        template_id = await get_sw_template_id_by_uuid(template_uuid)
        logger.info(f"[get_template_details] Found template_id={template_id} for uuid={template_uuid}")
        # Get current config for this store/template (via helper)
        config = None
        if store_id and template_id:
            logger.info(f"[get_template_details] Fetching current config for store_id={store_id}, template_id={template_id}")
            config = await get_current_sw_template_configuration(store_id, template_id)
            if config:
                logger.info(f"[get_template_details] Found config for store_id={store_id}, template_id={template_id}")
            else:
                logger.info(f"[get_template_details] No config found for store_id={store_id}, template_id={template_id}")
        # Get latest job runs for this template/store (via helper)
        latest_job_runs = []
        if store_id and template_id:
            jobs = await get_sw_template_run_jobs(store_id, template_id)
            for job in jobs[:5]:
                latest_job_runs.append({
                    "job_id": job.get("uuid"),
                    "job_status": job.get("status"),
                    "job_duration": job.get("job_duration"),
                    "created_at": job.get("created_at"),
                    "job_title": job.get("job_title"),
                })
        # Prepare config columns (from schema)
        config_keys = [
            "id", "uuid", "created_at", "updated_at", "template_id", "store_id",
            "current_configuration", "current_key_values", "current_version", "configuration_history",
            "store_data_type", "store_data", "additional_inputs", "include_metadata"
        ]
        template_details = dict(template)
        # Always include template's own id, uuid, created_at, updated_at
        template_details["id"] = template.get("id")
        template_details["uuid"] = template.get("uuid")
        template_details["created_at"] = template.get("created_at")
        template_details["updated_at"] = template.get("updated_at")
        # Add configuration-prefixed fields
        if config:
            template_details["configuration_id"] = config.get("id")
            template_details["configuration_uuid"] = config.get("uuid")
            template_details["configuration_created_at"] = config.get("created_at")
            template_details["configuration_updated_at"] = config.get("updated_at")
        else:
            template_details["configuration_id"] = None
            template_details["configuration_uuid"] = None
            template_details["configuration_created_at"] = None
            template_details["configuration_updated_at"] = None
        if config:
            # Use all config columns from config (except the four above, which are now prefixed)
            for key in config_keys:
                if key not in ["id", "uuid", "created_at", "updated_at"]:
                    template_details[key] = config.get(key)
        else:
            # No config: use base config for current_configuration, empty/defaults for others
            template_details["current_configuration"] = template.get("base_configuration")
            template_details["current_key_values"] = {}
            template_details["current_version"] = 1
            template_details["configuration_history"] = []
            template_details["store_data_type"] = template.get("store_data_type") if template.get("store_data_type") is not None else {}
            # store_data block with required structure
            template_details["store_data"] = [{
                "store_data_type": template.get("store_data_type") if template.get("store_data_type") is not None else {},
                "store_data": template.get("store_data") if template.get("store_data") is not None else [],
                "include_metadata": template.get("include_metadata") if template.get("include_metadata") is not None else True
            }]
            template_details["additional_inputs"] = {}
            template_details["include_metadata"] = template.get("include_metadata") if template.get("include_metadata") is not None else True
            # The following keys are not meaningful without a config, but include as None
            template_details["template_id"] = template_id
            template_details["store_id"] = store_id
        template_details["latest_job_runs"] = latest_job_runs
        logger.info("[get_template_details] Successfully fetched template details")
        return format_success_response(
            message="Template details fetched successfully",
            data=template_details
        )
    except Exception as e:
        logger.error(f"[get_template_details] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to fetch template details",
            error=str(e)
        )

# 3. /store-template-configuration - create or update a template configuration for a store/template
@router.post("/store-template-configuration", response_model=StoreTemplateConfigurationResponse)
async def store_template_configuration(
    request: StoreTemplateConfigurationRequest = Body(...),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Create or update a template configuration for a store/template. All fields are optional. If config_update is true, bump version and backup. Otherwise, just update provided fields. Always preserve existing values for missing fields.
    """
    try:
        logger.info("[store-template-configuration] Step 1: Start endpoint")
        store_id, app_id, request_id = await validate_auth_data(auth_data)
        logger.info(f"[store-template-configuration] Step 2: Auth validated, store_id={store_id}, template_uuid={request.template_uuid}")
        template_id = await get_sw_template_id_by_uuid(request.template_uuid)
        logger.info(f"[store-template-configuration] Step 3: Fetched template_id={template_id}")
        if not template_id:
            logger.error(f"[store-template-configuration] Step 3b: Template not found for uuid={request.template_uuid}")
            return format_error_response(message="Template not found", error="No template with this uuid", status_code=404)
        config_data = request.model_dump(exclude_unset=True)
        logger.info(f"[store-template-configuration] Step 4: Prepared config_data keys={list(config_data.keys())}")
        config_data.pop('template_uuid', None)
        logger.info(f"[store-template-configuration] Step 5: Calling helper to upsert config")
        new_config = await store_template_configuration_for_store(store_id, template_id, config_data)
        if not new_config:
            logger.error(f"[store-template-configuration] Step 6: Failed to save template configuration for store_id={store_id}, template_id={template_id}")
            return format_error_response(
                message="Failed to save template configuration",
                error="Database insert/update failed",
                status_code=500
            )
        logger.info(f"[store-template-configuration] Step 7: Upserted config for store_id={store_id}, template_id={template_id}, config_id={new_config.get('id')}")
        return format_success_response(
            message="Template configuration upserted successfully",
            data=StoreTemplateConfigurationResponse(success=True, config=new_config).model_dump()
        )
    except Exception as e:
        logger.error(f"[store-template-configuration] Step 8: Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to upsert template configuration",
            error=str(e)
        )

# 4. /run-template - run a template by calling the deployed endpoint for a template
@router.post("/run-template")
async def run_template(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Run a template by calling the deployed endpoint for a template.
    Input: template_uuid (str), configurations (dict with required output_structure)
    """
    try:
        logger.info("[run-template] Starting template run endpoint")
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        # Store the staring time as created_at timestamp
        created_at_timestamp = datetime.utcnow().isoformat()
        logger.info(f"[run-template] Created at: {created_at_timestamp}")
        body = await request.json()
        template_uuid = body.get("template_uuid")
        configurations = body.get("configurations")
        store_name = body.get("store_name") or None # Optional store name

        # --- Access Token Decoding Logic ---
        if configurations and isinstance(configurations, dict) and "access_token" in configurations:
            try:
                base64_token = configurations["access_token"]
                decoded_bytes = base64.b64decode(base64_token)
                decoded_str = decoded_bytes.decode("utf-8")
                modified_token = decoded_str[:-3]  # Remove last 3 characters
                configurations["access_token"] = modified_token
            except Exception as decode_err:
                logger.error(f"[run-template] Failed to decode access_token: {decode_err}")

        # Validate required fields
        if not template_uuid:
            logger.error(f"[run-template] Missing template_uuid in request body.")
            return format_error_response(message="template_uuid is required in the request body", error="Missing template_uuid", status_code=400)
        if not configurations:
            logger.error(f"[run-template] Missing configurations in request body.")
            return format_error_response(message="configurations is required in the request body", error="Missing configurations", status_code=400)
        if not isinstance(configurations, dict):
            logger.error(f"[run-template] configurations must be a dict.")
            return format_error_response(message="configurations must be a dict", error="Invalid configurations type", status_code=400)
        if "output_structure" not in configurations:
            logger.error(f"[run-template] output_structure is required in configurations.")
            return format_error_response(message="output_structure is required in configurations", error="Missing output_structure", status_code=400)
        # 1. Get store_id from token
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)
        logger.info(f"[run-template] Received request for template_uuid={template_uuid}, store_id={store_id}")
        # 2. Get template_id from uuid
        template_id = await get_sw_template_id_by_uuid(template_uuid)
        if not template_id:
            logger.error(f"[run-template] Template not found for uuid={template_uuid}")
            return format_error_response(message="Template not found", error="No template with this uuid", status_code=404)
        # 3. Get deployment url/endpoint and function_name
        deployment = await get_sw_template_deployment_by_template_id(template_id)
        if not deployment or not deployment.get("url"):
            logger.error(f"[run-template] No url found for template_id={template_id}. Deployment: {deployment}")
            return format_error_response(message="No url found for template", error="Missing url", status_code=404)
        url = deployment["url"]
        function_app_name = deployment.get("function_app_name")
        # 4. Get api_key from Azure Function App details by function_app_name
        azure_details = await get_azure_function_app_details_by_function_app_name(function_app_name)
        if not azure_details or not azure_details.get("api_key"):
            logger.error(f"[run-template] No api_key found for function_app_name={function_app_name}. Azure details: {azure_details}")
            return format_error_response(message="No api_key found for function_name", error="Missing api_key", status_code=404)
        api_token = azure_details["api_key"]
        # 5. Insert run job record as 'running' before making the request
        job_id = str(uuid4())
        job_status = "running"
        error_messages = None
        output = None
        job_duration = None
        job_title = None
        start_time = time.time()
        run_job_record = {
            "uuid": job_id,
            "input_data": configurations,
            "output_data": None,
            "status": job_status,
            "job_duration": None,
            "job_title": None,
            "errors": None,
            "template_id": template_id,
            "store_id": store_id,
            "started_at": datetime.utcnow().isoformat(),
            "finished_at": None,
        }
        job_row = await create_sw_template_run_job(run_job_record)
        job_db_id = job_row.get("id") if job_row else None

        # Send Slack notification for job created
        try:
            await send_slack_webhook(
                operation_type="run_template_job",
                job_status="job_created",
                job_details={
                    "store_id": store_id,
                    "job_id": job_id,
                    "template_uuid": template_uuid,
                    "status": job_status,
                    "job_title": job_title,
                    "started_at": run_job_record["started_at"],
                    "job_duration": job_duration,
                    "error_message": error_messages,
                    "input_data": configurations
                }
            )
        except Exception as slack_err:
            logger.error(f"[Slack] Failed to send job_created notification: {slack_err}")

        try:
            full_url = url.rstrip("/")
            headers = {"x-functions-key": api_token, "Content-Type": "application/json"}
            logger.info(f"[run-template] Hitting endpoint: {full_url}")
            logger.debug(f"[run-template] Headers: {headers}")
            logger.debug(f"[run-template] Configurations: {configurations}")

            # Use async httpx instead of synchronous requests
            async with httpx.AsyncClient(timeout=300.0) as client:  # 5 minute timeout
                response = await client.post(full_url, headers=headers, json=configurations)
                logger.info(f"[run-template] Response status code: {response.status_code}")
                logger.debug(f"[run-template] Raw response text: {response.text}")
                response.raise_for_status()
                output = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                job_status = "completed"
                if isinstance(output, dict):
                    job_title = output.get('title')
                error_messages = None
        except Exception as e:
            response_obj = getattr(e, 'response', None)
            error_msg = f"[run-template] Failed to call template endpoint {full_url}. Error: {str(e)}."
            if response_obj is not None:
                try:
                    response_content = response_obj.json()
                    logger.debug(f"[run-template] Error response JSON: {json.dumps(response_content, indent=2)}")
                except Exception:
                    response_content = response_obj.text
                    logger.debug(f"[run-template] Error response text: {response_content}")
                error_msg += f" Response status code: {response_obj.status_code}. Response content: {response_content}"
            else:
                response_content = str(e)
                logger.debug(f"[run-template] No response object. Exception: {response_content}")
            logger.error(error_msg)
            error_messages = str(e)
            output = response_content
            job_status = "failed"
        job_duration = time.time() - start_time
        finished_at = datetime.utcnow().isoformat()
        # --- FORMAT THE RESPONSE (clean/parse result) BEFORE saving to DB ---
        if job_status == "completed":
            logger.info(f"[run-template] Output: {output}")
            result_data = output if isinstance(output, dict) else {"result": output}

            # --- FLATTEN NESTED 'result' FIELD IF NEEDED ---
            # If result_data['result'] is a dict and contains another 'result', flatten it
            if (
                isinstance(result_data, dict)
                and "result" in result_data
                and isinstance(result_data["result"], dict)
                and "result" in result_data["result"]
            ):
                inner = result_data["result"]
                # Remove 'status' if present
                inner = {k: v for k, v in inner.items() if k != "status"}
                # Replace outer 'result' with the flattened dict
                result_data["result"] = inner

            # --- SANITIZE result if needed ---
            if isinstance(result_data.get("result"), str):
                result_str = result_data["result"].strip()
                # Remove 'Output:' prefix (case-insensitive)
                if result_str.lower().startswith("output:"):
                    result_str = result_str[len("Output:"):].strip()
                # Remove code block markers (``` and language tags)
                if result_str.startswith("```"):
                    result_str = result_str[3:]
                    if result_str.lower().startswith("json\n"):
                        result_str = result_str[5:]
                    elif result_str.lower().startswith("python\n"):
                        result_str = result_str[7:]
                if result_str.endswith("```"):
                    result_str = result_str[:-3]
                result_str = result_str.strip()
                # Try to parse as JSON, then as Python literal
                try:
                    parsed_result = json.loads(result_str)
                except Exception:
                    try:
                        parsed_result = ast.literal_eval(result_str)
                    except Exception:
                        parsed_result = result_str  # fallback to string if not valid JSON
                result_data["result"] = parsed_result

            # Add metadata and identifiers
            result_data["template_uuid"] = template_uuid
            result_data["job_id"] = job_id
            result_data["configurations"] = configurations
            # Add static data (flag values for frontend)
            result_data["status"] = None
            # form job_metadata
            result_data["job_metadata"] = {
                "job_id": job_id,
                "job_status": job_status,
                "job_duration": job_duration,
                "job_title": job_title,
                "created_at": created_at_timestamp
            }
            if store_name:
                result_data["store_name"] = store_name
        else:
            result_data = output if isinstance(output, dict) else {"result": output}
        
        # --- SAVE THE FORMATTED OUTPUT TO DB ---
        update_data = {
            "output_data": result_data,
            "status": job_status,
            "errors": error_messages,
            "job_duration": job_duration,
            "job_title": job_title,
            "finished_at": finished_at
        }
        if job_db_id:
            await update_sw_template_run_job(job_db_id, update_data)
        
        # Send Slack notification for job completion (success or failure)
        try:
            await send_slack_webhook(
                operation_type="run_template_job",
                job_status="job_success" if job_status == "completed" else "job_failed",
                job_details={
                    "store_id": store_id,
                    "store_name": store_name,
                    "job_id": job_id,
                    "template_uuid": template_uuid,
                    "status": job_status,
                    "job_title": job_title,
                    "started_at": created_at_timestamp,
                    "finished_at": finished_at,
                    "job_duration": job_duration,
                    "error_message": error_messages,
                    "output_data": result_data
                }
            )
        except Exception as slack_err:
            logger.error(f"[Slack] Failed to send job completion notification: {slack_err}")

        # --- RETURN THE FORMATTED RESPONSE ---
        if job_status == "completed":
            return format_success_response(
                message="Template run completed successfully",
                data=result_data
            )
        else:
            logger.error(f"[run-template] job_id={job_id}, template_uuid={template_uuid}")
            return format_error_response(
                message="Failed to call template endpoint",
                error=error_messages
            )
    except Exception as e:
        logger.error(f"[run-template] Failed to run template. Error: {str(e)}", exc_info=True)
        return format_error_response(message="Failed to run template", error=str(e))

# Background task function for async template execution with callback
async def execute_template_background_with_callback(
    job_id: str,
    job_db_id: int,
    template_uuid: str,
    template_id: int,
    store_id: int,
    url: str,
    api_token: str,
    configurations: dict,
    created_at_timestamp: str,
    callback_url: str = None,
    store_name: str = None
):
    """
    Background task to execute template without blocking the main thread.
    Updates the job record in the database with results and sends callback if provided.

    Workflow:
    1. Update DB with 'processing' status
    2. Call Azure function endpoint
    3. Update DB with final results
    4. Send callback with final response (if callback_url provided)
    """
    start_time = time.time()
    job_status = "processing"
    error_messages = None
    output = None
    job_title = None
    callback_response_sent_status = None  # Track callback status for slack
    result_data = None  # Always define for all exit paths
    try:
        # Step 1: Update DB with 'processing' status
        logger.info(f"[background-template] Starting processing for job_id={job_id}")
        processing_update = {
            "status": "processing",
            "started_at": datetime.utcnow().isoformat()
        }
        await update_sw_template_run_job(job_db_id, processing_update)
        logger.info(f"[background-template] Updated job status to 'processing' for job_id={job_id}")

        # Step 2: Call Azure function endpoint
        full_url = url.rstrip("/")
        headers = {"x-functions-key": api_token, "Content-Type": "application/json"}
        logger.info(f"[background-template] Calling Azure endpoint: {full_url} for job_id={job_id}")

        try:
            async with httpx.AsyncClient(timeout=600.0) as client:  # 10 minute timeout
                response = await client.post(full_url, headers=headers, json=configurations)
                logger.info(f"[background-template] Azure response status: {response.status_code} for job_id={job_id}")
                try:
                    response.raise_for_status()
                except Exception as http_exc:
                    # Try to get error content from Azure
                    error_content = None
                    try:
                        error_content = response.json()
                    except Exception:
                        error_content = response.text
                    logger.error(f"[background-template] Azure function HTTP error for job_id={job_id}: {http_exc}, content: {error_content}")
                    job_status = "failed"
                    error_messages = error_content if error_content else str(http_exc)
                    job_title = None
                    job_duration = time.time() - start_time
                    finished_at = datetime.utcnow().isoformat()
                    result_data = {
                        "result": None,
                        "template_uuid": template_uuid,
                        "configurations": configurations,
                        "job_metadata": {
                            "job_id": job_id,
                            "job_status": job_status,
                            "job_duration": job_duration,
                            "job_title": job_title,
                            "created_at": created_at_timestamp,
                            "finished_at": finished_at
                        },
                        "store_name": store_name,
                        "error": error_messages
                    }
                    if callback_url:
                        callback_response_sent_status = await send_callback_response(callback_url, job_id, job_status, result_data, error_messages, store_name)
                    update_data = {
                        "output_data": result_data,
                        "status": job_status,
                        "errors": error_messages,
                        "job_duration": job_duration,
                        "job_title": job_title,
                        "finished_at": finished_at
                    }
                    if job_db_id:
                        await update_sw_template_run_job(job_db_id, update_data)
                    await send_slack_webhook(
                        operation_type="run_template_job",
                        job_status="job_failed",
                        job_details={
                            "store_id": store_id,
                            "store_name": store_name,
                            "callback_response_sent_status": callback_response_sent_status,
                            "job_id": job_id,
                            "template_uuid": template_uuid,
                            "status": job_status,
                            "job_title": job_title,
                            "started_at": created_at_timestamp,
                            "finished_at": finished_at,
                            "job_duration": job_duration,
                            "error_message": error_messages,
                            "output_data": error_content
                        }
                    )
                    return  # Stop further processing
                # Now parse JSON (if not already handled above)
                try:
                    output = response.json()
                except Exception as json_err:
                    logger.error(f"[background-template] Azure function did not return valid JSON for job_id={job_id}: {json_err}")
                    job_status = "failed"
                    error_messages = f"Azure function did not return valid JSON: {json_err} | Raw: {response.text}"
                    output = None
                    job_title = None
                    job_duration = time.time() - start_time
                    finished_at = datetime.utcnow().isoformat()
                    result_data = {
                        "result": None,
                        "template_uuid": template_uuid,
                        "configurations": configurations,
                        "job_metadata": {
                            "job_id": job_id,
                            "job_status": job_status,
                            "job_duration": job_duration,
                            "job_title": job_title,
                            "created_at": created_at_timestamp,
                            "finished_at": finished_at
                        },
                        "store_name": store_name,
                        "error": error_messages
                    }
                    if callback_url:
                        callback_response_sent_status = await send_callback_response(callback_url, job_id, job_status, result_data, error_messages, store_name)
                    update_data = {
                        "output_data": result_data,
                        "status": job_status,
                        "errors": error_messages,
                        "job_duration": job_duration,
                        "job_title": job_title,
                        "finished_at": finished_at
                    }
                    if job_db_id:
                        await update_sw_template_run_job(job_db_id, update_data)
                    await send_slack_webhook(
                        operation_type="run_template_job",
                        job_status="job_failed",
                        job_details={
                            "store_id": store_id,
                            "store_name": store_name,
                            "callback_response_sent_status": callback_response_sent_status,
                            "job_id": job_id,
                            "template_uuid": template_uuid,
                            "status": job_status,
                            "job_title": job_title,
                            "started_at": created_at_timestamp,
                            "finished_at": finished_at,
                            "job_duration": job_duration,
                            "error_message": error_messages,
                            "output_data": response.text
                        }
                    )
                    return  # Stop further processing
        except TimeoutException as timeout_exc:
            logger.error(f"[background-template] Azure function timed out for job_id={job_id}. Error: {timeout_exc}")
            job_status = "failed"
            error_messages = f"Azure function timed out after 10 minutes: {timeout_exc}"
            job_title = None
            job_duration = time.time() - start_time
            finished_at = datetime.utcnow().isoformat()
            result_data = {
                "result": None,
                "template_uuid": template_uuid,
                "configurations": configurations,
                "job_metadata": {
                    "job_id": job_id,
                    "job_status": job_status,
                    "job_duration": job_duration,
                    "job_title": job_title,
                    "created_at": created_at_timestamp,
                    "finished_at": finished_at
                },
                "store_name": store_name,
                "error": error_messages
            }
            if callback_url:
                callback_response_sent_status = await send_callback_response(callback_url, job_id, job_status, result_data, error_messages, store_name)
            update_data = {
                "output_data": result_data,
                "status": job_status,
                "errors": error_messages,
                "job_duration": job_duration,
                "job_title": job_title,
                "finished_at": finished_at
            }
            if job_db_id:
                await update_sw_template_run_job(job_db_id, update_data)
            await send_slack_webhook(
                operation_type="run_template_job",
                job_status="job_failed",
                job_details={
                    "store_id": store_id,
                    "store_name": store_name,
                    "callback_response_sent_status": callback_response_sent_status,
                    "job_id": job_id,
                    "template_uuid": template_uuid,
                    "status": job_status,
                    "job_title": job_title,
                    "started_at": created_at_timestamp,
                    "finished_at": finished_at,
                    "job_duration": job_duration,
                    "error_message": error_messages,
                    "output_data": None
                }
            )
            return
        except Exception as e:
            # Handle HTTP errors and other exceptions
            logger.error(f"[background-template] Azure function failed for job_id={job_id}. Error: {str(e)}")
            job_status = "failed"
            error_messages = str(e)
            job_title = None
            job_duration = time.time() - start_time
            finished_at = datetime.utcnow().isoformat()
            result_data = {
                "result": None,
                "template_uuid": template_uuid,
                "configurations": configurations,
                "job_metadata": {
                    "job_id": job_id,
                    "job_status": job_status,
                    "job_duration": job_duration,
                    "job_title": job_title,
                    "created_at": created_at_timestamp,
                    "finished_at": finished_at
                },
                "store_name": store_name,
                "error": error_messages
            }
            if callback_url:
                callback_response_sent_status = await send_callback_response(callback_url, job_id, job_status, result_data, error_messages, store_name)
            update_data = {
                "output_data": result_data,
                "status": job_status,
                "errors": error_messages,
                "job_duration": job_duration,
                "job_title": job_title,
                "finished_at": finished_at
            }
            if job_db_id:
                await update_sw_template_run_job(job_db_id, update_data)
            await send_slack_webhook(
                operation_type="run_template_job",
                job_status="job_failed",
                job_details={
                    "store_id": store_id,
                    "store_name": store_name,
                    "callback_response_sent_status": callback_response_sent_status,
                    "job_id": job_id,
                    "template_uuid": template_uuid,
                    "status": job_status,
                    "job_title": job_title,
                    "started_at": created_at_timestamp,
                    "finished_at": finished_at,
                    "job_duration": job_duration,
                    "error_message": error_messages,
                    "output_data": None
                }
            )
            return

        # --- Check for error in Azure function response (even if status 200) ---
        # Common error keys: 'error', 'status' == 'error' or 'failed', etc.
        error_detected = False
        error_message_from_azure = None
        if isinstance(output, dict):
            if output.get('error'):
                error_detected = True
                error_message_from_azure = output.get('error')
            elif output.get('status', '').lower() in ('error', 'failed'):
                error_detected = True
                error_message_from_azure = output.get('message') or output.get('error') or str(output)
        if error_detected:
            job_status = "failed"
            error_messages = error_message_from_azure or "Azure function returned error"
            job_title = None
            job_duration = time.time() - start_time
            finished_at = datetime.utcnow().isoformat()
            result_data = {
                "result": None,
                "template_uuid": template_uuid,
                "configurations": configurations,
                "job_metadata": {
                    "job_id": job_id,
                    "job_status": job_status,
                    "job_duration": job_duration,
                    "job_title": job_title,
                    "created_at": created_at_timestamp,
                    "finished_at": finished_at
                },
                "store_name": store_name,
                "error": output  # Return the full error object/stack from Azure
            }
            if callback_url:
                callback_response_sent_status = await send_callback_response(callback_url, job_id, job_status, result_data, error_messages, store_name)
            update_data = {
                "output_data": result_data,
                "status": job_status,
                "errors": error_messages,
                "job_duration": job_duration,
                "job_title": job_title,
                "finished_at": finished_at
            }
            if job_db_id:
                await update_sw_template_run_job(job_db_id, update_data)
            await send_slack_webhook(
                operation_type="run_template_job",
                job_status="job_failed",
                job_details={
                    "store_id": store_id,
                    "store_name": store_name,
                    "callback_response_sent_status": callback_response_sent_status,
                    "job_id": job_id,
                    "template_uuid": template_uuid,
                    "status": job_status,
                    "job_title": job_title,
                    "started_at": created_at_timestamp,
                    "finished_at": finished_at,
                    "job_duration": job_duration,
                    "error_message": error_messages,
                    "output_data": output
                }
            )
            return

        # --- Success path ---
        job_status = "completed"
        error_messages = None
        # logger.info(f"@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@[background-template] Azure response: {output}")
        # --- FLATTEN AND WRAP RESULT ---
        # 1. Flatten nested 'result' if needed
        result_obj = output
        if (
            isinstance(result_obj, dict)
            and "result" in result_obj
            and isinstance(result_obj["result"], dict)
            and "result" in result_obj["result"]
        ):
            # Remove 'status' if present
            inner = {k: v for k, v in result_obj["result"].items() if k != "status"}
            result_obj["result"] = inner
        # 2. Remove 'status' at top level if present
        if "status" in result_obj:
            result_obj.pop("status")
        # 3. Always wrap result in a list
        result_val = result_obj.get("result")
        if result_val is not None:
            if isinstance(result_val, list):
                wrapped_result = result_val
            else:
                wrapped_result = [result_val]
        else:
            wrapped_result = []
        # 4. Extract job_title
        job_title = None
        if wrapped_result and isinstance(wrapped_result[0], dict):
            job_title = wrapped_result[0].get("total_run_job_metadata", {}).get("title")
        # 5. Build result_data
        result_data = {
            "result": wrapped_result,
            "template_uuid": template_uuid,
            "configurations": configurations,
            "job_metadata": {
                "job_id": job_id,
                "job_status": job_status,
                "job_duration": time.time() - start_time,
                "job_title": job_title,
                "created_at": created_at_timestamp,
                "finished_at": datetime.utcnow().isoformat()
            },
            "store_name": store_name
        }
        update_data = {
            "output_data": result_data,
            "status": job_status,
            "errors": error_messages,
            "job_duration": result_data["job_metadata"]["job_duration"],
            "job_title": job_title,
            "finished_at": result_data["job_metadata"]["finished_at"]
        }
        if job_db_id:
            await update_sw_template_run_job(job_db_id, update_data)
        if callback_url:
            callback_response_sent_status = await send_callback_response(callback_url, job_id, job_status, result_data, error_messages, store_name)
        await send_slack_webhook(
            operation_type="run_template_job",
            job_status="job_success",
            job_details={
                "store_id": store_id,
                "store_name": store_name,
                "callback_response_sent_status": callback_response_sent_status,
                "job_id": job_id,
                "template_uuid": template_uuid,
                "status": job_status,
                "job_title": job_title,
                "started_at": created_at_timestamp,
                "finished_at": result_data["job_metadata"]["finished_at"],
                "job_duration": result_data["job_metadata"]["job_duration"],
                "error_message": error_messages,
                # "output_data": result_data
            }
        )
    except Exception as e:
        # Catch any unexpected error in the whole function
        logger.error(f"[background-template] Unexpected error for job_id={job_id}. Error: {str(e)}")
        job_status = "failed"
        error_messages = str(e)
        job_title = None
        job_duration = time.time() - start_time
        finished_at = datetime.utcnow().isoformat()
        result_data = {
            "result": None,
            "template_uuid": template_uuid,
            "configurations": configurations,
            "job_metadata": {
                "job_id": job_id,
                "job_status": job_status,
                "job_duration": job_duration,
                "job_title": job_title,
                "created_at": created_at_timestamp,
                "finished_at": finished_at
            },
            "store_name": store_name,
            "error": error_messages
        }
        if callback_url:
            callback_response_sent_status = await send_callback_response(callback_url, job_id, job_status, result_data, error_messages, store_name)
        update_data = {
            "output_data": result_data,
            "status": job_status,
            "errors": error_messages,
            "job_duration": job_duration,
            "job_title": job_title,
            "finished_at": finished_at
        }
        if job_db_id:
            await update_sw_template_run_job(job_db_id, update_data)
        await send_slack_webhook(
            operation_type="run_template_job",
            job_status="job_failed",
            job_details={
                "store_id": store_id,
                "store_name": store_name,
                "callback_response_sent_status": callback_response_sent_status,
                "job_id": job_id,
                "template_uuid": template_uuid,
                "status": job_status,
                "job_title": job_title,
                "started_at": created_at_timestamp,
                "finished_at": finished_at,
                "job_duration": job_duration,
                "error_message": error_messages,
                "output_data": None
            }
        )

async def send_callback_response(callback_url: str, job_id: str, job_status: str, result_data: dict, error_messages: str = None, store_name: str = None):
    """
    Send the final response to the callback URL using response formatters.
    If job is successful, send only the data part. If failed, send only the error part.
    No timestamp in the payload.
    Also send a Slack notification after the callback, including callback_response_sent_status and response code.
    """
    callback_response_sent_status = "unknown"
    try:
        logger.info(f"[callback] Sending callback for job_id={job_id} to {callback_url}")

        # Use response formatters
        if job_status == "completed":
            formatted_response = format_success_response(message="Template execution completed successfully", data=result_data)
        else:
            formatted_response = format_error_response(message="Template execution failed", error=error_messages)

        # Extract the actual dict from the JSONResponse
        if hasattr(formatted_response, 'body'):
            import json as _json
            payload = _json.loads(formatted_response.body.decode())
        elif hasattr(formatted_response, 'content'):
            payload = formatted_response.content
        else:
            payload = formatted_response

        # Send callback with timeout
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    callback_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                if response.status_code == 200:
                    logger.info(f"[callback] Successfully sent callback for job_id={job_id}")
                    callback_response_sent_status = "success"
                    return callback_response_sent_status
                else:
                    logger.warning(f"[callback] Callback returned status {response.status_code} for job_id={job_id}")
                    callback_response_sent_status = f"failure - ({response.status_code})"
                    return callback_response_sent_status
        except Exception as cb_exc:
            logger.error(f"[callback] Failed to send callback for job_id={job_id} to {callback_url}. Error: {str(cb_exc)}")
            callback_response_sent_status = f"failed - exception: {str(cb_exc)}"
            return callback_response_sent_status
    except Exception as e:
        logger.error(f"[callback] Unexpected error in send_callback_response for job_id={job_id}: {str(e)}")
        callback_response_sent_status = f"exception: {str(e)}"
        return callback_response_sent_status

# New non-blocking endpoint with callback support
@router.post("/run-template-async")
async def run_template_async(
    request: Request,
    background_tasks: BackgroundTasks,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Run a template asynchronously without blocking the server with optional callback support.

    Workflow:
    1. Receive request (with optional callback_url)
    2. Update DB with job 'running' status
    3. Return immediate response with job_id and job_status
    4. Call Azure function endpoint in background
    5. Update DB with final results and send callback (if provided)

    Input:
    - template_uuid (str): UUID of the template to run
    - configurations (dict): Template configurations with required output_structure
    - callback_url (str, optional): URL to receive final results
    """
    try:
        logger.info("[run-template-async] Starting async template run endpoint")
        created_at_timestamp = datetime.utcnow().isoformat()

        body = await request.json()
        template_uuid = body.get("template_uuid")
        configurations = body.get("configurations")
        callback_url = body.get("callback_url")  # Optional callback URL
        store_name = body.get("store_name") 

        if not store_name:
            return format_error_response(message="store_name is required", error="Missing store_name", status_code=400)

        # --- Access Token Decoding Logic ---
        if configurations and isinstance(configurations, dict) and "access_token" in configurations:
            try:
                # logger.info(f"[run-template-async] Decoding access_token: {configurations['access_token']}")
                base64_token = configurations["access_token"]
                decoded_bytes = base64.b64decode(base64_token)
                decoded_str = decoded_bytes.decode("utf-8")
                modified_token = decoded_str[:-3]  # Remove last 3 characters
                configurations["access_token"] = modified_token
                # logger.info(f"[run-template-async] Decoded access_token: {modified_token}")
            except Exception as decode_err:
                logger.error(f"[run-template-async] Failed to decode access_token: {decode_err}")

        # Validate required fields
        if not template_uuid:
            return format_error_response(message="template_uuid is required", error="Missing template_uuid", status_code=400)
        if not configurations:
            return format_error_response(message="configurations is required", error="Missing configurations", status_code=400)
        if not configurations.get("output_structure"):
            return format_error_response(message="output_structure is required in configurations", error="Missing output_structure", status_code=400)

        # Validate callback_url format if provided
        if callback_url and not callback_url.startswith(('http://', 'https://')):
            return format_error_response(message="Invalid callback_url format", error="callback_url must start with http:// or https://", status_code=400)

        # Get store_id from token
        store_id, app_id, request_id = await validate_auth_data(auth_data, request)

        logger.info(f"[run-template-async] Received request for template_uuid={template_uuid}, store_id={store_id}")

        # Get template_id from uuid
        template_id = await get_sw_template_id_by_uuid(template_uuid)
        if not template_id:
            return format_error_response(message="Template not found", error="No template with this uuid", status_code=404)

        # Get deployment url/endpoint and function_name
        deployment = await get_sw_template_deployment_by_template_id(template_id)
        if not deployment or not deployment.get("url"):
            return format_error_response(message="No url found for template", error="Missing url", status_code=404)

        url = deployment["url"]
        function_app_name = deployment.get("function_app_name")

        # Get api_key from Azure Function App details
        azure_details = await get_azure_function_app_details_by_function_app_name(function_app_name)
        if not azure_details or not azure_details.get("api_key"):
            return format_error_response(message="No api_key found for function_name", error="Missing api_key", status_code=404)

        api_token = azure_details["api_key"]

        # Step 2: Create job record as 'running' in database
        job_id = str(uuid4())
        run_job_record = {
            "uuid": job_id,
            "input_data": {
                **configurations,
                "callback_url": callback_url  # Store callback_url in input_data for reference
            },
            "output_data": None,
            "status": "running",
            "job_duration": None,
            "job_title": None,
            "errors": None,
            "template_id": template_id,
            "store_id": store_id,
            "started_at": datetime.utcnow().isoformat(),
            "finished_at": None,
        }

        job_row = await create_sw_template_run_job(run_job_record)
        job_db_id = job_row.get("id") if job_row else None

        # Send Slack notification for job created (async)
        try:
            await send_slack_webhook(
                operation_type="run_template_job",
                job_status="job_created",
                job_details={
                    "store_id": store_id,
                    "store_name": store_name,
                    "job_id": job_id,
                    "template_uuid": template_uuid,
                    "status": "running",
                    "job_title": None,
                    "started_at": run_job_record["started_at"],
                    "job_duration": None,
                    "error_message": None,
                    # "input_data": configurations
                }
            )
        except Exception as slack_err:
            logger.error(f"[Slack] Failed to send job_created notification (async): {slack_err}")

        logger.info(f"[run-template-async] Created job record with job_id={job_id}, db_id={job_db_id}")

        # Step 3: Return immediate response with job details (before starting background task)
        response_data = {
            "job_id": job_id,
            "job_status": "running",
            "store_name": store_name,
            "template_uuid": template_uuid,
            "created_at": created_at_timestamp,
            "callback_url": callback_url if callback_url else None,
            "status_check_endpoint": f"/job-status/{job_id}"
        }

        # Step 4: Add background task for async execution (efficient order: respond first, then process)
        background_tasks.add_task(
            execute_template_background_with_callback,
            job_id=job_id,
            job_db_id=job_db_id,
            template_uuid=template_uuid,
            template_id=template_id,
            store_id=store_id,
            url=url,
            api_token=api_token,
            configurations=configurations,
            created_at_timestamp=created_at_timestamp,
            callback_url=callback_url,
            store_name=store_name
        )

        logger.info(f"[run-template-async] Started background task for job_id={job_id} with callback_url={callback_url}")

        return format_success_response(
            message="Template execution started successfully",
            data=response_data
        )

    except Exception as e:
        logger.error(f"[run-template-async] Failed to start template execution. Error: {str(e)}", exc_info=True)
        return format_error_response(message="Failed to start template execution", error=str(e))

# Job status endpoint
@router.get("/job-status/{job_id}")
async def get_job_status(
    job_id: str = Path(..., description="Job ID to check status"),
    auth_data: dict = Depends(verify_tokens)
):
    """
    Get the status and results of a template execution job.
    """
    try:
        logger.info(f"[get-job-status] Checking status for job_id={job_id}")

        # Get store_id from token for security
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        # Get job details from database
        jobs = await get_sw_template_run_jobs_for_store(store_id)
        job = next((j for j in jobs if j.get('uuid') == job_id), None)

        if not job:
            return format_error_response(
                message="Job not found",
                error="No job found with this ID for your store",
                status_code=404
            )

        # Format job response
        job_response = {
            "job_id": job.get("uuid"),
            "job_status": job.get("status"),
            "job_duration": job.get("job_duration"),
            "job_title": job.get("job_title"),
            "created_at": job.get("created_at"),
            "started_at": job.get("started_at"),
            "finished_at": job.get("finished_at"),
            "input_data": job.get("input_data"),
            "output_data": job.get("output_data"),
            "errors": job.get("errors"),
            "template_id": job.get("template_id")
        }

        return format_success_response(
            message="Job status retrieved successfully",
            data=job_response
        )

    except Exception as e:
        logger.error(f"[get-job-status] Error retrieving job status for job_id={job_id}. Error: {str(e)}", exc_info=True)
        return format_error_response(message="Failed to retrieve job status", error=str(e))

"""
=== ASYNC TEMPLATE EXECUTION WITH CALLBACK DOCUMENTATION ===

The /run-template-async endpoint provides non-blocking template execution with optional callback support.

WORKFLOW:
1. Client sends request with template_uuid, configurations, and optional callback_url
2. Server creates job record in database with 'running' status
3. Server returns immediate response with job_id and job_status
4. Background task calls Azure function endpoint
5. Background task updates database with final results
6. If callback_url provided, server sends final response to callback URL

REQUEST PAYLOAD:
{
    "template_uuid": "uuid-string",
    "configurations": {
        "output_structure": "json",  // Required
        "other_config": "value"
    },
    "callback_url": "https://your-domain.com/webhook"  // Optional
}

IMMEDIATE RESPONSE:
{
    "status": "success",
    "message": "Template execution started successfully",
    "data": {
        "job_id": "job-uuid",
        "job_status": "running",
        "template_uuid": "template-uuid",
        "created_at": "2024-01-01T12:00:00Z",
        "callback_url": "https://your-domain.com/webhook",
        "status_check_endpoint": "/job-status/job-uuid",
        "message": "Template execution started successfully. Processing in background."
    }
}

CALLBACK PAYLOAD (sent to callback_url when processing completes):
{
    "job_id": "job-uuid",
    "job_status": "completed" | "failed",
    "data": {
        // Template results (if successful)
        "result": {...},
        "template_uuid": "template-uuid",
        "job_metadata": {...}
    },
    "error": "error message" // Only if failed
}

JOB STATUS TRACKING:
- Use GET /job-status/{job_id} to check progress
- Job statuses: "running" -> "processing" -> "completed" | "failed"
- Database is updated at each step for persistence

BENEFITS:
- Non-blocking: Server remains responsive to other requests
- Immediate response: Client gets job_id instantly
- Callback support: Automated notification when complete
- Status tracking: Real-time progress monitoring
- Error handling: Comprehensive error capture and reporting
- Persistence: All steps logged in database

EXAMPLE USAGE:
```bash
# Start async execution
curl -X POST "/api/v1/storeworker/templates/run-template-async" \
  -H "Authorization: Bearer token" \
  -H "Content-Type: application/json" \
  -d '{
    "template_uuid": "abc-123",
    "configurations": {"output_structure": "json"},
    "callback_url": "https://myapp.com/webhook"
  }'

# Check status anytime
curl -X GET "/api/v1/storeworker/templates/job-status/job-uuid" \
  -H "Authorization: Bearer token"
```
"""

# Webhook testing endpoint for developers
@router.post("/test-callback")
async def test_callback_endpoint(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Test endpoint to verify callback URL functionality.
    Sends a test payload to the provided callback URL.
    """
    try:
        logger.info("[test-callback] Starting callback test endpoint")

        body = await request.json()
        callback_url = body.get("callback_url")

        if not callback_url:
            return format_error_response(message="callback_url is required", error="Missing callback_url", status_code=400)

        if not callback_url.startswith(('http://', 'https://')):
            return format_error_response(message="Invalid callback_url format", error="callback_url must start with http:// or https://", status_code=400)

        # Get store_id for security
        store_id, _, _ = await validate_auth_data(auth_data)

        # Create test payload
        test_payload = {
            "job_id": "test-job-" + str(uuid4())[:8],
            "job_status": "completed",
            "data": {
                "result": "This is a test callback from StoreWorker API",
                "template_uuid": "test-template-uuid",
                "job_metadata": {
                    "job_id": "test-job-id",
                    "job_status": "completed",
                    "job_duration": 5.2,
                    "job_title": "Test Template Execution",
                    "created_at": datetime.utcnow().isoformat()
                }
            },
            "error": None,
            "test": True,
            "store_id": store_id
        }

        # Send test callback
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    callback_url,
                    json=test_payload,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"[test-callback] Test callback sent to {callback_url}, status: {response.status_code}")

                return format_success_response(
                    message="Test callback sent successfully",
                    data={
                        "callback_url": callback_url,
                        "response_status": response.status_code,
                        "response_text": response.text[:500],  # Limit response text
                        "test_payload": test_payload
                    }
                )

        except Exception as callback_error:
            logger.error(f"[test-callback] Failed to send test callback to {callback_url}. Error: {str(callback_error)}")
            return format_error_response(
                message="Failed to send test callback",
                error=f"Callback URL unreachable: {str(callback_error)}"
            )

    except Exception as e:
        logger.error(f"[test-callback] Error in test callback endpoint. Error: {str(e)}", exc_info=True)
        return format_error_response(message="Failed to test callback", error=str(e))

@router.get("/analytics", response_model=TemplateAnalyticsResponse)
async def get_templates_analytics(auth_data: dict = Depends(verify_tokens)):
    """
    Get analytics for templates for the current store.
    """
    try:
        logger.info("[get_templates_analytics] Starting analytics endpoint")
        store_id, app_id, request_id = await validate_auth_data(auth_data)
        logger.info(f"[get_templates_analytics] Authenticated store_id={store_id}, app_id={app_id}, request_id={request_id}")
        # Fetch all templates (all fields) visible to this store
        templates = await get_sw_templates_full_for_store(store_id)
        template_id_map = {tpl['id']: tpl for tpl in templates if 'id' in tpl}
        template_ids = set(template_id_map.keys())
        # Fetch all run jobs for this store
        jobs = await get_sw_template_run_jobs_for_store(store_id)
        # Filter jobs to only those for visible templates
        jobs = [job for job in jobs if job.get('template_id') in template_ids]
        # --- Analytics calculations ---
        total_templates = len(templates)
        # Time saved
        template_usage = {tpl_id: 0 for tpl_id in template_ids}
        for job in jobs:
            if job.get('status') == 'completed' and job.get('template_id') in template_usage:
                template_usage[job['template_id']] += 1
        total_time_saved_minutes = 0.0
        for tpl_id, count in template_usage.items():
            tpl = template_id_map[tpl_id]
            minutes = tpl.get('time_saved_per_run_minutes') or 0.0
            total_time_saved_minutes += count * minutes
        total_time_saved_hours = total_time_saved_minutes / 60.0 if total_time_saved_minutes else 0.0
        # Success/failure rates (convert to percentage)
        total_runs = len(jobs)
        success_count = sum(1 for job in jobs if job.get('status') == 'completed')
        failure_count = sum(1 for job in jobs if job.get('status') == 'failed')
        success_rate = round((success_count / total_runs) * 100, 2) if total_runs else 0.0
        failure_rate = round((failure_count / total_runs) * 100, 2) if total_runs else 0.0
        # Recent activity (last 10 jobs)
        recent_activity = [TemplateJobRun(
            job_id=job.get('uuid'),
            job_status=job.get('status'),
            job_duration=str(job.get('job_duration')) if job.get('job_duration') is not None else None,
            created_at=job.get('created_at'),
            job_title=job.get('job_title')
        ) for job in jobs[:10]]
        # Average run duration (seconds)
        durations = []
        for job in jobs:
            dur = job.get('job_duration')
            if dur is not None:
                try:
                    t = dur
                    if isinstance(t, str):
                        parts = t.split(':')
                        if len(parts) == 3:
                            h, m, s = int(parts[0]), int(parts[1]), float(parts[2])
                            seconds = h * 3600 + m * 60 + s
                            durations.append(seconds)
                        else:
                            durations.append(float(t))
                    else:
                        durations.append(float(t))
                except Exception:
                    continue
        average_run_duration_seconds = sum(durations) / len(durations) if durations else 0.0
        # Templates by category/type
        templates_by_category = defaultdict(int)
        for tpl in templates:
            cat = tpl.get('category') or 'Uncategorized'
            templates_by_category[cat] += 1
        templates_by_category = dict(templates_by_category)
        # Most used templates (top 5)
        most_used_templates = []
        usage_sorted = sorted(template_usage.items(), key=lambda x: x[1], reverse=True)
        for tpl_id, count in usage_sorted[:5]:
            tpl = template_id_map[tpl_id]
            minutes = tpl.get('time_saved_per_run_minutes') or 0.0
            most_used_templates.append({
                'id': tpl_id,
                'title': tpl.get('title'),
                'usage_count': count,
                'time_saved_minutes': count * minutes
            })
        # Average time saved per template (minutes)
        avg_time_saved_per_template = (total_time_saved_minutes / total_templates) if total_templates else 0.0
        # Top performing templates (by success rate, usage, and time saved, top 5)
        top_performing_templates = []
        tpl_success = {tpl_id: {'success': 0, 'fail': 0} for tpl_id in template_ids}
        for job in jobs:
            tpl_id = job.get('template_id')
            if tpl_id in tpl_success:
                if job.get('status') == 'completed':
                    tpl_success[tpl_id]['success'] += 1
                elif job.get('status') == 'failed':
                    tpl_success[tpl_id]['fail'] += 1
        tpl_perf = []
        for tpl_id in template_ids:
            tpl = template_id_map[tpl_id]
            usage = template_usage[tpl_id]
            succ = tpl_success[tpl_id]['success']
            fail = tpl_success[tpl_id]['fail']
            total = succ + fail
            rate = round((succ / total) * 100, 2) if total else 0.0
            tpl_perf.append({
                'id': tpl_id,
                'title': tpl.get('title'),
                'success_rate': rate,
                'usage_count': usage,
                'time_saved_minutes': (usage * (tpl.get('time_saved_per_run_minutes') or 0.0))
            })
        top_performing_templates = sorted(tpl_perf, key=lambda x: (x['success_rate'], x['usage_count'], x['time_saved_minutes']), reverse=True)[:5]
        # Templates with no usage
        templates_with_no_usage = []
        for tpl_id in template_ids:
            if template_usage[tpl_id] == 0:
                tpl = template_id_map[tpl_id]
                templates_with_no_usage.append({
                    'id': tpl_id,
                    'title': tpl.get('title'),
                    'category': tpl.get('category'),
                    'usage_count': 0,
                    'time_saved_minutes': 0
                })
        # Build response
        resp = TemplateAnalyticsResponse(
            total_templates=total_templates,
            total_time_saved_minutes=total_time_saved_minutes,
            total_time_saved_hours=total_time_saved_hours,
            success_rate=success_rate,
            failure_rate=failure_rate,
            recent_activity=recent_activity,
            average_run_duration_seconds=average_run_duration_seconds,
            templates_by_category=templates_by_category,
            most_used_templates=most_used_templates,
            average_time_saved_per_template_minutes=avg_time_saved_per_template,
            top_performing_templates=top_performing_templates,
            templates_with_no_usage=templates_with_no_usage
        )
        logger.info("[get_templates_analytics] Successfully computed analytics")
        return format_success_response(
            message="Template analytics fetched successfully",
            data=resp.model_dump()
        )
    except Exception as e:
        logger.error(f"[get_templates_analytics] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to fetch template analytics",
            error=str(e)
        )

@router.get("/templates")
async def list_templates(
    search: str = Query(None, description="Search term for templates"),
    label: str = Query(None, description="Field to search in (title, description, tags, category, template_type, summary, agents_list)", alias="label"),
    sort_by: str = Query(None, description="Field to sort by (default: created_at)"),
    sort_order: str = Query("desc", description="Sort order: asc or desc (default: desc)"),
    limit: int = Query(None, description="Number of results to return (default from config)"),
    offset: int = Query(0, description="Offset for pagination (default: 0)"),
    auth_data: dict = Depends(verify_tokens)
):
    """
    List all templates visible to the current store, with search, sort, and pagination.
    """
    try:
        # Get store_id from token
        store_id, app_id, request_id = await validate_auth_data(auth_data)
        # Get all visible templates for this store
        templates = await get_sw_templates_full_for_store(store_id)
        # Use config for default limit and search fields
        DEFAULT_LIMIT = getattr(templates_config, "TEMPLATES_DEFAULT_LIMIT_FOR_GET_TEMPLATES_ENDPOINT", 15)
        DEFAULT_SEARCH_FIELDS = getattr(templates_config, "TEMPLATES_DEFAULT_SEARCH_FIELDS", [
            "title", "description", "tags", "category", "template_type", "summary", "agents_list"
        ])
        # Apply search filter
        if search:
            search_lower = search.lower()
            fields_to_search = [label] if label else DEFAULT_SEARCH_FIELDS
            def match(template):
                for field in fields_to_search:
                    value = template.get(field)
                    if value is None:
                        continue
                    if field == "tags" and isinstance(value, list):
                        if any(search_lower in str(tag).lower() for tag in value):
                            return True
                    elif field == "agents_list" and isinstance(value, list):
                        # Search agent names
                        if any(search_lower in str(agent.get("name", "")).lower() for agent in value if isinstance(agent, dict)):
                            return True
                    elif isinstance(value, str):
                        if search_lower in value.lower():
                            return True
                    elif field == "category" or field == "template_type":
                        if search_lower == str(value).lower():
                            return True
                return False
            templates = [tpl for tpl in templates if match(tpl)]
        # Sorting
        sort_field = sort_by or "created_at"
        reverse = (sort_order or "desc").lower() == "desc"
        def get_sort_value(tpl):
            v = tpl.get(sort_field)
            # Try to parse date for created_at/updated_at
            if sort_field in ("created_at", "updated_at") and v:
                try:
                    from dateutil.parser import parse as dtparse
                    return dtparse(v)
                except Exception:
                    return v
            return v
        templates = sorted(templates, key=get_sort_value, reverse=reverse)
        # Pagination
        limit = limit or DEFAULT_LIMIT
        paged_templates = templates[offset:offset+limit]
        # Convert to TemplateSummary
        from StoreWorker.models.types import TemplateSummary
        summaries = [TemplateSummary(**{
            "uuid": tpl.get("uuid"),
            "title": tpl.get("title"),
            "description": tpl.get("description"),
            "emoji": tpl.get("emoji"),
            "template_type": tpl.get("template_type"),
            "category": tpl.get("category"),
            "tags": tpl.get("tags"),
            "summary": tpl.get("summary")
        }) for tpl in paged_templates]
        return format_success_response(
            message="Templates fetched successfully",
            data={
                "templates": [s.model_dump() for s in summaries],
                "total": len(templates),
                "limit": limit,
                "offset": offset
            }
        )
    except Exception as e:
        logger.error(f"[list_templates] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to fetch templates",
            error=str(e)
        )
