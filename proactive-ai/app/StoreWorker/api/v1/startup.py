from fastapi import APIRouter, Depends, status
from Middleware.auth_check import verify_tokens
from Utils.Helpers.validate_token import validate_auth_data
from StoreWorker.utils.response_formatter import format_success_response, format_error_response
from StoreWorker.logger.logger import LogContext, info, error
from app.Models.dbHelpers import assign_azure_function_app_to_store
import time

router = APIRouter()

TRACK_EXECUTION_TIME = True

@router.get("/assign-azure-function-app")
async def assign_azure_function_app(auth_data: dict = Depends(verify_tokens)):
    start_time = time.time() if TRACK_EXECUTION_TIME else None
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)
        with LogContext(request_id=request_id, store_id=store_id):
            info(f"Assigning Azure Function App for store_id: {store_id}")
            assigned = await assign_azure_function_app_to_store(store_id)
            if assigned:
                if TRACK_EXECUTION_TIME:
                    execution_time = time.time() - start_time
                    info(f"Azure Function App assigned in {execution_time:.2f} seconds")
                return format_success_response(
                    message="Azure Function App assigned successfully",
                    data=assigned
                )
            else:
                if TRACK_EXECUTION_TIME:
                    execution_time = time.time() - start_time
                    info(f"No Azure Function App available. Completed in {execution_time:.2f} seconds")
                return format_success_response(
                    message="No available Azure Function App to assign",
                    data=None,
                    status_code=status.HTTP_202_ACCEPTED
                )
    except Exception as e:
        error(f"Error assigning Azure Function App: {str(e)}", extra={"error": str(e)}, exc_info=True)
        return format_error_response(
            message="Failed to assign Azure Function App",
            error=str(e)
        )
