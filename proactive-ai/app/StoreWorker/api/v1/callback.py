from fastapi import APIRouter, Request
import logging
import json
from typing import Any

# Import DB helper functions
try:
    from app.Models.dbHelpers import (
        update_team_deploy_status, update_team_table_status, update_team_deploy_function_details, get_team_details
    )
except ImportError:
    from Models.dbHelpers import (
        update_team_deploy_status, update_team_table_status, update_team_deploy_function_details, get_team_details
    )

# Import response formatters
from StoreWorker.utils.response_formatter import (
    format_success_response, format_failure_response, format_error_response
)

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/azure-function-deployment-status")
async def azure_function_deployment_status(request: Request):
    try:
        body = await request.json()
        logger.info(f"[Callback] Received deployment status callback: {json.dumps(body, indent=2)}")
        # Try to extract team_id from top-level or from 'data' field
        team_id = body.get("team_id")
        data_field = body.get("data")
        if not team_id and data_field and isinstance(data_field, dict):
            team_id = data_field.get("team_id")
            logger.info(f"[Callback] Extracted team_id from 'data' field: {team_id}")
        else:
            logger.info(f"[Callback] Extracted team_id from top-level: {team_id}")
        if not team_id:
            logger.error("[Callback] Missing team_id in callback payload (checked top-level and 'data' field)")
            return format_failure_response(message="Missing team_id in callback payload", error="No team_id in payload")
        # Get store_id from team details
        team_details = await get_team_details(team_id)
        store_id = team_details.get("store_id") if team_details else None
        logger.info(f"[Callback] team_id={team_id}, store_id={store_id}")
        # Use data_field for deployment details if present
        payload_source = data_field if data_field and isinstance(data_field, dict) else body
        deployed_status = payload_source.get("deployed_status")
        function_app_name = payload_source.get("function_app_name")
        url = payload_source.get("url")
        endpoint = payload_source.get("endpoint")
        function_name = payload_source.get("function_name")
        backup_path = payload_source.get("backup_path")
        error_obj = payload_source.get("error")
        # Update DB based on result
        if deployed_status:
            logger.info(f"[Callback] Deployment SUCCESS for team_id={team_id}")
            # Success: update function details and status
            update_team_deploy_function_details(team_id, store_id, function_name=function_name, url=url, endpoint=endpoint)
            logger.info(f"[Callback] Updated function details in DB for team_id={team_id}")
            update_team_deploy_status(team_id, store_id, deploy_status="deployed", errors=None)
            logger.info(f"[Callback] Updated deploy status to 'deployed' for team_id={team_id}")
            update_team_table_status(team_id, "completed")
            logger.info(f"[Callback] Updated team table status to 'completed' for team_id={team_id}")
            payload = {
                "deployed_status": deployed_status,
                "function_app_name": function_app_name,
                "url": url,
                "endpoint": endpoint,
                "function_name": function_name,
                "backup_path": backup_path,
                "team_id": team_id
            }
            logger.info(f"[Callback] Sending success response for team_id={team_id}")
            return format_success_response(
                message="Template crew function generated and deployed successfully",
                data=payload
            )
        else:
            logger.error(f"[Callback] Deployment FAILURE for team_id={team_id}, error={error_obj}")
            # Failure: clear function details, set status to failed
            update_team_deploy_function_details(team_id, store_id, function_name=None, url=None, endpoint=None)
            logger.info(f"[Callback] Cleared function details in DB for team_id={team_id}")
            update_team_deploy_status(team_id, store_id, deploy_status="failed", errors=json.dumps(error_obj) if error_obj else str(payload_source))
            logger.info(f"[Callback] Updated deploy status to 'failed' for team_id={team_id}")
            update_team_table_status(team_id, "failed")
            logger.info(f"[Callback] Updated team table status to 'failed' for team_id={team_id}")
            payload = {
                "message": "Error generating and deploying template crew function",
                "error": error_obj,
                "team_id": team_id
            }
            logger.info(f"[Callback] Sending failure response for team_id={team_id}")
            return format_failure_response(
                message="Error generating and deploying template crew function",
                error=json.dumps(payload)
            )
    except Exception as e:
        logger.error(f"[Callback] Exception occurred while processing callback: {str(e)}", exc_info=True)
        return format_error_response(
            message="Exception occurred while processing callback",
            error=str(e)
        )
