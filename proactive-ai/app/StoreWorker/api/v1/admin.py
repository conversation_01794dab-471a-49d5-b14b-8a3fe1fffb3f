from fastapi import APIRouter, Request, Depends, HTTPException, Path, Body
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any, List
import logging
import os
from datetime import datetime

from StoreWorker.models.types import (
    AdminTemplateResponse, AdminTemplateListResponse, 
    AdminCreateTemplateRequest, AdminUpdateTemplateRequest,
    AdminTemplateDetails,
    AdminCreateDeploymentRequest
)
from StoreWorker.utils.response_formatter import format_success_response, format_error_response
from Models.dbHelpers_templates import (
    get_all_sw_templates_admin, get_sw_template_details_by_uuid_admin,
    create_sw_template_admin, update_sw_template_admin, soft_delete_sw_template_by_uuid,
    get_sw_template_id_by_uuid,
    create_sw_template_deployment
)
from StoreWorker.logger.logger import info, error
from StoreWorker.config.admin import get_admin_header_config

# Configure logging
logger = logging.getLogger(__name__)

# Admin configuration
ADMIN_HEADER_KEY, ADMIN_HEADER_VALUE = get_admin_header_config()

router = APIRouter()

async def verify_admin_access(request: Request) -> bool:
    """
    Verify admin access by checking the admin header.
    
    Args:
        request: FastAPI request object
        
    Returns:
        bool: True if admin access is granted
        
    Raises:
        HTTPException: If admin access is denied
    """
    admin_token = request.headers.get(ADMIN_HEADER_KEY)
    
    if not admin_token or admin_token != ADMIN_HEADER_VALUE:
        logger.warning(f"Unauthorized admin access attempt from {request.client.host if request.client else 'unknown'}")
        raise HTTPException(
            status_code=403,
            detail="Admin access denied. Valid admin credentials required."
        )
    
    logger.info("Admin access granted")
    return True

@router.get("/templates", response_model=AdminTemplateListResponse)
async def get_all_templates_admin(
    request: Request,
    admin_verified: bool = Depends(verify_admin_access)
):
    """
    Admin endpoint to retrieve all templates with full details and admin fields.
    
    Returns:
        AdminTemplateListResponse: List of all templates with complete information
    """
    try:
        logger.info("[get_all_templates_admin] Starting admin templates list endpoint")
        
        # Get all templates with admin details
        templates = await get_all_sw_templates_admin()
        logger.info(f"[get_all_templates_admin] Retrieved {len(templates)} templates")
        
        return format_success_response(
            message="Templates retrieved successfully",
            data={"templates": templates}
        )
        
    except Exception as e:
        logger.error(f"[get_all_templates_admin] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to retrieve templates",
            error=str(e)
        )

@router.get("/templates/{template_uuid}", response_model=AdminTemplateResponse)
async def get_template_by_uuid_admin(
    template_uuid: str = Path(..., description="UUID of the template"),
    request: Request = None,
    admin_verified: bool = Depends(verify_admin_access)
):
    """
    Admin endpoint to retrieve a specific template by UUID with full details and admin fields.
    
    Args:
        template_uuid: UUID of the template to retrieve
        
    Returns:
        AdminTemplateResponse: Complete template information including admin fields
    """
    try:
        logger.info(f"[get_template_by_uuid_admin] Starting admin template details for uuid={template_uuid}")
        
        # Get template details with admin fields
        template = await get_sw_template_details_by_uuid_admin(template_uuid)
        
        if not template:
            logger.warning(f"[get_template_by_uuid_admin] Template not found for uuid={template_uuid}")
            return format_error_response(
                message="Template not found",
                error="No template found with the specified UUID",
                status_code=404
            )
        
        logger.info(f"[get_template_by_uuid_admin] Retrieved template details for uuid={template_uuid}")
        
        return format_success_response(
            message="Template retrieved successfully",
            data=template
        )
        
    except Exception as e:
        logger.error(f"[get_template_by_uuid_admin] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to retrieve template",
            error=str(e)
        )

@router.post("/templates", response_model=AdminTemplateResponse)
async def create_template_admin(
    request_data: AdminCreateTemplateRequest = Body(...),
    request: Request = None,
    admin_verified: bool = Depends(verify_admin_access)
):
    """
    Admin endpoint to create a new template with configurations.
    
    Args:
        request_data: Template creation data
        
    Returns:
        AdminTemplateResponse: Created template information
    """
    try:
        logger.info("[create_template_admin] Starting admin template creation")
        
        # Convert request data to dict and prepare for database
        template_data = request_data.model_dump(exclude_unset=True)
        # Ensure agents_list is present (even if None)
        if 'agents_list' not in template_data:
            template_data['agents_list'] = None
        logger.info(f"[create_template_admin] Creating template with data keys: {list(template_data.keys())}")
        
        # Create the template
        created_template = await create_sw_template_admin(template_data)
        
        if not created_template:
            logger.error("[create_template_admin] Failed to create template")
            return format_error_response(
                message="Failed to create template",
                error="Database insert operation failed",
                status_code=500
            )
        
        logger.info(f"[create_template_admin] Successfully created template with uuid={created_template.get('uuid')}")
        
        return format_success_response(
            message="Template created successfully",
            data=created_template
        )
        
    except Exception as e:
        logger.error(f"[create_template_admin] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to create template",
            error=str(e)
        )

@router.put("/templates/{template_uuid}", response_model=AdminTemplateResponse)
async def update_template_admin(
    template_uuid: str = Path(..., description="UUID of the template to update"),
    request_data: AdminUpdateTemplateRequest = Body(...),
    request: Request = None,
    admin_verified: bool = Depends(verify_admin_access)
):
    """
    Admin endpoint to update template details and/or configurations.
    Auto-detects whether to update template details, configurations, or both based on provided fields.

    Args:
        template_uuid: UUID of the template to update
        request_data: Template update data

    Returns:
        AdminTemplateResponse: Updated template information
    """
    try:
        logger.info(f"[update_template_admin] Starting admin template update for uuid={template_uuid}")

        # Check if template exists
        template_id = await get_sw_template_id_by_uuid(template_uuid)
        if not template_id:
            logger.warning(f"[update_template_admin] Template not found for uuid={template_uuid}")
            return format_error_response(
                message="Template not found",
                error="No template found with the specified UUID",
                status_code=404
            )

        # Convert request data to dict and prepare for database
        update_data = request_data.model_dump(exclude_unset=True)
        # Preserve agents_list if not present in update_data
        if 'agents_list' not in update_data:
            current_template = await get_sw_template_details_by_uuid_admin(template_uuid)
            if current_template is not None:
                update_data['agents_list'] = current_template.get('agents_list', [])
            else:
                update_data['agents_list'] = []
        # If agents_list is present (even if None or empty), use as is
        logger.info(f"[update_template_admin] Updating template with data keys: {list(update_data.keys())}")

        # Update the template
        updated_template = await update_sw_template_admin(template_uuid, update_data)

        if not updated_template:
            logger.error(f"[update_template_admin] Failed to update template uuid={template_uuid}")
            return format_error_response(
                message="Failed to update template",
                error="Database update operation failed",
                status_code=500
            )

        logger.info(f"[update_template_admin] Successfully updated template uuid={template_uuid}")

        return format_success_response(
            message="Template updated successfully",
            data=updated_template
        )

    except Exception as e:
        logger.error(f"[update_template_admin] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to update template",
            error=str(e)
        )

@router.delete("/templates/{template_uuid}")
async def delete_template_admin(
    template_uuid: str = Path(..., description="UUID of the template to delete"),
    request: Request = None,
    admin_verified: bool = Depends(verify_admin_access)
):
    """
    Admin endpoint to soft delete a template by UUID.

    Args:
        template_uuid: UUID of the template to delete

    Returns:
        JSONResponse: Success or error response
    """
    try:
        logger.info(f"[delete_template_admin] Starting admin template deletion for uuid={template_uuid}")

        # Check if template exists
        template_id = await get_sw_template_id_by_uuid(template_uuid)
        if not template_id:
            logger.warning(f"[delete_template_admin] Template not found for uuid={template_uuid}")
            return format_error_response(
                message="Template not found",
                error="No template found with the specified UUID",
                status_code=404
            )

        # Soft delete the template
        deletion_success = await soft_delete_sw_template_by_uuid(template_uuid)

        if not deletion_success:
            logger.error(f"[delete_template_admin] Failed to delete template uuid={template_uuid}")
            return format_error_response(
                message="Failed to delete template",
                error="Database deletion operation failed",
                status_code=500
            )

        logger.info(f"[delete_template_admin] Successfully deleted template uuid={template_uuid}")

        return format_success_response(
            message="Template deleted successfully",
            data={"template_uuid": template_uuid, "deleted": True}
        )

    except Exception as e:
        logger.error(f"[delete_template_admin] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to delete template",
            error=str(e)
        )

@router.post("/templates/deployment")
async def create_deployment_admin(
    request_data: AdminCreateDeploymentRequest = Body(...),
    request: Request = None,
    admin_verified: bool = Depends(verify_admin_access)
):
    """
    Admin endpoint to create a new deployment record for a template.
    Requires template_id and url. All other fields are optional.
    """
    try:
        logger.info("[create_deployment_admin] Starting admin deployment creation")
        deployment_data = request_data.model_dump(exclude_unset=True)
        template_id = deployment_data.get("template_id")
        url = deployment_data.get("url")
        # Validate required fields
        if not template_id or not url:
            logger.error("[create_deployment_admin] Missing required fields: template_id or url")
            return format_error_response(
                message="template_id and url are required",
                error="Missing required fields",
                status_code=400
            )
        # Check if template_id exists
        from Models.dbHelpers_templates import get_sw_template_details
        template = await get_sw_template_details(template_id)
        if not template:
            logger.warning(f"[create_deployment_admin] Template not found for id={template_id}")
            return format_error_response(
                message="Template not found",
                error="No template found with the specified ID",
                status_code=404
            )
        # Insert deployment record
        created_deployment = await create_sw_template_deployment(deployment_data)
        if not created_deployment:
            logger.error("[create_deployment_admin] Failed to create deployment record")
            return format_error_response(
                message="Failed to create deployment record",
                error="Database insert operation failed",
                status_code=500
            )
        logger.info(f"[create_deployment_admin] Successfully created deployment for template_id={template_id}")
        return format_success_response(
            message="Deployment record created successfully",
            data=created_deployment
        )
    except Exception as e:
        logger.error(f"[create_deployment_admin] Error: {str(e)}", exc_info=True)
        return format_error_response(
            message="Failed to create deployment record",
            error=str(e)
        )
