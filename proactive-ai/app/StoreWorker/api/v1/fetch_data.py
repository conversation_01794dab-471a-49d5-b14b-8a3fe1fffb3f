from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from typing import List, Optional, Dict, Any    
import logging
from datetime import datetime

from Middleware.auth_check import verify_tokens
from Utils.Helpers.validate_token import validate_auth_data
from StoreWorker.utils.response_formatter import (
    format_success_response, format_error_response, format_failure_response
)
from StoreWorker.logger.logger import LogContext, info, error, debug
from app.Models.dbHelpers import (
    get_all_products_data_belongs_to_a_store,
    get_all_product_data_belongs_to_a_store_except_embedding,
    get_all_collections_for_store,
    get_all_blogs_for_store,
    get_all_articles_for_store
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/products")
async def get_products(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Endpoint to fetch all products for the authenticated store.
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        if not store_id:
            return format_error_response("Missing store_id in authentication data.")

        products = await get_all_product_data_belongs_to_a_store_except_embedding(store_id)
        return format_success_response(data=products, message="Products fetched successfully")
    except Exception as e:
        logger.error(f"Error in get_products: {str(e)}")
        return format_error_response(str(e))

@router.get("/collections")
async def get_collections(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Endpoint to fetch all collections for the authenticated store.
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        if not store_id:
            return format_error_response("Missing store_id in authentication data.")

        collections = await get_all_collections_for_store(store_id)
        return format_success_response(data=collections, message="Collections fetched successfully")
    except Exception as e:
        logger.error(f"Error in get_collections: {str(e)}")
        return format_error_response(str(e))

@router.get("/blogs")
async def get_blogs(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Endpoint to fetch all blogs for the authenticated store.
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        if not store_id:
            return format_error_response("Missing store_id in authentication data.")

        blogs = await get_all_blogs_for_store(store_id)
        return format_success_response(data=blogs, message="Blogs fetched successfully")
    except Exception as e:
        logger.error(f"Error in get_blogs: {str(e)}")
        return format_error_response(str(e))

@router.get("/articles")
async def get_articles(
    request: Request,
    auth_data: dict = Depends(verify_tokens)
):
    """
    Endpoint to fetch all articles for the authenticated store.
    """
    try:
        store_id, app_id, request_id = await validate_auth_data(auth_data)

        if not store_id:
            return format_error_response("Missing store_id in authentication data.")

        articles = await get_all_articles_for_store(store_id)
        return format_success_response(data=articles, message="Articles fetched successfully")
    except Exception as e:
        logger.error(f"Error in get_articles: {str(e)}")
        return format_error_response(str(e))
