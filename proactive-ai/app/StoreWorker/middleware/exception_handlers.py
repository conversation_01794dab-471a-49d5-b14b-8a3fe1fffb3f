from fastapi import Request
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from StoreWorker.utils.response_formatter import format_error_response
from StoreWorker.logger.logger import error

async def validation_exception_handler(request: Request, exc: ValidationError):
    error("Validation error occurred", extra={"errors": exc.errors()}, exc_info=True)
    error_message = "; ".join([f"{err['msg']} at {'.'.join(str(loc) for loc in err['loc'])}" for err in exc.errors()])
    return format_error_response(
        message="Validation error",
        error=error_message,
        status_code=422
    )

async def request_validation_exception_handler(request: Request, exc: RequestValidationError):
    error("Request validation error occurred", extra={"errors": exc.errors()}, exc_info=True)
    error_message = "; ".join([f"{err['msg']} at {'.'.join(str(loc) for loc in err['loc'])}" for err in exc.errors()])
    return format_error_response(
        message="Request validation error",
        error=error_message,
        status_code=422
    ) 