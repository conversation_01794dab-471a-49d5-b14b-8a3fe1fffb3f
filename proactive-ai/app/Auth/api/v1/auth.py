# auth/models.py
from dotenv import load_dotenv
import os
from datetime import datetime
from typing import Optional, List
import hmac
import hashlib
from Models.db_config import supabase
from Models.dbHelpers import insert_data_into_users_table, check_api_token_in_stores_table, check_api_token_in_apps_table
from Logging.logger_config import get_logger

# Load the .env file
load_dotenv()

# Initialize the logger
logger = get_logger(__name__)

class AppDetails:
        def __init__(self, id: int, app_name: str,
                 api_token: Optional[str] = None,
                 created_at: datetime = None,
                 updated_at: datetime = None):
            self.id = id
            self.app_name = app_name
            self.api_token = api_token
            self.created_at = created_at
            self.updated_at = updated_at

class Store:
    def __init__(self, id: int, uuid: str, name: str, url: str,
                 is_rankcollections_product: bool = False,
                 is_blogd_product: bool = False,
                 is_storeblog_product: bool = False,
                 is_storeworkers_product: bool = False,
                 api_token: Optional[str] = None,
                 created_at: datetime = None,
                 updated_at: datetime = None,
                 competitors_domains: Optional[List[str]] = None):
        self.id = id
        self.uuid = uuid
        self.name = name
        self.url = url
        self.is_rankcollections_product = is_rankcollections_product
        self.is_blogd_product = is_blogd_product
        self.is_storeblog_product = is_storeblog_product
        self.is_storeworkers_product = is_storeworkers_product
        self.api_token = api_token
        self.created_at = created_at
        self.updated_at = updated_at
        self.competitors_domains = None

class AuthManager:
    def __init__(self):
        self.HMAC_SECRET = os.getenv("HMAC_SECRET")
        logger.info("AuthManager initialized with HMAC_SECRET loaded.")
    
    def generate_api_token(self, store_name: str, timestamp: str) -> str:
        """Generate a unique, non-expiring API token using HMAC"""
        message = f"{store_name}:{timestamp}"
        logger.debug(f"Generating API token for store: {store_name} at {timestamp}.")
        hmac_obj = hmac.new(
            self.HMAC_SECRET.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        )
        return hmac_obj.hexdigest()

    async def register_store(self, name: str, url: str, app_type: str) -> Store:
        """Register a new store and generate its API token"""
        logger.info(f"Registering store: {name} with URL: {url} and app type: {app_type}.")
        timestamp = datetime.utcnow().isoformat()
        api_token = self.generate_api_token(name, timestamp)
        
        store_data = {
            "name": name,
            "url": url,
            "api_token": api_token,
            f"is_{app_type}_product": True
        }
        
        result = await insert_data_into_users_table(store_data)
        
        # Handle dictionary response
        if result and isinstance(result, dict):
            logger.info(f"Store registered successfully: {result}.")
            return Store(**result)
        elif result and hasattr(result, 'data') and result.data:
            logger.info(f"Store registered successfully: {result.data[0]}.")
            return Store(**result.data[0])
        logger.error("Failed to register store: No valid result returned.")
        raise Exception("Failed to register store")

    async def verify_token(self, api_token: str) -> Optional[Store]:
        """Verify an API token and return the associated store"""
        logger.debug(f"Verifying API token: {api_token}.")
        result = await check_api_token_in_stores_table(api_token)
        
        if result:
            logger.info(f"Token verified successfully, returning store details: {result['name']}.")
            return Store(**result)
        logger.warning("Token verification failed: No store found.")
        return None
    
    async def verify_app_token(self, api_token: str) -> Optional[Store]:
        """Verify an API token and return the associated app"""
        logger.debug(f"Verifying API token: {api_token}.")
        result = await check_api_token_in_apps_table(api_token)
        
        if result:
            logger.info(f"Token verified successfully, returning app: {result['app_name']}.")
            return AppDetails(**result)
        logger.warning("Token verification failed: No app found.")
        return None
    
