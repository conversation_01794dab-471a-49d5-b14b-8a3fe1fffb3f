import os
from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel
from Models.dbHelpers import check_if_store_exists, update_store_record, get_app_details
from Auth.api.v1.auth import AuthManager
from Utils.Helpers.response_formater import success_response, failure_response
from Logging.logger_config import get_logger

router = APIRouter()
logger = get_logger(__name__)

class InstallParams(BaseModel):
    shop_domain: str
    app_type: str

# Initialize Auth Manager
auth_manager = AuthManager()

@router.post("/install", response_description='route to save the store details after install')
async def install_app(request: Request, params: InstallParams):
    """Handle app installation"""
    try:
        # Parse installation data
        data = params.dict()

        # app type_small_case
        app_name_lower = data['app_type'].lower()

        # getting app details
        app_details = await get_app_details(data['app_type'])
        if not app_details:
            logger.error(f"App type {data['app_type']} not found")
            return failure_response(message=f"Invalid app type: {data['app_type']}")

        #check if store already present 
        try:
            existing_store = await check_if_store_exists(data['shop_domain'])
        except Exception as e:
            logger.error(f"Error checking store existence: {str(e)}")
            return failure_response(message="Error checking store details")

        if existing_store:
            try:
                # Update existing store's app type
                store_data = {
                    "id": existing_store['id'],
                    f"is_{app_name_lower}_product": True
                }
                result = await update_store_record(existing_store['id'], store_data)
                if result:
                    response_data = {
                        "api_token": existing_store['api_token'],
                        "app_token": app_details['api_token']
                    }
                    return success_response(message='App Registered Successfully',data=response_data)
                else:
                    logger.error("Failed to update store record")
                    return failure_response(message="Failed to update store details")
            except Exception as e:
                logger.error(f"Error updating store record: {str(e)}")
                return failure_response(message="Error updating store details")
        else:
            try:
                store = await auth_manager.register_store(
                    name=data["shop_domain"],
                    url=f"https://{data['shop_domain']}",
                    app_type=app_name_lower  # rankcollections, blogd, or storeblog
                )

                response_data = {
                    "api_token": store.api_token,
                    "app_token": app_details['api_token']
                }
                
                return success_response(message='App Registered Successfully', data=response_data)
            except Exception as e:
                logger.error(f"Error registering new store: {str(e)}")
                return failure_response(message="Failed to register new store")

    except Exception as e:
        logger.error(f"Unexpected error in install_app: {str(e)}")
        return failure_response(message="An unexpected error occurred")
