# auth/generate_store_api_token.py
"""
This file is used to generate a unique, non-expiring API token for a store.
Modify the store_name variable to test with different stores.
"""
from dotenv import load_dotenv
import os
from datetime import datetime
import hmac
import hashlib


# Load the .env file
load_dotenv()

class AuthManager:
    def __init__(self):
        self.HMAC_SECRET = os.getenv("HMAC_SECRET") or "Fairbanks_91939_1@mB@tm@n"
        print("AuthManager initialized with HMAC_SECRET loaded.")
    
    def generate_api_token(self, store_name: str, timestamp: str) -> str:
        """Generate a unique, non-expiring API token using HMAC"""
        message = f"{store_name}:{timestamp}"
        print(f"Generating API token for store: {store_name} at {timestamp}.")
        hmac_obj = hmac.new(
            self.HMAC_SECRET.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        )
        return hmac_obj.hexdigest()


    
#sample generation of api token if run directly as a script
if __name__ == "__main__":
    auth_manager = AuthManager()
    timestamp = datetime.utcnow().isoformat()
    store_name = "mmfoam.com/" #modify this to test with different stores
    api_token = auth_manager.generate_api_token(store_name, timestamp)
    print(f"Generated API token: {api_token}")