import sys
import time
from typing import Any, Dict
import structlog
from pathlib import Path
import logging

def get_logger(name: str = None) -> structlog.BoundLogger:
    """
    Creates and returns a structured logger instance that writes to both console and file.
    Args:
        name (str): Optional name for the logger, typically __name__ from the calling module
    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    # Create logs directory if it doesn't exist
    Path("logs").mkdir(exist_ok=True)

    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=logging.INFO,
    )

    # Create file handler
    file_handler = logging.FileHandler(
        # filename=f"logs/app_{time.strftime('%Y%m%d')}.json"
        filename=f"logs/proactive_ai_dev.log"
    )
    file_handler.setLevel(logging.INFO)

    # Get root logger and add file handler
    logging_logger = logging.getLogger()
    logging_logger.addHandler(file_handler)

    # Configure shared processors
    shared_processors = [
        structlog.stdlib.add_log_level,
        structlog.stdlib.add_logger_name,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]

    # Configure processors for different outputs
    processors = shared_processors + [
        structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
    ]

    # Configure structlog
    structlog.configure(
        processors=processors,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Create formatter for JSON output
    formatter = structlog.stdlib.ProcessorFormatter(
        processor=structlog.processors.JSONRenderer(),
        foreign_pre_chain=shared_processors,
    )

    # Apply formatter to file handler
    file_handler.setFormatter(formatter)

    # Create and return logger instance
    logger = structlog.get_logger(name)

    return logger