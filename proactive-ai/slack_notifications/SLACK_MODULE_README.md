# Slack Notification Module

A comprehensive Slack notification system for your GeneratorAI project with template-based messaging, channel management, and operation-specific notifications.

## 📁 File Structure

```
├── slack_utils.py                          # Main utility functions
├── slack_examples.py                       # Usage examples and demos
├── config/
│   ├── __init__.py                        # Package initialization
│   ├── slack_types.py                     # Type definitions and enums
│   ├── slack_channels.py                  # Channel ID mappings
│   ├── slack_templates.py                 # Main template configuration
│   └── templates/
│       ├── __init__.py                    # Templates package init
│       ├── storeWorker_templates.py       # StoreWorker templates
│       ├── rankCollections_templates.py   # RankCollections templates
│       ├── storeBlogs_templates.py        # StoreBlogs templates
│       └── installation_templates.py      # Installation templates
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install slack-sdk
```

### 2. Set Up Slack Bot Token

```bash
export SLACK_BOT_TOKEN='xoxb-your-slack-bot-token-here'
```

### 3. Configure Channels

Edit `config/slack_channels.py` to add your actual Slack channel IDs:

```python
SLACK_CHANNELS = {
    'storeWorker': {
        'default': '#store-worker-notifications',
        'your-project-name': '#your-project-channel',
        # ... more projects
    },
    # ... other operations
}
```

### 4. Send Your First Notification

```python
from slack_utils import send_store_worker_notification

success = send_store_worker_notification(
    project_name='your-project-name',
    job_type='runJob',
    status='completed',
    details={
        'execution_time': '2m 30s',
        'products_processed': 150,
        'success_count': 148
    }
)
```

## 📋 Supported Operations

### StoreWorker Operations
- `runJob` - General job execution
- `workflow_deployment` - Workflow deployments
- `inventory_update` - Inventory updates
- `product_sync` - Product synchronization
- `order_processing` - Order processing

### RankCollections Operations
- `collection_creation_job` - Collection creation
- `keyword_analysis` - Keyword analysis
- `ranking_update` - Ranking updates
- `competitor_analysis` - Competitor analysis

### StoreBlogs Operations
- `article_creation_job` - Article creation
- `content_generation` - Content generation
- `seo_optimization` - SEO optimization
- `blog_publishing` - Blog publishing

### Installation Operations
- `new_user_installation` - New user onboarding
- `upgrade_installation` - System upgrades
- `migration_installation` - Data migrations
- `configuration_update` - Configuration updates

## 🎯 Usage Examples

### Basic Notifications

```python
# StoreWorker notification
send_store_worker_notification(
    project_name='ecommerce-store-1',
    job_type='product_sync',
    status='completed',
    details={'products_processed': 100}
)

# RankCollections notification
send_rank_collections_notification(
    project_name='fashion-boutique',
    collection_name='Summer Collection',
    status='completed',
    details={'products_count': 50, 'ranking_score': 0.85}
)

# StoreBlogs notification
send_store_blogs_notification(
    project_name='sports-equipment',
    article_title='Best Running Shoes 2024',
    status='published',
    details={'word_count': 1200, 'seo_score': 87.5}
)

# Installation notification
send_installation_notification(
    project_name='new-store',
    user_info={'name': 'John Doe', 'email': '<EMAIL>'},
    status='completed',
    details={'version': 'v3.2.1', 'features_enabled': ['analytics', 'seo']}
)
```

### Advanced Usage with SlackNotifier

```python
from slack_utils import SlackNotifier

notifier = SlackNotifier()

# Test connection
if notifier.test_connection():
    print("Connected to Slack!")

# Send custom message
notifier.send_message(
    operation_type='storeWorker',
    project_name='my-project',
    message_data={
        'job_type': 'custom_job',
        'status': 'completed',
        'custom_field': 'custom_value'
    },
    template_override='performance_report'
)

# Send simple message
notifier.send_simple_message(
    channel='#general',
    text='Hello from the Slack module!'
)
```

## 🎨 Message Templates

### Template Types

Each operation supports multiple template types:

- `started` - Operation started
- `in_progress` - Operation in progress
- `completed` - Operation completed successfully
- `failed` - Operation failed
- `warning` - Warning messages
- `error` - Error messages
- `success` - Success messages

### Rich Message Format

Templates support rich Slack formatting with:
- **Headers** - Eye-catching titles
- **Fields** - Structured data display
- **Colors** - Status-based color coding
- **Emojis** - Visual status indicators
- **Attachments** - Additional context

### Custom Templates

Add custom templates in the respective template files:

```python
# In config/templates/storeWorker_templates.py
STORE_WORKER_TEMPLATES['my_custom_template'] = {
    "text": "🎯 Custom Operation",
    "blocks": [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "🎯 *Custom Operation* for *{project_name}*\n*Status:* {status}"
            }
        }
    ]
}
```

## ⚙️ Configuration

### Channel Configuration

Edit `config/slack_channels.py`:

```python
SLACK_CHANNELS = {
    'storeWorker': {
        'default': '#store-worker-notifications',
        'project-1': '#project-1-worker',
        'project-2': '#project-2-worker',
        'production': '#worker-prod',
        'staging': '#worker-staging'
    }
}
```

### Environment Variables

Required:
- `SLACK_BOT_TOKEN` - Your Slack bot token

Optional:
- `SLACK_DEFAULT_CHANNEL` - Default channel for notifications

### Slack Bot Setup

1. Create a Slack app at https://api.slack.com/apps
2. Add bot token scopes: `chat:write`, `chat:write.public`
3. Install the app to your workspace
4. Copy the bot token (starts with `xoxb-`)

## 🔧 Customization

### Adding New Operation Types

1. Add the operation type to `config/slack_types.py`:
```python
SlackOperationType = Literal[
    'storeWorker',
    'rankCollections',
    'storeBlogs',
    'installation',
    'your_new_operation'  # Add here
]
```

2. Create templates in `config/templates/your_operation_templates.py`

3. Add channel mappings in `config/slack_channels.py`

4. Update `config/slack_templates.py` to include your templates

### Custom Message Data

Define custom message data types in `config/slack_types.py`:

```python
class YourOperationMessageData(SlackMessageData, total=False):
    """Your operation specific message data."""
    custom_field: str
    another_field: Optional[int]
```

## 🧪 Testing

Run the examples to test your setup:

```bash
python slack_examples.py
```

This will:
- Test Slack connection
- Send sample notifications for all operation types
- Demonstrate error handling
- Show custom notification usage

## 🚨 Error Handling

The module includes comprehensive error handling:

- **Connection errors** - Slack API connectivity issues
- **Authentication errors** - Invalid bot tokens
- **Channel errors** - Non-existent channels
- **Template errors** - Missing or invalid templates
- **Data errors** - Missing required message data

All errors are logged and functions return `False` on failure.

## 📊 Monitoring

Monitor your Slack notifications:

1. Check function return values (`True`/`False`)
2. Review logs for error messages
3. Use `test_connection()` for health checks
4. Monitor Slack channel activity

## 🔒 Security

- Store bot tokens securely (environment variables)
- Use least-privilege bot scopes
- Validate input data before sending
- Monitor for rate limiting

## 📈 Performance

- Templates are cached for efficiency
- Batch operations supported
- Async operations can be implemented
- Rate limiting handled by slack-sdk

## 🤝 Contributing

To add new features:

1. Follow existing code patterns
2. Add comprehensive templates
3. Include type definitions
4. Update documentation
5. Add usage examples

## 📝 License

This module is part of the GeneratorAI project and follows the same license terms.
