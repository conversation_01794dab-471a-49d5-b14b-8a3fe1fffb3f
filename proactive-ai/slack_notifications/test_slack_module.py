"""
Test Slack Module

Simple tests to validate the Slack module structure and functionality.
Run this to ensure everything is properly configured.
"""

import os
import sys
from typing import Dict, Any


def test_imports():
    """Test that all modules can be imported correctly."""
    print("Testing imports...")

    try:
        # Test main module imports
        from .slack_utils import SlackNotifier
        from .slack_utils import (
            send_store_worker_notification,
            send_rank_collections_notification,
            send_store_blogs_notification,
            send_installation_notification
        )
        print("✅ Main slack_utils imports successful")

        # Test config imports
        from .config.slack_types import SlackOperationType, MessageStatus
        from .config.slack_channels import SLACK_CHANNELS, get_channel_id
        from .config.slack_templates import SLACK_TEMPLATES, get_template
        print("✅ Config imports successful")

        # Test template imports
        from .config.templates.storeWorker_templates import STORE_WORKER_TEMPLATES
        from .config.templates.rankCollections_templates import RANK_COLLECTIONS_TEMPLATES
        from .config.templates.storeBlogs_templates import STORE_BLOGS_TEMPLATES
        from .config.templates.installation_templates import INSTALLATION_TEMPLATES
        print("✅ Template imports successful")

        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during imports: {e}")
        return False


def test_configuration():
    """Test configuration structure and validity."""
    print("\nTesting configuration...")

    try:
        from .config.slack_channels import SLACK_CHANNELS
        from .config.slack_templates import SLACK_TEMPLATES

        # Test channel configuration
        required_operations = ['storeWorker', 'rankCollections', 'storeBlogs', 'installation']

        for operation in required_operations:
            if operation not in SLACK_CHANNELS:
                print(f"❌ Missing channel configuration for: {operation}")
                return False

            if 'default' not in SLACK_CHANNELS[operation]:
                print(f"❌ Missing default channel for: {operation}")
                return False

        print("✅ Channel configuration valid")

        # Test template configuration
        for operation in required_operations:
            if operation not in SLACK_TEMPLATES:
                print(f"❌ Missing template configuration for: {operation}")
                return False

            if 'default' not in SLACK_TEMPLATES[operation]:
                print(f"❌ Missing default template for: {operation}")
                return False

        print("✅ Template configuration valid")
        return True

    except Exception as e:
        print(f"❌ Configuration test error: {e}")
        return False


def test_template_formatting():
    """Test template formatting with sample data."""
    print("\nTesting template formatting...")

    try:
        from .config.slack_templates import format_template, get_template

        # Test data for different operations
        test_cases = [
            {
                'operation': 'storeWorker',
                'status': 'completed',
                'data': {
                    'project_name': 'test-project',
                    'job_type': 'runJob',
                    'status': 'completed',
                    'timestamp': '2024-01-01 12:00:00',
                    'execution_time': '2m 30s',
                    'products_processed': 100,
                    'success_count': 98,
                    'error_count': 2
                }
            },
            {
                'operation': 'rankCollections',
                'status': 'completed',
                'data': {
                    'project_name': 'test-project',
                    'collection_name': 'Test Collection',
                    'status': 'completed',
                    'timestamp': '2024-01-01 12:00:00',
                    'keywords_count': 25,
                    'products_count': 50,
                    'ranking_score': 0.85
                }
            },
            {
                'operation': 'storeBlogs',
                'status': 'completed',
                'data': {
                    'project_name': 'test-project',
                    'article_title': 'Test Article',
                    'status': 'completed',
                    'timestamp': '2024-01-01 12:00:00',
                    'word_count': 1200,
                    'seo_score': 87.5,
                    'author': 'Test Author'
                }
            },
            {
                'operation': 'installation',
                'status': 'completed',
                'data': {
                    'project_name': 'test-project',
                    'installation_type': 'new_user_installation',
                    'status': 'completed',
                    'timestamp': '2024-01-01 12:00:00',
                    'version': 'v1.0.0',
                    'user_info': {'name': 'Test User', 'email': '<EMAIL>'}
                }
            }
        ]

        for test_case in test_cases:
            template = get_template(test_case['operation'], test_case['status'])
            if template is None:
                print(f"❌ No template found for {test_case['operation']}.{test_case['status']}")
                return False

            try:
                formatted = format_template(template, test_case['data'])
                if formatted is None:
                    print(f"❌ Template formatting failed for {test_case['operation']}")
                    return False

                print(f"✅ Template formatting successful for {test_case['operation']}")

            except Exception as e:
                print(f"❌ Template formatting error for {test_case['operation']}: {e}")
                return False

        return True

    except Exception as e:
        print(f"❌ Template formatting test error: {e}")
        return False


def test_channel_resolution():
    """Test channel resolution for different projects."""
    print("\nTesting channel resolution...")

    try:
        from .config.slack_channels import get_channel_id

        test_cases = [
            ('storeWorker', 'default'),
            ('storeWorker', 'ecommerce-store-1'),
            ('rankCollections', 'default'),
            ('storeBlogs', 'default'),
            ('installation', 'default'),
            ('storeWorker', 'non-existent-project')  # Should fall back to default
        ]

        for operation, project in test_cases:
            channel = get_channel_id(operation, project)
            if not channel:
                print(f"❌ No channel resolved for {operation}.{project}")
                return False

            print(f"✅ Channel resolved for {operation}.{project}: {channel}")

        return True

    except Exception as e:
        print(f"❌ Channel resolution test error: {e}")
        return False


def test_slack_connection():
    """Test Slack connection if token is available."""
    print("\nTesting Slack connection...")

    if not os.getenv('SLACK_BOT_TOKEN'):
        print("⚠️  SLACK_BOT_TOKEN not set - skipping connection test")
        return True

    try:
        from .slack_utils import SlackNotifier

        notifier = SlackNotifier()
        if notifier.test_connection():
            print("✅ Slack connection successful")
            return True
        else:
            print("❌ Slack connection failed")
            return False

    except Exception as e:
        print(f"❌ Slack connection test error: {e}")
        return False


def test_convenience_functions():
    """Test convenience functions without actually sending messages."""
    print("\nTesting convenience functions...")

    try:
        from .slack_utils import (
            send_store_worker_notification,
            send_rank_collections_notification,
            send_store_blogs_notification,
            send_installation_notification
        )

        # These functions should be callable (we're not actually sending messages)
        print("✅ All convenience functions are importable and callable")
        return True

    except Exception as e:
        print(f"❌ Convenience functions test error: {e}")
        return False


def run_all_tests():
    """Run all tests and report results."""
    print("🧪 Running Slack Module Tests")
    print("=" * 50)

    tests = [
        ("Import Tests", test_imports),
        ("Configuration Tests", test_configuration),
        ("Template Formatting Tests", test_template_formatting),
        ("Channel Resolution Tests", test_channel_resolution),
        ("Slack Connection Tests", test_slack_connection),
        ("Convenience Functions Tests", test_convenience_functions)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)

        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1

    print(f"\nTotal: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Slack module is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
