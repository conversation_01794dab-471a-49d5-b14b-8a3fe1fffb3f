"""
Slack Channels Configuration

This module defines channel mappings for different operations and projects.
Channel IDs should be obtained from your Slack workspace.
"""

from typing import Dict
from .slack_types import SlackOperationType, ChannelMapping


# Slack Channel IDs - Replace with your actual channel IDs
# Format: 'C1234567890' (Channel ID) or '#channel-name' (Channel name)

# Default channels for different operations
DEFAULT_CHANNELS = {
    'storeWorker': '#store-worker-notifications',
    'rankCollections': '#rank-collections-notifications',
    'storeBlogs': '#store-blogs-notifications',
    'installation': '#installation-notifications',
    'general': '#general-notifications'
}

# Project-specific channel mappings
# Add your project names and their corresponding channel IDs
SLACK_CHANNELS: ChannelMapping = {
    'storeWorker': {
        # Default channel for StoreWorker operations
        'default': DEFAULT_CHANNELS['storeWorker'],

        # Project-specific channels
        'ecommerce-store-1': '#ecommerce-store-1-worker',
        'fashion-boutique': '#fashion-boutique-worker',
        'electronics-shop': '#electronics-shop-worker',
        'home-decor-store': '#home-decor-worker',
        'sports-equipment': '#sports-equipment-worker',

        # Environment-specific channels
        'production': '#store-worker-prod',
        'staging': '#store-worker-staging',
        'development': '#store-worker-dev',

        # High-priority operations
        'critical': '#store-worker-critical',
        'alerts': '#store-worker-alerts'
    },

    'rankCollections': {
        # Default channel for RankCollections operations
        'default': DEFAULT_CHANNELS['rankCollections'],

        # Project-specific channels
        'ecommerce-store-1': '#ecommerce-store-1-collections',
        'fashion-boutique': '#fashion-boutique-collections',
        'electronics-shop': '#electronics-shop-collections',
        'home-decor-store': '#home-decor-collections',
        'sports-equipment': '#sports-equipment-collections',

        # Collection type specific
        'trending-collections': '#trending-collections',
        'seasonal-collections': '#seasonal-collections',
        'competitor-analysis': '#competitor-analysis',

        # Environment-specific channels
        'production': '#collections-prod',
        'staging': '#collections-staging',
        'development': '#collections-dev'
    },

    'storeBlogs': {
        # Default channel for StoreBlogs operations
        'default': DEFAULT_CHANNELS['storeBlogs'],

        # Project-specific channels
        'ecommerce-store-1': '#ecommerce-store-1-blogs',
        'fashion-boutique': '#fashion-boutique-blogs',
        'electronics-shop': '#electronics-shop-blogs',
        'home-decor-store': '#home-decor-blogs',
        'sports-equipment': '#sports-equipment-blogs',

        # Content type specific
        'seo-articles': '#seo-articles',
        'product-reviews': '#product-reviews',
        'how-to-guides': '#how-to-guides',
        'industry-news': '#industry-news',

        # Environment-specific channels
        'production': '#blogs-prod',
        'staging': '#blogs-staging',
        'development': '#blogs-dev'
    },

    'installation': {
        # Default channel for Installation operations
        'default': DEFAULT_CHANNELS['installation'],

        # Installation type specific
        'new-users': '#new-user-installations',
        'upgrades': '#system-upgrades',
        'migrations': '#data-migrations',
        'configurations': '#config-updates',

        # Environment-specific channels
        'production': '#installation-prod',
        'staging': '#installation-staging',
        'development': '#installation-dev',

        # Priority channels
        'critical-installs': '#critical-installations',
        'failed-installs': '#failed-installations'
    },

    'general': {
        # Default general channel
        'default': DEFAULT_CHANNELS['general'],

        # General purpose channels
        'system-health': '#system-health',
        'monitoring': '#monitoring-alerts',
        'maintenance': '#maintenance-notifications',
        'announcements': '#announcements',

        # Team channels
        'dev-team': '#dev-team',
        'ops-team': '#ops-team',
        'product-team': '#product-team'
    }
}


# Channel aliases for easier reference
CHANNEL_ALIASES = {
    # StoreWorker aliases
    'sw-default': SLACK_CHANNELS['storeWorker']['default'],
    'sw-prod': SLACK_CHANNELS['storeWorker']['production'],
    'sw-staging': SLACK_CHANNELS['storeWorker']['staging'],
    'sw-dev': SLACK_CHANNELS['storeWorker']['development'],

    # RankCollections aliases
    'rc-default': SLACK_CHANNELS['rankCollections']['default'],
    'rc-prod': SLACK_CHANNELS['rankCollections']['production'],
    'rc-staging': SLACK_CHANNELS['rankCollections']['staging'],
    'rc-dev': SLACK_CHANNELS['rankCollections']['development'],

    # StoreBlogs aliases
    'sb-default': SLACK_CHANNELS['storeBlogs']['default'],
    'sb-prod': SLACK_CHANNELS['storeBlogs']['production'],
    'sb-staging': SLACK_CHANNELS['storeBlogs']['staging'],
    'sb-dev': SLACK_CHANNELS['storeBlogs']['development'],

    # Installation aliases
    'inst-default': SLACK_CHANNELS['installation']['default'],
    'inst-prod': SLACK_CHANNELS['installation']['production'],
    'inst-staging': SLACK_CHANNELS['installation']['staging'],
    'inst-dev': SLACK_CHANNELS['installation']['development']
}


def get_channel_id(operation_type: SlackOperationType, project_name: str) -> str:
    """
    Get channel ID for a specific operation and project.

    Args:
        operation_type: Type of operation
        project_name: Name of the project

    Returns:
        str: Channel ID or name
    """
    operation_channels = SLACK_CHANNELS.get(operation_type, {})
    return operation_channels.get(project_name, operation_channels.get('default', '#general'))


def get_all_channels_for_operation(operation_type: SlackOperationType) -> Dict[str, str]:
    """
    Get all channels for a specific operation type.

    Args:
        operation_type: Type of operation

    Returns:
        Dict[str, str]: Dictionary of project names to channel IDs
    """
    return SLACK_CHANNELS.get(operation_type, {})


def add_project_channel(
    operation_type: SlackOperationType,
    project_name: str,
    channel_id: str
) -> None:
    """
    Add a new project channel mapping.

    Args:
        operation_type: Type of operation
        project_name: Name of the project
        channel_id: Slack channel ID or name
    """
    if operation_type not in SLACK_CHANNELS:
        SLACK_CHANNELS[operation_type] = {}

    SLACK_CHANNELS[operation_type][project_name] = channel_id


def remove_project_channel(operation_type: SlackOperationType, project_name: str) -> bool:
    """
    Remove a project channel mapping.

    Args:
        operation_type: Type of operation
        project_name: Name of the project

    Returns:
        bool: True if removed successfully, False if not found
    """
    if operation_type in SLACK_CHANNELS and project_name in SLACK_CHANNELS[operation_type]:
        del SLACK_CHANNELS[operation_type][project_name]
        return True
    return False


def list_all_channels() -> Dict[str, Dict[str, str]]:
    """
    List all configured channels.

    Returns:
        Dict[str, Dict[str, str]]: All channel mappings
    """
    return SLACK_CHANNELS.copy()


# Environment-based channel selection
def get_environment_channel(operation_type: SlackOperationType, environment: str = 'production') -> str:
    """
    Get channel for specific environment.

    Args:
        operation_type: Type of operation
        environment: Environment name (production, staging, development)

    Returns:
        str: Channel ID or name
    """
    return get_channel_id(operation_type, environment)
