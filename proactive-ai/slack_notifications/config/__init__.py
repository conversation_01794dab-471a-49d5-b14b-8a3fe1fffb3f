"""
Slack Configuration Package

This package contains all Slack-related configurations including:
- Channel mappings
- Message templates
- Type definitions
- Template helpers
"""

from .slack_types import (
    SlackOperationType,
    MessageStatus,
    StoreWorkerJobType,
    RankCollectionsJobType,
    StoreBlogsJobType,
    InstallationType,
    SlackMessageData,
    StoreWorkerMessageData,
    RankCollectionsMessageData,
    StoreBlogsMessageData,
    InstallationMessageData,
    SlackMessage,
    NotificationPriority,
    NotificationSettings
)

from .slack_channels import (
    SLACK_CHANNELS,
    CHANNEL_ALIASES,
    get_channel_id,
    get_all_channels_for_operation,
    add_project_channel,
    remove_project_channel,
    list_all_channels,
    get_environment_channel
)

from .slack_templates import (
    SLACK_TEMPLATES,
    STATUS_EMOJIS,
    STATUS_COLORS,
    get_template,
    format_template,
    add_status_emoji,
    get_status_color,
    create_rich_message,
    validate_template,
    list_available_templates,
    get_template_preview
)

__all__ = [
    # Types
    'SlackOperationType',
    'MessageStatus',
    'StoreWorkerJobType',
    'RankCollectionsJobType',
    'StoreBlogsJobType',
    'InstallationType',
    'SlackMessageData',
    'StoreWorkerMessageData',
    'RankCollectionsMessageData',
    'StoreBlogsMessageData',
    'InstallationMessageData',
    'SlackMessage',
    'NotificationPriority',
    'NotificationSettings',
    
    # Channels
    'SLACK_CHANNELS',
    'CHANNEL_ALIASES',
    'get_channel_id',
    'get_all_channels_for_operation',
    'add_project_channel',
    'remove_project_channel',
    'list_all_channels',
    'get_environment_channel',
    
    # Templates
    'SLACK_TEMPLATES',
    'STATUS_EMOJIS',
    'STATUS_COLORS',
    'get_template',
    'format_template',
    'add_status_emoji',
    'get_status_color',
    'create_rich_message',
    'validate_template',
    'list_available_templates',
    'get_template_preview'
]
