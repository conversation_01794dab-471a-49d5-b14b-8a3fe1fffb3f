"""
StoreWorker Slack Templates

Templates for StoreWorker operations including job execution,
workflow deployment, inventory updates, and other store operations.
"""

from typing import Dict, Any, Union
from ..slack_types import SlackMessage


# StoreWorker message templates
STORE_WORKER_TEMPLATES: Dict[str, Union[str, SlackMessage]] = {
    # Default template
    'default': "🏪 StoreWorker: {job_type} for {project_name} - Status: {status}",

    # Job execution templates
    'started': {
        "text": "🚀 StoreWorker Job Started",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🚀 StoreWorker Job Started"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Job Type:* {job_type}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Started:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Job ID:* {job_id}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "StoreWorker job started for {project_name}"
            }
        ]
    },

    'in_progress': "⏳ StoreWorker: {job_type} in progress for {project_name} | Processed: {products_processed} products",

    'completed': {
        "text": "✅ StoreWorker Job Completed Successfully",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "✅ StoreWorker Job Completed"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Job Type:* {job_type}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Completed:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Execution Time:* {execution_time}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Products Processed:* {products_processed}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Success Count:* {success_count}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "StoreWorker job completed for {project_name}"
            }
        ]
    },

    'failed': {
        "text": "❌ StoreWorker Job Failed",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "❌ StoreWorker Job Failed"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Job Type:* {job_type}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Failed:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Error:* {error_message}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#ff0000",
                "fallback": "StoreWorker job failed for {project_name}"
            }
        ]
    },

    # Specific job type templates
    'runJob': "🏃‍♂️ StoreWorker: Running job '{job_type}' for {project_name} - {status}",

    'workflow_deployment': {
        "text": "🚀 Workflow Deployment",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🚀 *Workflow Deployment* for *{project_name}*\n*Workflow:* {workflow_name}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },

    'inventory_update': "📦 StoreWorker: Inventory update for {project_name} - {status} | Updated: {products_processed} products",

    'product_sync': "🔄 StoreWorker: Product sync for {project_name} - {status} | Synced: {products_processed} products",

    'order_processing': "📋 StoreWorker: Order processing for {project_name} - {status} | Processed: {success_count} orders",

    # Warning templates
    'warning': {
        "text": "⚠️ StoreWorker Warning",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "⚠️ *StoreWorker Warning* for *{project_name}*\n*Job Type:* {job_type}\n*Warning:* {error_message}\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ffcc00",
                "fallback": "StoreWorker warning for {project_name}"
            }
        ]
    },

    # Success templates
    'success': "✅ StoreWorker: {job_type} completed successfully for {project_name} | {success_count} items processed",

    # Error templates
    'error': "❌ StoreWorker: {job_type} failed for {project_name} | Error: {error_message}",

    # Cancelled templates
    'cancelled': "🛑 StoreWorker: {job_type} cancelled for {project_name} at {timestamp}",

    # Performance templates
    'performance_report': {
        "text": "📊 StoreWorker Performance Report",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "📊 StoreWorker Performance Report"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Job Type:* {job_type}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Execution Time:* {execution_time}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Products Processed:* {products_processed}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Success Rate:* {success_count}/{products_processed}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Error Count:* {error_count}"
                    }
                ]
            }
        ]
    },

    # Batch operation templates
    'batch_started': "🔄 StoreWorker: Batch operation started for {project_name} | {products_processed} items queued",

    'batch_completed': "✅ StoreWorker: Batch operation completed for {project_name} | {success_count}/{products_processed} successful",

    # Store-specific templates
    'store_sync': "🏪 StoreWorker: Store sync for {store_name} ({project_name}) - {status}",

    'store_health_check': "🩺 StoreWorker: Health check for {store_name} ({project_name}) - {status}",

    # Workflow templates
    'workflow_started': "🚀 StoreWorker: Workflow '{workflow_name}' started for {project_name}",

    'workflow_completed': "✅ StoreWorker: Workflow '{workflow_name}' completed for {project_name} in {execution_time}",

    'workflow_failed': "❌ StoreWorker: Workflow '{workflow_name}' failed for {project_name} | Error: {error_message}",

    # Maintenance templates
    'maintenance_started': "🔧 StoreWorker: Maintenance started for {project_name} - {job_type}",

    'maintenance_completed': "✅ StoreWorker: Maintenance completed for {project_name} - {job_type}",

    # Alert templates
    'critical_alert': {
        "text": "🚨 CRITICAL: StoreWorker Alert",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🚨 CRITICAL ALERT"
                }
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Project:* {project_name}\n*Job Type:* {job_type}\n*Issue:* {error_message}\n*Time:* {timestamp}\n\n<!channel> Immediate attention required!"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ff0000",
                "fallback": "Critical StoreWorker alert for {project_name}"
            }
        ]
    }
}


# Template helper functions specific to StoreWorker
def get_job_emoji(job_type: str) -> str:
    """Get emoji for specific job type."""
    job_emojis = {
        'runJob': '🏃‍♂️',
        'workflow_deployment': '🚀',
        'inventory_update': '📦',
        'product_sync': '🔄',
        'order_processing': '📋',
        'store_sync': '🏪',
        'maintenance': '🔧',
        'health_check': '🩺'
    }
    return job_emojis.get(job_type, '⚙️')


def format_execution_time(seconds: int) -> str:
    """Format execution time in human readable format."""
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds}s"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{hours}h {remaining_minutes}m"


def create_progress_bar(current: int, total: int, width: int = 20) -> str:
    """Create a text-based progress bar."""
    if total == 0:
        return "█" * width

    progress = current / total
    filled = int(width * progress)
    bar = "█" * filled + "░" * (width - filled)
    percentage = int(progress * 100)
    return f"{bar} {percentage}% ({current}/{total})"
