"""
StoreBlogs Slack Templates

Templates for StoreBlogs operations including article creation,
content generation, SEO optimization, and blog publishing.
"""

from typing import Dict, Any, Union
from ..slack_types import SlackMessage


# StoreBlogs message templates
STORE_BLOGS_TEMPLATES: Dict[str, Union[str, SlackMessage]] = {
    # Default template
    'default': "📝 StoreBlogs: {article_title} for {project_name} - Status: {status}",
    
    # Article creation templates
    'started': {
        "text": "🚀 Article Creation Started",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🚀 Article Creation Started"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Article:* {article_title}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Started:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Author:* {author}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "Article creation started for {project_name}"
            }
        ]
    },
    
    'in_progress': "⏳ StoreBlogs: Writing '{article_title}' for {project_name} | Target: {word_count} words",
    
    'completed': {
        "text": "✅ Article Created Successfully",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "✅ Article Created Successfully"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Article:* {article_title}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Completed:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Word Count:* {word_count}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*SEO Score:* {seo_score}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Author:* {author}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "Article created successfully for {project_name}"
            }
        ]
    },
    
    'failed': {
        "text": "❌ Article Creation Failed",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "❌ Article Creation Failed"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Article:* {article_title}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Failed:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Error:* {error_message}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#ff0000",
                "fallback": "Article creation failed for {project_name}"
            }
        ]
    },
    
    # Specific operation templates
    'article_creation_job': "📝 StoreBlogs: Creating article '{article_title}' for {project_name} - {status}",
    
    'content_generation': {
        "text": "✍️ Content Generation",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "✍️ *Content Generation* for *{project_name}*\n*Article:* {article_title}\n*Target Words:* {word_count}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    'seo_optimization': {
        "text": "🔍 SEO Optimization",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🔍 *SEO Optimization* for *{project_name}*\n*Article:* {article_title}\n*SEO Score:* {seo_score}\n*Keywords:* {target_keywords}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    'blog_publishing': {
        "text": "🚀 Blog Publishing",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🚀 *Blog Publishing* for *{project_name}*\n*Article:* {article_title}\n*Publication Status:* {publication_status}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    # Content type templates
    'seo_article': "🔍 StoreBlogs: SEO article '{article_title}' for {project_name} - {status} | Score: {seo_score}",
    
    'product_review': "⭐ StoreBlogs: Product review '{article_title}' for {project_name} - {status} | {word_count} words",
    
    'how_to_guide': "📚 StoreBlogs: How-to guide '{article_title}' for {project_name} - {status} | {word_count} words",
    
    'industry_news': "📰 StoreBlogs: Industry news '{article_title}' for {project_name} - {status} | {word_count} words",
    
    # Performance templates
    'performance_report': {
        "text": "📊 StoreBlogs Performance Report",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "📊 StoreBlogs Performance Report"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Article:* {article_title}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Word Count:* {word_count}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*SEO Score:* {seo_score}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Target Keywords:* {target_keywords}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Author:* {author}"
                    }
                ]
            }
        ]
    },
    
    # Warning templates
    'warning': {
        "text": "⚠️ StoreBlogs Warning",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "⚠️ *StoreBlogs Warning* for *{project_name}*\n*Article:* {article_title}\n*Warning:* {error_message}\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ffcc00",
                "fallback": "StoreBlogs warning for {project_name}"
            }
        ]
    },
    
    # Success templates
    'success': "✅ StoreBlogs: '{article_title}' completed successfully for {project_name} | {word_count} words, SEO: {seo_score}",
    
    # Error templates
    'error': "❌ StoreBlogs: '{article_title}' failed for {project_name} | Error: {error_message}",
    
    # Publishing templates
    'published': {
        "text": "🎉 Article Published",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🎉 *Article Published* for *{project_name}*\n*Title:* {article_title}\n*Word Count:* {word_count}\n*SEO Score:* {seo_score}\n*Published:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "Article published for {project_name}"
            }
        ]
    },
    
    'draft_saved': "💾 StoreBlogs: Draft saved for '{article_title}' ({project_name}) | {word_count} words",
    
    'review_required': {
        "text": "👀 Review Required",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "👀 *Review Required* for *{project_name}*\n*Article:* {article_title}\n*Word Count:* {word_count}\n*SEO Score:* {seo_score}\n*Author:* {author}\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ffcc00",
                "fallback": "Review required for {article_title}"
            }
        ]
    },
    
    # SEO templates
    'seo_optimized': "🎯 StoreBlogs: SEO optimization completed for '{article_title}' ({project_name}) | Score: {seo_score}",
    
    'keyword_research': "🔍 StoreBlogs: Keyword research for '{article_title}' ({project_name}) | {target_keywords} keywords identified",
    
    'content_audit': "📋 StoreBlogs: Content audit for '{article_title}' ({project_name}) | SEO Score: {seo_score}",
    
    # Batch operation templates
    'batch_started': "🔄 StoreBlogs: Batch article creation started for {project_name} | {word_count} articles queued",
    
    'batch_completed': "✅ StoreBlogs: Batch operation completed for {project_name} | {word_count} articles created",
    
    # Quality check templates
    'quality_check': {
        "text": "🔍 Content Quality Check",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🔍 *Quality Check* for *{article_title}* ({project_name})\n*Word Count:* {word_count}\n*SEO Score:* {seo_score}\n*Readability:* Good\n*Status:* {status}"
                }
            }
        ]
    },
    
    # Alert templates
    'low_seo_alert': {
        "text": "⚠️ Low SEO Score Alert",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "⚠️ *Low SEO Score Alert* for *{article_title}* ({project_name})\n*Current Score:* {seo_score}\n*Threshold:* Below 70\n*Action Required:* Review keywords and content\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ffcc00",
                "fallback": "Low SEO score alert for {article_title}"
            }
        ]
    },
    
    'high_quality_alert': {
        "text": "🎉 High Quality Content Alert",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🎉 *Excellent Content Quality* for *{article_title}* ({project_name})\n*Word Count:* {word_count}\n*SEO Score:* {seo_score}\n*Author:* {author}\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "High quality content alert for {article_title}"
            }
        ]
    }
}


# Template helper functions specific to StoreBlogs
def get_content_emoji(content_type: str) -> str:
    """Get emoji for specific content type."""
    content_emojis = {
        'seo_article': '🔍',
        'product_review': '⭐',
        'how_to_guide': '📚',
        'industry_news': '📰',
        'blog_post': '📝',
        'tutorial': '🎓',
        'case_study': '📊'
    }
    return content_emojis.get(content_type, '📝')


def format_seo_score(score: float) -> str:
    """Format SEO score with appropriate emoji."""
    if score >= 90:
        return f"🟢 {score:.1f} (Excellent)"
    elif score >= 70:
        return f"🟡 {score:.1f} (Good)"
    elif score >= 50:
        return f"🟠 {score:.1f} (Average)"
    else:
        return f"🔴 {score:.1f} (Needs Improvement)"


def format_word_count(count: int) -> str:
    """Format word count with appropriate description."""
    if count >= 2000:
        return f"{count:,} words (Long-form)"
    elif count >= 1000:
        return f"{count:,} words (Medium-form)"
    elif count >= 500:
        return f"{count:,} words (Short-form)"
    else:
        return f"{count:,} words (Brief)"


def create_keyword_list(keywords: list, max_display: int = 3) -> str:
    """Create a formatted list of keywords for display."""
    if not keywords:
        return "No keywords"
    
    if isinstance(keywords, str):
        keywords = [keywords]
    
    if len(keywords) <= max_display:
        return ", ".join(keywords)
    else:
        displayed = ", ".join(keywords[:max_display])
        remaining = len(keywords) - max_display
        return f"{displayed} (+{remaining} more)"


def get_publication_status_emoji(status: str) -> str:
    """Get emoji for publication status."""
    status_emojis = {
        'draft': '📝',
        'review': '👀',
        'approved': '✅',
        'published': '🎉',
        'scheduled': '⏰',
        'archived': '📦'
    }
    return status_emojis.get(status.lower(), '📄')
