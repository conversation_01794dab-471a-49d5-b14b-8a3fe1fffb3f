"""
Installation Slack Templates

Templates for Installation operations including new user installations,
upgrades, migrations, and configuration updates.
"""

from typing import Dict, <PERSON>, <PERSON>
from ..slack_types import SlackMessage


# Installation message templates
INSTALLATION_TEMPLATES: Dict[str, Union[str, SlackMessage]] = {
    # Default template
    'default': "🔧 Installation: {installation_type} for {project_name} - Status: {status}",
    
    # Installation process templates
    'started': {
        "text": "🚀 Installation Started",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🚀 Installation Started"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Type:* {installation_type}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Started:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Version:* {version}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "Installation started for {project_name}"
            }
        ]
    },
    
    'in_progress': "⏳ Installation: {installation_type} in progress for {project_name} | Version: {version}",
    
    'completed': {
        "text": "✅ Installation Completed Successfully",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "✅ Installation Completed Successfully"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Type:* {installation_type}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Completed:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Version:* {version}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*User:* {user_info}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Features:* {features_enabled}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "Installation completed successfully for {project_name}"
            }
        ]
    },
    
    'failed': {
        "text": "❌ Installation Failed",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "❌ Installation Failed"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Type:* {installation_type}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Failed:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Error:* {error_message}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#ff0000",
                "fallback": "Installation failed for {project_name}"
            }
        ]
    },
    
    # Specific installation type templates
    'new_user_installation': {
        "text": "👋 New User Installation",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "👋 *New User Installation* for *{project_name}*\n*User:* {user_info}\n*Version:* {version}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    'upgrade_installation': {
        "text": "⬆️ System Upgrade",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "⬆️ *System Upgrade* for *{project_name}*\n*From:* {previous_version}\n*To:* {version}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    'migration_installation': {
        "text": "🔄 Data Migration",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🔄 *Data Migration* for *{project_name}*\n*From:* {previous_version}\n*To:* {version}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    'configuration_update': {
        "text": "⚙️ Configuration Update",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "⚙️ *Configuration Update* for *{project_name}*\n*Changes:* {configuration_changes}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    # User onboarding templates
    'user_onboarding': {
        "text": "🎉 New User Onboarded",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🎉 New User Onboarded"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*User:* {user_info}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Onboarded:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Features Enabled:* {features_enabled}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "New user onboarded for {project_name}"
            }
        ]
    },
    
    # System health templates
    'health_check': {
        "text": "🩺 System Health Check",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🩺 *System Health Check* for *{project_name}*\n*Version:* {version}\n*Status:* {status}\n*Features:* {features_enabled}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    'system_ready': {
        "text": "✅ System Ready",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "✅ *System Ready* for *{project_name}*\n*Version:* {version}\n*All systems operational*\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "System ready for {project_name}"
            }
        ]
    },
    
    # Warning templates
    'warning': {
        "text": "⚠️ Installation Warning",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "⚠️ *Installation Warning* for *{project_name}*\n*Type:* {installation_type}\n*Warning:* {error_message}\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ffcc00",
                "fallback": "Installation warning for {project_name}"
            }
        ]
    },
    
    # Success templates
    'success': "✅ Installation: {installation_type} completed successfully for {project_name} | Version: {version}",
    
    # Error templates
    'error': "❌ Installation: {installation_type} failed for {project_name} | Error: {error_message}",
    
    # Rollback templates
    'rollback_started': {
        "text": "🔄 Rollback Started",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🔄 *Rollback Started* for *{project_name}*\n*From:* {version}\n*To:* {previous_version}\n*Reason:* {error_message}\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ffcc00",
                "fallback": "Rollback started for {project_name}"
            }
        ]
    },
    
    'rollback_completed': {
        "text": "✅ Rollback Completed",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "✅ *Rollback Completed* for *{project_name}*\n*Restored to:* {previous_version}\n*System stable*\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "Rollback completed for {project_name}"
            }
        ]
    },
    
    # Maintenance templates
    'maintenance_mode': {
        "text": "🔧 Maintenance Mode",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🔧 *Maintenance Mode* for *{project_name}*\n*Type:* {installation_type}\n*Expected Duration:* {configuration_changes}\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ffcc00",
                "fallback": "Maintenance mode for {project_name}"
            }
        ]
    },
    
    'maintenance_completed': "✅ Installation: Maintenance completed for {project_name} | System restored",
    
    # Feature templates
    'feature_enabled': "🎉 Installation: New feature '{features_enabled}' enabled for {project_name}",
    
    'feature_disabled': "🔒 Installation: Feature '{features_enabled}' disabled for {project_name}",
    
    # Performance templates
    'performance_report': {
        "text": "📊 Installation Performance Report",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "📊 Installation Performance Report"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Type:* {installation_type}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Version:* {version}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Features:* {features_enabled}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*User:* {user_info}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Completed:* {timestamp}"
                    }
                ]
            }
        ]
    },
    
    # Alert templates
    'critical_failure': {
        "text": "🚨 CRITICAL: Installation Failure",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🚨 CRITICAL INSTALLATION FAILURE"
                }
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Project:* {project_name}\n*Type:* {installation_type}\n*Error:* {error_message}\n*Time:* {timestamp}\n\n<!channel> Immediate attention required!"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ff0000",
                "fallback": "Critical installation failure for {project_name}"
            }
        ]
    },
    
    'security_alert': {
        "text": "🔒 Security Alert",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🔒 *Security Alert* for *{project_name}*\n*Type:* {installation_type}\n*Version:* {version}\n*Security Update Required*\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ff0000",
                "fallback": "Security alert for {project_name}"
            }
        ]
    }
}


# Template helper functions specific to Installation
def get_installation_emoji(installation_type: str) -> str:
    """Get emoji for specific installation type."""
    installation_emojis = {
        'new_user_installation': '👋',
        'upgrade_installation': '⬆️',
        'migration_installation': '🔄',
        'configuration_update': '⚙️',
        'maintenance': '🔧',
        'rollback': '↩️',
        'security_update': '🔒'
    }
    return installation_emojis.get(installation_type, '🔧')


def format_version_info(current_version: str, previous_version: str = None) -> str:
    """Format version information for display."""
    if previous_version:
        return f"{previous_version} → {current_version}"
    return current_version


def format_user_info(user_data: dict) -> str:
    """Format user information for display."""
    if isinstance(user_data, dict):
        name = user_data.get('name', 'Unknown')
        email = user_data.get('email', '')
        if email:
            return f"{name} ({email})"
        return name
    return str(user_data)


def format_features_list(features: list, max_display: int = 3) -> str:
    """Format features list for display."""
    if not features:
        return "No features"
    
    if isinstance(features, str):
        return features
    
    if len(features) <= max_display:
        return ", ".join(features)
    else:
        displayed = ", ".join(features[:max_display])
        remaining = len(features) - max_display
        return f"{displayed} (+{remaining} more)"


def get_status_priority(status: str) -> int:
    """Get priority level for status (higher number = higher priority)."""
    priority_map = {
        'critical_failure': 5,
        'failed': 4,
        'warning': 3,
        'in_progress': 2,
        'completed': 1,
        'success': 1
    }
    return priority_map.get(status, 2)
