"""
RankCollections Slack Templates

Templates for RankCollections operations including collection creation,
keyword analysis, ranking updates, and competitor analysis.
"""

from typing import Dict, Any, Union
from ..slack_types import SlackMessage


# RankCollections message templates
RANK_COLLECTIONS_TEMPLATES: Dict[str, Union[str, SlackMessage]] = {
    # Default template
    'default': "📊 RankCollections: {collection_name} for {project_name} - Status: {status}",
    
    # Collection creation templates
    'started': {
        "text": "🚀 Collection Creation Started",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🚀 Collection Creation Started"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Collection:* {collection_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Started:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Keywords:* {keywords_count}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "Collection creation started for {project_name}"
            }
        ]
    },
    
    'in_progress': "⏳ RankCollections: Creating '{collection_name}' for {project_name} | Processing {keywords_count} keywords",
    
    'completed': {
        "text": "✅ Collection Created Successfully",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "✅ Collection Created Successfully"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Collection:* {collection_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Completed:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Processing Time:* {processing_time}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Keywords Processed:* {keywords_count}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Products Found:* {products_count}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Ranking Score:* {ranking_score}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "Collection created successfully for {project_name}"
            }
        ]
    },
    
    'failed': {
        "text": "❌ Collection Creation Failed",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "❌ Collection Creation Failed"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Collection:* {collection_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Failed:* {timestamp}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Error:* {error_message}"
                    }
                ]
            }
        ],
        "attachments": [
            {
                "color": "#ff0000",
                "fallback": "Collection creation failed for {project_name}"
            }
        ]
    },
    
    # Specific operation templates
    'collection_creation_job': "📊 RankCollections: Creating collection '{collection_name}' for {project_name} - {status}",
    
    'keyword_analysis': {
        "text": "🔍 Keyword Analysis",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🔍 *Keyword Analysis* for *{project_name}*\n*Collection:* {collection_name}\n*Keywords Analyzed:* {keywords_count}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    'ranking_update': "📈 RankCollections: Ranking update for '{collection_name}' ({project_name}) - {status} | Score: {ranking_score}",
    
    'competitor_analysis': {
        "text": "🎯 Competitor Analysis",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🎯 *Competitor Analysis* for *{project_name}*\n*Collection:* {collection_name}\n*Competitors Analyzed:* {competitor_count}\n*Status:* {status}\n*Time:* {timestamp}"
                }
            }
        ]
    },
    
    # Performance templates
    'performance_report': {
        "text": "📊 RankCollections Performance Report",
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "📊 RankCollections Performance Report"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Project:* {project_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Collection:* {collection_name}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Processing Time:* {processing_time}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Keywords Processed:* {keywords_count}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Products Found:* {products_count}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Ranking Score:* {ranking_score}"
                    }
                ]
            }
        ]
    },
    
    # Warning templates
    'warning': {
        "text": "⚠️ RankCollections Warning",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "⚠️ *RankCollections Warning* for *{project_name}*\n*Collection:* {collection_name}\n*Warning:* {error_message}\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ffcc00",
                "fallback": "RankCollections warning for {project_name}"
            }
        ]
    },
    
    # Success templates
    'success': "✅ RankCollections: '{collection_name}' completed successfully for {project_name} | {products_count} products ranked",
    
    # Error templates
    'error': "❌ RankCollections: '{collection_name}' failed for {project_name} | Error: {error_message}",
    
    # Collection-specific templates
    'trending_collection': "🔥 RankCollections: Trending collection '{collection_name}' created for {project_name} | {products_count} trending products",
    
    'seasonal_collection': "🌟 RankCollections: Seasonal collection '{collection_name}' created for {project_name} | {products_count} seasonal products",
    
    'competitor_collection': "🎯 RankCollections: Competitor-based collection '{collection_name}' created for {project_name} | {competitor_count} competitors analyzed",
    
    # Keyword templates
    'keyword_expansion': "🔍 RankCollections: Keyword expansion for '{collection_name}' ({project_name}) | {keywords_count} keywords expanded",
    
    'keyword_filtering': "🎯 RankCollections: Keyword filtering for '{collection_name}' ({project_name}) | {keywords_count} keywords filtered",
    
    'keyword_ranking': "📈 RankCollections: Keyword ranking update for '{collection_name}' ({project_name}) | Score: {ranking_score}",
    
    # Product templates
    'product_matching': "🔗 RankCollections: Product matching for '{collection_name}' ({project_name}) | {products_count} products matched",
    
    'product_scoring': "⭐ RankCollections: Product scoring for '{collection_name}' ({project_name}) | Average score: {ranking_score}",
    
    # Batch operation templates
    'batch_started': "🔄 RankCollections: Batch collection creation started for {project_name} | {keywords_count} collections queued",
    
    'batch_completed': "✅ RankCollections: Batch operation completed for {project_name} | {products_count} collections created",
    
    # Quality check templates
    'quality_check': {
        "text": "🔍 Collection Quality Check",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🔍 *Quality Check* for *{collection_name}* ({project_name})\n*Keywords Quality:* {keywords_count} keywords\n*Products Quality:* {products_count} products\n*Ranking Score:* {ranking_score}\n*Status:* {status}"
                }
            }
        ]
    },
    
    # Alert templates
    'low_ranking_alert': {
        "text": "⚠️ Low Ranking Alert",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "⚠️ *Low Ranking Alert* for *{collection_name}* ({project_name})\n*Current Score:* {ranking_score}\n*Threshold:* Below expected\n*Action Required:* Review keywords and products\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#ffcc00",
                "fallback": "Low ranking alert for {collection_name}"
            }
        ]
    },
    
    'high_performance_alert': {
        "text": "🎉 High Performance Alert",
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "🎉 *Excellent Performance* for *{collection_name}* ({project_name})\n*Ranking Score:* {ranking_score}\n*Products:* {products_count}\n*Keywords:* {keywords_count}\n*Time:* {timestamp}"
                }
            }
        ],
        "attachments": [
            {
                "color": "#36a64f",
                "fallback": "High performance alert for {collection_name}"
            }
        ]
    }
}


# Template helper functions specific to RankCollections
def get_collection_emoji(collection_type: str) -> str:
    """Get emoji for specific collection type."""
    collection_emojis = {
        'trending': '🔥',
        'seasonal': '🌟',
        'competitor': '🎯',
        'keyword': '🔍',
        'product': '📦',
        'ranking': '📈',
        'quality': '⭐'
    }
    return collection_emojis.get(collection_type, '📊')


def format_ranking_score(score: float) -> str:
    """Format ranking score with appropriate emoji."""
    if score >= 0.9:
        return f"🟢 {score:.2f} (Excellent)"
    elif score >= 0.7:
        return f"🟡 {score:.2f} (Good)"
    elif score >= 0.5:
        return f"🟠 {score:.2f} (Average)"
    else:
        return f"🔴 {score:.2f} (Needs Improvement)"


def create_keyword_summary(keywords: list, max_display: int = 5) -> str:
    """Create a summary of keywords for display."""
    if not keywords:
        return "No keywords"
    
    if len(keywords) <= max_display:
        return ", ".join(keywords)
    else:
        displayed = ", ".join(keywords[:max_display])
        remaining = len(keywords) - max_display
        return f"{displayed} and {remaining} more..."


def format_processing_time(seconds: int) -> str:
    """Format processing time in human readable format."""
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds}s"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{hours}h {remaining_minutes}m"
