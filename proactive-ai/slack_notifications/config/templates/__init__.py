"""
Slack Templates Package

This package contains all Slack message templates for different operations:
- StoreWorker templates
- RankCollections templates
- StoreBlogs templates
- Installation templates
"""

from .storeWorker_templates import (
    STORE_WORKER_TEMPLATES,
    get_job_emoji,
    format_execution_time,
    create_progress_bar
)

from .rankCollections_templates import (
    RANK_COLLECTIONS_TEMPLATES,
    get_collection_emoji,
    format_ranking_score,
    create_keyword_summary,
    format_processing_time
)

from .storeBlogs_templates import (
    STORE_BLOGS_TEMPLATES,
    get_content_emoji,
    format_seo_score,
    format_word_count,
    create_keyword_list,
    get_publication_status_emoji
)

from .installation_templates import (
    INSTALLATION_TEMPLATES,
    get_installation_emoji,
    format_version_info,
    format_user_info,
    format_features_list,
    get_status_priority
)

__all__ = [
    # Templates
    'STORE_WORKER_TEMPLATES',
    'RANK_COLLECTIONS_TEMPLATES',
    'STORE_BLOGS_TEMPLATES',
    'INSTALLATION_TEMPLATES',
    
    # StoreWorker helpers
    'get_job_emoji',
    'format_execution_time',
    'create_progress_bar',
    
    # RankCollections helpers
    'get_collection_emoji',
    'format_ranking_score',
    'create_keyword_summary',
    'format_processing_time',
    
    # StoreBlogs helpers
    'get_content_emoji',
    'format_seo_score',
    'format_word_count',
    'create_keyword_list',
    'get_publication_status_emoji',
    
    # Installation helpers
    'get_installation_emoji',
    'format_version_info',
    'format_user_info',
    'format_features_list',
    'get_status_priority'
]
