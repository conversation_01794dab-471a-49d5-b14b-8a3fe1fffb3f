"""
Slack Templates Configuration

This module defines message templates for different operations and statuses.
Templates support string formatting with message data.
"""

from typing import Dict, Any, Union
from .slack_types import SlackMessage, TemplateMapping

# Import specific templates
from .templates.storeWorker_templates import STORE_WORKER_TEMPLATES
from .templates.rankCollections_templates import RANK_COLLECTIONS_TEMPLATES
from .templates.storeBlogs_templates import STORE_BLOGS_TEMPLATES
from .templates.installation_templates import INSTALLATION_TEMPLATES


# Main template mapping
SLACK_TEMPLATES: TemplateMapping = {
    'storeWorker': STORE_WORKER_TEMPLATES,
    'rankCollections': RANK_COLLECTIONS_TEMPLATES,
    'storeBlogs': STORE_BLOGS_TEMPLATES,
    'installation': INSTALLATION_TEMPLATES,
    'general': {
        'default': "🔔 General notification: {message}",
        'info': "ℹ️ Info: {message}",
        'warning': "⚠️ Warning: {message}",
        'error': "❌ Error: {message}",
        'success': "✅ Success: {message}"
    }
}


# Common emoji mappings for status
STATUS_EMOJIS = {
    'started': '🚀',
    'in_progress': '⏳',
    'completed': '✅',
    'success': '✅',
    'failed': '❌',
    'error': '❌',
    'warning': '⚠️',
    'cancelled': '🛑',
    'pending': '⏸️',
    'queued': '📋'
}


# Common color mappings for attachments
STATUS_COLORS = {
    'started': '#36a64f',      # Green
    'in_progress': '#ffcc00',  # Yellow
    'completed': '#36a64f',    # Green
    'success': '#36a64f',      # Green
    'failed': '#ff0000',       # Red
    'error': '#ff0000',        # Red
    'warning': '#ffcc00',      # Yellow
    'cancelled': '#808080',    # Gray
    'pending': '#0099cc',      # Blue
    'queued': '#9966cc'        # Purple
}


def get_template(operation_type: str, status: str = 'default') -> Union[str, Dict[str, Any], None]:
    """
    Get template for specific operation and status.

    Args:
        operation_type: Type of operation
        status: Status of the operation

    Returns:
        Template string or dictionary, None if not found
    """
    operation_templates = SLACK_TEMPLATES.get(operation_type, {})
    return operation_templates.get(status, operation_templates.get('default'))


def format_template(template: Union[str, Dict[str, Any]], data: Dict[str, Any]) -> Union[str, Dict[str, Any]]:
    """
    Format template with provided data.

    Args:
        template: Template to format
        data: Data to use for formatting

    Returns:
        Formatted template
    """
    if isinstance(template, str):
        return template.format(**data)
    elif isinstance(template, dict):
        formatted = {}
        for key, value in template.items():
            if isinstance(value, str):
                formatted[key] = value.format(**data)
            elif isinstance(value, list):
                formatted[key] = [
                    item.format(**data) if isinstance(item, str) else item
                    for item in value
                ]
            else:
                formatted[key] = value
        return formatted
    return template


def add_status_emoji(text: str, status: str) -> str:
    """
    Add status emoji to text.

    Args:
        text: Original text
        status: Status to get emoji for

    Returns:
        Text with emoji prepended
    """
    emoji = STATUS_EMOJIS.get(status, '🔔')
    return f"{emoji} {text}"


def get_status_color(status: str) -> str:
    """
    Get color for status.

    Args:
        status: Status to get color for

    Returns:
        Hex color code
    """
    return STATUS_COLORS.get(status, '#808080')


# Rich message templates with blocks
def create_rich_message(
    title: str,
    status: str,
    details: Dict[str, Any],
    footer: str = None
) -> SlackMessage:
    """
    Create a rich Slack message with blocks.

    Args:
        title: Message title
        status: Operation status
        details: Additional details to display
        footer: Optional footer text

    Returns:
        SlackMessage: Formatted Slack message
    """
    emoji = STATUS_EMOJIS.get(status, '🔔')
    color = STATUS_COLORS.get(status, '#808080')

    blocks = [
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": f"{emoji} {title}"
            }
        },
        {
            "type": "section",
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": f"*Status:* {status.title()}"
                },
                {
                    "type": "mrkdwn",
                    "text": f"*Time:* {details.get('timestamp', 'N/A')}"
                }
            ]
        }
    ]

    # Add details section if provided
    if details:
        detail_fields = []
        for key, value in details.items():
            if key != 'timestamp':
                detail_fields.append({
                    "type": "mrkdwn",
                    "text": f"*{key.replace('_', ' ').title()}:* {value}"
                })

        if detail_fields:
            # Split fields into chunks of 10 (Slack limit)
            for i in range(0, len(detail_fields), 10):
                blocks.append({
                    "type": "section",
                    "fields": detail_fields[i:i+10]
                })

    # Add footer if provided
    if footer:
        blocks.append({
            "type": "context",
            "elements": [
                {
                    "type": "mrkdwn",
                    "text": footer
                }
            ]
        })

    return {
        "text": f"{emoji} {title}",
        "blocks": blocks,
        "attachments": [
            {
                "color": color,
                "fallback": f"{title} - {status}"
            }
        ]
    }


# Template validation
def validate_template(template: Union[str, Dict[str, Any]], required_fields: list) -> bool:
    """
    Validate that template can be formatted with required fields.

    Args:
        template: Template to validate
        required_fields: List of required field names

    Returns:
        bool: True if template is valid, False otherwise
    """
    try:
        test_data = {field: f"test_{field}" for field in required_fields}
        format_template(template, test_data)
        return True
    except (KeyError, ValueError):
        return False


# Template utilities
def list_available_templates() -> Dict[str, list]:
    """
    List all available templates by operation type.

    Returns:
        Dict[str, list]: Dictionary of operation types to template names
    """
    result = {}
    for operation_type, templates in SLACK_TEMPLATES.items():
        result[operation_type] = list(templates.keys())
    return result


def get_template_preview(operation_type: str, status: str, sample_data: Dict[str, Any] = None) -> str:
    """
    Get a preview of how a template will look with sample data.

    Args:
        operation_type: Type of operation
        status: Status of the operation
        sample_data: Sample data for preview

    Returns:
        str: Preview of formatted template
    """
    template = get_template(operation_type, status)
    if not template:
        return "Template not found"

    if sample_data is None:
        sample_data = {
            'project_name': 'sample-project',
            'timestamp': '2024-01-01 12:00:00',
            'status': status,
            'job_type': 'sample_job',
            'details': 'Sample details'
        }

    try:
        formatted = format_template(template, sample_data)
        if isinstance(formatted, dict):
            return str(formatted)
        return formatted
    except Exception as e:
        return f"Error formatting template: {str(e)}"
