"""
Slack Types and Enums

This module defines types and enums for Slack operations and message data.
"""

from typing import Dict, Any, Literal, TypedDict, Optional, List, Union
from enum import Enum


# Operation Types
SlackOperationType = Literal[
    'storeWorker',
    'rankCollections', 
    'storeBlogs',
    'installation',
    'general'
]


# Message Status Types
class MessageStatus(Enum):
    """Common message status types."""
    STARTED = "started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    WARNING = "warning"
    SUCCESS = "success"
    ERROR = "error"
    CANCELLED = "cancelled"


# StoreWorker specific types
class StoreWorkerJobType(Enum):
    """StoreWorker job types."""
    RUN_JOB = "runJob"
    WORKFLOW_DEPLOYMENT = "workflow_deployment"
    INVENTORY_UPDATE = "inventory_update"
    PRODUCT_SYNC = "product_sync"
    ORDER_PROCESSING = "order_processing"


# RankCollections specific types
class RankCollectionsJobType(Enum):
    """RankCollections job types."""
    COLLECTION_CREATION = "collection_creation_job"
    KEYWORD_ANALYSIS = "keyword_analysis"
    RANKING_UPDATE = "ranking_update"
    COMPETITOR_ANALYSIS = "competitor_analysis"


# StoreBlogs specific types
class StoreBlogsJobType(Enum):
    """StoreBlogs job types."""
    ARTICLE_CREATION = "article_creation_job"
    CONTENT_GENERATION = "content_generation"
    SEO_OPTIMIZATION = "seo_optimization"
    BLOG_PUBLISHING = "blog_publishing"


# Installation specific types
class InstallationType(Enum):
    """Installation types."""
    NEW_USER = "new_user_installation"
    UPGRADE = "upgrade_installation"
    MIGRATION = "migration_installation"
    CONFIGURATION = "configuration_update"


# Base message data structure
class SlackMessageData(TypedDict, total=False):
    """Base structure for Slack message data."""
    status: str
    project_name: str
    timestamp: Optional[str]
    user_id: Optional[str]
    error_message: Optional[str]
    details: Optional[Dict[str, Any]]


# StoreWorker message data
class StoreWorkerMessageData(SlackMessageData, total=False):
    """StoreWorker specific message data."""
    job_type: str
    job_id: Optional[str]
    workflow_name: Optional[str]
    store_name: Optional[str]
    products_processed: Optional[int]
    execution_time: Optional[str]
    success_count: Optional[int]
    error_count: Optional[int]


# RankCollections message data
class RankCollectionsMessageData(SlackMessageData, total=False):
    """RankCollections specific message data."""
    collection_name: str
    collection_id: Optional[str]
    keywords_count: Optional[int]
    products_count: Optional[int]
    ranking_score: Optional[float]
    competitor_count: Optional[int]
    processing_time: Optional[str]


# StoreBlogs message data
class StoreBlogsMessageData(SlackMessageData, total=False):
    """StoreBlogs specific message data."""
    article_title: str
    article_id: Optional[str]
    word_count: Optional[int]
    seo_score: Optional[float]
    target_keywords: Optional[List[str]]
    publication_status: Optional[str]
    author: Optional[str]


# Installation message data
class InstallationMessageData(SlackMessageData, total=False):
    """Installation specific message data."""
    user_info: Dict[str, Any]
    installation_type: str
    version: Optional[str]
    previous_version: Optional[str]
    features_enabled: Optional[List[str]]
    configuration_changes: Optional[Dict[str, Any]]


# Slack message format types
class SlackBlock(TypedDict, total=False):
    """Slack block structure."""
    type: str
    text: Optional[Dict[str, str]]
    elements: Optional[List[Dict[str, Any]]]
    fields: Optional[List[Dict[str, str]]]


class SlackAttachment(TypedDict, total=False):
    """Slack attachment structure."""
    color: str
    title: Optional[str]
    text: Optional[str]
    fields: Optional[List[Dict[str, str]]]
    footer: Optional[str]
    ts: Optional[int]


class SlackMessage(TypedDict, total=False):
    """Complete Slack message structure."""
    text: str
    blocks: Optional[List[SlackBlock]]
    attachments: Optional[List[SlackAttachment]]


# Project configuration types
class ProjectConfig(TypedDict):
    """Project configuration structure."""
    name: str
    channels: Dict[SlackOperationType, str]
    default_channel: str
    notification_settings: Dict[str, bool]


# Channel mapping types
ChannelMapping = Dict[SlackOperationType, Dict[str, str]]
TemplateMapping = Dict[SlackOperationType, Dict[str, Union[str, SlackMessage]]]


# Priority levels for notifications
class NotificationPriority(Enum):
    """Notification priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


# Notification settings
class NotificationSettings(TypedDict, total=False):
    """Notification settings structure."""
    enabled: bool
    priority: NotificationPriority
    mention_users: Optional[List[str]]
    mention_channel: bool
    thread_replies: bool
    emoji_reactions: Optional[List[str]]


# Error types for better error handling
class SlackError(Exception):
    """Base Slack error class."""
    pass


class SlackConfigurationError(SlackError):
    """Slack configuration error."""
    pass


class SlackTemplateError(SlackError):
    """Slack template error."""
    pass


class SlackChannelError(SlackError):
    """Slack channel error."""
    pass
