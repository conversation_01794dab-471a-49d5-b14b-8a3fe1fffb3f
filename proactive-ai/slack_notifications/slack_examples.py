"""
Slack Module Usage Examples

This file demonstrates how to use the Slack notification system
for different operations and use cases.
"""

import os
from datetime import datetime
from .slack_utils import (
    SlackNotifier,
    send_store_worker_notification,
    send_rank_collections_notification,
    send_store_blogs_notification,
    send_installation_notification
)


def example_store_worker_notifications():
    """Examples of StoreWorker notifications."""
    print("=== StoreWorker Notification Examples ===")
    
    # Example 1: Job started
    success = send_store_worker_notification(
        project_name='ecommerce-store-1',
        job_type='runJob',
        status='started',
        details={
            'job_id': 'job_12345',
            'workflow_name': 'product_sync_workflow',
            'products_processed': 0
        }
    )
    print(f"Job started notification sent: {success}")
    
    # Example 2: Job completed
    success = send_store_worker_notification(
        project_name='ecommerce-store-1',
        job_type='runJob',
        status='completed',
        details={
            'job_id': 'job_12345',
            'execution_time': '2m 30s',
            'products_processed': 150,
            'success_count': 148,
            'error_count': 2
        }
    )
    print(f"Job completed notification sent: {success}")
    
    # Example 3: Workflow deployment
    success = send_store_worker_notification(
        project_name='fashion-boutique',
        job_type='workflow_deployment',
        status='completed',
        details={
            'workflow_name': 'inventory_management',
            'version': 'v2.1.0'
        }
    )
    print(f"Workflow deployment notification sent: {success}")


def example_rank_collections_notifications():
    """Examples of RankCollections notifications."""
    print("\n=== RankCollections Notification Examples ===")
    
    # Example 1: Collection creation started
    success = send_rank_collections_notification(
        project_name='electronics-shop',
        collection_name='Summer Electronics Collection',
        status='started',
        details={
            'collection_id': 'col_67890',
            'keywords_count': 25,
            'processing_time': '0s'
        }
    )
    print(f"Collection creation started notification sent: {success}")
    
    # Example 2: Collection creation completed
    success = send_rank_collections_notification(
        project_name='electronics-shop',
        collection_name='Summer Electronics Collection',
        status='completed',
        details={
            'collection_id': 'col_67890',
            'keywords_count': 25,
            'products_count': 89,
            'ranking_score': 0.85,
            'processing_time': '1m 45s'
        }
    )
    print(f"Collection creation completed notification sent: {success}")
    
    # Example 3: Competitor analysis
    success = send_rank_collections_notification(
        project_name='home-decor-store',
        collection_name='Modern Furniture Collection',
        status='completed',
        details={
            'competitor_count': 5,
            'keywords_count': 30,
            'ranking_score': 0.78
        },
        template_override='competitor_analysis'
    )
    print(f"Competitor analysis notification sent: {success}")


def example_store_blogs_notifications():
    """Examples of StoreBlogs notifications."""
    print("\n=== StoreBlogs Notification Examples ===")
    
    # Example 1: Article creation started
    success = send_store_blogs_notification(
        project_name='sports-equipment',
        article_title='Top 10 Running Shoes for 2024',
        status='started',
        details={
            'article_id': 'art_11111',
            'author': 'John Smith',
            'word_count': 0
        }
    )
    print(f"Article creation started notification sent: {success}")
    
    # Example 2: Article completed
    success = send_store_blogs_notification(
        project_name='sports-equipment',
        article_title='Top 10 Running Shoes for 2024',
        status='completed',
        details={
            'article_id': 'art_11111',
            'author': 'John Smith',
            'word_count': 1250,
            'seo_score': 87.5,
            'target_keywords': ['running shoes', 'athletic footwear', 'sports gear']
        }
    )
    print(f"Article completed notification sent: {success}")
    
    # Example 3: SEO optimization
    success = send_store_blogs_notification(
        project_name='fashion-boutique',
        article_title='Spring Fashion Trends 2024',
        status='completed',
        details={
            'seo_score': 92.0,
            'target_keywords': ['spring fashion', 'fashion trends', 'style guide'],
            'word_count': 1800
        },
        template_override='seo_optimization'
    )
    print(f"SEO optimization notification sent: {success}")


def example_installation_notifications():
    """Examples of Installation notifications."""
    print("\n=== Installation Notification Examples ===")
    
    # Example 1: New user installation
    success = send_installation_notification(
        project_name='ecommerce-store-1',
        user_info={
            'name': 'Jane Doe',
            'email': '<EMAIL>',
            'company': 'Example Store'
        },
        status='completed',
        details={
            'installation_type': 'new_user_installation',
            'version': 'v3.2.1',
            'features_enabled': ['inventory_management', 'analytics', 'seo_tools']
        }
    )
    print(f"New user installation notification sent: {success}")
    
    # Example 2: System upgrade
    success = send_installation_notification(
        project_name='fashion-boutique',
        user_info={'name': 'System Admin'},
        status='completed',
        details={
            'installation_type': 'upgrade_installation',
            'version': 'v3.3.0',
            'previous_version': 'v3.2.1',
            'features_enabled': ['new_dashboard', 'advanced_analytics']
        }
    )
    print(f"System upgrade notification sent: {success}")
    
    # Example 3: Critical failure
    success = send_installation_notification(
        project_name='electronics-shop',
        user_info={'name': 'System Admin'},
        status='failed',
        details={
            'installation_type': 'upgrade_installation',
            'version': 'v3.3.0',
            'error_message': 'Database migration failed - connection timeout'
        },
        template_override='critical_failure'
    )
    print(f"Critical failure notification sent: {success}")


def example_custom_notifications():
    """Examples of custom notifications using SlackNotifier directly."""
    print("\n=== Custom Notification Examples ===")
    
    # Initialize notifier
    notifier = SlackNotifier()
    
    # Test connection
    if notifier.test_connection():
        print("✅ Slack connection successful")
    else:
        print("❌ Slack connection failed")
        return
    
    # Example 1: Custom message with rich formatting
    message_data = {
        'project_name': 'custom-project',
        'job_type': 'custom_operation',
        'status': 'completed',
        'execution_time': '5m 20s',
        'success_count': 100,
        'products_processed': 100
    }
    
    success = notifier.send_message(
        operation_type='storeWorker',
        project_name='custom-project',
        message_data=message_data,
        template_override='performance_report'
    )
    print(f"Custom performance report sent: {success}")
    
    # Example 2: Simple message
    success = notifier.send_simple_message(
        channel='#general-notifications',
        text='🔔 This is a simple test message from the Slack module!'
    )
    print(f"Simple message sent: {success}")


def example_error_handling():
    """Examples of error handling."""
    print("\n=== Error Handling Examples ===")
    
    # Example 1: Invalid project name
    success = send_store_worker_notification(
        project_name='non-existent-project',
        job_type='runJob',
        status='started',
        details={'job_id': 'test_job'}
    )
    print(f"Notification to non-existent project: {success}")
    
    # Example 2: Missing required data
    try:
        success = send_rank_collections_notification(
            project_name='test-project',
            collection_name='',  # Empty collection name
            status='started',
            details={}
        )
        print(f"Notification with missing data: {success}")
    except Exception as e:
        print(f"Error with missing data: {str(e)}")


def main():
    """Run all examples."""
    print("Slack Module Examples")
    print("=" * 50)
    
    # Check if Slack token is set
    if not os.getenv('SLACK_BOT_TOKEN'):
        print("⚠️  Warning: SLACK_BOT_TOKEN environment variable not set.")
        print("   Set your Slack bot token to test actual notifications.")
        print("   Example: export SLACK_BOT_TOKEN='xoxb-your-token-here'")
        print()
    
    # Run examples
    example_store_worker_notifications()
    example_rank_collections_notifications()
    example_store_blogs_notifications()
    example_installation_notifications()
    example_custom_notifications()
    example_error_handling()
    
    print("\n" + "=" * 50)
    print("Examples completed!")


if __name__ == "__main__":
    main()
