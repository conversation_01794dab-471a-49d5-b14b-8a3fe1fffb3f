import os
import logging
from datetime import datetime
import httpx
from dotenv import load_dotenv
import asyncio
from typing import Dict, Any

load_dotenv()

logger = logging.getLogger(__name__)

async def send_slack_webhook(
    operation_type: str,
    job_status: str,
    job_details: Dict[str, Any],
    callback_response_sent_status: str = None
) -> None:
    """
    Send a Slack notification for any operation with dynamic content.
    :param operation_type: The type of operation (e.g., 'run_template_job', 'syncing_store_data')
    :param job_status: The status of the job (e.g., 'job_created', 'job_success', 'job_failed')
    :param job_details: Dictionary with all relevant job details
    """
    current_datetime = datetime.now().strftime('%m/%d/%Y %I:%M %p')
    store_id = job_details.get('store_id')
    store_name = job_details.get('store_name', None)  # Optional, fallback to store_id
    job_id = job_details.get('job_id')
    template_uuid = job_details.get('template_uuid')
    status = job_details.get('status')
    error_message = job_details.get('error_message')
    job_title = job_details.get('job_title')
    started_at = job_details.get('started_at')
    finished_at = job_details.get('finished_at')
    job_duration = job_details.get('job_duration')
    extra_fields = {k: v for k, v in job_details.items() if k not in [
        'store_id', 'store_name', 'job_id', 'template_uuid', 'status', 'error_message', 'job_title', 'started_at', 'finished_at', 'job_duration']}
    
    # Get running environment from env variable
    slack_running_environment = os.getenv('SLACK_RUNNING_ENVIRONMENT') or 'PRODUCTION'

    # Header
    header = f"*🔔 {operation_type.replace('_', ' ').title()} Notification*\n"
    # Status block
    if job_status == 'job_created':
        status_block = f"> ┌────────────────────────────┐\n> │ 🚀 Job Created In {slack_running_environment} Environment │\n> └────────────────────────────┘\n"
    elif job_status == 'job_success':
        status_block = f"> ┌────────────────────────────┐\n> │ ✅ Job Success In {slack_running_environment} Environment │\n> └────────────────────────────┘\n"
    elif job_status == 'job_failed':
        status_block = f"> ┌────────────────────────────┐\n> │ ❌ Job Failed In {slack_running_environment} Environment │\n> └────────────────────────────┘\n"
    else:
        status_block = f"> ┌────────────────────────────┐\n> │ ℹ️ {job_status.replace('_', ' ').title()} │\n> └────────────────────────────┘\n"

    # Details block
    details = []
    details.append(f"*Store ID:* `{store_id}`")
    if store_name:
        details.append(f"*Store Name:* {store_name}")
    details.append(f"*Job ID:* `{job_id}`")
    if callback_response_sent_status:
        details.append(f"*Callback Response Sent Status:* {callback_response_sent_status}")
    if template_uuid:
        details.append(f"*Template UUID:* `{template_uuid}`")
    if job_title:
        details.append(f"*Job Title:* {job_title}")
    if status:
        details.append(f"*Status:* {status}")
    if started_at:
        details.append(f"*Started At:* {started_at}")
    if finished_at:
        details.append(f"*Finished At:* {finished_at}")
    if job_duration:
        details.append(f"*Job Duration:* {job_duration}")
    details.append(f"*Date/Time:* {current_datetime}")
    if error_message:
        details.append(f"*Error:* {error_message}")
    # Add any extra fields
    for k, v in extra_fields.items():
        details.append(f"*{k.replace('_', ' ').title()}:* {v}")
    details_block = '\n'.join(details)

    message = f"{header}\n{status_block}\n{details_block}"

    try:
        endpoint = f"{os.getenv('SLACK_WEBHOOK_URL')}{os.getenv('SLACK_WEBHOOK_ACCESS_KEY')}"
        logger.info(f"[Slack] Sending to endpoint: {endpoint} - Store: {store_id}")
        async with httpx.AsyncClient() as client:
            response = await client.post(
                endpoint,
                headers={'Content-Type': 'application/json'},
                json={'text': message}
            )
        if not response.status_code == 200:
            logger.info(f"[Slack] Webhook failed with status - Store: {store_id} - Response: {response}")
            print(f"[Slack] Webhook failed with status - Store: {store_id} - Response: {response}")
        else:
            logger.info("[Slack] Message sent successfully to Slack")
            print("[Slack] Message sent successfully to Slack")
    except Exception as error:
        logger.error(f"[Slack] Error sending Slack webhook data: {str(error)}")
        print(f"[Slack] Error sending Slack webhook data: {str(error)}")


def main():
    # Sample payloads
    run_job_data = {
        'store_id': '123',
        'store_name': 'Sample Shop',
        'job_id': 'job_456',
        'template_uuid': 'tpl_789',
        'status': 'started',
        'started_at': datetime.now().strftime('%m/%d/%Y %I:%M %p'),
        'message': 'Job started successfully!'
    }
    error_data = {
        'store_id': '123',
        'store_name': 'Sample Shop',
        'job_id': 'job_456',
        'template_uuid': 'tpl_789',
        'status': 'failed',
        'error_message': 'Something went wrong!',
        'finished_at': datetime.now().strftime('%m/%d/%Y %I:%M %p')
    }

    print("\n--- Testing run-job notification ---")
    asyncio.run(send_slack_webhook('run_template_job', 'job_success', run_job_data))

    print("\n--- Testing error notification ---")
    asyncio.run(send_slack_webhook('run_template_job', 'job_failed', error_data))

if __name__ == "__main__":
    main()