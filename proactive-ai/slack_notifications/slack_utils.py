"""
Slack Utilities Module

This module provides utilities for sending messages to Slack channels
with template-based messaging and channel management.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

from .config.slack_channels import SLACK_CHANNELS
from .config.slack_templates import SLACK_TEMPLATES
from .config.slack_types import SlackOperationType, SlackMessageData


class SlackNotifier:
    """
    Slack notification handler for sending messages to different channels
    based on operation types and templates.
    """

    def __init__(self, token: Optional[str] = None):
        """
        Initialize Slack client with bot token.

        Args:
            token: Slack bot token. If None, will try to get from environment.
        """
        self.token = token or os.getenv('SLACK_BOT_TOKEN')
        if not self.token:
            raise ValueError("Slack bot token is required. Set SLACK_BOT_TOKEN environment variable.")

        self.client = WebClient(token=self.token)
        self.logger = logging.getLogger(__name__)

    def send_message(
        self,
        operation_type: SlackOperationType,
        project_name: str,
        message_data: SlackMessageData,
        custom_channel: Optional[str] = None,
        template_override: Optional[str] = None
    ) -> bool:
        """
        Send a message to Slack channel based on operation type and project.

        Args:
            operation_type: Type of operation (e.g., 'storeWorker', 'rankCollections')
            project_name: Name of the project for channel selection
            message_data: Data to populate the message template
            custom_channel: Override default channel selection
            template_override: Override default template selection

        Returns:
            bool: True if message sent successfully, False otherwise
        """
        try:
            # Get channel ID
            channel_id = self._get_channel_id(operation_type, project_name, custom_channel)
            if not channel_id:
                self.logger.error(f"No channel found for operation: {operation_type}, project: {project_name}")
                return False

            # Get and format message
            message = self._format_message(operation_type, message_data, template_override)
            if not message:
                self.logger.error(f"Failed to format message for operation: {operation_type}")
                return False

            # Send message
            response = self.client.chat_postMessage(
                channel=channel_id,
                text=message.get('text', ''),
                blocks=message.get('blocks'),
                attachments=message.get('attachments')
            )

            if response['ok']:
                self.logger.info(f"Message sent successfully to {channel_id}")
                return True
            else:
                self.logger.error(f"Failed to send message: {response.get('error', 'Unknown error')}")
                return False

        except SlackApiError as e:
            self.logger.error(f"Slack API error: {e.response['error']}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error sending Slack message: {str(e)}")
            return False

    def _get_channel_id(
        self,
        operation_type: SlackOperationType,
        project_name: str,
        custom_channel: Optional[str] = None
    ) -> Optional[str]:
        """Get channel ID based on operation type and project name."""
        if custom_channel:
            return custom_channel

        # Get channel mapping for the operation type
        operation_channels = SLACK_CHANNELS.get(operation_type, {})

        # Try to get project-specific channel first, then default
        return operation_channels.get(project_name) or operation_channels.get('default')

    def _format_message(
        self,
        operation_type: SlackOperationType,
        message_data: SlackMessageData,
        template_override: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Format message using templates."""
        try:
            # Get template for operation type
            operation_templates = SLACK_TEMPLATES.get(operation_type, {})

            # Determine which template to use
            template_key = template_override or message_data.get('status', 'default')
            template = operation_templates.get(template_key)

            if not template:
                self.logger.error(f"No template found for {operation_type}.{template_key}")
                return None

            # Add timestamp to message data
            formatted_data = {**message_data, 'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

            # Format the template
            if isinstance(template, dict):
                formatted_message = {}
                for key, value in template.items():
                    if isinstance(value, str):
                        formatted_message[key] = value.format(**formatted_data)
                    elif isinstance(value, list):
                        formatted_message[key] = [
                            item.format(**formatted_data) if isinstance(item, str) else item
                            for item in value
                        ]
                    else:
                        formatted_message[key] = value
                return formatted_message
            else:
                return {'text': template.format(**formatted_data)}

        except KeyError as e:
            self.logger.error(f"Missing required data for template formatting: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error formatting message template: {str(e)}")
            return None

    def send_simple_message(self, channel: str, text: str) -> bool:
        """
        Send a simple text message to a channel.

        Args:
            channel: Channel ID or name
            text: Message text

        Returns:
            bool: True if sent successfully, False otherwise
        """
        try:
            response = self.client.chat_postMessage(channel=channel, text=text)
            return response['ok']
        except SlackApiError as e:
            self.logger.error(f"Failed to send simple message: {e.response['error']}")
            return False

    def test_connection(self) -> bool:
        """Test Slack API connection."""
        try:
            response = self.client.auth_test()
            if response['ok']:
                self.logger.info(f"Connected to Slack as {response['user']}")
                return True
            return False
        except SlackApiError as e:
            self.logger.error(f"Slack connection test failed: {e.response['error']}")
            return False


# Convenience functions for common operations
def send_store_worker_notification(
    project_name: str,
    job_type: str,
    status: str,
    details: Dict[str, Any],
    custom_channel: Optional[str] = None
) -> bool:
    """Send StoreWorker operation notification."""
    notifier = SlackNotifier()
    message_data = {
        'job_type': job_type,
        'status': status,
        'project_name': project_name,
        **details
    }
    return notifier.send_message('storeWorker', project_name, message_data, custom_channel)


def send_rank_collections_notification(
    project_name: str,
    collection_name: str,
    status: str,
    details: Dict[str, Any],
    custom_channel: Optional[str] = None
) -> bool:
    """Send RankCollections operation notification."""
    notifier = SlackNotifier()
    message_data = {
        'collection_name': collection_name,
        'status': status,
        'project_name': project_name,
        **details
    }
    return notifier.send_message('rankCollections', project_name, message_data, custom_channel)


def send_store_blogs_notification(
    project_name: str,
    article_title: str,
    status: str,
    details: Dict[str, Any],
    custom_channel: Optional[str] = None
) -> bool:
    """Send StoreBlogs operation notification."""
    notifier = SlackNotifier()
    message_data = {
        'article_title': article_title,
        'status': status,
        'project_name': project_name,
        **details
    }
    return notifier.send_message('storeBlogs', project_name, message_data, custom_channel)


def send_installation_notification(
    project_name: str,
    user_info: Dict[str, Any],
    status: str,
    details: Dict[str, Any],
    custom_channel: Optional[str] = None
) -> bool:
    """Send Installation operation notification."""
    notifier = SlackNotifier()
    message_data = {
        'user_info': user_info,
        'status': status,
        'project_name': project_name,
        **details
    }
    return notifier.send_message('installation', project_name, message_data, custom_channel)
