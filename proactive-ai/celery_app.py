# # GeneratorAI/celery_app.py
# import os
# import sys
# from pathlib import Path

# # Add project root to Python path
# project_root = str(Path(__file__).resolve().parent)
# if project_root not in sys.path:
#     sys.path.append(project_root)

# from app.RankCollections.utils.celery_config import celery_app

# # This enables running the worker using this file
# if __name__ == '__main__':
#     celery_app.start()

import os
import sys
from pathlib import Path
from celery import Celery

# Add project root to Python path
project_root = str(Path(__file__).resolve().parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# Initialize the main Celery app
celery_app = Celery('generatorai')

# Load configuration from celeryconfig.py
celery_app.config_from_object('celeryconfig')

# Explicitly specify the packages where tasks can be found
celery_app.autodiscover_tasks(
    packages=[
        'app.RankCollections',
        'app.Blogging.StoreBlog',
        'app.celery_sync'
    ],
    force=True  # Force task discovery
)

# Register tasks explicitly
from app.Blogging.StoreBlog.celery_config_article import generate_article_task, get_article_task_status
from app.RankCollections.celery_config_collection import generate_collection_task, get_task_status
from app.celery_sync.celery_config_products import sync_products_task, get_product_sync_status
from app.celery_sync.celery_config_blogs import sync_blogs_task, get_blog_sync_status
from app.celery_sync.celery_config_articles import sync_articles_task, get_article_sync_status
from app.celery_sync.celery_config_collections import sync_collections_task, get_collection_sync_status

if __name__ == '__main__':
    celery_app.start()