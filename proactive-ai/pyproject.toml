[project]
name = "bincha-proactive-ai"
version = "0.1.0"
description = "Default template for PDM package"
authors = [
    {name = "<PERSON> , <PERSON><PERSON>K<PERSON>", email = "<EMAIL>"}
]
dependencies = [
    "fastapi>=0.115.8",
    "uvicorn>=0.34.0",
    "pydantic",
    "typing",
    "asyncio",
    "pyyaml",
    "ratelimit>=2.2.1",
    "supabase",
    "celery",
    "portkey-ai",
    "python-dotenv",
    "numpy",
    "structlog",
    "autogen-agentchat>=0.4.7",
    "spacy==3.8.2",
    "openai==1.58.1",
    "pandas>=2.2.3",
    "scikit-learn",
    "redis>=5.2.1",
    "requests>=2.32.3",
    "dataforseo-client>=1.0.42",
    "autogenstudio>=0.1.5",
    "autogen-ext[azure]>=0.4.7",
    "httpx>=0.28.1",
    "beautifulsoup4>=4.13.3",
    "lxml>=5.3.1",
    "sentence-transformers>=3.4.1",
    "keybert>=0.9.0",
    "rapidfuzz>=3.12.1",
    "nvidia-cufft-cu12>=********",
    "pinecone>=6.0.2",
    "composio-crewai>=0.7.16",
]
requires-python = "==3.12.*"
readme = "README.md"
license = {text = "MIT"}

[tool.pdm]
distribution = false