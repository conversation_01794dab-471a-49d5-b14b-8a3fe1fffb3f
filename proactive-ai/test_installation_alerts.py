#!/usr/bin/env python3
"""
Sample Working Script: Installation Function Alerts

This script demonstrates how to send installation alerts to Slack channels.
It includes various installation scenarios and provides a complete working example.
"""

import os
import sys
import time
from datetime import datetime
from typing import Dict, Any

# Add the slack_notifications module to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from slack_notifications.slack_utils import (
    SlackNotifier,
    send_installation_notification
)


def test_slack_connection():
    """Test if Slack connection is working."""
    print("🔍 Testing Slack connection...")
    
    try:
        notifier = SlackNotifier()
        if notifier.test_connection():
            print("✅ Slack connection successful!")
            return True
        else:
            print("❌ Slack connection failed!")
            return False
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False


def send_new_user_installation_alert():
    """Send a new user installation alert."""
    print("\n📋 Sending New User Installation Alert...")
    
    user_info = {
        'name': '<PERSON>',
        'email': '<EMAIL>',
        'company': 'Example Corp',
        'role': 'Store Manager'
    }
    
    details = {
        'installation_type': 'new_user_installation',
        'version': 'v3.2.1',
        'features_enabled': ['inventory_management', 'analytics', 'seo_tools', 'reporting'],
        'installation_time': '2m 45s',
        'server_location': 'US-East-1'
    }
    
    success = send_installation_notification(
        project_name='ecommerce-store-demo',
        user_info=user_info,
        status='completed',
        details=details
    )
    
    if success:
        print("✅ New user installation alert sent successfully!")
    else:
        print("❌ Failed to send new user installation alert!")
    
    return success


def send_system_upgrade_alert():
    """Send a system upgrade alert."""
    print("\n📋 Sending System Upgrade Alert...")
    
    user_info = {
        'name': 'System Administrator',
        'email': '<EMAIL>'
    }
    
    details = {
        'installation_type': 'upgrade_installation',
        'version': 'v3.3.0',
        'previous_version': 'v3.2.1',
        'features_enabled': ['new_dashboard', 'advanced_analytics', 'ai_recommendations'],
        'upgrade_time': '5m 12s',
        'downtime': '30 seconds'
    }
    
    success = send_installation_notification(
        project_name='fashion-boutique-demo',
        user_info=user_info,
        status='completed',
        details=details
    )
    
    if success:
        print("✅ System upgrade alert sent successfully!")
    else:
        print("❌ Failed to send system upgrade alert!")
    
    return success


def send_installation_failure_alert():
    """Send an installation failure alert."""
    print("\n📋 Sending Installation Failure Alert...")
    
    user_info = {
        'name': 'Jane Doe',
        'email': '<EMAIL>',
        'company': 'New Store Inc'
    }
    
    details = {
        'installation_type': 'new_user_installation',
        'version': 'v3.2.1',
        'error_message': 'Database connection timeout - unable to initialize user workspace',
        'attempted_time': '1m 23s',
        'error_code': 'DB_TIMEOUT_001'
    }
    
    success = send_installation_notification(
        project_name='electronics-shop-demo',
        user_info=user_info,
        status='failed',
        details=details
    )
    
    if success:
        print("✅ Installation failure alert sent successfully!")
    else:
        print("❌ Failed to send installation failure alert!")
    
    return success


def send_migration_alert():
    """Send a data migration alert."""
    print("\n📋 Sending Data Migration Alert...")
    
    user_info = {
        'name': 'Migration Service',
        'email': '<EMAIL>'
    }
    
    details = {
        'installation_type': 'migration_installation',
        'version': 'v3.3.0',
        'previous_version': 'v2.8.5',
        'features_enabled': ['legacy_data_import', 'data_validation', 'backup_restore'],
        'migration_time': '15m 30s',
        'records_migrated': 50000,
        'data_size': '2.3 GB'
    }
    
    success = send_installation_notification(
        project_name='home-decor-store-demo',
        user_info=user_info,
        status='completed',
        details=details
    )
    
    if success:
        print("✅ Data migration alert sent successfully!")
    else:
        print("❌ Failed to send data migration alert!")
    
    return success


def send_custom_installation_alert():
    """Send a custom installation alert using SlackNotifier directly."""
    print("\n📋 Sending Custom Installation Alert...")
    
    try:
        notifier = SlackNotifier()
        
        message_data = {
            'project_name': 'sports-equipment-demo',
            'installation_type': 'configuration_update',
            'status': 'completed',
            'version': 'v3.2.1',
            'user_info': {
                'name': 'Config Manager',
                'email': '<EMAIL>'
            },
            'configuration_changes': {
                'payment_gateway': 'Updated to Stripe v2',
                'shipping_zones': 'Added international shipping',
                'tax_settings': 'Updated for EU compliance'
            },
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        success = notifier.send_message(
            operation_type='installation',
            project_name='sports-equipment-demo',
            message_data=message_data,
            template_override='configuration_update'
        )
        
        if success:
            print("✅ Custom installation alert sent successfully!")
        else:
            print("❌ Failed to send custom installation alert!")
        
        return success
        
    except Exception as e:
        print(f"❌ Error sending custom alert: {e}")
        return False


def send_critical_installation_failure():
    """Send a critical installation failure alert."""
    print("\n📋 Sending Critical Installation Failure Alert...")
    
    user_info = {
        'name': 'Production System',
        'email': '<EMAIL>'
    }
    
    details = {
        'installation_type': 'upgrade_installation',
        'version': 'v3.3.0',
        'error_message': 'Critical system failure during upgrade - immediate rollback required',
        'error_code': 'CRITICAL_001',
        'affected_services': ['payment_processing', 'inventory_sync', 'user_authentication']
    }
    
    # Use custom channel for critical alerts
    success = send_installation_notification(
        project_name='production-store',
        user_info=user_info,
        status='failed',
        details=details,
        custom_channel='#critical-installations'  # Override to critical channel
    )
    
    if success:
        print("✅ Critical installation failure alert sent successfully!")
    else:
        print("❌ Failed to send critical installation failure alert!")
    
    return success


def main():
    """Main function to run all installation alert tests."""
    print("🚀 Installation Function Alert Testing Script")
    print("=" * 60)
    
    # Check if Slack token is set
    if not os.getenv('SLACK_BOT_TOKEN'):
        print("❌ ERROR: SLACK_BOT_TOKEN environment variable not set!")
        print("\nPlease set your Slack bot token:")
        print("export SLACK_BOT_TOKEN='xoxb-your-slack-bot-token-here'")
        print("\nSee the setup instructions below for details.")
        return False
    
    # Test connection first
    if not test_slack_connection():
        print("\n❌ Cannot proceed - Slack connection failed!")
        print("Please check your SLACK_BOT_TOKEN and bot permissions.")
        return False
    
    print(f"\n🕐 Starting installation alert tests at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all test scenarios
    test_results = []
    
    # Test 1: New User Installation
    test_results.append(send_new_user_installation_alert())
    time.sleep(2)  # Wait between messages
    
    # Test 2: System Upgrade
    test_results.append(send_system_upgrade_alert())
    time.sleep(2)
    
    # Test 3: Installation Failure
    test_results.append(send_installation_failure_alert())
    time.sleep(2)
    
    # Test 4: Data Migration
    test_results.append(send_migration_alert())
    time.sleep(2)
    
    # Test 5: Custom Installation Alert
    test_results.append(send_custom_installation_alert())
    time.sleep(2)
    
    # Test 6: Critical Failure
    test_results.append(send_critical_installation_failure())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    successful = sum(test_results)
    total = len(test_results)
    
    test_names = [
        "New User Installation",
        "System Upgrade", 
        "Installation Failure",
        "Data Migration",
        "Custom Installation Alert",
        "Critical Failure"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {name}")
    
    print(f"\nTotal: {successful}/{total} alerts sent successfully")
    
    if successful == total:
        print("🎉 All installation alerts sent successfully!")
        print("\nCheck your Slack channels to see the messages!")
    else:
        print("⚠️  Some alerts failed to send. Check your configuration.")
    
    return successful == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
