"""
Slack Configuration Example

Copy this file and customize it with your actual Slack workspace details.
This makes it easy to configure your Slack integration.
"""

# =============================================================================
# SLACK BOT CONFIGURATION
# =============================================================================

# Your Slack Bot Token (get this from https://api.slack.com/apps)
# Format: 'xoxb-1234567890-1234567890123-abcdefghijklmnopqrstuvwx'
SLACK_BOT_TOKEN = 'xoxb-your-slack-bot-token-here'

# =============================================================================
# CHANNEL CONFIGURATION
# =============================================================================

# Default channels for different types of notifications
DEFAULT_CHANNELS = {
    'installation': '#installation-notifications',
    'critical': '#critical-alerts',
    'development': '#dev-notifications',
    'production': '#prod-alerts'
}

# Project-specific channel mappings
# Replace these with your actual project names and channel IDs
PROJECT_CHANNELS = {
    # Installation channels
    'installation': {
        'default': DEFAULT_CHANNELS['installation'],
        
        # Your actual projects - replace these
        'ecommerce-store-1': '#ecommerce-installations',
        'fashion-boutique': '#fashion-installations', 
        'electronics-shop': '#electronics-installations',
        'home-decor-store': '#homedecor-installations',
        'sports-equipment': '#sports-installations',
        
        # Environment-specific
        'production': DEFAULT_CHANNELS['production'],
        'staging': '#staging-installations',
        'development': DEFAULT_CHANNELS['development'],
        
        # Special channels
        'critical-installs': DEFAULT_CHANNELS['critical'],
        'failed-installs': '#failed-installations'
    }
}

# =============================================================================
# TEST CONFIGURATION
# =============================================================================

# Test projects for the sample scripts
TEST_PROJECTS = [
    'ecommerce-store-demo',
    'fashion-boutique-demo', 
    'electronics-shop-demo',
    'home-decor-store-demo',
    'sports-equipment-demo'
]

# Sample user data for testing
TEST_USERS = [
    {
        'name': 'John Smith',
        'email': '<EMAIL>',
        'company': 'Example Corp',
        'role': 'Store Manager'
    },
    {
        'name': 'Jane Doe', 
        'email': '<EMAIL>',
        'company': 'New Store Inc',
        'role': 'Administrator'
    },
    {
        'name': 'Mike Johnson',
        'email': '<EMAIL>', 
        'company': 'Fashion Store LLC',
        'role': 'Owner'
    }
]

# =============================================================================
# INSTALLATION SCENARIOS
# =============================================================================

# Different installation types to test
INSTALLATION_SCENARIOS = {
    'new_user': {
        'installation_type': 'new_user_installation',
        'version': 'v3.2.1',
        'features_enabled': ['inventory_management', 'analytics', 'seo_tools', 'reporting'],
        'typical_time': '2-5 minutes'
    },
    
    'upgrade': {
        'installation_type': 'upgrade_installation', 
        'version': 'v3.3.0',
        'previous_version': 'v3.2.1',
        'features_enabled': ['new_dashboard', 'advanced_analytics', 'ai_recommendations'],
        'typical_time': '5-10 minutes'
    },
    
    'migration': {
        'installation_type': 'migration_installation',
        'version': 'v3.3.0', 
        'previous_version': 'v2.8.5',
        'features_enabled': ['legacy_data_import', 'data_validation', 'backup_restore'],
        'typical_time': '15-30 minutes'
    },
    
    'config_update': {
        'installation_type': 'configuration_update',
        'version': 'v3.2.1',
        'features_enabled': ['payment_gateway_update', 'shipping_zones', 'tax_settings'],
        'typical_time': '1-2 minutes'
    }
}

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_channel_for_project(project_name: str, operation_type: str = 'installation') -> str:
    """Get the appropriate channel for a project and operation type."""
    operation_channels = PROJECT_CHANNELS.get(operation_type, {})
    return operation_channels.get(project_name, operation_channels.get('default', '#general'))


def get_test_user(index: int = 0) -> dict:
    """Get a test user by index."""
    return TEST_USERS[index % len(TEST_USERS)]


def get_installation_scenario(scenario_type: str) -> dict:
    """Get installation scenario details by type."""
    return INSTALLATION_SCENARIOS.get(scenario_type, INSTALLATION_SCENARIOS['new_user'])


# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

def validate_config() -> bool:
    """Validate the configuration."""
    issues = []
    
    # Check bot token
    if not SLACK_BOT_TOKEN or SLACK_BOT_TOKEN == 'xoxb-your-slack-bot-token-here':
        issues.append("❌ SLACK_BOT_TOKEN not set or still using placeholder")
    
    # Check if channels are still using placeholders
    placeholder_channels = [ch for ch in DEFAULT_CHANNELS.values() if 'notifications' in ch]
    if len(placeholder_channels) == len(DEFAULT_CHANNELS):
        issues.append("⚠️  Still using default channel names - consider customizing")
    
    # Check project channels
    installation_channels = PROJECT_CHANNELS.get('installation', {})
    if len(installation_channels) <= 3:  # default, production, development
        issues.append("ℹ️  Consider adding project-specific channels")
    
    if issues:
        print("Configuration Issues:")
        for issue in issues:
            print(f"  {issue}")
        return False
    
    print("✅ Configuration looks good!")
    return True


# =============================================================================
# USAGE EXAMPLE
# =============================================================================

if __name__ == "__main__":
    print("Slack Configuration Example")
    print("=" * 40)
    
    print(f"Bot Token: {SLACK_BOT_TOKEN[:12]}..." if len(SLACK_BOT_TOKEN) > 12 else "Not set")
    print(f"Default Installation Channel: {DEFAULT_CHANNELS['installation']}")
    print(f"Test Projects: {len(TEST_PROJECTS)}")
    print(f"Installation Scenarios: {len(INSTALLATION_SCENARIOS)}")
    
    print("\nValidating configuration...")
    validate_config()
    
    print("\nExample usage:")
    print(f"Channel for 'my-project': {get_channel_for_project('my-project')}")
    print(f"Test user: {get_test_user(0)['name']}")
    print(f"New user scenario: {get_installation_scenario('new_user')['installation_type']}")
