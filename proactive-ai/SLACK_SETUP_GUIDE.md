# 🚀 Slack Installation Alerts - Complete Setup Guide

This guide will walk you through setting up and testing Slack installation alerts with a live Slack workspace.

## 📋 **Prerequisites**

- Python 3.7+ installed
- Access to a Slack workspace (admin permissions preferred)
- Terminal/Command line access

## 🔧 **Step 1: Create a Slack App**

### 1.1 Go to Slack API
1. Visit https://api.slack.com/apps
2. Click **"Create New App"**
3. Choose **"From scratch"**
4. Enter app name: `GeneratorAI Notifications`
5. Select your workspace
6. Click **"Create App"**

### 1.2 Configure Bot Permissions
1. In your app dashboard, go to **"OAuth & Permissions"** (left sidebar)
2. Scroll down to **"Scopes"** section
3. Under **"Bot Token Scopes"**, add these permissions:
   - `chat:write` - Send messages
   - `chat:write.public` - Send messages to public channels
   - `channels:read` - View basic information about public channels
   - `groups:read` - View basic information about private channels

### 1.3 Install App to Workspace
1. Scroll up to **"OAuth Tokens for Your Workspace"**
2. Click **"Install to Workspace"**
3. Review permissions and click **"Allow"**
4. **COPY THE BOT TOKEN** (starts with `xoxb-`) - you'll need this!

## 📺 **Step 2: Create Slack Channels**

Create these channels in your Slack workspace (or use existing ones):

```bash
#installation-notifications     # Default installation channel
#critical-installations        # For critical alerts
#installation-dev              # For development testing
#general-notifications         # General purpose
```

**To create channels:**
1. In Slack, click the **"+"** next to "Channels"
2. Choose **"Create a channel"**
3. Enter channel name (without #)
4. Make it **Public** so the bot can access it
5. Click **"Create"**

## 🔑 **Step 3: Set Environment Variables**

### On macOS/Linux:
```bash
# Set your Slack bot token (replace with your actual token)
export SLACK_BOT_TOKEN='xoxb-1234567890-1234567890123-abcdefghijklmnopqrstuvwx'

# Verify it's set
echo $SLACK_BOT_TOKEN
```

### On Windows (Command Prompt):
```cmd
# Set your Slack bot token
set SLACK_BOT_TOKEN=xoxb-1234567890-1234567890123-abcdefghijklmnopqrstuvwx

# Verify it's set
echo %SLACK_BOT_TOKEN%
```

### On Windows (PowerShell):
```powershell
# Set your Slack bot token
$env:SLACK_BOT_TOKEN = "xoxb-1234567890-1234567890123-abcdefghijklmnopqrstuvwx"

# Verify it's set
echo $env:SLACK_BOT_TOKEN
```

## ⚙️ **Step 4: Configure Channel IDs**

### 4.1 Get Channel IDs
You need the actual channel IDs. Here are two methods:

**Method 1: From Slack URL**
1. Open the channel in Slack web app
2. Look at the URL: `https://yourworkspace.slack.com/messages/C1234567890/`
3. The channel ID is the part after `/messages/` (e.g., `C1234567890`)

**Method 2: Right-click method**
1. Right-click on the channel name
2. Select "Copy link"
3. The ID is in the URL

### 4.2 Update Channel Configuration
Edit `slack_notifications/config/slack_channels.py`:

```python
# Replace these with your actual channel IDs
SLACK_CHANNELS = {
    'installation': {
        'default': '#installation-notifications',  # or 'C1234567890'
        'critical-installs': '#critical-installations',
        'development': '#installation-dev',
        'ecommerce-store-demo': '#installation-notifications',
        'fashion-boutique-demo': '#installation-notifications',
        'electronics-shop-demo': '#installation-notifications',
        'home-decor-store-demo': '#installation-notifications',
        'sports-equipment-demo': '#installation-notifications',
        'production-store': '#critical-installations',
    },
    # ... other operations
}
```

## 🧪 **Step 5: Install Dependencies**

```bash
# Make sure you're in the project directory
cd /path/to/your/GeneratorAI

# Install required packages
pip install slack-sdk

# Verify installation
python -c "import slack_sdk; print('✅ slack-sdk installed successfully')"
```

## 🚀 **Step 6: Run the Test Script**

### 6.1 Basic Test
```bash
# Test the module imports first
python -m slack_notifications.test_slack_module
```

### 6.2 Run Installation Alerts Test
```bash
# Run the installation alerts test script
python test_installation_alerts.py
```

### 6.3 Expected Output
You should see output like this:

```
🚀 Installation Function Alert Testing Script
============================================================
🔍 Testing Slack connection...
✅ Slack connection successful!

🕐 Starting installation alert tests at 2024-01-15 14:30:25

📋 Sending New User Installation Alert...
✅ New user installation alert sent successfully!

📋 Sending System Upgrade Alert...
✅ System upgrade alert sent successfully!

📋 Sending Installation Failure Alert...
✅ Installation failure alert sent successfully!

📋 Sending Data Migration Alert...
✅ Data migration alert sent successfully!

📋 Sending Custom Installation Alert...
✅ Custom installation alert sent successfully!

📋 Sending Critical Installation Failure Alert...
✅ Critical installation failure alert sent successfully!

============================================================
📊 Test Results Summary
============================================================
✅ PASS - New User Installation
✅ PASS - System Upgrade
✅ PASS - Installation Failure
✅ PASS - Data Migration
✅ PASS - Custom Installation Alert
✅ PASS - Critical Failure

Total: 6/6 alerts sent successfully
🎉 All installation alerts sent successfully!

Check your Slack channels to see the messages!
```

## 📱 **Step 7: Check Slack Channels**

Go to your Slack workspace and check the channels. You should see rich, formatted messages like:

```
🚀 Installation Started
Project: ecommerce-store-demo
Type: new_user_installation
Started: 2024-01-15 14:30:25
Version: v3.2.1
```

## 🔧 **Troubleshooting**

### Common Issues:

**1. "SLACK_BOT_TOKEN not set" error:**
```bash
# Make sure token is set correctly
echo $SLACK_BOT_TOKEN  # Should show your token
```

**2. "Slack connection failed" error:**
- Check if your bot token is correct
- Verify bot is installed in workspace
- Check bot permissions

**3. "No channel found" error:**
- Update channel IDs in `slack_notifications/config/slack_channels.py`
- Make sure channels exist and are public
- Invite the bot to private channels if needed

**4. "Import error" messages:**
```bash
# Clean and reinstall
pip uninstall slack-sdk
pip install slack-sdk

# Test imports
python -c "from slack_notifications.slack_utils import SlackNotifier; print('OK')"
```

**5. Bot can't send to channel:**
- Make sure the bot is added to the channel
- For private channels: `/invite @GeneratorAI Notifications`
- Check channel permissions

## 🎯 **Step 8: Customize for Your Use**

### 8.1 Add Your Project Names
Edit `slack_notifications/config/slack_channels.py` and add your actual project names:

```python
'installation': {
    'default': '#installation-notifications',
    'your-project-name': '#your-project-channel',
    'production-env': '#production-alerts',
    # ... add more as needed
}
```

### 8.2 Customize Templates
Edit templates in `slack_notifications/config/templates/installation_templates.py` to match your messaging style.

### 8.3 Create Your Own Test Script
```python
from slack_notifications.slack_utils import send_installation_notification

# Your custom installation alert
send_installation_notification(
    project_name='your-project',
    user_info={'name': 'Your User', 'email': '<EMAIL>'},
    status='completed',
    details={
        'installation_type': 'new_user_installation',
        'version': 'v1.0.0',
        'features_enabled': ['feature1', 'feature2']
    }
)
```

## ✅ **Verification Checklist**

- [ ] Slack app created and installed
- [ ] Bot token copied and set as environment variable
- [ ] Required channels created in Slack
- [ ] Channel IDs updated in configuration
- [ ] Dependencies installed (`slack-sdk`)
- [ ] Test script runs without errors
- [ ] Messages appear in Slack channels
- [ ] Rich formatting displays correctly

## 🎉 **You're Ready!**

Once all tests pass and you see messages in your Slack channels, your installation alert system is ready for production use!

## 📞 **Need Help?**

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all environment variables are set
3. Ensure bot permissions are correct
4. Test with a simple message first
5. Check Slack app logs in the Slack API dashboard
