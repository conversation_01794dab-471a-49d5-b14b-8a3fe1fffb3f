#!/bin/bash

# =============================================================================
# Slack Installation Alerts - Test Runner Script
# =============================================================================

echo "🚀 Slack Installation Alerts Test Runner"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    case $1 in
        "error") echo -e "${RED}❌ $2${NC}" ;;
        "success") echo -e "${GREEN}✅ $2${NC}" ;;
        "warning") echo -e "${YELLOW}⚠️  $2${NC}" ;;
        "info") echo -e "${BLUE}ℹ️  $2${NC}" ;;
        *) echo "$2" ;;
    esac
}

# Step 1: Check if Python is available
print_status "info" "Step 1: Checking Python installation..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    print_status "success" "Python 3 found: $(python3 --version)"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    print_status "success" "Python found: $(python --version)"
else
    print_status "error" "Python not found! Please install Python 3.7+"
    exit 1
fi

# Step 2: Check if slack-sdk is installed
print_status "info" "Step 2: Checking slack-sdk installation..."
if $PYTHON_CMD -c "import slack_sdk" 2>/dev/null; then
    print_status "success" "slack-sdk is installed"
else
    print_status "warning" "slack-sdk not found. Installing..."
    pip install slack-sdk
    if [ $? -eq 0 ]; then
        print_status "success" "slack-sdk installed successfully"
    else
        print_status "error" "Failed to install slack-sdk"
        exit 1
    fi
fi

# Step 3: Check if SLACK_BOT_TOKEN is set
print_status "info" "Step 3: Checking Slack bot token..."
if [ -z "$SLACK_BOT_TOKEN" ]; then
    print_status "error" "SLACK_BOT_TOKEN environment variable not set!"
    echo ""
    echo "To fix this, run one of the following:"
    echo ""
    echo "For macOS/Linux:"
    echo "export SLACK_BOT_TOKEN='xoxb-your-slack-bot-token-here'"
    echo ""
    echo "For Windows (Command Prompt):"
    echo "set SLACK_BOT_TOKEN=xoxb-your-slack-bot-token-here"
    echo ""
    echo "For Windows (PowerShell):"
    echo "\$env:SLACK_BOT_TOKEN = \"xoxb-your-slack-bot-token-here\""
    echo ""
    echo "Get your token from: https://api.slack.com/apps"
    exit 1
else
    TOKEN_PREVIEW="${SLACK_BOT_TOKEN:0:12}..."
    print_status "success" "Slack bot token found: $TOKEN_PREVIEW"
fi

# Step 4: Test module imports
print_status "info" "Step 4: Testing module imports..."
if $PYTHON_CMD -c "from slack_notifications.slack_utils import SlackNotifier" 2>/dev/null; then
    print_status "success" "Module imports working"
else
    print_status "error" "Module import failed. Check if slack_notifications folder exists."
    exit 1
fi

# Step 5: Run quick test
print_status "info" "Step 5: Running quick connection test..."
echo ""
$PYTHON_CMD quick_test.py
QUICK_TEST_RESULT=$?

if [ $QUICK_TEST_RESULT -eq 0 ]; then
    print_status "success" "Quick test passed!"
    echo ""
    
    # Ask user if they want to run full test
    echo "Do you want to run the full installation alerts test? (y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        print_status "info" "Step 6: Running full installation alerts test..."
        echo ""
        $PYTHON_CMD test_installation_alerts.py
        FULL_TEST_RESULT=$?
        
        if [ $FULL_TEST_RESULT -eq 0 ]; then
            echo ""
            print_status "success" "All tests completed successfully!"
            echo ""
            echo "🎉 Your Slack integration is working perfectly!"
            echo ""
            echo "Next steps:"
            echo "1. Check your Slack channels for the test messages"
            echo "2. Customize channels in slack_notifications/config/slack_channels.py"
            echo "3. Integrate into your application workflows"
        else
            print_status "error" "Full test failed. Check the output above for details."
            exit 1
        fi
    else
        echo ""
        print_status "info" "Skipping full test. You can run it manually with:"
        echo "python test_installation_alerts.py"
    fi
else
    print_status "error" "Quick test failed. Please check your configuration."
    echo ""
    echo "Common issues:"
    echo "1. Incorrect SLACK_BOT_TOKEN"
    echo "2. Bot not installed in workspace"
    echo "3. Missing bot permissions"
    echo "4. Network connectivity issues"
    echo ""
    echo "See SLACK_SETUP_GUIDE.md for detailed troubleshooting."
    exit 1
fi

echo ""
echo "=========================================="
echo "🎯 Test completed!"
echo "=========================================="
