{"name": "mcp-quickstart-ts", "version": "1.0.0", "main": "index.js", "type": "module", "bin": {"weather": "./build/index.js"}, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\""}, "files": ["build"], "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^22.10.0", "typescript": "^5.7.2"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.4.0"}}