{"name": "mcp-client-typescript", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\""}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@anthropic-ai/sdk": "^0.36.3", "@modelcontextprotocol/sdk": "^1.5.0", "dotenv": "^16.4.7"}, "devDependencies": {"@types/node": "^22.13.4", "typescript": "^5.7.3"}, "engines": {"node": ">=16.0.0"}}