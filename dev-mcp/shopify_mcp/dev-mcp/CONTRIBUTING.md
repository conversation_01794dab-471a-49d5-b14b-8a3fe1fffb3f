# Contributing

We welcome your contributions to the project. There are a few steps to take when looking to make a contribution.

- Open an issue to discuss the feature/bug
- If feature/bug is deemed valid then fork repo.
- Implement patch to resolve issue.
- Include tests to prevent regressions and validate the patch.
- Update the docs for any API changes.
- Submit a pull request.

> [!NOTE]
> If you're contributing changes to optimize or refactor existing code, you must also provide data proving that the changes have a positive performance impact.

## Bug Reporting

Shopify App Express package for Node uses GitHub issue tracking to manage bugs, please open an issue there.

## Feature Request

You can open a new issue on the GitHub issues and describe the feature you would like to see.

## Developing the packages

For instructions on how to set up for developing in this repo, please see the [instructions in the README](./README.md#developing-in-this-repo).
