import os
import json
import time
import requests # For requests.exceptions.ConnectionError
import sseclient # For handling SSE stream
from crewai import Agent, Task, Crew, Process
from crewai_tools import BaseTool

# 1. Define the Custom Tool for SSE
class SSESubscriberTool(BaseTool):
    name: str = "SSE Event Subscriber"
    description: str = (
        "Connects to a Server-Sent Events (SSE) stream, "
        "listens for events for a specified duration, until a specific data string is found, or a maximum number of events is reached. "
        "Returns the collected event data as a JSON string."
    )

    def _run(self, sse_url: str, duration_seconds: int = 10, stop_on_data: str = None, max_events: int = None) -> str:
        """
        Connects to an SSE stream and collects events based on specified criteria.

        Args:
            sse_url: The URL of the SSE stream.
            duration_seconds: Maximum duration in seconds to listen for events. Defaults to 10 seconds.
            stop_on_data: A substring. If found in any event's data, stop listening.
            max_events: Maximum number of events to collect before stopping.

        Returns:
            A JSON string representing a list of collected events, or an error/status message.
        """
        events_collected = []
        start_time = time.time()

        if not sse_url:
            return "Error: SSE URL must be provided."

        try:
            print(f"Attempting to connect to SSE stream: {sse_url} with duration: {duration_seconds}s, stop_on_data: '{stop_on_data}', max_events: {max_events}")
            # You can pass requests keyword arguments like headers or timeout to SSEClient
            # e.g., sseclient.SSEClient(sse_url, headers={'Authorization': 'Bearer ...'})
            # The default connection timeout for requests (used by sseclient-py) applies here.
            stream_messages = sseclient.SSEClient(sse_url)

            for event in stream_messages:
                current_time = time.time()
                elapsed_time = current_time - start_time

                # Ensure event.data is treated as a string
                event_data_str = str(event.data)
                
                event_dict = {
                    "id": event.id,
                    "event": event.event, # e.g., 'message', 'update', or custom
                    "data": event_data_str,
                    "retry": event.retry # retry timeout in milliseconds, if provided by server
                }
                events_collected.append(event_dict)
                print(f"Received event: {event_dict}")

                if max_events is not None and len(events_collected) >= max_events:
                    print(f"Stopping: Reached max events ({max_events}).")
                    break
                
                if stop_on_data and stop_on_data in event_data_str:
                    print(f"Stopping: Found stop_on_data ('{stop_on_data}') in event data.")
                    break

                if elapsed_time > duration_seconds:
                    print(f"Stopping: Exceeded duration ({duration_seconds} seconds).")
                    break
            
            stream_messages.close() # Important to close the connection

        except requests.exceptions.ConnectionError as e:
            return f"Error connecting to SSE stream {sse_url}: {e}. Check if the server is running and the URL is correct."
        except Exception as e:
            # Catching generic exception to handle any other issues during streaming
            return f"An unexpected error occurred during SSE stream processing from {sse_url}: {e}"

        if not events_collected:
            return f"No events collected from SSE stream {sse_url} within the given constraints (duration: {duration_seconds}s, stop_on_data: '{stop_on_data}', max_events: {max_events}). The stream might be quiet or ended early."
        
        return json.dumps(events_collected, indent=2)

# 2. (Optional but good practice) Set up API keys if your agents use LLMs that need them
# os.environ["OPENAI_API_KEY"] = "YOUR_OPENAI_API_KEY" 
# For other models like Claude, Google, etc., set the corresponding environment variables.
# If you're using a local LLM, this might not be necessary.

# 3. Instantiate your custom tool
sse_tool = SSESubscriberTool()

# 4. Define your agent(s)
sse_data_collector_agent = Agent(
    role='Real-time SSE Data Collector',
    goal=(
        "Connect to a given Server-Sent Events (SSE) stream, "
        "collect events according to specified parameters (duration, stop conditions), "
        "and report the findings accurately."
    ),
    backstory=(
        "You are a specialized agent expert in handling real-time data streams, "
        "particularly Server-Sent Events (SSE). You are adept at connecting to SSE endpoints, "
        "listening for incoming events, and extracting the required information efficiently and reliably. "
        "You pay close attention to collection parameters like duration, specific data triggers, or event limits."
    ),
    verbose=True,
    allow_delegation=False,
    tools=[sse_tool]
    # llm=your_llm_instance # Optionally specify an LLM, e.g., from Ollama, Anthropic, OpenAI
)

# 5. Define your task(s)
# IMPORTANT: Replace 'YOUR_SSE_ENDPOINT_URL_HERE' with the actual SSE endpoint you want to connect to.
# For testing, you can use a public SSE test endpoint.
# Example public SSE test endpoint: https://dummy-sse-server.onrender.com/stream (sends a JSON message every few seconds)
# Another one: https://stream.wikimedia.org/v2/stream/recentchange (high volume of data)

sse_collection_task = Task(
    description=(
        "Use the 'SSE Event Subscriber' tool to connect to an SSE stream. "
        # --- Replace with your target SSE URL below ---
        "The target SSE URL is 'https://dummy-sse-server.onrender.com/stream'. "
        "Listen for events for a maximum of 15 seconds. "
        "However, if you receive 5 events before the 15 seconds are up, you should stop then. "
        "Also, if any event data contains the string 'IMPORTANT_STOP_SIGNAL', stop immediately. "
        "Report all collected events."
    ),
    expected_output=(
        "A JSON string containing all events collected from the SSE stream, formatted clearly. "
        "Each event in the JSON array should include its id, event type, data, and any retry information. "
        "If no events are collected or an error occurs, provide a clear message explaining the situation."
    ),
    agent=sse_data_collector_agent
    # The agent will infer parameters like sse_url, duration_seconds, stop_on_data, max_events
    # from the task description and pass them to the SSESubscriberTool.
)

# 6. Create and configure the crew
data_collection_crew = Crew(
    agents=[sse_data_collector_agent],
    tasks=[sse_collection_task],
    process=Process.sequential, # Tasks will be executed one after another
    verbose=2 # 0 for no output, 1 for basic, 2 for detailed
)

# 7. Run the crew
if __name__ == '__main__':
    print("🚀 Starting SSE Data Collection Crew...")
    # Make sure you have any necessary API keys set if your agent's LLM requires them.
    # For example, for OpenAI:
    # if not os.getenv("OPENAI_API_KEY"):
    #     print("⚠️ OPENAI_API_KEY not set. Please set it as an environment variable.")
    #     exit()
        
    result = data_collection_crew.kickoff()

    print("\n\n✅ Crew work finished!")
    print("📊 Final Result:")
    print(result) 