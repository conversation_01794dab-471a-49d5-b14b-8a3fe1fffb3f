import { EventSource } from 'eventsource';
import axios from 'axios';
import { z } from 'zod';

const SSE_URL = 'http://localhost:8080/sse';
let sessionId = null;
let messageEndpoint = null;

console.log('Connecting to SSE stream...');

const es = new EventSource(SSE_URL);

es.addEventListener('endpoint', async (event) => {
  console.log('🟢 Received endpoint:', event.data);
  messageEndpoint = event.data;
  const urlParams = new URLSearchParams(messageEndpoint.split('?')[1]);
  sessionId = urlParams.get('sessionId');

  // Send message after receiving endpoint
  await sendToolCommand(sessionId);
});

es.addEventListener('message', (event) => {
  try {
    const data = JSON.parse(event.data);
    console.log('📩 Message:', JSON.stringify(data, null, 2));
  } catch (err) {
    console.log('📩 Message (raw):', event.data);
  }
});

es.onerror = (err) => {
  console.error('❌ SSE connection error:', err);
};

// Function to POST a tool command
async function sendToolCommand(sessionId) {
  const postUrl = `http://localhost:8080/messages?sessionId=${sessionId}`;

  // const payload = {
  //   "jsonrpc": "2.0",
  //   "id": 1,
  //   "method": "tools/list",
  //   "params": {
  //     "cursor": "optional-cursor-value"
  //   }
  // };

  const payload = {
    "jsonrpc": "2.0",
    "id": "2",
    "method": "tools/call",
    "params": {
      "name": "introspect_admin_schema",
      "arguments": {
        "query": "products",
        "filter": ["all"]
      }
      // "arguments": {
      //   "prompt": "how to create a new product"
      // }

    }
  };

  try {
    console.log(`➡️ Sending tool command to ${postUrl}`);
    const response = await axios.post(postUrl, payload, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('✅ POST response:', response.status);
  } catch (err) {
    console.error('❌ Failed to send message:', err.message);
  }
}
