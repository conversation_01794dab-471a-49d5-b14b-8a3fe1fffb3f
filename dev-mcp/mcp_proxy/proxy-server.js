const express = require('express');
const cors = require('cors'); // Optional: useful for local testing
const { spawn } = require('child_process');
const path = require('path');
const readline = require('readline');

const app = express();

// --- Configuration ---
const PORT = process.env.PORT || 8080;
// Crucial: Path within the container to the actual MCP script to run
const MCP_SCRIPT_PATH = '/Users/<USER>/Documents/binary_products/storeworkers/dev-mcp/dist/index.js';
// Optional: Arguments for the MCP script, passed as a JSON string array
const MCP_SCRIPT_ARGS = JSON.parse(process.env.MCP_SCRIPT_ARGS || '[]');
// Optional: Working directory for the MCP script
const MCP_CWD = '/Users/<USER>/Documents/binary_products/storeworkers/dev-mcp';
const LOG_LEVEL = 'debug'; // 'info' or 'debug'

const log = (level, ...args) => {
    if (level === 'debug' && LOG_LEVEL !== 'debug') return;
    console.log(`[${new Date().toISOString()}] [${level.toUpperCase()}]`, ...args);
}

app.use(cors()); // Enable CORS if needed
app.use(express.json()); // Middleware to parse JSON POST bodies

let clients = []; // Array to hold connected SSE client response objects
let mcpProcess = null;
let restartTimer = null;

function broadcastToClients(message) {
    log('debug', 'Broadcasting to clients:', message);
    clients.forEach(clientRes => {
        try {
            // Format as Server-Sent Event: data: <json_string>\n\n
            clientRes.write(`data: ${JSON.stringify(message)}\n\n`);
        } catch (error) {
            log('error', 'Failed to write to a client:', error.message);
            // Attempt to close this specific client's connection
            try { clientRes.end(); } catch (e) { }
        }
    });
}

function startMcpProcess() {
    clearTimeout(restartTimer);
    if (mcpProcess) {
        log('warn', 'MCP process already running. Killing existing one.');
        mcpProcess.kill();
        mcpProcess = null;
    }

    const scriptFullPath = path.resolve(MCP_SCRIPT_PATH);
    const cwdFullPath = path.resolve(MCP_CWD);

    log('info', `Starting MCP process: node ${scriptFullPath} ${MCP_SCRIPT_ARGS.join(' ')} in ${cwdFullPath}`);

    try {
        mcpProcess = spawn('node', [scriptFullPath, ...MCP_SCRIPT_ARGS], {
            stdio: ['pipe', 'pipe', 'pipe'], // pipe stdin, stdout, stderr
            cwd: cwdFullPath,
            env: { ...process.env } // Pass environment variables
        });
    } catch (spawnError) {
        log('error', `Failed to spawn MCP process: ${spawnError.message}`);
        scheduleRestart();
        return;
    }


    // --- Handle MCP stdout (Send to SSE clients) ---
    // Use readline to handle messages potentially split across data chunks
    const rl = readline.createInterface({
        input: mcpProcess.stdout,
        crlfDelay: Infinity
    });

    rl.on('line', (line) => {
        log('debug', `MCP_STDOUT: ${line}`);
        try {
            const message = JSON.parse(line); // Assuming MCP uses JSON per line
            broadcastToClients(message);
        } catch (parseError) {
            log('warn', `Failed to parse MCP stdout line as JSON: "${line}". Error: ${parseError.message}`);
            // Optionally broadcast raw line or an error message
            broadcastToClients({ type: 'mcp_raw_stdout', content: line });
        }
    });

    // --- Handle MCP stderr ---
    mcpProcess.stderr.on('data', (data) => {
        const message = data.toString().trim();
        log('error', `MCP_STDERR: ${message}`);
        // Send stderr over SSE as a specific event type
        broadcastToClients({ type: 'mcp_stderr', content: message });
    });

    // --- Handle MCP process exit ---
    mcpProcess.on('close', (code, signal) => {
        log('warn', `MCP process exited with code ${code}, signal ${signal}`);
        mcpProcess = null;
        broadcastToClients({ type: 'mcp_status', status: 'disconnected', code, signal });
        // Clean up clients immediately on process exit
        clients.forEach(clientRes => { try { clientRes.end(); } catch (e) { } });
        clients = [];
        scheduleRestart(); // Attempt to restart after a delay
    });

    mcpProcess.on('error', (err) => {
        log('error', `MCP process error: ${err.message}`);
        if (mcpProcess && !mcpProcess.killed) {
            mcpProcess.kill(); // Ensure it's terminated
        }
        mcpProcess = null;
        broadcastToClients({ type: 'mcp_status', status: 'error', message: err.message });
        clients.forEach(clientRes => { try { clientRes.end(); } catch (e) { } });
        clients = [];
        scheduleRestart(); // Attempt to restart
    });

    log('info', 'MCP process started successfully.');
}

function scheduleRestart(delay = 5000) { // Restart after 5 seconds
    clearTimeout(restartTimer);
    log('info', `Scheduling MCP process restart in ${delay}ms`);
    restartTimer = setTimeout(() => {
        if (clients.length > 0) { // Only restart if clients are still connected/waiting
            log('info', 'Attempting scheduled restart...');
            startMcpProcess();
        } else {
            log('info', 'Skipping scheduled restart as no clients are connected.');
        }
    }, delay);
}

// --- SSE Endpoint (Server -> Client stream) ---
app.get('/sse', (req, res) => {
    log('info', `SSE client connected from ${req.ip}`);
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // Useful for Nginx buffering issues
    res.flushHeaders(); // Send headers immediately

    clients.push(res); // Add the response object to the list
    log('info', `Total SSE clients: ${clients.length}`);

    // Send an initial connection message
    try {
        res.write(`data: ${JSON.stringify({ type: 'proxy_status', status: 'connected' })}\n\n`);
    } catch (e) {
        log('error', 'Error sending initial SSE status', e);
        // Remove client if write fails immediately
        clients = clients.filter(clientRes => clientRes !== res);
        return; // Don't proceed if we can't write
    }


    // Start the MCP process if it's not running and this is the first client
    if (!mcpProcess && clients.length === 1) {
        log('info', 'First client connected, starting MCP process.');
        startMcpProcess();
    } else if (mcpProcess) {
        // If process is already running, maybe send current status or tool list?
        // Example: Send a status message indicating MCP is already running
        try {
            res.write(`data: ${JSON.stringify({ type: 'mcp_status', status: 'already_running' })}\n\n`);
        } catch (e) { }
    }

    req.on('close', () => {
        log('info', `SSE client disconnected ${req.ip}`);
        clients = clients.filter(clientRes => clientRes !== res); // Remove client on disconnect
        log('info', `Total SSE clients: ${clients.length}`);
        // Optional: Stop the MCP process if no clients are left? Depends on desired behavior.
        // if (clients.length === 0 && mcpProcess) {
        //   log('info', 'Last client disconnected, stopping MCP process.');
        //   clearTimeout(restartTimer);
        //   mcpProcess.kill();
        //   mcpProcess = null;
        // }
    });
});

// --- POST Endpoint (Client -> Server messages) ---
app.post('/message', (req, res) => {
    const message = req.body;
    log('debug', 'Received message via POST:', message);

    if (mcpProcess && mcpProcess.stdin && !mcpProcess.stdin.destroyed) {
        try {
            // MCP protocol often expects newline-delimited JSON
            const messageString = JSON.stringify(message) + '\n';
            log('debug', `Writing to MCP_STDIN: ${messageString.trim()}`);
            mcpProcess.stdin.write(messageString);
            res.status(200).send({ status: 'Message relayed to MCP process' });
        } catch (error) {
            log('error', "Error writing to MCP stdin:", error.message);
            res.status(500).send({ error: 'Error writing to MCP process stdin' });
        }
    } else {
        log('warn', 'POST received but MCP process stdin is not available or process not running.');
        res.status(502).send({ error: 'MCP process not running or stdin unavailable' });
    }
});

// --- Health Check Endpoint ---
app.get('/health', (req, res) => {
    if (mcpProcess && !mcpProcess.killed) {
        res.status(200).send({ status: 'OK', mcp_process: 'running' });
    } else {
        res.status(503).send({ status: 'ERROR', mcp_process: 'not_running' });
    }
});


// --- Start the HTTP server ---
app.listen(PORT, () => {
    log('info', `Generic MCP stdio-to-HTTP/SSE Proxy listening on port ${PORT}`);
    log('info', `Target MCP script path: ${path.resolve(MCP_SCRIPT_PATH)}`);
    log('info', `Target MCP CWD: ${path.resolve(MCP_CWD)}`);
    log('info', `Target MCP args: ${JSON.stringify(MCP_SCRIPT_ARGS)}`);
    // Decide whether to start immediately or wait for first client (currently waits)
});

// Graceful shutdown
process.on('SIGTERM', () => {
    log('info', 'SIGTERM signal received. Shutting down proxy and MCP process.');
    clearTimeout(restartTimer);
    if (mcpProcess) {
        mcpProcess.kill('SIGTERM'); // Send SIGTERM to child first
    }
    clients.forEach(clientRes => { try { clientRes.end(); } catch (e) { } }); // Close client connections
    process.exit(0);
});
process.on('SIGINT', () => {
    log('info', 'SIGINT signal received. Shutting down proxy and MCP process.');
    clearTimeout(restartTimer);
    if (mcpProcess) {
        mcpProcess.kill('SIGINT');
    }
    clients.forEach(clientRes => { try { clientRes.end(); } catch (e) { } });
    process.exit(0);
});
