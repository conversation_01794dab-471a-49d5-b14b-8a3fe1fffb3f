# Use a specific Node.js version (e.g., 18-alpine) for stability
FROM node:18-alpine AS proxy-base

# Install necessary OS packages if any (less likely needed for basic proxy)
# RUN apk update && apk add --no-cache some-package

WORKDIR /usr/src/proxy

# Install proxy dependencies only
# Copy package.json and package-lock.json for the proxy application
# Make sure these are in the build context directory
COPY package.proxy.json ./package.json
COPY package-lock.proxy.json ./package-lock.json
RUN npm install --only=production

# Copy the proxy server code
COPY proxy-server.js .

# Define the location for the target MCP application
# The user's Dockerfile MUST copy the necessary files here
# Example: COPY --from=mcp-builder /usr/src/app /usr/src/proxy/mcp-app
RUN mkdir -p /usr/src/proxy/mcp-app

# --- Runtime Configuration ---
# Default port the proxy listens on
ENV PORT=8080
# Default path *within the container* to the target MCP script
ENV MCP_SCRIPT_PATH=./mcp-app/dist/index.js
# Default working directory *within the container* for the target MCP script
ENV MCP_CWD=./mcp-app
# Default arguments for the MCP script (JSON array string)
ENV MCP_SCRIPT_ARGS='[]'
# Logging level ('info' or 'debug')
ENV LOG_LEVEL='info'

EXPOSE ${PORT}

# Command to run the proxy server
CMD [ "node", "proxy-server.js" ]