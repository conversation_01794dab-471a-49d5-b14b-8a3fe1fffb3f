name: Run Tests
on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review
jobs:
  test:
    runs-on: ubuntu-latest
    name: Test
    strategy:
      fail-fast: true
      matrix:
        node:
          - 22
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Setup NodeJS ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: "pnpm"
          cache-dependency-path: "**/pnpm-lock.yaml"
      - name: Install dependencies
        run: pnpm install
      - name: Run tests
        run: pnpm test
