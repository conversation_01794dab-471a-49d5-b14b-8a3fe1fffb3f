{"name": "mcp-proxy", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsup", "test": "vitest run && tsc && prettier --check . && eslint .", "format": "prettier --write . && eslint --fix ."}, "bin": {"mcp-proxy": "dist/bin/mcp-proxy.js"}, "keywords": ["MCP", "SSE", "proxy"], "type": "module", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "description": "A TypeScript SSE proxy for MCP servers that use stdio transport.", "module": "dist/index.js", "types": "dist/index.d.ts", "dependencies": {"@modelcontextprotocol/sdk": "^1.10.1", "eventsource": "^3.0.6", "yargs": "^17.7.2"}, "repository": {"url": "https://github.com/punkpeye/mcp-proxy"}, "homepage": "https://glama.ai/mcp", "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github"]}, "devDependencies": {"@eslint/js": "^9.25.1", "@sebbo2002/semantic-release-jsr": "^2.0.5", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.1", "@types/yargs": "^17.0.33", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-perfectionist": "^4.11.0", "get-port-please": "^3.1.2", "jiti": "^2.4.2", "prettier": "^3.5.3", "semantic-release": "^24.2.3", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1", "vitest": "^3.1.1"}, "tsup": {"entry": ["src/index.ts", "src/bin/mcp-proxy.ts"], "format": ["esm"], "dts": true, "splitting": true, "sourcemap": true, "clean": true}}