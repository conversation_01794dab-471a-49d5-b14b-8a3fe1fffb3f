const EventSource = require('eventsource');

const es = new EventSource('http://localhost:8080/mcp/stream');

es.onmessage = (event) => {
    console.log('Default event:', event.data);
};

es.addEventListener('prediction', (event) => {
    console.log('Prediction:', JSON.parse(event.data));
});

es.addEventListener('done', (event) => {
    console.log('Stream complete.');
    es.close(); // Close the stream
});
