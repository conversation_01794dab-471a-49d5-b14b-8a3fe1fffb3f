# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "6c27ef9733d2372a53740b73425cea81"
name = "storeworkers-dev"
handle = "storeworkers-dev"
application_url = "https://overseas-locate-tray-vessels.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "harsha-stores-5.myshopify.com"
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_content,read_online_store_pages,unauthenticated_read_content,write_content,write_metaobject_definitions,write_metaobjects,write_online_store_pages,write_products,write_publications,write_themes,read_all_orders,read_orders,write_orders,read_products,write_products,read_returns,write_returns,read_order_edits,write_order_edits,read_draft_orders,write_draft_orders,read_fulfillments,write_fulfillments,read_customer_payment_methods,write_customers,read_customers,read_inventory,write_inventory,read_shipping,write_shipping"

[auth]
redirect_urls = [
  "https://overseas-locate-tray-vessels.trycloudflare.com/auth/callback",
  "https://overseas-locate-tray-vessels.trycloudflare.com/auth/shopify/callback",
  "https://overseas-locate-tray-vessels.trycloudflare.com/api/auth/callback"
]

[pos]
embedded = false
