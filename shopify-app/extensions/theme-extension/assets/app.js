const o="shipready",r=`/apps/${o}/api/events`;window.shipready=window.shipready||[];function i(e,n){const s={event:e,details:n,timestamp:new Date().toISOString()};fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}).then(t=>{t.ok?console.log("Event sent successfully"):console.error("Error sending event:",t.status)}).catch(t=>{console.error("Error sending event:",t)})}window.shipready.push=function(e,n){Array.prototype.push.call(this,[e,n]),i(e,n)};
