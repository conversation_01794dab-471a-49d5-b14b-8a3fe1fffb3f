{"name": "shipready-v0-1", "private": true, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite", "extension-build": "vite build --config vite.extension.config.js", "extension-watch": "vite build --config vite.extension.config.js --watch"}, "prisma": {"seed": "node prisma/seeds/index.js"}, "type": "module", "engines": {"node": ">=18.20.0"}, "dependencies": {"@intercom/messenger-js-sdk": "^0.0.14", "@prisma/client": "^6.6.0", "@remix-run/dev": "^2.16.5", "@remix-run/node": "^2.16.5", "@remix-run/react": "^2.16.5", "@remix-run/serve": "^2.16.5", "@shopify/app-bridge-react": "^4.1.8", "@shopify/polaris": "^13.9.5", "@shopify/polaris-icons": "^9.3.1", "@shopify/polaris-viz": "^16.12.1", "@shopify/shopify-api": "^11.12.0", "@shopify/shopify-app-remix": "^3.8.2", "@shopify/shopify-app-session-storage-prisma": "^6.0.6", "@supabase/supabase-js": "^2.49.4", "axios": "^1.8.4", "i18next": "^25.0.0", "isbot": "^5.1.26", "mixpanel-browser": "^2.64.0", "moment": "^2.30.1", "pg": "^8.14.1", "pino": "^9.6.0", "postmark": "^4.0.5", "prisma": "^6.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.1", "vite-tsconfig-paths": "^5.1.4", "zustand": "^5.0.3"}, "devDependencies": {"@remix-run/eslint-config": "^2.16.5", "@shopify/api-codegen-preset": "^1.1.7", "@types/eslint": "^9.6.1", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint-config-prettier": "^10.1.2", "prettier": "^3.5.3", "typescript": "^5.8.3", "vite": "^6.3.1"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"undici": "6.13.0"}, "overrides": {"undici": "6.13.0"}, "author": "<PERSON>u<PERSON><PERSON><PERSON>"}