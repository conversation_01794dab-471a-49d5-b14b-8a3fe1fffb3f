import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";

export async function fetchShopifyCollections(admin) {
  const storeName = "";
  loggerInfo("Fetch All  collections from the Shopify Started", storeName);
  try {
    let allCollections = [];
    let hasNextPage = true;
    let cursor = null;
    while (hasNextPage) {
      const collections = await admin.graphql(
        `
       query($cursor: String) {
     collections(first: 250, after: $cursor) {
    nodes {
      id
      title
      products(first: 250) {
        edges {
          node {
            id
            title
            description
            vendor
            tags
            priceRangeV2 {
              minVariantPrice {
                amount
              }
            }
            media(first: 1) {
              edges {
                node {
                  ... on MediaImage {
                    image {
                      originalSrc
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}

          `,
        { variables: { cursor } },
      );

      const response = await collections.json();
      if (response?.data?.collections) {
        const collectionsData = response.data.collections.nodes.map(
          (collection) => {
            return {
              title: collection.title,
              description: collection.description || "",
              products: collection.products.edges.map(({ node }) => ({
                id: node.id,
                title: node.title,
                description: node.description || "",
                priceRangeV2: node.priceRangeV2,
                tags: node.tags || [],
                vendor: node.vendor,
                media: node.media?.edges?.[0]?.node || {},
              })),
            };
          },
        );
        allCollections.push(...collectionsData);
        hasNextPage = response.data.collections.pageInfo.hasNextPage;
        cursor = response.data.collections.pageInfo.endCursor;
      } else {
        hasNextPage = false;
      }
    }
    loggerInfo("Fetch All  collections from the Shopify completed", storeName);
    return sendResponse({
      status: 200,
      message: "collections fetched successfully",
      data: allCollections || null,
    });
  } catch (error) {
    loggerError(
      "Failed to fetch collections data from Shopify)",
      storeName,
      error.message,
    );
    return sendResponse({
      status: 500,
      message: `Failed to fetch collections data from Shopify : ${error.message}`,
    });
  }
}
