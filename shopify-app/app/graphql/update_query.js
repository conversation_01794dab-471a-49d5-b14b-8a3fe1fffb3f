import axios from "axios";
import { loggerError, loggerInfo, sendResponse } from "../utilities/helper";
import { graphqlAPIVersion } from "../config/config";
export async function updateQuery({ query, variables, accessToken, store }) {
  const url = `https://${store}/admin/api/${graphqlAPIVersion}/graphql.json`;
  loggerInfo("query update started", store);
  try {
    const response = await axios.post(
      url,
      {
        query,
        variables,
      },
      {
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": accessToken,
        },
      },
    );
    if (response.data.errors) {
      const errorMessage = response.data.errors[0].message;
      loggerError(
        "GraphQL errors in query update",
        store,
        JSON.stringify(response.data.errors),
      );
      return sendResponse({
        status: 400,
        message: errorMessage,
        functionType: "updateQuery",
        data: null,
      });
    }
    const mutationName = Object.keys(response.data.data || {})[0];
    const userErrors = response.data?.data?.[mutationName]?.userErrors || [];
    if (userErrors.length > 0) {
      const errorMessage = userErrors[0].message;
      loggerError(
        "User errors in query update",
        store,
        JSON.stringify(userErrors),
      );
      return sendResponse({
        status: 400,
        message: errorMessage,
        functionType: "updateQuery",
        data: null,
      });
    }
    loggerInfo("query update completed", store, JSON.stringify(response.data));
    return sendResponse({
      status: 200,
      message: "Query updated successfully",
      data: response.data,
    });
  } catch (error) {
    const errorMessage =
      error.response?.data?.errors?.[0]?.message || error.message;
    loggerError(
      "Error in query update",
      store,
      error.response?.data || error.message,
    );
    return sendResponse({
      status: 500,
      message: errorMessage,
      data: null,
    });
  }
}
