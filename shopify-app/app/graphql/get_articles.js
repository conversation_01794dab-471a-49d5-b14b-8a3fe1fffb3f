import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
export async function fetchShopifyArticles(admin) {
  const shopName = admin?.rest?.session?.shop || "";
  loggerInfo(`Fetching Shopify Articles for the store`, shopName);
  const articles = [];
  let hasNextPage = true;
  let endCursor = null;
  while (hasNextPage) {
    try {
      const response = await admin.graphql(
        `#graphql
          query ArticleList($endCursor: String) {
            articles(first: 50, after: $endCursor) {
              edges {
                node {
                  id
                  handle
                  title
                  createdAt
                  publishedAt
                  body
                  author {
                    name
                  }
                  blog{
                    id
                    title
                  }
                  image{
                    id
                    src
                  }
                }
              }
              pageInfo {
                hasNextPage
                endCursor
              }
            }
          }`,
        {
          variables: { endCursor },
        },
      );

      const data = await response.json();
      if (data.errors) {
        loggerError(
          `Error fetching Shopify Articles GraphQL Error`,
          shopName,
          JSON.stringify(data?.errors),
        );
        return sendResponse({
          status: 400,
          message: `GraphQL Error Failed to fetch articles data from Shopify : ${data.errors}`,
        });
      }
      const articleNodes = data.data.articles.edges.map((edge) => {
        const updatedNode = {
          ...edge.node,
          id:edge.node.id,
          blog: edge.node.blog
            ? {
                ...edge.node.blog,
                id:edge.node.blog.id
              }
            : null,
        };
        return updatedNode;
      });

      articles.push(...articleNodes);
      hasNextPage = data.data.articles.pageInfo.hasNextPage;
      endCursor = data.data.articles.pageInfo.endCursor;
    } catch (error) {
      loggerError(`Error fetching Shopify Articles`, shopName, error.message);
      return sendResponse({
        status: 500,
        message: `Failed to fetch articles data from Shopify : ${error.message}`,
      });
      break;
    }
  }
  loggerInfo(
    `Fetching Shopify Articles completed. Total articles fetched: ${articles.length}`,
    shopName,
  );
  return sendResponse({
    status: 200,
    message: "Articles fetched successfully",
    data: articles || [],
  });
}
