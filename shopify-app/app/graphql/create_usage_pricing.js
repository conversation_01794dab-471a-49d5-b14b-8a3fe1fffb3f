import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
export async function createAppUsageRecord(admin, data) {
  const shopName = admin?.rest?.session?.shop || "";
  const { amount, subscriptionLineItemId, currencyCode, description } = data;
  loggerInfo("Creating App usage based charge started", shopName);
  try {
    const response = await admin.graphql(
      `#graphql
          mutation appUsageRecordCreate($description: String!, $price: MoneyInput!, $subscriptionLineItemId: ID!) {
            appUsageRecordCreate(description: $description, price: $price, subscriptionLineItemId: $subscriptionLineItemId) {
              userErrors {
                field
                message
              }
              appUsageRecord {
                id
              }
            }
          }`,
      {
        variables: {
          subscriptionLineItemId,
          price: {
            amount,
            currencyCode,
          },
          description,
        },
      },
    );
    const data = await response.json();
    if (data.errors) {
      loggerError("Failed to create app usage record:", shopName, data.errors);
      return sendResponse({
        status: 400,
        message: "Failed to create app usage record",
      });
    }
    const { userErrors, appUsageRecord } = data.data.appUsageRecordCreate;
    if (userErrors.length > 0) {
      loggerError("Error creating app usage record:", shopName, userErrors);
      throw sendResponse({
        status: 400,
        message: "Error creating app usage record",
      });
    }
    loggerInfo(
      "Creating App usage based charge completed",
      shopName,
      appUsageRecord.id,
    );
    return sendResponse({
      status: 200,
      message: "success",
      data: appUsageRecord.id || null,
    });
  } catch (error) {
    console.error("Error creating app usage record:", error.message);
    loggerError("Error creating app usage record:", shopName, error.message);
    throw sendResponse({
      status: 500,
      message: "Error creating app usage record",
    });
  }
}
