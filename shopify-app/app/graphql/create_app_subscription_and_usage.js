import { loggerInfo, loggerError } from "../utilities/helper";
export async function createAppSubscriptionWithUsage(admin, plan, appHandle, shopName) {
  loggerInfo("Creating App Subscription started", shopName, plan);
  try {
    const mutation = `
        mutation AppSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!, $test: Boolean!) {
          appSubscriptionCreate(name: $name, returnUrl: $returnUrl, lineItems: $lineItems, test: $test) {
            userErrors { field message }
            appSubscription { id }
            confirmationUrl
          }
        }
      `;
    const variables = {
      name: plan.name,
      returnUrl: `https://admin.shopify.com/store/${shopName}/apps/${appHandle}/app/`,
      test: plan.test,
      lineItems: [
        {
          plan: {
            appUsagePricingDetails: {
              terms: plan.terms,
              cappedAmount: {
                amount: plan.cappedAmount.amount,
                currencyCode: plan.cappedAmount.currencyCode,
              },
            },
          },
        },
        {
          plan: {
            appRecurringPricingDetails: {
              price: { amount: plan.amount, currencyCode: plan.currencyCode },
              //cappedAmount: { amount: 2, currencyCode: "USD" }, // Add this lin
              interval: plan.interval,
            },
          },
        },
      ],
    };
    const response = await admin.graphql(mutation, { variables });
    loggerInfo("Creating App Subscription completed", shopName);
    if (!response.ok) {
      loggerError(
        "Error from GraphQL Creating App Subscription",
        shopName,
        response.message,
      );
      throw new Error(`GraphQL request failed with status: ${response.status}`);
    }
    const data = await response.json();
    if (data.errors) {
      loggerError(
        "Error from GraphQL Creating App Subscription",
        shopName,
        data.error,
      );
      console.error("GraphQL errors:", data.errors);
      throw new Error("GraphQL operation failed");
    }
    const { userErrors, appSubscription, confirmationUrl } =
      data.data.appSubscriptionCreate;

    if (userErrors && userErrors.length > 0) {
      loggerError(
        "User errors occurred during subscription creation",
        shopName,
        userErrors,
      );
      throw new Error("User errors occurred during subscription creation");
    }
    return {
      appSubscriptionId: appSubscription.id,
      confirmationUrl: confirmationUrl,
    };
  } catch (error) {
    loggerError(
      "Error from Error creating app subscription",
      shopName,
      error.message,
    );
    throw error;
  }
}
