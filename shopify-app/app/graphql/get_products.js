import { loggerInfo, loggerError } from "../utilities/helper";
// import { metaFieldKey, metaFieldNameSpace } from "../config/config";
//import assetImage from "../config/images";
export async function fetchShopifyProducts(admin, isSync = false) {
  const shopName = ""
  loggerInfo(`Fetching Shopify products started`, shopName);
  let products = [];
  let hasNextPage = true;
  let endCursor = null;

  while (hasNextPage) {
    try {
      const response = await admin.graphql(`
        #graphql
        query {
          products(first: 250, after: ${endCursor ? `"${endCursor}"` : "null"}, reverse: true) {
            edges {
              node {
                id
                title
                description
                handle
                createdAt
                vendor
                tags
                totalInventory
                images(first: 10) {
                  edges {
                    node {
                      id
                      url
                      altText
                      width
                      height
                    }
                  }
                }
                variants(first: 250) {
                  edges {
                    cursor
                    node {
                      id
                      title
                      price
                      position
                      displayName
                    }
                  }
                }
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `);

      const data = await response.json();
      const fetchedProducts = data.data.products.edges.map(({ node }) => ({
        ...node,
        imagesUrl: node.images.edges[0]?.node.url  || "",
        variants: node.variants.edges.map(({cursor, node: variant }) => ({
          id: variant.id,
          title: variant.title === "Default Title" ? node.title : variant.title,
          price: variant.price,
          position: variant.position,
          displayName: variant.displayName,
          cursor:cursor

        })),
      }));

      products.push(...fetchedProducts);
      hasNextPage = data.data.products.pageInfo.hasNextPage;
      endCursor = data.data.products.pageInfo.endCursor;
      loggerInfo(`Fetching Shopify products completed`, shopName);
    } catch (error) {
      loggerError(
        `Error fetching Shopify products`,
        shopName,
        JSON.stringify(error)
      );
      break;
    }
  }
  if (isSync) {
    const transformedProducts = products.map(product => ({
      id: product.id,
      title: product.title,
      handle: product.handle,
      totalInventory: product.totalInventory,
      tags: product.tags,
      vendor: product.vendor,
      description: product.description,
      createdAt: product.createdAt,
      image_url:product.imagesUrl,
      variants: product.variants.map(variant => ({
        cursor: variant.cursor,
        node: {
          id: variant.id,
          title: variant.title,
          price: variant.price,
          position: variant.position,
          displayName: variant.displayName
        }
      }))
    }));
    products = transformedProducts
  }
  return products;
}


