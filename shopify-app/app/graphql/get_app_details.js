import { loggerInfo, loggerError } from "../utilities/helper";
export async function fetchAppDetail(admin) {
    const shopName = admin?.rest?.session?.shop || "";
    try {
        const query = `
        query {
          app {
            handle
          }
        }
      `;
        const response = await admin.graphql(query);
        if (!response.ok) {
            loggerError(`GraphQL request failed with status`, shopName, response.status)
            throw new Error(`GraphQL request failed with status: ${response.status}`);
        }
        const data = await response.json();
        if (data.errors) {
            loggerError(`Failed to fetch app handle due to GraphQL errors`, shopName, data.errors)
            throw new Error("Failed to fetch app handle due to GraphQL errors");
        }
        const appHandle = data?.data?.app?.handle || "";
        return appHandle;
    } catch (error) {
        loggerError(`Error fetching app handle`, shopName, error.message)
        console.error("Error fetching app handle:", error.message);
        throw error;
    }
}


