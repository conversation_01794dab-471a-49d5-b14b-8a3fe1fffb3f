import { loggerInfo, loggerError } from "../utilities/helper";
export async function fetchStoreData(admin) {
    const response = await admin.graphql(
      `#graphql
      query {
        shop {
          id
          name
          email
          myshopifyDomain
          ianaTimezone
          primaryDomain {
            url
          }
          billingAddress {
            phone
            countryCode
          }
        }
      }`,
    );
    const storeData = await response.json();
    const shopId = storeData.data.shop.id.match(/\d+$/)[0];
    return {
      id: shopId,
      name: storeData.data.shop.name,
      email: storeData.data.shop.email,
      phone: storeData.data.shop.billingAddress.phone,
      countryCode: storeData.data.shop.billingAddress.countryCode || "us",
      myshopifyDomain: storeData.data.shop.myshopifyDomain,
      timeZone: storeData.data.shop.ianaTimezone,
      store_domain: storeData.data.shop.primaryDomain.url
    };
  }
  