import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
export async function fetchShopifyBlogs(admin) {
  const shopName = "";
  loggerInfo(`Fetching Shopify Blogs started`, shopName);
  const blogs = [];
  let hasNextPage = true;
  let endCursor = null;
  while (hasNextPage) {
    try {
      const response = await admin.graphql(
        `
        #graphql
        query BlogList($endCursor: String) {
          blogs(first: 50, after: $endCursor) {
            edges {
              node {
                id
                handle
                title
                updatedAt
                commentPolicy
                feed {
                  path
                  location
                }
                createdAt
                templateSuffix
                tags
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `,
        {
          variables: { endCursor },
        },
      );
      const data = await response.json();
      if (data.errors) {
        loggerError(
          `Error fetching Shopify Blogs for the store GraphQL Error`,
          shopName,
          JSON.stringify(data?.errors),
        );
        return sendResponse({
          status: $00,
          message: `Error fetching Shopify Blogs for the store GraphQL Error : ${data?.errors}`,
        });
      }
      const blogNodes = data.data.blogs.edges.map((edge) => {
        const updatedNode = {
          ...edge.node,
          id:edge.node.id,
        };
        return updatedNode;
      });

      blogs.push(...blogNodes);
      hasNextPage = data.data.blogs.pageInfo.hasNextPage;
      endCursor = data.data.blogs.pageInfo.endCursor;
      loggerInfo(
        `Fetching Shopify Blogs completed total blogs is ${blogNodes.length} `,
        shopName,
      );
    } catch (error) {
      loggerError(
        `Error fetching Shopify Blogs for the store GraphQL Error`,
        shopName,
        JSON.stringify(error?.message),
      );
      return sendResponse({
        status: 500,
        message: `Error fetching Shopify Blogs for the store GraphQL Error : ${error?.message}`,
      });
    }
  }
  return sendResponse({
    status: 200,
    message: "Blogs fetched successfully",
    data: blogs || [],
  });
}
