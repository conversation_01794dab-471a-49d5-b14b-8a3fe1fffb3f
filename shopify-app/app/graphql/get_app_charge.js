import { loggerError } from "../utilities/helper";
export async function getSubscriptionStatus(admin) {
  const shopName = admin?.rest?.session?.shop || "";
  try {
    const response = await admin.graphql(`
      query {
        currentAppInstallation {
          activeSubscriptions {
            id
            name
            test
            status
            trialDays
            createdAt
            currentPeriodEnd
            returnUrl
            lineItems {
              id
              plan {
                pricingDetails {
                  __typename
                  ... on AppUsagePricing {
                    balanceUsed {
                      amount
                      currencyCode
                    }
                    cappedAmount {
                      amount
                      currencyCode
                    }
                    terms
                  }
                }
              }
            }
          }
        }
      }
    `);

    const result = await response.json();
    const subs = result?.data?.currentAppInstallation?.activeSubscriptions || [];

    if (!subs.length) return null;

    const sub = subs[0];

    return {
      id: sub.id,
      name: sub.name,
      test: sub.test,
      status: sub.status,
      trialDays: sub.trialDays,
      createdAt: sub.createdAt,
      currentPeriodEnd: sub.currentPeriodEnd,
      returnUrl: sub.returnUrl,
      lineItems: sub.lineItems?.map((item) => ({
        id: item.id,
        plan: {
          pricingDetails: item.plan?.pricingDetails?.__typename === "AppUsagePricing"
            ? {
                balanceUsed: item.plan?.pricingDetails?.balanceUsed,
                cappedAmount: item.plan?.pricingDetails?.cappedAmount,
                terms: item.plan?.pricingDetails?.terms,
              }
            : {},
        },
      })) || [],
    };

  } catch (error) {
    loggerError(`Error fetching subscription status`, shopName, error.message);
    return null;
  }
}
