// EmailPreferenceCard.jsx
import React from "react";
import { Card, BlockStack, Text, RadioButton } from "@shopify/polaris"; // or your UI lib
// Assuming i18n `t` and `shopName` are passed as props
export const EmailPreferenceCard = ({
  t,
  isEmailEnable,
  emailLoader,
  handleEmailChange,
  showDisable,
}) => {
  return (
    <Card>
      <BlockStack gap="200">
        <Text as="h2" variant="headingMd">
          {t("store_workers_email")}
        </Text>
        <div>
          <RadioButton
            disabled={emailLoader}
            label={t("enable")}
            helpText={t("enable_desc")}
            checked={isEmailEnable === true}
            id="enabled"
            name="accounts"
            onChange={() => handleEmailChange(true)}
          />
          {showDisable && (
            <RadioButton
              disabled={emailLoader}
              label={t("disable")}
              helpText={t("disable_desc")}
              checked={isEmailEnable === false}
              id="disabled"
              name="accounts"
              onChange={() => handleEmailChange(false)}
            />
          )}
        </div>
      </BlockStack>
    </Card>
  );
};
