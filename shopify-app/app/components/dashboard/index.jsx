// Dashboard.jsx
import React from "react";
import {
  Page,
  Text,
  Card,
  Box,
  Button,
  BlockStack,
  InlineStack,
  Badge,
  Divider,
  Layout,
  ProgressBar,
  EmptyState,
  Grid,
} from "@shopify/polaris";
import "../../css/analytics_dashboard.css";
import { useNavigate } from "@remix-run/react";
import imageUrls from "../../config/images"
export const Dashboard = () => {
  const navigate = useNavigate();
  const dashboardData = {
    statistics: {
      tasksCompleted: 247,
      activeWorkflows: 4,
      totalWorkflows: 18,
      timeSaved: "63.5h",
      timePeriod: "This month",
    },
    quickActions: [
      {
        title: "Create Content",
        description: "Blogs, descriptions, emails",
      },
      {
        title: "Analyze Data",
        description: "Reports and insights",
      },
      {
        title: "Optimize SEO",
        description: "Improve search rankings",
      },
      {
        title: "Product Manager",
        description: "Update product details",
      },
    ],
    workflows: [
      {
        title: "Summer Campaign Bundle",
        categories: ["Content", "Email", "Social Media"],
        status: "In Progress",
        progress: 60,
      },
      {
        title: "Weekly Sales Analysis",
        categories: ["Data Analysis", "Reporting"],
        status: "In Progress",
        progress: 45,
      },
      {
        title: "Product Catalog Update",
        categories: ["Inventory", "Descriptions", "Images"],
        status: "Complete",
        progress: 100,
      },
    ],
  };

  const StatCard = ({ title, value, subtitle, tone }) => {
    return (
      <div style={{ width: "32%" }}>
        <Card>
          <Box>
            <Text variant="headingMd" as="h3">
              {title}
            </Text>
            <Text variant="headingXl" as="p" tone={tone}>
              {value}
            </Text>
            <Text variant="bodyMd" as="p" color="subdued">
              {subtitle}
            </Text>
          </Box>
        </Card>
      </div>
    );
  };

  return (
    <Page
      title="Dashboard"
      primaryAction={
        <Button
          onClick={() => {
            navigate(`/app/create/workers`);
          }}
          variant="primary"
        >
          New Workflow
        </Button>
      }
    >
      <Card>
        <BlockStack gap="400">
          <Layout>
            <Layout.Section>
              <InlineStack gap="400" wrap>
                <StatCard
                  title="Total Tasks Completed"
                  value={dashboardData.statistics.tasksCompleted}
                  subtitle="Across all workflows"
                />
                <StatCard
                  title="Active Workflows"
                  value={dashboardData.statistics.activeWorkflows}
                  subtitle={`From ${dashboardData.statistics.totalWorkflows} total workflows`}
                />
                <StatCard
                  title="Time Saved"
                  value={dashboardData.statistics.timeSaved}
                  subtitle={dashboardData.statistics.timePeriod}
                  tone="success"
                />
              </InlineStack>
            </Layout.Section>
          </Layout>

          {/* Quick Actions */}
          <Card padding={0}>
            <div className="light-grey-box">
              <Text variant="headingMd" as="h3">
                Quick Actions
              </Text>
            </div>
            <Box paddingBlock="100" />
            <Box padding={200}>
              <Grid>
                {dashboardData.quickActions.map((action, index) => (
                  <Grid.Cell
                    key={index}
                    columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}
                  >
                    <Card padding="400" sectioned>
                      <BlockStack gap="200">
                        <Text variant="headingMd" as="h4">
                          {action.title}
                        </Text>
                        <Text variant="bodyMd" as="p" color="subdued">
                          {action.description}
                        </Text>
                      </BlockStack>
                    </Card>
                  </Grid.Cell>
                ))}
              </Grid>
            </Box>
          </Card>
          {/* Active Workflows */}
          <Card padding={0}>
            <div className="light-grey-box">
              <Text variant="headingMd" as="h3">
                Active Workflows
              </Text>
            </div>
            {dashboardData.workflows.length === 0 ? (
              <EmptyState
                heading="No Active Workers available"
                image={imageUrls.emptyImage}
              />
            ) : (
              dashboardData.workflows.map((workflow, index) => (
                <React.Fragment key={index}>
                  <Box paddingInline="400" paddingBlock="300">
                    <BlockStack gap="300">
                      <InlineStack blockAlign="center" align="space-between">
                        <Text variant="headingMd" as="h4">
                          {workflow.title}
                        </Text>
                        <div>
                          <InlineStack blockAlign="center" gap="200">
                            <InlineStack blockAlign="center" gap="100" wrap>
                              {workflow.categories.map((category, i) => (
                                <Text key={i} variant="bodyMd" color="subdued">
                                  {category}
                                  {i < workflow.categories.length - 1 ? "," : ""}
                                </Text>
                              ))}
                            </InlineStack>
                            <Badge
                              status={
                                workflow.status === "Complete"
                                  ? "success"
                                  : "info"
                              }
                            >
                              {workflow.status}
                            </Badge>
                            <Button variant="primary">View</Button>
                          </InlineStack>
                        </div>
                      </InlineStack>

                      <ProgressBar size="small" progress={workflow.progress} />
                    </BlockStack>
                  </Box>
                  {index < dashboardData.workflows.length - 1 && <Divider />}
                </React.Fragment>
              ))
            )}
          </Card>
        </BlockStack>
      </Card>
      <br />
    </Page>
  );
};
