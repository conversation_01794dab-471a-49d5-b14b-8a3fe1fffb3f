import { NavMenu } from "@shopify/app-bridge-react";
import { Link } from "@remix-run/react";
import i18n from "i18next";

const SideNavigation = ({ isActive }) => {
  return (
    <NavMenu>
      {isActive ? (
        <>
          <Link to="/app/home">{i18n.t("home")}</Link>
          <Link to="/app/templates">{i18n.t("templates")}</Link>
          <Link to="/app/integrations">{i18n.t("integrations")}</Link>
          <Link to="/app/analytics">{i18n.t("analytics")}</Link>
          <Link to="/app/pricing">{i18n.t("pricing")}</Link>
          <Link to="/app/settings">{i18n.t("settings")}</Link>
          
          {/*
          <Link to="/app/contents">{i18n.t("content")}</Link>
           <Link to="/app/teams">{i18n.t("jobs")}</Link>
           <Link to="/app/team/done">{i18n.t("job_done")}</Link>
            <Link to="/app/workers">{i18n.t("workers")}</Link>
            <Link to="/app">{i18n.t("dashboard")}</Link>
          <Link to="/app/faq">{i18n.t("faq")}</Link>
          */}
        </>
      ) : (
        <Link to="/app/pricing">{i18n.t("pricing")}</Link>
      )}
    </NavMenu>
  );
};

export default SideNavigation;
