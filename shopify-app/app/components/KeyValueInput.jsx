// app/components/KeyValueInput.jsx
import React, { useState } from "react";
import { Text, Button, InlineStack, TextField, Box } from "@shopify/polaris";
import useRunWorkflowState from "../contexts/state.js";

export function KeyValueInput({ title = "Additional Inputs", t }) {
  const { additional_fields, setAdditionalFields } = useRunWorkflowState();
  const convertObjectToArray = (obj) => {
    if (!obj || typeof obj !== 'object' || Object.keys(obj).length === 0) {
      return [];
    }
    return Object.entries(obj).map(([key, value]) => ({ key, value }));
  };

  const convertArrayToObject = (arr) => {
    return arr.reduce((acc, { key, value }) => {
      if (key.trim()) {
        acc[key] = value;
      }
      return acc;
    }, {});
  };

  const [inputs, setInputs] = useState(() => convertObjectToArray(additional_fields));
  const updateGlobalState = (newInputs) => {
    const updated = convertArrayToObject(newInputs);
    setAdditionalFields(updated);
  };

  const handleAddField = () => {
    const newInputs = [...inputs, { key: "", value: "" }];
    setInputs(newInputs);
    updateGlobalState(newInputs);
  };

  const handleDeleteField = (index) => {
    const newInputs = inputs.filter((_, i) => i !== index);
    setInputs(newInputs);
    updateGlobalState(newInputs);
  };

  const handleKeyChange = (index, newKey) => {
    const newInputs = [...inputs];
    newInputs[index].key = newKey;
    setInputs(newInputs);
    updateGlobalState(newInputs);
  };

  const handleValueChange = (index, newValue) => {
    const newInputs = [...inputs];
    newInputs[index].value = newValue;
    setInputs(newInputs);
    updateGlobalState(newInputs);
  };

  return (
    <Box padding="4">
      <InlineStack align="space-between" blockAlign="center">
        <Text variant="headingMd">{title}</Text>
        <Button variant="primary" onClick={handleAddField}>
          {t("add_field")}
        </Button>
      </InlineStack>

      {inputs.length > 0 && (
        <Box paddingBlockStart="4">
          {inputs.map((input, index) => (
            <div key={`field-${index}`} style={{ marginBottom: "10px" }}>
              <InlineStack gap="200" align="space-between" blockAlign="center">
                <Box width="45%">
                  <TextField
                    label={t("key")}
                    value={input.key}
                    onChange={(value) => handleKeyChange(index, value)}
                    autoComplete="off"
                  />
                </Box>
                <Box width="45%">
                  <TextField
                    label={t("value")}
                    value={input.value}
                    onChange={(value) => handleValueChange(index, value)}
                    autoComplete="off"
                  />
                </Box>
                <Box>
                  <div style={{ marginTop: "20px" }} />
                  <Button destructive onClick={() => handleDeleteField(index)}>
                    {t("delete")}
                  </Button>
                </Box>
              </InlineStack>
            </div>
          ))}
        </Box>
      )}
    </Box>
  );
}
