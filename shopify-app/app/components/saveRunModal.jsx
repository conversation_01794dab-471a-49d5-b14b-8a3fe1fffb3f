import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { useTranslation } from "react-i18next";
import { Text, Button, ButtonGroup, InlineStack, Box } from "@shopify/polaris";

export const SaveRunModal = ({ id, title, desc, onConfirm, onClose }) => {
  const { t } = useTranslation();
  return (
    <Modal id={id}>
      <TitleBar title={title}></TitleBar>
      <Box padding="400">
        <Text variant="subdued">{desc}</Text>
        <br />
        <InlineStack align="end">
          <ButtonGroup>
            <Button onClick={() => onConfirm("runTemplate")}>
              {t("start_job")}
            </Button>
            <Button
              variant="primary"
              // tone="critical"
              onClick={() => onConfirm("saveRunTemplate")}
            >
              {t("save_and_run")}
            </Button>
          </ButtonGroup>
        </InlineStack>
      </Box>
    </Modal>
  );
};
