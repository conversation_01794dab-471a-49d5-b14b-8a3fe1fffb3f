import React from "react";
import {
  Layout,
  Card,
  Text,
  Box,
  EmptyState,
  InlineStack,
  Badge,
  Button,
} from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useNavigate } from "@remix-run/react";
import imageUrls from "../../config/images";
import { JobHistoryCard } from "./JobHistoryCard.jsx";

export const OverviewTab = ({ templateData, isLoading, setIsLoading }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleViewJob = (jobId) => {
    setIsLoading(true);
    navigate(`/app/job-result/${jobId}`);
  };

  return (
    <Layout>
      <Layout.Section>
        <Card>
          <div style={{ minHeight: "350px" }}>
            <Text variant="headingMd">{t("summary")}</Text>
            <Box paddingBlockStart="4" />
            {!templateData?.summary || templateData?.summary?.length === 0 ? (
              <EmptyState
                heading={t("no_summary_available")}
                image={imageUrls.emptyImage}
              />
            ) : (
              <div dangerouslySetInnerHTML={{ __html: templateData.summary }} />
            )}
          </div>
        </Card>
      </Layout.Section>
    </Layout>
  );
};
