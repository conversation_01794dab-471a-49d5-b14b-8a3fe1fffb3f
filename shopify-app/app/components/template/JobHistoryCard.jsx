import React from "react";
import { <PERSON>, <PERSON>, Text, Badge, Button, InlineStack } from "@shopify/polaris";
import moment from "moment";

export const JobHistoryCard = ({ history, index, isLoading, onViewJob, t }) => {
  return (
    <Box className="margin-bottom-10" key={history.job_id}>
      <Card>
        <InlineStack align="space-between" gap={400}>
          <div>
            <Text variant="bodyMd">
              {history?.job_title || `${t("job")} ${index + 1}`}
            </Text>
            <Text variant="bodyXs">
              {history?.created_at
                ? moment(history.created_at).format("MMM DD, YYYY h:mm A")
                : ""}
            </Text>
          </div>
          <Box>
            <InlineStack align="end" gap={400}>
              <Badge>
                <Text>{t(history?.job_status)}</Text>
              </Badge>
              <Button
                loading={isLoading}
                disabled={history?.job_status !== "completed"}
                onClick={() => onViewJob(history.job_id)}
                variant="primary"
              >
                {t("view")}
              </Button>
            </InlineStack>
          </Box>
        </InlineStack>
      </Card>
    </Box>
  );
};
