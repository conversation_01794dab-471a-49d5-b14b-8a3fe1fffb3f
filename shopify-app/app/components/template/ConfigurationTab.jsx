import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  Text,
  Button,
  InlineStack,
  Box,
  EmptyState,
  Divider,
} from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import imageUrls from "../../config/images";
import { FieldRenderer } from "../config.jsx";
import { StoreInputSelector } from "./StoreInputSelector.jsx";
import useRunWorkflowState from "../../contexts/state.js";

export const ConfigurationTab = ({ 
  templateData, 
  isLoading, 
  itemsData,
  onSave 
}) => {
  const { t } = useTranslation();
  const formHasErrorsRef = useRef(false);
  
  const configValue = useRunWorkflowState((state) => state.configValue);
  const setConfigValue = useRunWorkflowState((state) => state.setConfigValue);
  const selectedItems = useRunWorkflowState((state) => state.selectedItems);
  
  const [localFormValues, setLocalFormValues] = useState(configValue);

  useEffect(() => {
    setLocalFormValues(configValue);
  }, [configValue]);

  const handleValueChange = (key, newValue) => {
    const updated = {
      ...localFormValues,
      [key]: {
        ...localFormValues[key],
        value: newValue,
        errorText: "",
      },
    };

    setLocalFormValues(updated);
    setConfigValue(updated);
  };

  useEffect(() => {
    const handleForceUpdate = () => {
      const currentConfigValue = useRunWorkflowState.getState().configValue;
      setLocalFormValues({ ...currentConfigValue });
    };

    window.addEventListener("force-form-update", handleForceUpdate);
    return () => {
      window.removeEventListener("force-form-update", handleForceUpdate);
    };
  }, []);

  useEffect(() => {
    if (formHasErrorsRef.current) {
      const currentConfigValue = useRunWorkflowState.getState().configValue;
      setLocalFormValues({ ...currentConfigValue });
      formHasErrorsRef.current = false;
    }
  }, []);

  return (
    <>
      <Card>
        <InlineStack align="space-between">
          <Text variant="headingMd">{t("job_specifications")}</Text>
        {/*   <Button
            variant="primary"
            loading={isLoading}
            onClick={onSave}
          >
            {t("save")}
          </Button> */}
        </InlineStack>
        <Box paddingBlockStart="4" />
        
        {(isLoading && !templateData) ||
        templateData?.current_configuration?.length === 0 ? (
          <EmptyState
            heading={t("no_specifications_available")}
            image={imageUrls.emptyImage}
          />
        ) : (
          templateData?.current_configuration?.map((config) => (
            <div key={config.name} className="margin-bottom-32">
              <Text variant="headingMd" as="h3">
                {config.name}
              </Text>
              <div className="desc-text">{config.description}</div>
              {config.fields.map((field) => {
                const fieldState = localFormValues?.[field.key] || {
                  value: "",
                  errorText: "",
                };
                return (
                  <div key={field.key} className="margin-bottom-16">
                    <FieldRenderer
                      field={field}
                      value={fieldState.value}
                      errorText={fieldState.errorText}
                      onChange={handleValueChange}
                    />
                  </div>
                );
              })}
              <Divider />
            </div>
          ))
        )}
        
        <StoreInputSelector
          templateData={templateData}
          itemsData={itemsData}
          selectedItems={selectedItems}
        />
      </Card>
    </>
  );
};
