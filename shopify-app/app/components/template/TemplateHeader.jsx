import React, { useState, useEffect } from "react";
import { Text, Box, InlineStack, Badge } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import "../../css/team.css";

export const TemplateHeader = ({ isLoading, templateData, shopName }) => {
  const { t } = useTranslation();

  const formatDate = (dateString) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString("en-GB").replace(/\//g, "-");
  };

  return (
    <>
      <div className="custom-box">
        <Text variant="bodySm">
          {!isLoading
            ? templateData?.created_at
              ? `${t("created_at")}: ${formatDate(templateData.created_at)}`
              : ""
            : t("loading")}
        </Text>
        {templateData?.tags?.length > 0 && (
          <div className="top-space-10">
            <InlineStack gap="200">
              {templateData.tags.map((tag, index) => (
                <Badge key={index} tone="info">
                  {tag}
                </Badge>
              ))}
            </InlineStack>
          </div>
        )}
      </div>
      <Box paddingBlockStart="4" />
    </>
  );
};
