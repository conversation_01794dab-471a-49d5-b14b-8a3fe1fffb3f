import React from "react";
import {
  Layout,
  Card,
  Text,
  Box,
  EmptyState,
  InlineStack,
  Badge,
  Button,
} from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { useNavigate } from "@remix-run/react";
import imageUrls from "../../config/images";
import { JobHistoryCard } from "./JobHistoryCard.jsx";

export const RecentJobsTab = ({ templateData, isLoading, setIsLoading }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleViewJob = (jobId) => {
    setIsLoading(true);
    navigate(`/app/job-result/${jobId}`);
  };

  return (
    <Layout>
     <Layout.Section>
        <Card>
          <Text variant="headingMd">
            {t("job_history")}
            {templateData?.history?.length
              ? ` (${templateData.latest_job_runs.length} ${t("tasks")})`
              : ""}
          </Text>
          <br />
          {!templateData?.latest_job_runs ||
          templateData?.latest_job_runs?.length === 0 ? (
            <EmptyState
              heading={t("no_tasks_generated_yet")}
              image={imageUrls.emptyImage}
            />
          ) : (
            templateData?.latest_job_runs?.map((history, index) => (
              <JobHistoryCard
                key={history.job_id}
                history={history}
                index={index}
                isLoading={isLoading}
                onViewJob={handleViewJob}
                t={t}
              />
            ))
          )}
        </Card>
      </Layout.Section>
    </Layout>
  );
};
