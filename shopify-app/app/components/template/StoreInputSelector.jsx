import React from "react";
import { Text, Select, Box, Divider } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import { SelectionSection } from "../selectionSection.jsx";
import { KeyValueInput } from "../KeyValueInput.jsx";
import useRunWorkflowState from "../../contexts/state.js";

export const StoreInputSelector = ({
  templateData,
  itemsData,
  selectedItems,
}) => {
  const { t } = useTranslation();
  const setSelectedItems = useRunWorkflowState(
    (state) => state.setSelectedItems,
  );

  const handleSingleSelection = (value) => {
    const selectedItem = itemsData.find((item) => item.shopify_gid === value);
    if (selectedItem) {
      setSelectedItems([selectedItem]);
    }
  };

  if (!templateData?.store_data || templateData?.store_data === 0) {
    return null;
  }

  const hasStoreDataType = templateData?.store_data_type?.type;
  const category = templateData?.store_data_type?.category;

  const showSelectionSection =
    hasStoreDataType === "multiple" ||
    (hasStoreDataType === "single" && category === "products");

  return (
    <>
      {hasStoreDataType === "single" && category !== "products" && (
        <div className="margin-top-negative-10">
          <Text variant="headingMd">{t("store_inputs")}</Text>
          <Select
            label={category === "blog" ? t("blogs") : t(category)}
            placeholder={t("select_item")}
            options={itemsData.map((item) => ({
              label: item.title || item.name || item.shopify_gid,
              value: item.shopify_gid,
            }))}
            onChange={handleSingleSelection}
            value={selectedItems?.[0]?.shopify_gid || ""}
          />
        </div>
      )}

      {hasStoreDataType && (
        <>
          <br />
          <Box paddingBlockStart="4" />
          <Divider />
          <br />
        </>
      )}
      <KeyValueInput title={t("additional_inputs")} t={t} />
      <br />
      <Divider />
      <Box paddingBlockStart="4" />
      {showSelectionSection && (
        <div className="product-selection-section">
          <SelectionSection
            itemsData={itemsData}
            itemType={category}
            selectionType={hasStoreDataType}
          />
        </div>
      )}
    </>
  );
};
