import React from "react";
import {
  Card,
  Text,
  Box,
  EmptyState,
} from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import imageUrls from "../../config/images";
import { WorkerCard } from "./WorkerCard.jsx";

export const WorkersTab = ({ templateData }) => {
  const { t } = useTranslation();

  return (
    <Card>
      <Text variant="headingMd">{t("workers")}</Text>
      <br />
      <Box paddingBlockStart="4" />
      {!templateData?.agents_list ||
      templateData?.agents_list?.length === 0 ? (
        <EmptyState
          heading={t("no_workers_available")}
          image={imageUrls.emptyImage}
        />
      ) : (
        templateData?.agents_list?.map((worker) => (
          <WorkerCard key={worker.uuid} worker={worker} />
        ))
      )}
    </Card>
  );
};
