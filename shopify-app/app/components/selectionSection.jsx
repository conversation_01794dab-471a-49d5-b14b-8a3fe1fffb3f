import React, { useState, useMemo, useEffect } from "react";
import {
  Box,
  Card,
  Text,
  Button,
  TextField,
  Divider,
  EmptyState,
  BlockStack,
  InlineStack,
  Pagination,
} from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import SelectionCard from "./selectionCard";
import imageUrls from "../config/images";
import useRunWorkflowState from "../contexts/state.js";

export const SelectionSection = ({
  itemsData,
  itemType = "items",
  selectionType = "single",
}) => {
  const { t } = useTranslation();
  const { selectedItems, setSelectedItems } = useRunWorkflowState();

  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const isItemSelected = (item) => {
    const itemId = item.id || item?.shopify_gid;
    return selectedItems.some((i) => (i.id || i.shopify_gid) === itemId);
  };

  const filteredItems = useMemo(() => {
    if (!itemsData || itemsData.length === 0) return [];

    const filtered = itemsData.filter(
      (item) =>
        item.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()),
    );

    // Sort items to show selected items first
    return filtered.sort((a, b) => {
      const aIsSelected = isItemSelected(a);
      const bIsSelected = isItemSelected(b);
      if (aIsSelected && !bIsSelected) return -1;
      if (!aIsSelected && bIsSelected) return 1;
      return 0;
    });
  }, [itemsData, searchQuery, selectedItems]);

  const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredItems.slice(startIndex, endIndex);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const handleSingleSelection = (item) => {
    const itemId = item.id || item.shopify_gid;
    const isAlreadySelected = selectedItems.some(
      (i) => (i.id || i.shopify_gid) === itemId,
    );

    const newSelectedItems = isAlreadySelected
      ? []
      : [
          {
            ...item,
            selectedAt: new Date().toISOString(),
          },
        ];

    setSelectedItems(newSelectedItems);
  };

  const handleMultipleSelection = (item, isChecked) => {
    const itemId = item.id || item.shopify_gid;

    let newSelectedItems;
    if (isChecked) {
      const alreadySelected = selectedItems.some(
        (i) => (i.id || i.shopify_gid) === itemId,
      );
      newSelectedItems = alreadySelected
        ? selectedItems
        : [
            ...selectedItems,
            {
              ...item,
              selectedAt: new Date().toISOString(),
            },
          ];
    } else {
      newSelectedItems = selectedItems.filter(
        (i) => (i.id || i.shopify_gid) !== itemId,
      );
    }

    setSelectedItems(newSelectedItems);
  };

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(1, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(totalPages, prev + 1));
  };

  return (
    <Box paddingBlockStart="5">
      <Card>
        <Box padding="4">
          <BlockStack gap="4">
            <Text variant="headingMd" as="h2">
              {t("store_inputs")}
            </Text>
            <div className="margin-bottom-10"></div>
            <InlineStack align="space-between">
              <Text variant="bodyMd" as="h2">
                {`${t("select")} ${itemType}`}
              </Text>
            </InlineStack>

            <div className="margin-bottom-10" />
            <TextField
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder={t(`search`)}
              clearButton
              onClearButtonClick={() => setSearchQuery("")}
              autoComplete="off"
            />

            <div className="margin-bottom-10" />
            <Divider />
            <div className="margin-bottom-10" />

            {!itemsData || itemsData.length === 0 ? (
              <EmptyState
                heading={t(`no_${itemType}_available`)}
                image={imageUrls.emptyImage}
              />
            ) : filteredItems.length === 0 ? (
              <EmptyState heading={t(`matching_your_search_criteria`)} />
            ) : (
              <BlockStack gap="3">
                {currentItems.map((item) => (
                  <SelectionCard
                    key={item.id}
                    item={item}
                    selectionType={selectionType}
                    isSelected={isItemSelected(item)}
                    onSingleSelect={() => handleSingleSelection(item)}
                    onMultipleSelect={(checked) =>
                      handleMultipleSelection(item, checked)
                    }
                  />
                ))}

                {totalPages > 1 && (
                  <InlineStack align="end">
                    <Pagination
                      hasPrevious={currentPage > 1}
                      onPrevious={handlePreviousPage}
                      hasNext={currentPage < totalPages}
                      onNext={handleNextPage}
                      label={`${t("page")} ${currentPage} ${t("of")} ${totalPages}`}
                    />
                  </InlineStack>
                )}
              </BlockStack>
            )}
          </BlockStack>
        </Box>
      </Card>
    </Box>
  );
};
