import React, { useEffect } from "react";
import { Card, BlockStack, InlineStack, Text, Box } from "@shopify/polaris";
import "@shopify/polaris/build/esm/styles.css";
import "../../css/workflow.css";
import useWorkflowState from "../../contexts/workflowState";
import { useTranslation } from "react-i18next";
export function WorkFlowStepTwo() {
  const { t } = useTranslation();
  const selectedWorkers = useWorkflowState((state) => state.selectedWorkers);
  const workersData = useWorkflowState((state) => state.workersData);

  useEffect(() => {}, [workersData, selectedWorkers]);
  const isWorkerSelected = (workerId, taskId) => {
    if (selectedWorkers && Array.isArray(selectedWorkers)) {
      const foundInSelected = selectedWorkers.some(
        (selectedTask) =>
          selectedTask.formatted_agents &&
          selectedTask.formatted_agents.some((agent) => agent.id === workerId),
      );
      if (foundInSelected) return true;
      if (selectedWorkers.includes(workerId)) return true;
    }
    return false;
  };

  return (
    <>
      {workersData.map((task) => (
        <div style={{ marginBottom: "10px" }} key={task.id}>
          <Card padding={0}>
            <BlockStack>
              <Box
                paddingInlineStart="400"
                paddingInlineEnd="400"
                paddingBlockStart="400"
              >
                <InlineStack gap="400" align="start">
                  <div className="light-grey-box">
                    <Text as="h2" variant="headingMd">
                      Task: {task.name}
                    </Text>
                  </div>
                  <Text as="p" variant="bodyMd">
                    {task.Description}
                  </Text>
                </InlineStack>
              </Box>

              <Box
                paddingInlineStart="400"
                paddingInlineEnd="400"
                paddingBlockStart="400"
              >
                <Text as="h3" variant="headingSm">
                 {t('hand_picked_store_workers')}
                </Text>
              </Box>

              <Box
                paddingInlineStart="400"
                paddingInlineEnd="400"
                paddingBlockStart="200"
                paddingBlockEnd="400"
              >
                <InlineStack gap="400" wrap>
                  {task.formatted_agents && task.formatted_agents.length > 0 ? (
                    task.formatted_agents.map((worker) => (
                      <div
                        key={worker.id}
                        className={`worker-card ${isWorkerSelected(worker.id, task.id) ? "selected" : ""}`}
                      >
                        <InlineStack align="start" gap="400">
                          <div className="icon-inner">{worker.icon}</div>
                          <BlockStack gap="100">
                            <Text as="h4" variant="headingSm">
                              {worker.title}
                            </Text>
                            <Text as="p" variant="bodySm">
                              {worker.description}
                            </Text>
                          </BlockStack>
                        </InlineStack>
                      </div>
                    ))
                  ) : (
                    <Text>No workers available</Text>
                  )}
                </InlineStack>
              </Box>
            </BlockStack>
          </Card>
        </div>
      ))}
    </>
  );
}

export default WorkFlowStepTwo;
