import React, { useState, useEffect } from "react";
import {
  Card,
  RadioButton,
  Text,
  Box,
  InlineStack,
  Divider,
} from "@shopify/polaris";
import "../../css/workflow.css";
import useWorkflowState from "../../contexts/workflowState";
import { useTranslation } from "react-i18next";
export function WorkFlowStepFour() {
  const { t } = useTranslation();
  const summary = useWorkflowState((state) => state.summary);
  const configurationsValue = useWorkflowState(
    (state) => state.configurationsValue,
  );

  function attachConfigValuesToSummary(configurationsValue, summaryData) {
    return summaryData.map((task) => {
      const matched = configurationsValue.find(
        (c) => c.task_id === task.task_id,
      );
      if (matched) {
        return {
          ...task,
          configValues: matched.values || [],
        };
      }
      return task;
    });
  }
  const updatedSummary = attachConfigValuesToSummary(
    configurationsValue,
    summary,
  );

  const handleChange = (key, val) => {
    const updated = {
      ...storedData,
      [key]: { value: val },
    };
    setStoredData(updated);
  };

  return (
    <>
      <div className="light-grey-box">
        {updatedSummary.map((task) => (
          <div key={task.task_id} style={{ marginBottom: "10px" }}>
            <Card>
              <Text as="h2" variant="headingMd">
                {t("task_summary")}
              </Text>
              <div style={{ marginTop: "10px" }} />
              <InlineStack gap={100}>
                <Text>{task.task_name}</Text>
              </InlineStack>
              <div style={{ marginTop: "10px" }} />
              {task.summary.map((role, index) => (
                <Box paddingBlockEnd="4" key={index}>
                  <div style={{ marginTop: "10px" }} />
                  <Text variant="headingMd">{role.name}</Text>
                  <Text variant="bodyMd" color="subdued">
                    {role.description}
                  </Text>
                  {index < task.summary.length - 1 && (
                    <div style={{ marginTop: "10px", marginBottom: "10px" }}>
                      <Divider />
                    </div>
                  )}
                </Box>
              ))}
              <div style={{ marginTop: "10px", marginBottom: "10px" }}>
                <Divider />
              </div>
            </Card>
            <br />
            <Card>
              <Text as="h2" variant="headingMd">
                {t("task_specifications")}:
              </Text>
              <div style={{ marginTop: "10px" }} />
              {task.configValues.map((config, index) => (
                <Box paddingBlockEnd="4" key={index}>
                  <InlineStack align="start" gap="4">
                    <div style={{ width: "220px" }}>
                      <Text variant="bodyMd" fontWeight="semibold">
                        {config.display_name}
                      </Text>
                    </div>
                    <Text variant="bodyMd" color="subdued">
                      {config.value?.toString() || "-"}
                    </Text>
                  </InlineStack>
                  {index < task.configValues.length - 1 && (
                    <div style={{ marginTop: "10px", marginBottom: "10px" }}>
                      <Divider />
                    </div>
                  )}
                </Box>
              ))}
            </Card>
          </div>
        ))}
      </div>

      {/*<Card>
        <Text variant="headingMd" as="h2">
          How do you want to run these tasks?
        </Text>
        <br />
        <Box paddingBlockStart="4">
          <Card>
            <RadioButton
              label={
                <>
                  <Text variant="headingMd">One-time task</Text>
                  <Text variant="bodyMd">
                    Run these tasks once, immediately or at a scheduled time
                  </Text>
                  <Text variant="bodyMd" color="subdued">
                    Good for: ad-hoc reports, seasonal campaigns, one-off tasks
                  </Text>
                </>
              }
              checked={storedData?.task_settings?.value === "one-time" || false}
              onChange={(val) => handleChange("task_settings", "one-time")}
              id="one-time"
              name="taskType"
            />
          </Card>
        </Box>
        <br />
        <Box paddingBlockStart="4">
          <Card>
            <RadioButton
              label={
                <>
                  <Text variant="headingMd" fontWeight="bold">
                    Save as workflow
                  </Text>
                  <Text variant="bodyMd">
                    Create a reusable workflow that can be run on demand or
                    scheduled
                  </Text>
                  <Text variant="bodyMd" color="subdued">
                    Good for: regular reports, recurring maintenance, standard
                    procedures
                  </Text>
                </>
              }
              checked={storedData?.task_settings?.value === "workflow" || false}
              onChange={(val) => handleChange("task_settings", "workflow")}
              id="workflow"
              name="taskType"
            />
          </Card>
        </Box>
      </Card>
      <br /> */}
      <br />
    </>
  );
}
export default WorkFlowStepFour;
