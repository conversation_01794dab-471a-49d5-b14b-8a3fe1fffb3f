import React, { useState } from "react";
import { Divider, Box, Text, Card, BlockStack } from "@shopify/polaris";
import "../../css/workflow.css";
import useWorkflowState from "../../contexts/workflowState";
import { <PERSON><PERSON>enderer } from "../config";
import { useTranslation } from "react-i18next";
const capitalizeLabel = (key) =>
  key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());

export function WorkFlowStepThree() {
  const { t } = useTranslation();
  const configurationsValue = useWorkflowState(
    (state) => state.configurationsValue,
  );

  const [selectedTaskId, setSelectedTaskId] = useState(
    configurationsValue?.[0]?.task_id || "",
  );

  const selectedTask = configurationsValue.find(
    (t) => t.task_id === selectedTaskId,
  );

  const getInitialFormValues = (task) => {
    return (
      task?.values?.reduce((acc, curr) => {
        acc[curr.key] = {
          value: curr.value ?? "",
          errorText: curr.errorText ?? "",
        };
        return acc;
      }, {}) || {}
    );
  };

  const [formValues, setFormValues] = useState(
    getInitialFormValues(selectedTask),
  );

  const handleValueChange = (key, newValue) => {
    setFormValues((prev) => ({
      ...prev,
      [key]: {
        ...prev[key],
        value: newValue,
      },
    }));

    const taskIndex = configurationsValue.findIndex(
      (t) => t.task_id === selectedTaskId,
    );

    if (taskIndex !== -1) {
      const task = configurationsValue[taskIndex];
      const valueEntry = task.values.find((v) => v.key === key);
      if (valueEntry) {
        valueEntry.value = newValue;
      }
    }
  };

  const renderField = (field) => {
    const { key } = field;
    const fieldState = formValues[key] || { value: "", errorText: "" };

    return (
      <FieldRenderer
        key={field.key}
        field={field}
        value={fieldState.value}
        errorText={fieldState.errorText}
        onChange={handleValueChange}
      />
    );
  };

  if (!configurationsValue || configurationsValue.length === 0) {
    return (
      <div>
        No tasks available. Please ensure tasks are properly configured.
      </div>
    );
  }

  return (
    <Card>
      <div className="task-selection">
        {configurationsValue.map((task) => (
          <button
            key={task.task_id}
            className={`task-button ${selectedTaskId === task.task_id ? "active" : ""}`}
            onClick={() => {
              setSelectedTaskId(task.task_id);
              setFormValues(getInitialFormValues(task));
            }}
          >
            {task.name}
          </button>
        ))}
      </div>

      <Box padding="4">
        <BlockStack gap="6">
          {selectedTask && (
            <>
              <Text variant="headingLg" as="h2">
                {t("task_name")} {selectedTask.name}
              </Text>
              <br />
              <Card>
                <Box padding="4">
                  {selectedTask.configurations.map((config) => (
                    <div
                      key={config.name}
                      className="settings-section"
                      style={{ marginBottom: "32px" }}
                    >
                      <Text variant="headingMd" as="h3">
                        {config.agent_name}
                      </Text>
                      <Text
                        variant="bodyMd"
                        as="p"
                        tone="subdued"
                        style={{ marginBottom: "16px" }}
                      >
                        {config.description}
                      </Text>
                      <br />
                      {config.fields.map((field) => (
                        <div key={field.key} style={{ marginBottom: "16px" }}>
                          {renderField(field)}
                        </div>
                      ))}
                      <Divider />
                    </div>
                  ))}
                </Box>
              </Card>
            </>
          )}
        </BlockStack>
      </Box>
    </Card>
  );
}

export default WorkFlowStepThree;
