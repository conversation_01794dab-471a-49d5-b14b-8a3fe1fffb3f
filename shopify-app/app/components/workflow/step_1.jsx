import React, { useEffect, useState } from "react";
import {
  Card,
  Text,
  TextField,
  Button,
  BlockStack,
  Box,
  EmptyState,
  InlineStack,
  RadioButton,
  Divider,
} from "@shopify/polaris";
import "../../css/workflow.css";
import { useLocation } from "@remix-run/react";
import imageUrls from "../../config/images";
import useWorkflowState from "../../contexts/workflowState";
import { useTranslation } from "react-i18next";
export function WorkFlowStepOne({ errors, handleGetTaskData, isLoading }) {
  const { t } = useTranslation();
  // Properly use Zustand selectors for performance - only get what you need
  const query = useWorkflowState((state) => state.query);
  const taskData = useWorkflowState((state) => state.taskData);
  const selectedTask = useWorkflowState((state) => state.selectedTask);
  const workflow_name = useWorkflowState((state) => state.workflow_name);
  const suggestedQuery = useWorkflowState((state) => state.suggestedQuery);

  // Get only the setter functions you need
  const setQuery = useWorkflowState((state) => state.setQuery);
  const setSelectedTask = useWorkflowState((state) => state.setSelectedTask);
  const setWorkflowName = useWorkflowState((state) => state.setWorkflowName);
  const setSuggestedQuery = useWorkflowState(
    (state) => state.setSuggestedQuery,
  );

  const location = useLocation();
  const { input } = location.state || {};

  useEffect(() => {}, [input]);

  const toggleTask = (task) => {
    setSelectedTask([task]);
  };

  return (
    <Card>
      <BlockStack>
        {/* Step 1 */}
        <Box padding="200">
          <div className="light-grey-box">
            <Text as="h2" variant="headingMd">
              {t("what_do_you_want_to_get_done_today")}
            </Text>
          </div>
          <Box paddingBlock="400">
            <TextField
              value={query || ""}
              onChange={(val) => {
                setQuery(val);
              }}
              multiline={3}
              autoComplete="off"
              placeholder={t('what_do_you_want_to_get_done_today_placeholder')}
              error={errors?.input_task_description}
            />
          </Box>
          {suggestedQuery && suggestedQuery.length > 0 && (
            <BlockStack gap="300">
              <Text variant="headingMd">
             {t('we_could_clearly_understand_your_requirement_maybe_pick_from_these_examples')}
              </Text>
              {suggestedQuery.map((suggestion, index) => (
                <Box key={index}>
                  <RadioButton
                    checked={query === suggestion}
                    onChange={() => {
                      setQuery(suggestion);
                    }}
                    label={suggestion}
                  />
                </Box>
              ))}
            </BlockStack>
          )}
          <InlineStack align="end">
            <Box paddingInlineEnd="400">
              <Button
                loading={isLoading}
                disabled={query.trim().length === 0}
                variant="primary"
                onClick={() => {
                  setSuggestedQuery([]);
                  handleGetTaskData(query || "");
                }}
              >
                {t("generate_tasks")}
              </Button>
            </Box>
          </InlineStack>
        </Box>

        <Divider />
        <br />
        {/* Step 2 */}
        <Box padding="200">
          <div className="light-grey-box">
            <InlineStack align="space-between">
              <Text as="h2" variant="headingMd">
                {t("store_job_name")}
              </Text>
              {/* <Box paddingInlineEnd="400">
                <Button variant="primary">Get Help From Workers</Button>
              </Box> */}
            </InlineStack>
          </div>
          <Box paddingBlock="400">
            <TextField
              value={workflow_name || ""}
              placeholder={t("placeholder_workflow_name")}
              onChange={(val) => {
                setWorkflowName(val);
              }}
              autoComplete="off"
              error={errors?.workflow_name}
            />
          </Box>
        </Box>
        <Divider />
        <br />
        {/* Step 3 */}
        <Box padding="200">
          <div style={{ marginBottom: "20px" }} className="light-grey-box">
            <Text as="h2" variant="headingMd">
              {t("task_list_recommended_based_on_your_input")}
            </Text>
          </div>
          <div>
            {!taskData || taskData?.length === 0 ? (
              <>
                <EmptyState
                  heading={t('no_tasks_created_yet')}
                  image={imageUrls.emptyImage}
                />
                <Text tone="critical">{errors?.selected_task}</Text>
              </>
            ) : (
              <BlockStack gap="300">
                {taskData?.map((task) => (
                  <Box key={task.task_id}>
                    <div className="light-grey-box">
                      <InlineStack align="start">
                        <RadioButton
                          disabled={isLoading}
                          checked={selectedTask?.some((t) => t === task)}
                          onChange={() => toggleTask(task)}
                          label=""
                        />
                        <div className="task-content">
                          <Text
                            as="span"
                            variant="bodyMd"
                            fontWeight="semibold"
                          >
                            {task.name}
                          </Text>
                          <br />
                          <Text as="span" variant="bodyMd" color="subdued">
                            {task.description}
                          </Text>
                        </div>
                      </InlineStack>
                    </div>
                  </Box>
                ))}
                <Text tone="critical">{errors?.selected_task}</Text>
              </BlockStack>
            )}
          </div>

          {/*   <Box paddingBlock="500" className="suggestion-box">
              <InlineStack align="space-between">
                <Text as="p" variant="bodyMd" color="subdued">
                  {suggestion}
                </Text>
                <Button onClick={handleAddSuggested}>
                  Add Suggested Tasks
                </Button>
              </InlineStack>
            </Box> */}
        </Box>
      </BlockStack>
    </Card>
  );
}

export default WorkFlowStepOne;
