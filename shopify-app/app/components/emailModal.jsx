import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { useTranslation } from "react-i18next";
import { Text, Button, ButtonGroup, InlineStack, Box } from "@shopify/polaris";
import { EmailPreferenceCard } from "./EmailPreferenceCard";

export const EmailModal = ({ id, title, desc, onConfirm, onClose }) => {
  const { t } = useTranslation();
  return (
    <Modal id={id}>
      <TitleBar title={title}></TitleBar>
      <Box padding="400">
        <EmailPreferenceCard
          t={t}
          isEmailEnable={true}
          emailLoader={false}
          handleEmailChange={null}
          showDisable={false}
        />
        <br />
        <InlineStack align="end">
          <Button variant="primary" onClick={onConfirm}>
            {t("update")}
          </Button>
        </InlineStack>
      </Box>
    </Modal>
  );
};
