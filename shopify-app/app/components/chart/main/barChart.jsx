// Returning customer rate
import { <PERSON><PERSON><PERSON> as Chart } from "@shopify/polaris-viz";
import { ChartBase } from "./chartBase";
import { Heading } from "./heading";
import EmptyState from "./emptyState";

export const BarChart = ({ title, subTitle, fluctuation, data }) => {
  return (
    <ChartBase>
      <Heading title={title} subTitle={subTitle} fluctuation={fluctuation} />
      {data?.length > 0 ? (
        <Chart
          showLegend={true}
          data={data}
          isAnimated={true}
          yAxisOptions={{
            labelFormatter: formatValueToMinutes,
          }}
          type="default"
          xAxisOptions={{
            labelFormatter: (value) => {
              return value?.name || value?.optionName || value;
            },
          }}
        />
      ) : (
        <EmptyState />
      )}
    </ChartBase>
  );
};

const formatValueToMinutes = (value) => {
  if (typeof value !== "number" || isNaN(value)) {
    return "0";
  }
  if (value >= 60) {
    const hours = Math.floor(value / 60);
    const minutes = value % 60;
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  } else {
    return `${value}m`;
  }
};
