import { DonutChart as Chart } from "@shopify/polaris-viz";
import { ChartBase } from "./chartBase";
import { Heading } from "./heading";
import EmptyState from "./emptyState";

export const DonutChart = ({
  title,
  subTitle,
  fluctuation,
  data,
  trendMetric,
}) => {
  return (
    <ChartBase>
      <Heading title={title} subTitle={subTitle} fluctuation={fluctuation} />
      {data?.length > 0 ? (
        <Chart
          showLegend={true}
          legendPosition="bottom"
          data={data}
          isAnimated={true}
          labelFormatter={formatValueToMinutes}
          comparisonMetric={
            trendMetric
              ? parseFloat(trendMetric) > 0
                ? {
                    metric: `${parseFloat(trendMetric)}%`,
                    trend: "positive",
                  }
                : {
                    metric: `${parseFloat(trendMetric)}%`,
                    trend: "negative",
                  }
              : {}
          }
        />
      ) : (
        <EmptyState />
      )}
    </ChartBase>
  );
};

const formatValueToMinutes = (value) => {
  if (typeof value !== "number" || isNaN(value)) {
    return "0";
  }
  if (value >= 60) {
    const hours = Math.floor(value / 60);
    const minutes = value % 60;
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  } else {
    return `${value}m`;
  }
};
