import { Layout } from "@shopify/polaris";
import { useLoaderData, useSubmit, useActionData } from "@remix-run/react";
import { PageTitleBar } from "../shared/pageTitleBar";
import { SideTabs } from "./sideTabs";
import { GeneralSettings } from "./generalSettings";
import { FeaturesSettings } from "./featuresSettings";
import { NotificationsSettings } from "./notificationsSettings";
import { ChangeLogs } from "./changeLogs";
import PageLayout from "../shared/pageLayout";
import { useTranslation } from "react-i18next";
import { EmailSettings } from "./emailSettings";
import { useCallback, useEffect, useState } from "react";
export default function Settings({ settingsData: initialSettingsData }) {
  const { t } = useTranslation();
  // const { settingsData } = useLoaderData();
  const actionData = useActionData();
  const [settings, setSettings] = useState(null);
  const [selected, setSelected] = useState("change-log");
  const [settingsData, setSettingsData] = useState(initialSettingsData);
  const [shopName, setShopName] = useState(null);
  const submit = useSubmit();
  const [emailLoader, setEmailLoader] = useState(false);
  const initialFetch = useCallback(() => {
    const result = {
      functionType: "initialFetch",
    };
    submit(result, { method: "post" });
  }, []);

  useEffect(() => {
    initialFetch();
  }, []);

  useEffect(() => {
    if (actionData) {
      if (actionData.status === "ok") {
        switch (actionData.data?.functionType) {
          case "initialFetch":
            const data = actionData.data.data;
            setShopName(data?.store || "");
            setSettingsData(data?.settingsData || null);
            break;
          case "updateEmailEnable":
            // setSettingsData(data?.settingsData || null);
            shopify.toast.show(actionData.message);
            break;
          default:
            shopify.toast.show(actionData.message, { isError: true });
            break;
        }
      } else {
        shopify.toast.show(actionData.message, { isError: true });
      }
      //  setEmailLoader(false);
      //  setIsSearchLoading(false);
      //  setIsExtensionLoading(false);
    }
    setEmailLoader(false);
  }, [actionData]);

  return (
    <PageLayout
      showBackButton
      title={t("settings_page_title")}
      subtitle={t("settings_page_desc")}
    >
      <PageTitleBar title="Settings page" />
      <Layout>
        <SideTabs setSelectedTab={setSelected} selectedTab={selected} />
        <Layout.Section>
          <GeneralSettings
            settings={settings}
            setSettings={setSettings}
            selectedTab={selected}
          />
          <EmailSettings
            selectedTab={selected}
            settingsData={settingsData}
            emailLoader={emailLoader}
            setEmailLoader={setEmailLoader}
          />
          <FeaturesSettings selectedTab={selected} />
          <NotificationsSettings selectedTab={selected} />
          <ChangeLogs selectedTab={selected} />
        </Layout.Section>
      </Layout>
    </PageLayout>
  );
}
