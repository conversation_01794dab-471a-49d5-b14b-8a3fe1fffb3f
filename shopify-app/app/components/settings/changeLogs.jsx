import {
  Card,
  BlockStack,
  Text,
  List,
  <PERSON>,
  Button,
  InlineStack,
} from "@shopify/polaris";
import {  EyeCheckMarkIcon } from "@shopify/polaris-icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export function ChangeLogs({ selectedTab }) {
  const { t } = useTranslation();
  const [changeLogs, setChangeLogs] = useState([]);

  return selectedTab !== "change-log" ? null : (
    <Card>
      <BlockStack gap="200">
        <Text as="h2" variant="headingMd">
          {t("store_workers_change_log")}
        </Text>
        <List>
          {changeLogs.length > 0 ? (
            changeLogs.map((changeLog, index) => (
              <List.Item key={index}>
                <InlineStack
                  gap="200"
                  align="space-between"
                  inlineAlign="center"
                  blockAlign="start"
                >
                  <BlockStack gap="200">
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {changeLog.content}
                    </Text>
                    <Link url={changeLog.link}>{changeLog.title}</Link>
                  </BlockStack>
                 {/*  <Button
                    icon={EyeCheckMarkIcon}
                    onClick={() => handleDeleteChangeLog(index)}
                  >
                    View
                  </Button> */}
                </InlineStack>
              </List.Item>
            ))
          ) : (
            <Card>
              <BlockStack gap="400" inlineAlign="center">
                <Text as="h2" variant="headingMd">
               {t('no_change_logs')}
                </Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                {t('there_are_no_change_logs_available_at_the_moment')}
                </Text>
              </BlockStack>
            </Card>
          )}
        </List>
      </BlockStack>
    </Card>
  );
}
