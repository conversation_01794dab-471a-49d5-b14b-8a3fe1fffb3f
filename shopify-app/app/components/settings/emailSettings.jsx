import { useState, useCallback, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { trackButtonClick, trackPageView } from "../../utilities/mixpanel.js";
import { useSubmit } from "@remix-run/react";
import {EmailPreferenceCard } from "../EmailPreferenceCard.jsx"

export function EmailSettings({ selectedTab, settingsData, emailLoader, setEmailLoader }) {
  const { t } = useTranslation();
  const [isEmailEnable, setIsEmailEnable] = useState(false);
  const submit = useSubmit();

  useEffect(() => {
    if (settingsData) {
      setIsEmailEnable(settingsData?.email_enable);
    }
  }, [settingsData]);

  const handleEmailChange = useCallback(
    (newValue) => {
      setIsEmailEnable(newValue);
      handleEmailEnable(newValue);
    },
    [isEmailEnable],
  );

  const handleEmailEnable = async (email) => {
    const result = {
      email_enable: email,
      functionType: "updateEmailEnable",
    };
    setEmailLoader(true);
    submit(result, { method: "post" });
  };

  return selectedTab !== "email" ? null : (
    <EmailPreferenceCard
    t={t}
    isEmailEnable={isEmailEnable}
    emailLoader={emailLoader}
    handleEmailChange={handleEmailChange}
    showDisable={true}
  />
  );
}
