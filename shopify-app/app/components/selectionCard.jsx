import React from "react";
import { Card, InlineStack, Box, RadioButton, Checkbox } from "@shopify/polaris";

/**
 * SelectionCard - A reusable component for displaying and selecting items (products, collections, or articles)
 * 
 * @param {Object} props
 * @param {Object} props.item - The item object containing title, description, and id
 * @param {string} props.selectionType - Either "single" or "multiple" to determine selection mode
 * @param {boolean} props.isSelected - Whether the item is currently selected
 * @param {Function} props.onSingleSelect - Callback for single selection mode
 * @param {Function} props.onMultipleSelect - Callback for multiple selection mode (receives checked state)
 */
const SelectionCard = ({
  item,
  selectionType = "single",
  isSelected,
  onSingleSelect,
  onMultipleSelect,
}) => {
  const itemId = item.id || item.shopify_gid;
  
  return (
    <div style={{ marginBottom: "10px" }}>
      <Card>
        <InlineStack gap="3" align="start">
          <Box paddingBlockStart="1">
            {selectionType === "single" ? (
              <RadioButton
                label={item?.title || ""}
                checked={isSelected}
                id={`item-${itemId}`}
                name="item-selection"
                helpText={item?.description || ""}
                onChange={onSingleSelect}
              />
            ) : (
              <Checkbox
                label={item.title || ""}
                checked={isSelected}
                helpText={item?.description || ""}
                onChange={onMultipleSelect}
              />
            )}
          </Box>
        </InlineStack>
      </Card>
    </div>
  );
};

export default SelectionCard;