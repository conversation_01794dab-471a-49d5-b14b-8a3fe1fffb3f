import {
  Text,
  BlockStack,
  InlineStack,
  TextField,
  Select,
} from "@shopify/polaris";
import "../css/workflow.css";
import { ToggleSwitch } from "./ToggleSwitch";
import React, { useState } from "react";

export const FieldRenderer = ({ field, value, errorText, onChange }) => {
  const { type, key, options, display_name, required, placeholder } = field;
  const label = display_name || key;
  const [listInput, setListInput] = useState("");
  const handleListKeyDown = (e) => {
    if (e.key === "Enter" && listInput.trim()) {
      const newItems = listInput
        .split(",")
        .map((item) => item.trim())
        .filter(Boolean);
      const updatedList = Array.isArray(value)
        ? [...value, ...newItems]
        : newItems;
      onChange(field.key, updatedList);
      setListInput("");
      e.preventDefault();
    }
  };

  return (
    <BlockStack gap="4" key={key}>
      <InlineStack blockAlign="start">
        <div className="field-label">
          <Text variant="headingSm" as="h3">
            {label}
          </Text>
        </div>
        <div className="field-input">
          {type === "boolean" ? (
            <div style={{ marginLeft: "-13px" }}>
              <ToggleSwitch
                id={label}
                isOn={!!value}
                handleToggle={() => onChange(key, !value)}
              />
            </div>
          ) : type === "dropdown" ? (
            <Select
              options={options.map((opt) => ({ label: opt, value: opt }))}
              value={value}
              onChange={(val) => onChange(key, val)}
              required={required}
              placeholder={placeholder || "Select an option"}
              error={errorText}
            />
          ) : type === "number" ? (
            <TextField
              type="number"
              value={value}
              onChange={(val) => onChange(key, val)}
              required={required}
              error={errorText}
              placeholder={placeholder}
              min={0}
            />
          ) : type === "textarea" ? (
            <TextField
              component="textarea"
              value={value}
              onChange={(val) => onChange(key, val)}
              required={required}
              error={errorText}
              placeholder={placeholder}
            />
          ) : type === "text" ? (
            <TextField
              value={value}
              onChange={(val) => onChange(key, val)}
              required={required}
              error={errorText}
              placeholder={placeholder}
              
            />
          ) : type === "text_area" ? (
            <TextField
              value={value}
              onChange={(val) => onChange(key, val)}
              required={required}
              error={errorText}
              placeholder={placeholder}
              multiline={3}
            />
          ) : type === "list" ? (
            <div style={{ width: "100%" }}>
              <input
                type="text"
                value={listInput}
                onChange={(e) => setListInput(e.target.value)}
                onKeyDown={handleListKeyDown}
                onBlur={() => {
                  if (listInput.trim()) {
                    const newItems = listInput
                      .split(",")
                      .map((item) => item.trim())
                      .filter(Boolean);
                    const updatedList = Array.isArray(value)
                      ? [...value, ...newItems]
                      : newItems;
                    onChange(key, updatedList);
                    setListInput("");
                  }
                }}
                placeholder={
                  placeholder || "Type and press Enter (comma separated)"
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: `1px solid ${errorText ? "#a94655" : "#c4cdd5"}`,
                  borderRadius: "6px",
                  fontSize: "16px",
                  marginBottom: "4px",
                }}
              />
              <div style={{ marginTop: 8 }}>
                {(Array.isArray(value) ? value : []).map((item, idx) => (
                  <span
                    key={idx}
                    style={{
                      display: "inline-block",
                      background: "#f1f1f1",
                      borderRadius: 12,
                      padding: "2px 10px",
                      marginRight: 6,
                      marginBottom: 4,
                      fontSize: 13,
                      position: "relative",
                    }}
                  >
                    {item}
                    <span
                      style={{
                        marginLeft: 8,
                        color: "#d72c0d",
                        cursor: "pointer",
                        fontWeight: "bold",
                        fontSize: 14,
                        paddingLeft: 4,
                      }}
                      onClick={() => {
                        const updated = value.filter((_, i) => i !== idx);
                        onChange(key, updated.length === 0 ? "" : updated);
                      }}
                      title="Remove"
                    >
                      ×
                    </span>
                  </span>
                ))}
              </div>
              {errorText && <div style={{ color: "#a94655" }}>{errorText}</div>}
            </div>
          ) : null}
        </div>
      </InlineStack>
    </BlockStack>
  );
};


