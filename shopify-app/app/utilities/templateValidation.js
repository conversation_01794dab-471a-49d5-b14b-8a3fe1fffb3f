import useRunWorkflowState from "../contexts/state.js";

/**
 * Validates form values for template configuration
 * @param {Object} templateData - Template configuration data
 * @param {Function} t - Translation function
 * @returns {boolean} - Returns true if form is valid, false otherwise
 */
export const validateFormValues = (templateData, t) => {
  const configValue = useRunWorkflowState.getState().configValue;
  
  if (!templateData?.current_configuration) return false;
  
  const updatedValues = { ...configValue };
  let hasErrors = false;
  
  templateData.current_configuration.forEach((config) => {
    config.fields.forEach((field) => {
      const fieldState = configValue[field.key] || {
        value: "",
        errorText: "",
      };
      const value = fieldState.value;
      
      if (field.required && (value === "" || value === null)) {
        updatedValues[field.key] = {
          ...fieldState,
          errorText: t("this_field_is_required"),
        };
        hasErrors = true;
      } else {
        updatedValues[field.key] = {
          ...fieldState,
          errorText: "",
        };
      }
    });
  });

  useRunWorkflowState.getState().setConfigValue(updatedValues || {});
  return !hasErrors;
};

/**
 * Validates if store inputs are selected
 * @param {Object} selectedItems - Selected store items with store_data_type and store_data
 * @param {Function} t - Translation function
 * @returns {boolean} - Returns true if valid, false otherwise
 */
export const validateStoreInputs = (templateData, selectedItems, t) => {
  if (!selectedItems) {
    return true;
  }
  const category =
    templateData?.store_data.store_data_type?.category?.toLowerCase();
  if (
    category === "blog" ||
    category === "article" ||
    category === "products"
  ) {
    if (!selectedItems || selectedItems.length === 0) {
      if (typeof shopify !== "undefined") {
        shopify.toast.show(t("please_select_any_store_input"), {
          isError: true,
        });
      }
      return false;
    }
  }
  return true;
};

/**
 * Triggers force form update event
 */
export const triggerForceFormUpdate = () => {
  const forceUpdateEvent = new CustomEvent("force-form-update");
  window.dispatchEvent(forceUpdateEvent);
};
