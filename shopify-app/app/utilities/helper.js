//helper.js
import path from "path";
import pino from "pino";
const isNode = typeof window === "undefined";
let logger;
async function setupLogger() {
  if (isNode) {
    const fs = await import("fs");
    const logsDir = "./logs";
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir);
    }
    return pino(pino.destination(path.join(logsDir, "app.log")));
  } else {
    return pino({ browser: { asObject: true } });
  }
}
setupLogger().then((initializedLogger) => {
  logger = initializedLogger;
});

export function loggerInfo(text, shopName, bodyParams) {
  const logData = {
    message: text || "",
    store: shopName || "unknown",
    timestamp: new Date().toISOString(),
  };
  if (bodyParams != null) {
    logData.bodyParams = bodyParams;
  }
  if (isNode) {
    logger?.info(logData);
  } else {
    // console.info(logData);
  }
  console.info(logData);
}

export function loggerError(text, shopName, errorMessage, bodyParams) {
  const logData = {
    message: text || "",
    store: shopName || "unknown",
    timestamp: new Date().toISOString(),
    errorMessage: errorMessage,
  };
  if (bodyParams != null) {
    logData.bodyParams = bodyParams;
  }
  if (isNode) {
    logger?.error(logData);
  } else {
    //    console.error(logData);
  }
  console.info(logData);
}

export async function sendResponse({ status, message, functionType, data }) {
  if (data != null) {
    return {
      status: status,
      message: message,
      functionType: functionType,
      data: data,
    };
  } else {
    return {
      status: status,
      message: message,
      functionType,
      functionType,
    };
  }
}

export async function generateProductUrl(storeDomain, productName) {
  const url = `${storeDomain}/products/${productName}`;
  return url;
}

export function extractGlobalId(gid) {
  const regex = /\/(\d+)$/;
  const match = gid.match(regex);
  return match ? parseInt(match[1], 10) : null;
}

export function toGlobalId(resourceType, id) {
  return `gid://shopify/${resourceType}/${id}`;
}

export async function parseStringToJSON(inputStr) {
  try {
    let cleaned = inputStr
      .replace(/^```[a-z]*\s*/i, "") // Remove markdown-style block start
      .replace(/```[\s\S]*$/, "") // Remove markdown-style block end
      .trim();
    // Handle if entire string is quoted and has escaped quotes
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.slice(1, -1); // remove outer quotes
    }
    // Unescape common JSON escape characters
    cleaned = cleaned
      .replace(/\\"/g, '"')
      .replace(/\\n/g, "\n")
      .replace(/\\t/g, "\t")
      .replace(/\\\\/g, "\\");
    // First parse attempt
    let parsed = JSON.parse(cleaned);
    // If result is a string, try parsing again (handle double JSON encoding)
    if (typeof parsed === "string") {
      parsed = JSON.parse(parsed);
    }

    return parsed;
  } catch (err) {
    console.error("Failed to parse JSON:", err.message);
    return [];
  }
}

// Generates 3 random alphanumeric characters
function generateRandomChars() {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < 3; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Appends 3 random characters to a string
function appendRandomToString(str) {
  const randomPart = generateRandomChars();
  return str + randomPart;
}

// Encodes a string to Base64
function toBase64(str) {
  return btoa(str);
}

// Decodes a Base64 string
function fromBase64(base64Str) {
  return atob(base64Str);
}

// Encodes after appending random characters
export async function appendAndEncode(str) {
  const appended = appendRandomToString(str);
  return toBase64(appended);
}

// Decodes and removes last 3 characters
function decodeAndRemoveRandom(base64Str) {
  const decoded = fromBase64(base64Str);
  return decoded.slice(0, -3); // Remove last 3 characters
}
