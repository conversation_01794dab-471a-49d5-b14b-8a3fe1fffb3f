import { Intercom, show } from "@intercom/messenger-js-sdk";
import intercomAppId from "../config/intercom_config";
export function initializeIntercom(user = {}) {
  if (typeof window === "undefined") return;
  try {
    Intercom({
      app_id: intercomAppId,
      ...user,
    });
    // Only show Intercom if it's the user's first visit and path is /app/home
    const hasVisited = localStorage.getItem("hasVisited") || false;
    if (
      !hasVisited &&
      (window.location.pathname === "/app/home" ||
        window.location.pathname === "/app")
    ) {
      show();
    }
  } catch (error) {
    console.error("Error initializing Intercom:", error);
  }
}
