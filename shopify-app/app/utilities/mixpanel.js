// mixpanel.js
import { mixPanelToken } from "../config/mix_panel_token";
import mixpanel from "mixpanel-browser";
import locale from "../locales/en.json";
const MIXPANEL_TOKEN = mixPanelToken;

// Event types
const EVENTS = {
  PAGE_VIEW: "Page View",
  BUTTON_CLICK: "Button Click",
};

// Initialize Mixpanel
if (MIXPANEL_TOKEN) {
  mixpanel.init(MIXPANEL_TOKEN, {
    debug: process.env.NODE_ENV !== "production",
  });
} else {
  console.warn("Mixpanel token is not set. Tracking will be disabled.");
}

// Generic track function
const track = (eventName, properties = {}) => {
  if (MIXPANEL_TOKEN) {
    mixpanel.track(eventName, properties);
  }
};

// Track page view
export const trackPageView = (pageName, additionalProperties = {}) => {
  track(`${pageName} ${EVENTS.PAGE_VIEW}`, {
    page_name: pageName,
    app_name: locale.app_name,
    path: window.location.pathname,
    ...additionalProperties,
  });
};

// Track button click
export const trackButtonClick = (
  buttonName,
  pageName,
  additionalProperties = {},
) => {
  track(`${pageName} ${EVENTS.BUTTON_CLICK}`, {
    button_name: buttonName,
    page_name: pageName,
    path: window.location.pathname,
    ...additionalProperties,
  });
};

// Custom hook for page view tracking
import { useEffect, useRef } from "react";

export const usePageViewTracking = (pageName, additionalProperties = {}) => {
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      trackPageView(pageName, additionalProperties);
      isFirstRender.current = false;
    }
  }, [pageName, additionalProperties]);
};
