import { loggerInfo, loggerError } from "../utilities/helper";
import dotenv from "dotenv";
import moment from "moment";
dotenv.config();

/**
 * @typedef {Object} SlackNotificationData
 * @property {string} [shop_id]
 * @property {string} [shop]
 * @property {string} [store]
 * @property {string} [store_domain]
 * @property {string} [email]
 * @property {string} [phone]
 * @property {string} [timezone]
 * @property {number} [status_code]
 * @property {string} [message]
 * @property {string} [job_type]
 * @property {string} [job_id]
 * @property {string} [job_title]
 * @property {string} [job_status]
 * @property {string} [job_result]
 */
/**
 * @typedef {'install' | 'uninstall' | 'startup' || 'runJob'} NotificationType
 */

/**
 * Sends a notification to Slack webhook
 * @param {SlackNotificationData} data - The notification data
 * @param {NotificationType} [type='install'] - The type of notification
 */
export const sendSlackWebhook = async (data, type = "install") => {
  const filteredSession = {
    shop_id: data.shop_id,
    shop: data.shop || data.store,
    store_domain: data.store_domain,
    email: data.email,
    phone: data.phone || "",
    timezone: data.timezone,
    job_type: data.job_type,
    job_id: data.job_id,
    job_title: data.job_title,
    job_status: data.job_status,
    job_result: data.job_result,
  };

  const {
    shop_id,
    shop,
    store_domain,
    email,
    phone,
    timezone,
    job_id,
    job_title,
    job_status,
  } = filteredSession;

  const currentDateTime = moment().format("MM/DD/YYYY hh:mm A");
  let message;

  switch (type) {
    case "uninstall":
      message = `*🛒 Store-worker App 📝*\n\n> ┌────────────────────────────┐\n> │ 😞 User Uninstalled 😞   │\n> └────────────────────────────┘\n\n*Shop Name:* ${shop}`;
      break;

    case "startup":
      message = `*🛒 Store-worker App 📝*\n\n> ┌────────────────────────────┐\n> │ 🚀 *App Started API Issue* 🚀    │\n> └────────────────────────────┘\n\n*Shop Name:* ${shop}\n*Store Domain:* 🌐 ${store_domain}\n*Status Code:* ${data.status_code}\n*Message:* ${data.message}\n*Date/Time:* ${currentDateTime}`;
      break;
    case "runJob":
      message = `*🛒 Store-worker App 📝*\n\n> ┌────────────────────────────┐\n> │ 🚀 *Run Job Completed* 🚀    │\n> └────────────────────────────┘\n\n*Shop Name:* ${shop}\n*Job Title:* 🌐 ${job_title}\n*Job ID:* ${job_id}\n*Job Status:* ${job_status}\n*Date/Time:* ${currentDateTime}`;
      break;
    case "install":
    default:
      message = `*🛒 Store-worker App 📝*\n\n> ┌────────────────────────────┐\n> │ 😊 *New User Installed* 😊    │\n> └────────────────────────────┘\n\n*Shop ID:* \`${shop_id}\`\n*Shop Name:* ${shop}\n*Store Domain:* 🌐 ${store_domain}\n*Email:* 📧 ${email}\n*Phone:* 📞 ${phone}\n*Timezone:* 🕒 ${timezone}\n*Date/Time:* ${currentDateTime}`;
      break;
  }

  try {
    const endpoint = `${process.env.SLACK_WEBHOOK_URL}${process.env.SLACK_WEBHOOK_ACCESS_KEY}`;
    loggerInfo(`Sending to endpoint: ${endpoint}`, shop);
    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        text: message,
      }),
    });

    if (!response.ok) {
      loggerInfo(`Slack webhook failed with status`, shop, response);
    }

    loggerInfo("Message sent successfully to Slack");
  } catch (error) {
    loggerError(`Error sending Slack webhook data`, "", error);
  }
};
