import { integrationTempId } from "../config/config.js";

/**
 * Transforms config values to simplified format for API submission
 * @param {Object} configValue - Configuration values from state
 * @returns {Object} - Simplified key-value pairs
 */
export const simplifyConfigValues = (configValue) => {
  return Object.fromEntries(
    Object.entries(configValue).map(([key, { value }]) => [key, value]),
  );
};

/**
 * Creates initial form values from template configuration
 * @param {Object} templateData - Template data containing configuration
 * @returns {Object} - Initial form values
 */
export const createInitialFormValues = (templateData) => {
  const initialValues = {};

  if (!templateData?.current_configuration) return initialValues;

  templateData.current_configuration.forEach((config) => {
    config.fields.forEach((field) => {
      initialValues[field.key] = {
        value: templateData?.current_key_values?.[field.key] ?? "",
        errorText: "",
        required: field.required,
      };
    });
  });

  return initialValues;
};

/**
 * Creates connection IDs mapping for integration templates
 * @param {Object} templateData - Template data containing integration information
 * @returns {Object} - Connected IDs mapping or null if not an integration template
 */
export const createConnectionIds = (templateData) => {
  // Only process if the template UUID is in integrationTempId array
  if (!integrationTempId.includes(templateData.uuid)) {
    return null;
  }

  const integrationData = templateData?.integrationData || [];
  const connectedIds = {};

  integrationData.forEach((integration) => {
    if (integration.appUniqueId && integration.id) {
      // Map appUniqueId to readable names
      let readableName = integration.appUniqueId;
      if (integration.appUniqueId === "gmail") {
        readableName = "mailing";
      } else if (integration.appUniqueId === "googlesheets") {
        readableName = "sheets";
      }
      connectedIds[readableName] = integration.id;
    }
  });

  // Ensure default values for required integrations
  if (!connectedIds.mailing) connectedIds.mailing = "";
  if (!connectedIds.sheets) connectedIds.sheets = "";

  return connectedIds;
};

/**
 * Creates payload for running template
 * @param {Object} templateData - Template data
 * @param {Array} selectedItems - Selected store items
 * @param {Object} configValue - Configuration values
 * @param {Object} additionalFields - Additional input fields
 * @param {Object} storeInput - Store input configuration (optional)
 * @returns {Object} - Formatted payload for template execution
 */
export const createRunTemplatePayload = (
  templateData,
  selectedItems,
  configValue,
  additionalFields,
  storeInput,
) => {
  const simplifiedValues = simplifyConfigValues(configValue);
  const connectedIds = createConnectionIds(templateData);

  const configurations = {
    output_structure: [templateData.output_structure],
    ...simplifiedValues,
    additional_inputs: additionalFields || {},
  };

  // Only include input_data if there's meaningful data
  const inputData = {};
  let hasInputData = false;

  // Add store_data_type if it exists and is not empty
  if (storeInput?.store_data_type && Object.keys(storeInput.store_data_type).length > 0) {
    inputData.store_data_type = storeInput.store_data_type;
    hasInputData = true;
  }

  // Add store_data if it exists and is not empty
  if (selectedItems && selectedItems.length > 0) {
    inputData.store_data = selectedItems;
    hasInputData = true;
  }

  // Always include include_metadata
  inputData.include_metadata = true;
  hasInputData = true;

  // Only add input_data to configurations if there's meaningful data
  if (hasInputData) {
    configurations.input_data = inputData;
  }

  if (connectedIds) {
    configurations.connected_ids = connectedIds;
  }

  return {
    template_uuid: templateData?.uuid,
    configurations,
  };
};

/**
 * Creates payload for saving template configuration
 * @param {Object} templateData - Template data
 * @param {Array} selectedItems - Selected store items
 * @param {Object} configValue - Configuration values
 * @param {Object} additionalFields - Additional input fields
 * @param {Object} storeInput - Store input configuration
 * @returns {Object} - Formatted payload for saving configuration
 */
export const createSaveConfigPayload = (
  templateData,
  selectedItems,
  configValue,
  additionalFields,
  storeInput,
) => {
  const simplifiedValues = simplifyConfigValues(configValue);
  const storeData = templateData.store_data ? { ...templateData.store_data } : {};
  storeData.store_data = selectedItems;

  return {
    configurations: templateData?.current_configuration,
    values: simplifiedValues,
    additional_inputs: additionalFields.length === 0 ? null : additionalFields,
    store_data_type: storeInput?.store_data_type,
    store_data: [storeData],
  };
};
