// serverAnalytics.js
import { mixPanelToken } from "../config/mix_panel_token";
import mixpanel from "mixpanel";
const MIXPANEL_TOKEN = mixPanelToken;
import locale from "../locales/en.json";

const mixpanelClient = mixpanel.init(MIXPANEL_TOKEN, {
  protocol: "https",
  api_host: "https://api.mixpanel.com",
});

// Backend track function
const trackBackend = (eventName, properties = {}) => {
  if (MIXPANEL_TOKEN) {
    mixpanelClient.track(eventName, properties);
  }
};

// Export backend track functions
export const trackPageViewBackend = (pageName, additionalProperties = {}) => {
  trackBackend(`${pageName}`, {
    page_name: pageName,
    app_name: locale.app_name,
    ...additionalProperties,
  });
};
