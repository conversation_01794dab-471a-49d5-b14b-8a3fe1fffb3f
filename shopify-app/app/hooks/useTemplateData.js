import { useState, useEffect, useCallback } from "react";
import { useActionData, useSubmit } from "@remix-run/react";
import { trackPageView } from "../utilities/mixpanel.js";
import useRunWorkflowState from "../contexts/state.js";
import { createInitialFormValues } from "../utilities/templateHelpers.js";

/**
 * Custom hook for managing template data and related state
 * @returns {Object} - Template data state and handlers
 */
export const useTemplateData = () => {
  const submit = useSubmit();
  const actionData = useActionData();

  const [isLoading, setIsLoading] = useState(false);
  const [templateData, setTemplateData] = useState(null);
  const [shopName, setShopName] = useState("");
  const [itemsData, setItemsData] = useState([]);
  const [storeInput, setStoreInput] = useState(null);
  const [runLoader, setRunLoader] = useState(false);

  // Initial data fetch
  const initialFetch = useCallback(() => {
    setIsLoading(true);
    const result = {
      functionType: "initialFetch",
    };
    submit(result, { method: "post" });
  }, [submit]);

  // Handle action data responses
  useEffect(() => {
    if (actionData) {
      if (actionData.status === 200 || actionData.status === "ok") {
        switch (actionData.data?.functionType) {
          case "initialFetch":
            useRunWorkflowState.getState().setAdditionalFields([]);
            const data = actionData?.data.data;
            setShopName(data.shopName);
            setTemplateData(data?.templateData || null);
            setItemsData(data?.templateData?.itemData || []);
            setStoreInput(data?.templateData?.store_data || []);
            // Update global state
            useRunWorkflowState
              .getState()
              .setAdditionalFields(data?.templateData?.additional_inputs || []);
              
            useRunWorkflowState
              .getState()
              .setSelectedItems(data?.templateData?.store_data?.store_data || []);  
            // Set initial form values
            const initialValues = createInitialFormValues(data?.templateData);
            useRunWorkflowState.getState().setConfigValue(initialValues || {});
            break;

          case "runTemplate":
            templateData.latest_job_runs = templateData?.latest_job_runs || [];
            templateData?.latest_job_runs.unshift(actionData?.data?.data);
            if (typeof shopify !== "undefined") {
              shopify.toast.show(actionData.message);
            }
            break;
          case "saveTemplateConfiguration":
            if (typeof shopify !== "undefined") {
              shopify.toast.show(actionData.message);
            }
            break;
          case "saveRunTemplate":
            templateData.latest_job_runs = templateData?.latest_job_runs || [];
            templateData?.latest_job_runs.unshift(actionData?.data?.data);
            if (typeof shopify !== "undefined") {
              shopify.toast.show(actionData.message);
            }
            break;
          case "updateEmailEnable":
            if (templateData) {
              setTemplateData({
                ...templateData,
                settingsData: {
                  ...templateData.settingsData,
                  email_enable: true,
                },
              });
            }
            // shopify.toast.show(actionData.message);
            break;
          default:
            break;
        }
      } else {
        if (typeof shopify !== "undefined") {
          shopify.toast.show(actionData.message, { isError: true });
        }
      }
      setIsLoading(false);
      setRunLoader(false);
    }
  }, [actionData, initialFetch]);

  // Track page view when shop name is available
  useEffect(() => {
    if (shopName) {
      trackPageView("Template Detail", { shop_name: shopName });
    }
  }, [shopName]);

  // Initial fetch on mount
  useEffect(() => {
    initialFetch();
  }, [initialFetch]);

  return {
    isLoading,
    setIsLoading,
    templateData,
    shopName,
    itemsData,
    storeInput,
    runLoader,
    setRunLoader,
    initialFetch,
  };
};
