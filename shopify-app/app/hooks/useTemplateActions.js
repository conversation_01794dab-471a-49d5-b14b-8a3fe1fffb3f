import { useCallback } from "react";
import { useSubmit } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import { trackButtonClick } from "../utilities/mixpanel.js";
import useRunWorkflowState from "../contexts/state.js";
import {
  validateFormValues,
  validateStoreInputs,
  triggerForceFormUpdate,
} from "../utilities/templateValidation.js";
import {
  createRunTemplatePayload,
  createSaveConfigPayload,
} from "../utilities/templateHelpers.js";
import { integrationTempId } from "../config/config.js";

/**
 * Custom hook for template actions (run, save, validate)
 * @param {Object} templateData - Template data
 * @param {string} shopName - Shop name for tracking
 * @param {Function} setSelectedTab - Function to set selected tab
 * @param {Function} setRunLoader - Function to set run loader state
 * @param {Function} setIsLoading - Function to set loading state
 * @param {Object} storeInput - Store input configuration
 * @returns {Object} - Action handlers
 */
export const useTemplateActions = (
  templateData,
  shopName,
  setSelectedTab,
  setRunLoader,
  setIsLoading,
  storeInput,
) => {
  const { t } = useTranslation();
  const submit = useSubmit();

  const handleEmailEnable = async (value) => {
    const result = {
      email_enable: true,
      functionType: "updateEmailEnable",
    };
    submit(result, { method: "post" });
  };
  // Handle Run Task action
  const handleRunTask = useCallback(
    async (runType) => {
      const additional_fields =
        useRunWorkflowState.getState().additional_fields;
      const selectedItems = useRunWorkflowState.getState().selectedItems;
      const configValue = useRunWorkflowState.getState().configValue;

      trackButtonClick("Run Task", "Template Detail", {
        shop_name: shopName,
      });

      // Validate form values
      const isValid = validateFormValues(templateData, t);
      if (!isValid) {
        if (setSelectedTab) {
          setSelectedTab(2);
        }
        triggerForceFormUpdate();
        return;
      }

      // Validate store inputs
      if (!validateStoreInputs(templateData,selectedItems, t)) {
        return;
      }

      if (
        integrationTempId.includes(templateData.uuid) &&
        templateData.integrationData.length < 2
      ) {
        shopify.toast.show(t("Please_integrate_the_tool_to_proceed"), {
          isError: true,
        });
        return;
      }

      // Create and submit payload
      const payload = createRunTemplatePayload(
        templateData,
        selectedItems,
        configValue,
        additional_fields,
      );
      let saveData;
      if (runType === "saveRunTemplate") {
        const savePayload = createSaveConfigPayload(
          templateData,
          selectedItems,
          configValue,
          additional_fields,
          storeInput,
        );
        saveData = JSON.stringify(savePayload);
      }
      const result = {
        functionType: runType || "runTemplate",
        value: JSON.stringify(payload),
        saveData: saveData,
      };

      setRunLoader(true);
      setIsLoading(true);
      submit(result, { method: "post" });
    },
    [templateData, shopName, t, setSelectedTab, setRunLoader, submit],
  );

  // Handle Save Configuration action
  const handleSave = useCallback(() => {
    const additional_fields = useRunWorkflowState.getState().additional_fields;
    const selectedItems = useRunWorkflowState.getState().selectedItems;
    const configValue = useRunWorkflowState.getState().configValue;

    trackButtonClick("Save Template Configuration", "Template Detail", {
      shop_name: shopName,
    });

    // Validate form values
    const isValid = validateFormValues(templateData, t);
    if (!isValid) {
      triggerForceFormUpdate();
      return;
    }

    // Validate store inputs
    if (!validateStoreInputs(templateData,selectedItems, t)) {
      return;
    }

    // Create and submit payload
    const payload = createSaveConfigPayload(
      templateData,
      selectedItems,
      configValue,
      additional_fields,
      storeInput,
    );

    const result = {
      functionType: "saveTemplateConfiguration",
      value: JSON.stringify(payload),
    };

    submit(result, { method: "post" });
    setIsLoading(true);
    setRunLoader(true)
  }, [templateData, shopName, t, storeInput, setIsLoading, submit]);

  return {
    handleRunTask,
    handleSave,
    handleEmailEnable,
  };
};
