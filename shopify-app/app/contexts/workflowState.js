// useWorkflowState.js
import { create } from "zustand";

/**
 * Custom Zustand store for workflow state management
 * Maintains all workflow-related state in a centralized store
 * Provides methods for updating state and comparing data between steps
 */
const useWorkflowState = create((set, get) => ({
  // Current state values
  workflow_name: "",
  query: "",
  taskData: null,
  selectedTask: [],
  teamId: "",
  suggestedQuery: [],
  workersData: null,
  selectedWorkers: [],
  configurationsData: null,
  configurationsValue: null,
  summary: [],

  // Initial values for step comparison (used to determine if API calls are needed)
  initialStep1Data: {
    selectedTask: [],
    workflow_name: "",
    query: "",
    teamId: "",
  },
  initialStep2Data: {
    selectedWorkers: [],
  },
  initialStep3Data: {
    configurationsValue: null,
  },

  // Setters with appropriate data normalization
  setQuery: (newQuery) => set({ query: newQuery }),
  setTaskData: (newTaskData) => set({ taskData: newTaskData }),
  setSelectedTask: (newSelectedTask) => set({ selectedTask: newSelectedTask }),
  setWorkflowName: (newWorkflowName) => set({ workflow_name: newWorkflowName }),
  setTeamId: (newTeamId) => set({ teamId: newTeamId }),
  setSuggestedQuery: (newSuggestedQuery) => set({ suggestedQuery: newSuggestedQuery }),
  setWorkersData: (newWorkersData) => set({ workersData: newWorkersData }),
  setSelectedWorkers: (newSelectedWorkers) => set({ selectedWorkers: newSelectedWorkers }),
  
  // Configuration setters
  setConfigurationsData: (newConfigurationsData) => set({ 
    configurationsData: newConfigurationsData 
  }),
  
  setConfigurationsValue: (newConfigurationsValue) => set({ 
    configurationsValue: newConfigurationsValue 
  }),
  
  setSummary: (newSummary) => set({ summary: newSummary }),

  // Snapshot current state for step comparison
  updateInitialStep1Data: () => set((state) => ({
    initialStep1Data: {
      selectedTask: JSON.parse(JSON.stringify(state.selectedTask)),
      workflow_name: state.workflow_name,
      query: state.query,
      teamId: state.teamId,
    },
  })),
  
  updateInitialStep2Data: () => set((state) => ({
    initialStep2Data: {
      selectedWorkers: JSON.parse(JSON.stringify(state.selectedWorkers)),
    },
  })),
  
  updateInitialStep3Data: () => set((state) => ({
    initialStep3Data: {
      configurationsValue: state.configurationsValue 
        ? JSON.parse(JSON.stringify(state.configurationsValue)) 
        : null,
    },
  })),

  // Reset the entire store to initial state
  reset: () => set({
    workflow_name: "",
    query: "",
    taskData: null,
    selectedTask: [],
    teamId: "",
    suggestedQuery: [],
    workersData: null,
    selectedWorkers: [],
    configurationsData: null,
    configurationsValue: null,
    summary: [],
    initialStep1Data: {
      selectedTask: [],
      workflow_name: "",
      query: "",
      teamId: "",
    },
    initialStep2Data: {
      selectedWorkers: [],
    },
    initialStep3Data: {
      configurationsValue: null,
    },
  }),
}));

export default useWorkflowState;