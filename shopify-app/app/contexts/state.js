import { create } from "zustand";

/**
 * Custom Zustand store for managing the "Run Workflow" state
 * Centralized store for handling execution-related state and updates
 */
const useRunWorkflowState = create((set, get) => ({
  // Current state values
  selectedItems: [],
  additional_fields: {},
  configValue: {},

  // Initial snapshot for comparison or reset
  initialRunData: {
    selectedItems: [],
    additional_fields: {},
    configValue: {},
  },

  // Setters
  setSelectedItems: (newSelectedItems) => set({ selectedItems: newSelectedItems }),
  setAdditionalFields: (newAdditionalFields) => set({ additional_fields: newAdditionalFields }),
  setConfigValue: (newConfigValue) => set({ configValue: newConfigValue }),

  // Snapshot current state
  updateInitialRunData: () => set((state) => ({
    initialRunData: {
      selectedItems: JSON.parse(JSON.stringify(state.selectedItems)),
      additional_fields: JSON.parse(JSON.stringify(state.additional_fields)),
      configValue: JSON.parse(JSON.stringify(state.configValue)),
    },
  })),

  // Reset to initial state
  reset: () => set({
    selectedItems: [],
    additional_fields: {},
    configValue: {},
    initialRunData: {
      selectedItems: [],
      additional_fields: {},
      configValue: {},
    },
  }),
}));

export default useRunWorkflowState;
