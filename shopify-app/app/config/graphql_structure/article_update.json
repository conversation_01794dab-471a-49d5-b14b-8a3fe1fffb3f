{"total_run_job_metadata": {"title": "<total_run_job_title>", "description": "<total_run_job_description>"}, "graphql_string": "mutation CreateArticle($article: ArticleCreateInput!) { articleCreate(article: $article) { article { id title author { name } handle body summary tags } userErrors { code field message } } }", "input": {"article": {"blogId": "<shopify_gid>", "title": "<article_title>", "author": {"name": "<article_author>"}, "handle": "<article_handler>", "article_content": "<total_article_content_in_html_format_exclude_title_h1_tag>", "summary": "<summary_of_article>", "isPublished": "<True/False>(boolean)", "publishDate": "<current_date>", "tags": "<[Tag1, Tag2]>"}}, "display_data": {"id": "<generate_uuid>", "title": "<article_title>", "article_content": "<total_article_content_in_html_format_exclude_title_h1_tag>", "tags": "<[Tag1, Tag2]>", "summary": "<summary_of_article>", "author": "<author_name>", "status": "<null || false>"}}