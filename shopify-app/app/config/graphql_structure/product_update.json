{"total_run_job_metadata": {"title": "<total_run_job_title>", "description": "<total_run_job_description (generate_two_line_description)>"}, "graphql_string": "mutation productUpdate($input: ProductUpdateInput!) { productUpdate(product: $input) { product { id title descriptionHtml } userErrors { field message } } }", "input": {"input": {"id": "<shopify_gid>", "descriptionHtml": "<product_description>"}}, "display_data": {"id": "<generate_uuid>", "title": "<product_title>", "product_description": "<product_description>"}}