// app/app-config.js

import {
  ReceiptDollarFilledIcon,
  QuestionCircleIcon,
  SettingsIcon,
  ContentIcon,
} from "@shopify/polaris-icons";
import { BillingInterval } from "@shopify/shopify-app-remix/server";
import i18n from "i18next";

const appConfig = {
  // Constants
  IDENTIFIER_PREFIX: "Binarychakra",
  showSideNavigation: true,
  showTabNavigation: false,
  showTitleBar: false,
  fullWidthPageLayout: false,
  requireAppEmbed: true,
  showLegacyPriceBanner: false,
  // Menu data - Add more menu items as needed
  menuData: [
    {
      label: i18n.t("home"),
      destination: "/app/home",
    },
    {
      label: i18n.t("dashboard"),
      destination: "/app",
      icon: ContentIcon,
    },
    {
      label: i18n.t("analytics"),
      destination: "/app/analytics",
      icon: ContentIcon,
    },
    {
      label: i18n.t("jobs"),
      destination: "/app/workers",
    },
    {
      label: i18n.t("teams"),
      destination: "/app/teams",
    },
    {
      label: i18n.t("content"),
      destination: "/app/contents",
      icon: ContentIcon,
    },
    {
      label: i18n.t("settings"),
      destination: "/app/settings",
      icon: SettingsIcon,
    },
    {
      label: i18n.t("pricing"),
      destination: "/app/pricing",
      icon: ReceiptDollarFilledIcon,
    },
    {
      label: i18n.t("faq"),
      destination: "/app/faq",
      icon: QuestionCircleIcon,
    },
    // Add more menu items as needed
  ],

  // Pricing data
  pricingPlans: [
    {
      name: "Free",
      id: "free",
      amount: 0,
      currencyCode: "USD",
      interval: BillingInterval.Every30Days,
      description: "For small business",
      cta_label: "Upgrade to unlock all features",
      features: [
        "1k pages views & visitors",
        "10 basic features",
        "Basic customisation",
        "Basic support",
      ],
    },

    {
      name: "Pro",
      id: "pro",
      amount: 19,
      currencyCode: "USD",
      trialDays: 7,
      interval: BillingInterval.Every30Days,
      description: "For growing business",
      cta_label: "Start 7-days trial",
      features: [
        "Unlimted pages views & visitors",
        "All features",
        "Customisation",
        "Fast email support",
        "Custom domain",
      ],
    },

    {
      name: "Premium",
      id: "premium",
      subheader: "Most popular",
      amount: 49,
      currencyCode: "USD",
      trialDays: 7,
      interval: BillingInterval.Every30Days,
      description: "For large business",
      cta_label: "Start 7-days trial",
      features: [
        "Unlimted pages views & visitors",
        "All features",
        "Customisation",
        "Dedicated developer support",
        "Priority email support",
        "Custom domain",
        "Custom analytics",
      ],
    }
  ],
};

export default appConfig;
