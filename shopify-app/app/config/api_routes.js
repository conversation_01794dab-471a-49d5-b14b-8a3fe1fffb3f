//api_routes.js
export const fetch = "fetch";
export const products = 'products';
export const collections = 'collections';
export const store_worker_route = "storeworker";
export const workflow = 'workflow';
export const create_workflow = 'create-workflow';
export const summarize_tasks = 'summarize-tasks';
export const get_dashboard = 'get-dashboard-data';
export const dashboard = 'dashboard';
export const identify_configurations = 'identify-configurations';
export const validate_nlq = 'validate-nlq';
export const team_details = 'team-details';
export const resources = 'resources';
export const teams = 'teams';
export const run_workflow = 'run-workflow';
export const templates = 'templates';
export const list_workers_for_task = 'list-workers-for-task';
export const list_workers = 'list-workers';
export const install = 'install';
export const auth = 'auth';
export const approved_task_configuration = 'approved-task-configuration';
export const approved_nlq_task = 'approved-nlq-task';
export const startup = 'startup';
export const assign_azure_function_app = 'assign-azure-function-app';
export const sync = 'sync';
export const articles_route = 'articles';
export const blogs_route = 'blogs';
export const collections_route = 'collections';
export const products_route = 'products';
export const home = 'home';
export const template_details = 'template-details';
export const store_template_configuration = 'store-template-configuration';
export const run_template = 'run-template';
export const composio = 'composio';
export const connect = 'connect';
export const test_connection = 'test-connection';
export const connections = 'connections';
export const analytics = 'analytics';
export const run_template_async = 'run-template-async';