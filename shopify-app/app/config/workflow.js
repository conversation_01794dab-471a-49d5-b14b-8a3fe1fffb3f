const workFlowData = [
  {
    id: 1,
    name: "Create Blog Posts",
    description: "5 blog posts about summer fashion",
    type: "blog",
    workers: [
      {
        id: "writer",
        title: "Writer",
        description: "Creates engaging content with proper structure and style",
        icon: "✏️",
        iconColor: "success",
      },
      {
        id: "seo-pro",
        title: "SEO Pro",
        description: "Optimizes content for search engines and keyword ranking",
        icon: "🔍",
        iconColor: "highlight",
      },
      {
        id: "social-pro",
        title: "Social Pro",
        description: "Creates social media ready content",
        icon: "📱",
        iconColor: "critical",
      },
    ],
    task_config: [
      {
        id: 101,
        name: "Content Settings",
        description: "Configuration related to content creation",
        fields: [
          {
            key: "number_of_blog_posts",
            type: "number",
            component: "textfield",
            required: true,
          },
          {
            key: "average_post_length",
            type: "string",
            component: "dropdown",
            options: ["500 words", "800 words", "1200 words"],
            required: true,
          },
          {
            key: "main_topic",
            type: "string",
            component: "textfield",
            required: false,
          },
        ],
      },
      {
        id: 102,
        name: "SEO Settings",
        description: "Search engine optimization settings",
        fields: [
          {
            key: "target_keywords",
            type: "string",
            component: "textfield",
            required: true,
          },
          {
            key: "include_meta_descriptions",
            type: "boolean",
            component: "toggle",
            required: false,
          },
          {
            key: "generate_schema_markup",
            type: "boolean",
            component: "toggle",
            required: false,
          },
        ],
      },
    ],
  },
  {
    id: 2,
    name: "Create Collection Pages",
    description: "10 collection pages for seasonal items",
    type: "collection",
    task_config: [
      {
        id: 201,
        name: "Collection Setup",
        description: "Define collection page layout and content",
        fields: [
          {
            key: "collection_title",
            type: "string",
            component: "textfield",
            required: true,
          },
          {
            key: "number_of_collections",
            type: "number",
            component: "textfield",
            required: true,
          },
          {
            key: "layout_style",
            type: "string",
            component: "dropdown",
            options: ["Grid", "List"],
            required: false,
          },
        ],
      },
      {
        id: 202,
        name: "SEO Settings",
        description: "Search engine optimization for collections",
        fields: [
          {
            key: "seo_keywords",
            type: "string",
            component: "textfield",
            required: true,
          },
          {
            key: "enable_schema_markup",
            type: "boolean",
            component: "toggle",
            required: false,
          },
          {
            key: "custom_meta_description",
            type: "string",
            component: "textfield",
            required: false,
          },
        ],
      },
      {
        id: 203,
        name: "Visibility Settings",
        description: "Control collection page publishing status",
        fields: [
          {
            key: "visibility",
            type: "string",
            component: "dropdown",
            options: ["Draft (don't publish)", "Publish", "Schedule"],
            required: true,
          },
        ],
      },
    ],
    workers: [
      {
        id: "designer",
        title: "Designer",
        description: "Creates attractive collection layouts and organization",
        icon: "🏷️",
        iconColor: "highlight",
      },
      {
        id: "seo-pro",
        title: "SEO Pro",
        description:
          "Optimizes collections for search engines and discoverability",
        icon: "🔍",
        iconColor: "highlight",
      },
      {
        id: "product-pro",
        title: "Product Pro",
        description: "Organizes products into logical groupings",
        icon: "📦",
        iconColor: "critical",
      },
    ],
  },
];

export default workFlowData;
