import { BillingInterval } from "@shopify/shopify-app-remix/server";
let usagePricingPlans = [
  {
    name: "Pro",
    id: "pro",
    amount: 0,
    currencyCode: "USD",
    trialDays: 0,
    test:true,
    interval: BillingInterval.Every30Days,
    description: "For growing business",
    cta_label: "Subscribe",
    cappedAmount: {
      amount: 100.0,
      currencyCode: "USD",
    },
    terms: "$1 to create one workflow",
    features: [
      "Unlimited pages views & visitors",
      "All features",
      "Customisation",
      "Fast email support",
      "Custom domain",
    ],
  },
];
export { usagePricingPlans };
