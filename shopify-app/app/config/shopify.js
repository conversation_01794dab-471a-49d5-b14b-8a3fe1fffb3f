import "@shopify/shopify-app-remix/adapters/node";
import {
  AppDistribution,
  DeliveryMethod,
  shopifyApp,
  LATEST_API_VERSION,
  ApiVersion,
} from "@shopify/shopify-app-remix/server";
import { PrismaSessionStorage } from "@shopify/shopify-app-session-storage-prisma";
import { restResources } from "@shopify/shopify-api/rest/admin/2024-07";
import prisma from "./db.js";
import appConfig from "./app.js";
import { MetaObject } from "../entities/metaobject.js";
import { Metafield } from "../entities/metafield.js";
import { ShipReady } from "../entities/shipready.js";
import { Webhook } from "../entities/webhook.js";
import { createTables } from "../db/create_table.js";
import { updateStore, isExistStore } from "../models/store.server.js";
import { sendSlackWebhook } from "../utilities/slack_webhook.js";
import { saveSettings } from "../models/settings.server.js";
import { sendEmail } from "../models/settings.server.js";
import { fetchAppDetail } from "../graphql/get_app_details.js";
import {
  generateProActiveAIToken,
  syncAll,
} from "../models/afterAuth.server.js";
import { sendEmailFlag } from "./config.js";
//import { trackPageViewBackend } from "../utilities/mixpanel_server.js";
import { fetchStoreData } from "../graphql/get_store.js";
console.log(
  "---------- LATEST_API_VERSION: ",
  LATEST_API_VERSION,
  "----------",
);
console.log("---------- Available ApiVersions: ", ApiVersion, "----------");

/*
 * Transform appConfig.pricingPlans into an object (pricingPlans) where each key is the plan ID in uppercase,
 * and each value is an object containing the plan's amount, currencyCode, and interval.
 *
 */

const pricingPlans = appConfig.pricingPlans.reduce(
  (accumulatedPlans, currentPlan) => {
    const planId = currentPlan.id.toUpperCase();
    const planDetails = {
      amount: currentPlan.amount,
      currencyCode: currentPlan.currencyCode,
      interval: currentPlan.interval,
    };

    accumulatedPlans[planId] = planDetails;
    return accumulatedPlans;
  },
  {},
);

const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  apiVersion: ApiVersion.January25,
  scopes: process.env.SCOPES?.split(","),
  appUrl: process.env.SHOPIFY_APP_URL || "",
  authPathPrefix: "/auth",
  sessionStorage: new PrismaSessionStorage(prisma),
  distribution: AppDistribution.AppStore,
  restResources,
  billing: pricingPlans,
  webhooks: {
    APP_UNINSTALLED: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks",
    },
  },
  hooks: {
    afterAuth: async ({ session, admin }) => {
      let appToken;
      //logic when app is installed
      shopify.registerWebhooks({ session });
      //create table
      await createTables();
      //fetch store details from shopify graphql
      const storeData = await fetchStoreData(admin);
      //check store data exist or not
      const storeExist = await isExistStore(session.shop);
      //formatting the data to store
      const inputStoreData = {
        shop_id: storeData.id,
        email: storeData.email,
        session_id: session.id,
        uuid: null,
        name: storeData.name,
        phone: storeData.phone,
        // access_token: session.accessToken,
        store: storeData.myshopifyDomain,
        store_domain: storeData.store_domain,
        country_code: storeData.countryCode,
        timezone: storeData.timeZone,
      };
      if (!storeExist) {
        if (sendEmailFlag) {
          const appHandle = await fetchAppDetail(admin);
          const userShop = session?.shop.replace(".myshopify.com", "");
          const appRedirectUrl = `https://admin.shopify.com/store/${userShop}/apps/${appHandle}/app/`;
          await sendEmail({
            type: "welcome",
            email: storeData?.email || "",
            store: session.shop,
            TemplateModel: {
              app_url: appRedirectUrl,
            },
          });
        }
        sendSlackWebhook(inputStoreData, "install");
        await saveSettings({
          store: session.shop,
          functionType: "settingsUpdate",
          email_enable: true,
        });
        const payLoadProactiveAi = {
          shop: session.shop,
          shopDomain: inputStoreData.store_domain,
        };
        const proActiveAiRes = await generateProActiveAIToken(
          payLoadProactiveAi,
          inputStoreData,
        );
        appToken = proActiveAiRes?.data?.api_token || "";
        inputStoreData.proactive_ai_api_token = appToken || "";
        //insert or update store details to Supabase db
        await updateStore(session.shop, inputStoreData);
        //  trackPageViewBackend("Initial App start ", {
        //  shop_name: session.shop,
        //email: store?.email || "",
        //});
        const value = {
          domain: inputStoreData.store_domain,
          store: session.shop,
          appToken: appToken,
          admin: admin,
        };
        syncAll(value);
      }
    },
  },
  future: {
    unstable_newEmbeddedAuthStrategy: true,
    removeRest: true,
  },
  ...(process.env.SHOP_CUSTOM_DOMAIN
    ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
    : {}),
});

console.log("---------- shopify: ", shopify, "----------");
// print access token
console.log(
  "---------- shopify.accessToken: ",
  shopify.accessToken,
  "----------",
);
export default shopify;
export const apiVersion = LATEST_API_VERSION;
export const addDocumentResponseHeaders = shopify.addDocumentResponseHeaders;
export const authenticate = shopify.authenticate;
export const unauthenticated = shopify.unauthenticated;
export const login = shopify.login;
export const registerWebhooks = shopify.registerWebhooks;
export const sessionStorage = shopify.sessionStorage;

export const authenticateExtra = async (request) => {
  const { admin, billing, cors, redirect, session, sessionToken } =
    await authenticate.admin(request);
  const metaobject = new MetaObject(admin);
  const metafield = new Metafield(admin);
  const shipReady = new ShipReady(admin, session);
  const webhook = new Webhook(admin, session);

  return {
    metaobject,
    metafield,
    shipReady,
    webhook,
    admin,
    billing,
    cors,
    redirect,
    session,
    sessionToken,
  };
};

export const authenticateProxy = async (request) => {
  const { admin, session, storefront } =
    await authenticate.public.appProxy(request);
  const metaobject = new MetaObject(admin);
  const metafield = new Metafield(admin);
  return {
    metaobject,
    metafield,
    admin,
    session,
    storefront,
  };
};
