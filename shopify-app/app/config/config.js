//config.js
let STORES_TABLE;
let CHARGE_TABLE;
let proactiveaiBaseUrl;
let proactiveaiApiVersion;
let proactiveaiRoute;
let isUsageBasedBilling;
let usagePricingRate;
let JOB_OUTPUT_TABLE;
let SETTINGS_TABLE;
let showPromptFlow;
let graphqlAPIVersion;
let integrationTempId;
let sendEmailFlag;

if (process.env.NODE_ENV == "development" || process.env.NODE_ENV == "stage") {
  //Database table name
  STORES_TABLE = "stores";
  CHARGE_TABLE = "charge";
  //pro-active-ai
  proactiveaiBaseUrl = "https://stage.proactive.ai.chakril.site";
  proactiveaiApiVersion = "api/v1";
  proactiveaiRoute = "storeworker";
  usagePricingRate = 1.99;
  JOB_OUTPUT_TABLE = "job_output";
  (SETTINGS_TABLE = "settings"),
    //billing
    (isUsageBasedBilling = true);
  showPromptFlow = false;
  graphqlAPIVersion = "2025-04";
  integrationTempId = [
    "184f33ac-59bd-4f80-ab31-8fb80cddecb5",
    "ee951fe3-d26a-4b93-9683-3843ff6cd176",
    "1ae4408f-fec5-4a03-b231-d1f99abdec2a",
  ];
  sendEmailFlag = false;
} else {
  //Database table name
  STORES_TABLE = "stores";
  CHARGE_TABLE = "charge";
  //pro-active-ai
  proactiveaiBaseUrl = "https://stage.proactive.ai.chakril.site";
  proactiveaiApiVersion = "api/v1";
  proactiveaiRoute = "storeworker";
  //billing
  isUsageBasedBilling = true;
  usagePricingRate = 1.99;
  JOB_OUTPUT_TABLE = "job_output";
  SETTINGS_TABLE = "settings";
  showPromptFlow = false;
  graphqlAPIVersion = "2025-04";
  integrationTempId = [
    "184f33ac-59bd-4f80-ab31-8fb80cddecb5",
    "ee951fe3-d26a-4b93-9683-3843ff6cd176",
    "1ae4408f-fec5-4a03-b231-d1f99abdec2a",
  ];
  sendEmailFlag = false;
}
export {
  STORES_TABLE,
  CHARGE_TABLE,
  proactiveaiBaseUrl,
  proactiveaiApiVersion,
  proactiveaiRoute,
  isUsageBasedBilling,
  usagePricingRate,
  JOB_OUTPUT_TABLE,
  SETTINGS_TABLE,
  showPromptFlow,
  graphqlAPIVersion,
  integrationTempId,
  sendEmailFlag,
};

