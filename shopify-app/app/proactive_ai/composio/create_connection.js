import dotenv from "dotenv";
dotenv.config();

import axios from "axios";
import { loggerInfo, loggerError, sendResponse } from "../../utilities/helper";
import { proactiveaiBaseUrl, proactiveaiApiVersion } from "../../config/config";
import { connect, composio, store_worker_route } from "../../config/api_routes";

export const proactiveAiComposioConnect = async (storeData, inputCreate) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { store, store_token } = storeData || {};
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${composio}/${connect}`;
  const payload = inputCreate;
  try {
    loggerInfo(`composio-connect API: ${url}`, store, JSON.stringify(payload));
    const headers = {
      "Content-Type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };

    const response = await axios.post(url, payload, { headers });
    const responseData = response.data;

    if (responseData?.status_code === 200) {
      loggerInfo(`Completed composio-connect successfully.`, store);
      return sendResponse({
        status: 200,
        message: "Proactive AI composio-connect Success",
        data: responseData,
      });
    } else {
      const errorText = responseData?.message || "Unknown error from API";
      loggerError("composio-connect failed", store, errorText);
      return sendResponse({
        status: 400,
        message: `Error from composio-connect AI API: ${errorText}`,
      });
    }
  } catch (error) {
    const errorMessage =
      error.response?.data?.message || error.message || "Unexpected error";
    loggerError("composio-connect encountered an error", store, errorMessage);
    return sendResponse({
      status: 500,
      message: `Proactive AI composio-connect Error: ${errorMessage}`,
    });
  }
};
