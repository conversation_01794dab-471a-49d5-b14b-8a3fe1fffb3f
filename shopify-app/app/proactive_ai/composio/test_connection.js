import dotenv from "dotenv";
import axios from "axios";
import { loggerInfo, loggerError, sendResponse } from "../../utilities/helper";
import { proactiveaiBaseUrl, proactiveaiApiVersion } from "../../config/config";
dotenv.config();
import { test_connection, composio } from "../../config/api_routes";
export const proactiveAiComposioTestConnection = async (
  storeData,
  inputCreate,
) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { store, store_token } = storeData || {};
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${composio}/${test_connection}/${store}`;
  const payload = inputCreate;
  try {
    loggerInfo(
      `Composio-Test-connection API: ${url}`,
      store,
      JSON.stringify(payload),
    );
    const headers = {
      "Content-Type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };

    const response = await axios.post(url, payload, { headers });
    const responseData = response.data;
    if (responseData?.status_code === 200) {
      loggerInfo(`Completed Composio-Test-connection successfully.`, store);
      return sendResponse({
        status: 200,
        message: "Proactive AI approved-nlq-task Success",
        data: responseData,
      });
    } else {
      const errorText = responseData?.message || "Unknown error from API";
      loggerError("Composio-Test-connection failed", store, errorText);
      return sendResponse({
        status: 400,
        message: `Error from Composio-Test-connection AI API: ${errorText}`,
      });
    }
  } catch (error) {
    const errorMessage =
      error.response?.data?.message || error.message || "Unexpected error";
    loggerError(
      "Composio-Test-connection encountered an error",
      store,
      errorMessage,
    );
    return sendResponse({
      status: 500,
      message: `Proactive AI Composio-Test-connection Error: ${errorMessage}`,
    });
  }
};
