import dotenv from "dotenv";
import axios from "axios";
import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveaiBaseUrl, proactiveaiApiVersion } from "../config/config";
dotenv.config();
import {
  templates,
  store_worker_route,
  store_template_configuration,
} from "../config/api_routes";

export const proactiveAiSaveTemplateConfiguration = async (
  storeData,
  inputSaveTemplateConfiguration,
) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { store, store_token } = storeData || {};
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${templates}/${store_template_configuration}`;
  const payload = inputSaveTemplateConfiguration;
  try {
    loggerInfo(
      `Starting store-template-configuration. API: ${url}`,
      store,
      JSON.stringify(payload),
    );
    const headers = {
      "Content-Type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };

    const response = await axios.post(url, payload, { headers });
    const responseData = response.data;
    if (responseData?.status_code === 200) {
      loggerInfo(`Completed store-template-configuration successfully.`, store);
      return sendResponse({
        status: 200,
        message: "Proactive AI store-template-configuration Success",
        data: responseData,
      });
    } else {
      const errorText = responseData?.message || "Unknown error from API";
      loggerError("store-template-configuration failed", store, errorText);
      return sendResponse({
        status: 400,
        message: `Error from Proactive AI API store-template-configuration: ${errorText}`,
      });
    }
  } catch (error) {
    const errorMessage =
      error.response?.data?.message || error.message || "Unexpected error";
    loggerError(
      "store-template-configuration encountered an error",
      store,
      errorMessage,
    );
    return sendResponse({
      status: 500,
      message: `Proactive AI store-template-configuration Error: ${errorMessage}`,
    });
  }
};
