import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveaiBaseUrl, proactiveaiApiVersion } from "../config/config";
import axios from "axios";
import { install, auth } from "../config/api_routes";
export const proactiveAiInstall = async (data) => {
  const appType = "storeWorkers";
  const { shopDomain } = data;
  const filterDomain = shopDomain.replace(/^https?:\/\//, "");
  const shopName = data?.shop || "";
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${auth}/${install}`;
  const payload = {
    shop_domain: filterDomain || "",
    app_type: appType,
  };
  try {
    loggerInfo(
      `App Install API from proactive AI started. API URL: ${url}`,
      shopName,
      payload,
    );
    const response = await axios.post(url, payload);
    if (!response.data || response.data.status_code !== 200) {
      const errorText = response.data?.message || "Unknown error from API";
      loggerError(
        `App Install API from proactive AI failed`,
        shopName,
        errorText,
      );
      return sendResponse({
        status: 500,
        message: `App Install API from proactive AI failed: ${errorText}`,
      });
    }
    return sendResponse({
      status: 200,
      message: "App Install API done and store token  successfully",
      data: response.data || null,
    });
  } catch (error) {
    loggerError(
      `App Install API from proactive AI encountered an error`,
      shopName,
      error.message,
    );
    return sendResponse({
      status: 500,
      message: `Proactive AI Install Error: ${error.message}`,
    });
  }
};
