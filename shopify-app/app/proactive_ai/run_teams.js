import dotenv from "dotenv";
import axios from "axios";
import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import {
  proactiveaiBaseUrl,
  proactiveaiApiVersion,
  
} from "../config/config";
dotenv.config();
import { run_workflow,store_worker_route ,teams} from "../config/api_routes";
export const proactiveAiRunTeam = async (storeData, inputCreate) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { store, store_token } = storeData || {};
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${teams}/${run_workflow}`;
  const payload = inputCreate;
  try {
    loggerInfo(`Run Team API: ${url}`, store, JSON.stringify(payload));
    const headers = {
      "Content-Type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };

    const response = await axios.post(url, payload, { headers });
    const responseData = response.data;
    if (responseData?.status_code === 200) {
      loggerInfo(`Completed Run Team successfully.`, store);
      return sendResponse({
        status: 200,
        message: "Proactive AI approved-nlq-task Success",
        data: responseData,
      });
    } else {
      const errorText = responseData?.message || "Unknown error from API";
      loggerError("Run Team failed", store, errorText);
      return sendResponse({
        status: 400,
        message: `Error from Run Team AI API: ${errorText}`,
      });
    }
  } catch (error) {
    const errorMessage =
      error.response?.data?.message || error.message || "Unexpected error";
    loggerError("Run Team encountered an error", store, errorMessage);
    return sendResponse({
      status: 500,
      message: `Proactive AI Run Team Error: ${errorMessage}`,
    });
  }
};
