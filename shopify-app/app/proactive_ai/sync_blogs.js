import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import {
  proactiveaiBaseUrl,
  proactiveaiApiVersion,
  proactiveaiRoute,
} from "../config/config";
import dotenv from "dotenv";
import axios from "axios";
import { sync,store_worker_route,blogs_route } from "../config/api_routes";
dotenv.config();

export const proactiveAiBlogSync = async (data) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { blogs, shopName, store_token, functionType } = data;
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${sync}/${blogs_route}`;
  const payload = {
    blogs: blogs || [],
  };
  try {
    loggerInfo(`Proactive AI Sync Blog started. API URL: ${url}`, shopName);
    const headers = {
      "content-type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };
    const response = await axios.post(url, payload, { headers });
    if (!response.data || response.data.status_code !== 200) {
      const errorText = response.data?.message || "Unknown error from API";
      loggerError(`Proactive AI Sync Blog failed`, shopName, errorText);
      return sendResponse({
        status: 400,
        message: `Proactive AI Sync Blog Error: ${errorText}`,
        functionType,
      });
    }
    loggerInfo(`Proactive AI Sync Blog Completed`, shopName,response.data);
    return sendResponse({
      status: 200,
      message: `Proactive AI Sync Blog successful`,
      functionType,
      data: response.data,
    });
  } catch (error) {
    loggerError(
      `Proactive AI Sync Blog encountered an error`,
      shopName,
      error.message,
    );
    return sendResponse({
      status: 500,
      message: `Proactive AI Sync Blog Error: ${error.message}`,
      functionType,
    });
  }
};
