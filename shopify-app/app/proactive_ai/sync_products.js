import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveaiBaseUrl, proactiveaiApiVersion } from "../config/config";
import dotenv from "dotenv";
import axios from "axios";
dotenv.config();
import { sync, store_worker_route, products_route } from "../config/api_routes";
export const proactiveAiProductSync = async (data) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { products, shopName, store_token, functionType } = data;
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${sync}/${products_route}`;
  const payload = products;
  try {
    loggerInfo(`Proactive AI Sync Product started. API URL: ${url}`, shopName);
    const headers = {
      "content-type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };
    const response = await axios.post(url, payload, { headers });
    if (!response.data || response.data.status_code !== 200) {
      const errorText = response.data?.message || "Unknown error from API";
      loggerError(`Proactive AI Sync Product failed`, shopName, errorText);
      return sendResponse({
        status: 400,
        message: `Proactive AI Sync Product Error: ${errorText}`,
        functionType,
      });
    }
    loggerInfo(`Proactive AI Sync Product Completed`, shopName, response.data);
    return sendResponse({
      status: 200,
      message: `Proactive AI Sync Product Success`,
      functionType,
      data: response.data,
    });
  } catch (error) {
    loggerError(
      `Proactive AI Sync Product encountered an error`,
      shopName,
      error.message,
    );
    return sendResponse({
      status: 500,
      message: `Proactive AI Sync Product Error: ${error.message}`,
      functionType,
    });
  }
};
