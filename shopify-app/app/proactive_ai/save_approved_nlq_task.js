import dotenv from "dotenv";
import axios from "axios";
import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveaiBaseUrl, proactiveaiApiVersion } from "../config/config";
dotenv.config();
import {
  approved_nlq_task,
  store_worker_route,
  workflow,
} from "../config/api_routes";

export const proactiveAiSaveApprovedNlqTask = async (
  storeData,
  inputSaveWorkers,
) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { store, store_token } = storeData || {};
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${workflow}/${approved_nlq_task}`;
  const payload = inputSaveWorkers;
  try {
    loggerInfo(
      `Starting approved-nlq-task. API: ${url}`,
      store,
      JSON.stringify(payload),
    );
    const headers = {
      "Content-Type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };

    const response = await axios.post(url, payload, { headers });
    const responseData = response.data;
    if (responseData?.status_code === 200) {
      loggerInfo(`Completed approved-nlq-task successfully.`, store);
      return sendResponse({
        status: 200,
        message: "Proactive AI approved-nlq-task Success",
        data: responseData,
      });
    } else {
      const errorText = responseData?.message || "Unknown error from API";
      loggerError("Approved-nlq-task failed", store, errorText);
      return sendResponse({
        status: 400,
        message: `Error from Proactive AI API: ${errorText}`,
      });
    }
  } catch (error) {
    const errorMessage =
      error.response?.data?.message || error.message || "Unexpected error";
    loggerError("Approved-nlq-task encountered an error", store, errorMessage);
    return sendResponse({
      status: 500,
      message: `Proactive AI approved-nlq-task Error: ${errorMessage}`,
    });
  }
};
