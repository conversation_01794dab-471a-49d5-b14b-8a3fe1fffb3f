import dotenv from "dotenv";
import axios from "axios";
import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveaiBaseUrl, proactiveaiApiVersion } from "../config/config";
dotenv.config();
import {
  store_worker_route,
  templates,
  run_template_async,
} from "../config/api_routes";
export const proactiveAiRunTemplateAsync = async (storeData, inputCreate) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { store, store_token } = storeData || {};
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${templates}/${run_template_async}`;
  const callbackUrl = `${process.env.SHOPIFY_APP_URL}/callback/runJob`;
  let payload = inputCreate;
  payload.callback_url = callbackUrl;
  payload.store_name = store;
  try {
    loggerInfo(
      `Run Template Async API: ${url}`,
      store,
      JSON.stringify(payload),
    );
    const headers = {
      "Content-Type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };

    const response = await axios.post(url, JSON.stringify(payload), {
      headers,
    });
    const responseData = response.data;
    if (responseData?.status_code === 200) {
      loggerInfo(`Completed Run Template Async successfully.`, store);
      return sendResponse({
        status: 200,
        message: "Proactive AI Run Template Async Success",
        data: responseData,
      });
    } else {
      const errorText = responseData?.message || "Unknown error from API";
      loggerError("Run Template Async failed", store, errorText);
      return sendResponse({
        status: 400,
        message: `Error from Run Template Async AI API: ${errorText}`,
      });
    }
  } catch (error) {
    const errorMessage =
      error.response?.data?.message || error.message || "Unexpected error";
    loggerError("Run Template Async encountered an error", store, errorMessage);
    return sendResponse({
      status: 500,
      message: `Proactive AI Run Template Async  Error: ${errorMessage}`,
    });
  }
};
