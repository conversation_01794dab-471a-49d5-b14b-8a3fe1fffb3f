import dotenv from "dotenv";
import axios from "axios";
import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import {
  proactiveaiBaseUrl,
  proactiveaiApiVersion,
} from "../config/config";
import { validate_nlq,store_worker_route ,workflow} from "../config/api_routes";
dotenv.config();
export const proactiveAiGetTaskValidateNlq = async (data) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { query, shopName, store_token, workflow_name } = data || {};
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${workflow}/${validate_nlq}`;
  const payload = { query, workflow_name };
  try {
    loggerInfo(
      `Starting validate-nlq task via Proactive AI. API: ${url}`,
      shopName,
      JSON.stringify(payload),
    );
    const headers = {
      "Content-Type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };
    const response = await axios.post(url, payload, { headers });
    const responseData = response.data;
    if (responseData?.status_code === 200) {
      loggerInfo(`Completed validate-nlq task successfully.`, shopName);
      return sendResponse({
        status: 200,
        message: "Proactive AI get task validate-nlq Success",
        data: responseData,
      });
    } else {
      const errorText = responseData?.message || "Unknown error from API";
      loggerError("validate-nlq task failed", shopName, errorText);
      return sendResponse({
        status: 400,
        message: `Error from Proactive AI API: ${errorText}`,
      });
    }
  } catch (error) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      "Unexpected error occurred";
      loggerError(
      "validate-nlq task encountered an error",
      shopName,
      errorMessage,
    );
    return sendResponse({
      status: 500,
      message: `Proactive AI get task validate-nlq Error: ${errorMessage}`,
    });
  }
};
