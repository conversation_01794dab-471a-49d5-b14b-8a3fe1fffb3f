import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveaiBaseUrl, proactiveaiApiVersion } from "../config/config";
import axios from "axios";
import { startup,store_worker_route,assign_azure_function_app } from "../config/api_routes";
export const proactiveAiStartup = async (data) => {
  const { store_token } = data;
  const shopName = data?.shop || "";
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${startup}/${assign_azure_function_app}`;
  try {
    loggerInfo(
      `App assign-azure-function-app API from proactive AI started. API URL: ${url}`,
      shopName,
    );
    const headers = {
      "Content-Type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };
    const response = await axios.get(url, { headers });
    if (!response.data || response.data.status_code !== 200) {
      const errorText = response.data?.message || "Unknown error from API";
      loggerError(
        `App assign-azure-function-app API from proactive AI failed`,
        shopName,
        errorText,
      );
      return sendResponse({
        status: 500,
        message: `App assign-azure-function-app API from proactive AI failed: ${errorText}`,
      });
    }
    loggerInfo(
      `App assign-azure-function-app API from proactive AI completed`,
      shopName,
      JSON.stringify(response.data),
    );
    return sendResponse({
      status: 200,
      message:
        "App assign-azure-function-app API done and store token  successfully",
      data: response.data || null,
    });
  } catch (error) {
    loggerError(
      `App assign-azure-function-app API from proactive AI encountered an error`,
      shopName,
      error.message,
    );
    return sendResponse({
      status: error?.status || 500,
      message: `Proactive AI assign-azure-function-app Error: ${error.message}`,
    });
  }
};
