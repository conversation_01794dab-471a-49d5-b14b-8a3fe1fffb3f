import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import {
  proactiveaiBaseUrl,
  proactiveaiApiVersion,
} from "../config/config";
import dotenv from "dotenv";
import axios from "axios";
dotenv.config();
import { get_dashboard,store_worker_route ,dashboard} from "../config/api_routes";
export const proactiveAiGetAnalytics = async (storeData) => {
  const { store, store_token } = storeData || {};
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${dashboard}/${get_dashboard}`;
  loggerInfo(`Proactive AI get analytics API URL: ${url}`, store);
  try {
    const response = await axios.get(url, {
      headers: {
        "Content-Type": "application/json",
        "X-BINCHA-APP-TOKEN": appToken,
        "X-PROACTIVE-TOKEN": store_token,
      },
    });
    const { data: resData } = response;
    if (resData?.status_code === 200) {
      loggerInfo(
        `Proactive AI get get-dashboard-data Completed. API URL: ${url}`,
        store,
      );
      return sendResponse({
        status: 200,
        message: "Proactive AI get get-dashboard-data Success",
        data: resData,
      });
    }
    const errorMessage = resData?.message || "Unknown error from API";
    loggerError(
      "Proactive AI get get-dashboard-data failed",
      store,
      errorMessage,
    );
    throw sendResponse({
      status: 400,
      message: `Proactive AI get get-dashboard-data Error: ${errorMessage}`,
    });
  } catch (error) {
    const message =
      error?.response?.data?.message || error.message || "Unknown error";
    loggerError(
      "Proactive AI get get-dashboard-data encountered an error",
      store,
      message,
    );
    throw sendResponse({
      status: 500,
      message: `Proactive AI get get-dashboard-data Error: ${message}`,
    });
  }
};
