import dotenv from "dotenv";
import axios from "axios";
import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import {
  proactiveaiBaseUrl,
  proactiveaiApiVersion,
} from "../config/config";
import { approved_task_configuration,store_worker_route ,workflow} from "../config/api_routes";
dotenv.config();

export const proactiveAiSaveApprovedConfig = async (
  storeData,
  inputSaveConfiguration,
) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { store, store_token } = storeData || {};
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${workflow}/${approved_task_configuration}`;
  const payload = inputSaveConfiguration;
  try {
    loggerInfo(`Starting approved-task-configuration. API: ${url}`, store,  JSON.stringify(payload));
    const headers = {
      "Content-Type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };

    const response = await axios.post(url, payload, { headers });
    const responseData = response.data;
    if (responseData?.status_code === 200) {
      loggerInfo(`Completed approved-task-configuration successfully.`, store);
      return sendResponse({
        status: 200,
        message: "Proactive AI approved-task-configuration Success",
        data: responseData,
      });
    } else {
      const errorText = responseData?.message || "Unknown error from API";
      loggerError("approved-task-configuration failed", store, errorText);
      return sendResponse({
        status: 400,
        message: `Error from Proactive AI API: ${errorText}`,
      });
    }
  } catch (error) {
    const errorMessage =
      error.response?.data?.message || error.message || "Unexpected error";
    loggerError("approved-task-configuration encountered an error", store, errorMessage);
    return sendResponse({
      status: 500,
      message: `Proactive AI approved-task-configuration Error: ${errorMessage}`,
    });
  }
};
