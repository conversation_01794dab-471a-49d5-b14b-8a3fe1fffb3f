import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import {
  proactiveaiBaseUrl,
  proactiveaiApiVersion,
  proactiveaiRoute,
} from "../config/config";
import dotenv from "dotenv";
import axios from "axios";
dotenv.config();
import { summarize_tasks,store_worker_route ,workflow} from "../config/api_routes";

export const proactiveAiGetTaskSummarize = async (
  storeData,
  inputGetWorkers,
) => {
  const { store, store_token } = storeData || {};
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${workflow}/${summarize_tasks}`;
  const payload = inputGetWorkers;
  loggerInfo(
    `Proactive AI get summarize-tasks. API URL: ${url}`,
    store,
    JSON.stringify(payload),
  );
  try {
    const response = await axios.post(url, payload, {
      headers: {
        "Content-Type": "application/json",
        "X-BINCHA-APP-TOKEN": appToken,
        "X-PROACTIVE-TOKEN": store_token,
      },
    });

    const { data: resData } = response;

    if (resData?.status_code === 200) {
      loggerInfo(
        `Proactive AI get summarize-tasks Completed. API URL: ${url}`,
        store,
      );
      return sendResponse({
        status: 200,
        message: "Proactive AI get summarize-tasks Success",
        data: resData,
      });
    }

    const errorMessage = resData?.message || "Unknown error from API";
    loggerError("Proactive AI get summarize-tasks failed", store, errorMessage);
    return sendResponse({
      status: 400,
      message: `Proactive AI get summarize-tasks Error: ${errorMessage}`,
    });
  } catch (error) {
    const message =
      error?.response?.data?.message || error.message || "Unknown error";
    loggerError(
      "Proactive AI get summarize-tasks encountered an error",
      store,
      message,
    );
    return sendResponse({
      status: 500,
      message: `Proactive AI get summarize-tasks Error: ${message}`,
    });
  }
};
