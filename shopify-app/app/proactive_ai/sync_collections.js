import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveaiBaseUrl, proactiveaiApiVersion } from "../config/config";
import dotenv from "dotenv";
import axios from "axios";
dotenv.config();
import {
  sync,
  store_worker_route,
  collections_route,
} from "../config/api_routes";

export const proactiveAiCollectionSync = async (data) => {
  const appToken = process.env.PROACTIVE_AI_APP_TOKEN;
  const { collections, shopName, store_token, functionType } = data;
  const url = `${proactiveaiBaseUrl}/${proactiveaiApiVersion}/${store_worker_route}/${sync}/${collections_route}`;
  const payload = {
    collections: JSON.parse(collections) || [],
  };
  try {
    loggerInfo(
      `Proactive AI Sync Collection started. API URL: ${url}`,
      shopName,
    );
    const headers = {
      "content-type": "application/json",
      "X-BINCHA-APP-TOKEN": appToken,
      "X-PROACTIVE-TOKEN": store_token,
    };
    const response = await axios.post(url, payload, { headers });
    if (!response.data || response.data.status_code !== 200) {
      const errorText = response.data?.message || "Unknown error from API";
      loggerError(`Proactive AI Sync Collection failed`, shopName, errorText);
      return sendResponse({
        status: 400,
        message: `Proactive AI Sync Collection Error: ${errorText}`,
        functionType,
      });
    }
    loggerInfo(
      `Proactive AI Sync Collection Completed`,
      shopName,
      response.data,
    );
    return sendResponse({
      status: 200,
      message: `Proactive AI Sync Collection Success`,
      functionType,
      data: response.data,
    });
  } catch (error) {
    loggerError(
      `Proactive AI Sync Collection encountered an error`,
      shopName,
      error.message,
    );
    return sendResponse({
      status: 500,
      message: `Proactive AI Sync Collection Error: ${error.message}`,
      functionType,
    });
  }
};
