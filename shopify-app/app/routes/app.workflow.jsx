import {
  <PERSON>,
  Layout,
  <PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>ton,
  Text,
  Box,
  Banner,
  BlockStack,
  InlineStack,
  Icon
} from "@shopify/polaris";
import {
  NoteIcon,
  CollectionIcon
} from "@shopify/polaris-icons";
import { useState } from "react";

export default function NewWorkflow() {
  const [prompt, setPrompt] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [suggestedSteps, setSuggestedSteps] = useState([]);

  const handleAnalyze = () => {
    setIsAnalyzing(true);
    setTimeout(() => {
      setSuggestedSteps([
        {
          type: "blog_creation",
          description: "Create blog posts about XYZ topic",
          estimatedTime: "10-15 mins",
          icon: NoteIcon
        },
        {
          type: "collection_creation",
          description: "Create collection with 10 products",
          estimatedTime: "5-10 mins",
          icon: CollectionIcon
        }
      ]);
      setIsAnalyzing(false);
    }, 1500);
  };

  return (
    <Page
      title="Create Workflow"
      primaryAction={{
        content: "View All Workflows",
        url: "/app/workflows"
      }}
    >
      <Layout>
        <Layout.Section>
          <div className="max-w-4xl mx-auto">
            <Card>
              <div className="p-10">
                <BlockStack gap="8">
                  <div className="max-w-2xl">
                    <BlockStack gap="4">
                      <Text variant="headingLg" as="h2">
                        Describe Your Workflow
                      </Text>
                      <Text color="subdued">
                        Use natural language to describe what you want to achieve. Our AI will analyze and create the perfect workflow for you.
                      </Text>
                    </BlockStack>
                  </div>

                  <div className="bg-slate-50 p-5 rounded-lg border border-slate-200">
                    <Text as="p" color="subdued">
                      Example: "Create 5 blog posts about summer fashion and make a collection of trending products"
                    </Text>
                  </div>

                  <div className="my-6">
                    <TextField
                      label="Workflow Description"
                      value={prompt}
                      onChange={setPrompt}
                      multiline={4}
                      autoComplete="off"
                      placeholder="I want to..."
                      className="shadow-sm"
                    />
                  </div>

                  <div className="flex justify-end my-6">
                    <Button size="medium" primary loading={isAnalyzing} onClick={handleAnalyze}>
                      Analyze & Create Workflow
                    </Button>
                  </div>
                </BlockStack>
              </div>
            </Card>

            {suggestedSteps.length > 0 && (
              <div className="mt-16">
                <Card>
                  <div className="p-10">
                    <BlockStack gap="8">
                      <BlockStack gap="6">
                        <Text variant="headingLg" as="h3">
                          Suggested Workflow Steps
                        </Text>

                        <Banner status="info" className="bg-sky-50 p-4">
                          We've analyzed your request and created the following workflow. Please review and confirm.
                        </Banner>
                      </BlockStack>

                      <div className="relative mt-8">
                        <div className="absolute left-[27px] top-[52px] bottom-8 w-0.5 bg-slate-200"></div>
                        {suggestedSteps.map((step, index) => (
                          <div key={index} className="mb-12 last:mb-0">
                            <div className="relative">
                              <Card>
                                <div className="p-8">
                                  <InlineStack gap="6" align="start">
                                    <div className="flex-shrink-0">
                                      <div className="w-14 h-14 rounded-full bg-violet-50 border-4 border-violet-100 flex items-center justify-center">
                                        <Icon source={step.icon} tone="primary" />
                                      </div>
                                    </div>
                                    <div className="flex-grow pt-1">
                                      <BlockStack gap="4">
                                        <Text variant="headingMd" as="h4">
                                          {step.type.split('_').map(word =>
                                            word.charAt(0).toUpperCase() + word.slice(1)
                                          ).join(' ')}
                                        </Text>
                                        <Text as="p">{step.description}</Text>
                                        <div className="mt-4">
                                          <span className="inline-flex items-center rounded-full bg-violet-50 px-4 py-2 text-sm">
                                            <Icon source={step.icon} tone="primary" />
                                            <span className="ml-2 text-violet-700">
                                              Estimated time: {step.estimatedTime}
                                            </span>
                                          </span>
                                        </div>
                                      </BlockStack>
                                    </div>
                                  </InlineStack>
                                </div>
                              </Card>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="flex justify-end gap-4 mt-8 border-t pt-8">
                        <Button onClick={() => setSuggestedSteps([])}>
                          Start Over
                        </Button>
                        <Button size="large" primary>
                          Confirm & Start Workflow
                        </Button>
                      </div>
                    </BlockStack>
                  </div>
                </Card>
              </div>
            )}
          </div>
        </Layout.Section>
      </Layout>
    </Page>
  );
}