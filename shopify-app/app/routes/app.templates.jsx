import React, { useState, useCallback, useEffect } from "react";
import {
  Page,
  Card,
  Badge,
  Button,
  Icon,
  TextField,
  ButtonGroup,
  Text,
  Grid,
  EmptyState,
  Pagination,
  SkeletonBodyText,
  InlineStack,
  InlineGrid,
  Select,
} from "@shopify/polaris";
import i18n from "i18next";
import { json } from "@remix-run/node";
import {
  useActionData,
  useSubmit,
  useNavigate,
  useSearchParams,
} from "@remix-run/react";
import { loggerError, sendResponse } from "../utilities/helper";
import { useTranslation } from "react-i18next";
import imageUrls from "../config/images";
import { authenticate } from "../config/shopify.js";
import { getTemplatesData } from "../models/templates.server.js";
import "../css/team.css";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";
import { showPromptFlow } from "../config/config.js";
import { SearchIcon } from "@shopify/polaris-icons";

export async function action({ request }) {
  const { session, admin } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    const actions = {
      initialFetch: async () => {
        const templatesList = await getTemplatesData(data);
        bodyData = {
          status: "ok",
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            templatesList:
              templatesList.status === 200 ? templatesList.data : [],
            total: templatesList.total || 0,
            limit: templatesList.limit || 15,
            offset: templatesList.offset || 0,
          },
        };
        return bodyData;
      },
    };
    const actionFn = actions[data.functionType];
    if (!actionFn) {
      throw new Error("Unknown functionType");
    }
    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: bodyData.status || "ok",
        message: bodyData.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch Templates ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}

const templatesManagement = () => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const submit = useSubmit();
  const actionData = useActionData();

  // State from URL params with defaults
  const [searchValue, setSearchValue] = useState(
    searchParams.get("search") || "",
  );
  const [labelValue, setLabelValue] = useState(searchParams.get("label") || "");
  const [sortBy, setSortBy] = useState(searchParams.get("sort_by") || "title");
  const [sortOrder, setSortOrder] = useState(
    searchParams.get("sort_order") || "asc",
  );
  const [limit, setLimit] = useState(parseInt(searchParams.get("limit")) || 15);
  const [offset, setOffset] = useState(
    parseInt(searchParams.get("offset")) || 0,
  );

  const [isLoading, setIsLoading] = useState(true);
  const [shopName, setShopName] = useState("");
  const [templatesList, setTemplatesList] = useState([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate current page from offset and limit
  useEffect(() => {
    setCurrentPage(Math.floor(offset / limit) + 1);
  }, [offset, limit]);

  // Update URL when filters change
  const updateURL = useCallback(
    (newParams) => {
      const params = new URLSearchParams(searchParams);
      Object.entries(newParams).forEach(([key, value]) => {
        if (value) {
          params.set(key, value);
        } else {
          params.delete(key);
        }
      });
      setSearchParams(params);
    },
    [searchParams, setSearchParams],
  );

  const initialFetch = useCallback(() => {
    setIsLoading(true);
    setTemplatesList([]);
    const result = {
      functionType: "initialFetch",
      search: searchValue,
      label: labelValue,
      sort_by: sortBy,
      sort_order: sortOrder,
      limit: limit,
      offset: offset,
    };
    submit(result, { method: "post" });
  }, [searchValue, labelValue, sortBy, sortOrder, limit, offset]);

  useEffect(() => {
    if (actionData) {
      if (actionData.status === "ok") {
        switch (actionData.data?.functionType) {
          case "initialFetch":
            const data = actionData?.data?.data;
            setShopName(data.shopName);
            setTemplatesList(data?.templatesList || []);
            setTotal(data?.total || 0);
            break;
        }
      } else {
        shopify.toast.show(actionData.message, { isError: true });
      }
      setIsLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    initialFetch();
  }, [labelValue, sortBy, sortOrder, limit, offset]);

  useEffect(() => {
    if (shopName) {
      trackPageView("Templates list", { shop_name: shopName });
    }
  }, [shopName]);

  const handleSearchChange = (value) => {
    setSearchValue(value);
  };

  const handleSearchClick = () => {
    updateURL({ search: searchValue, offset: 0 });
    initialFetch();
  };

  const handleLabelChange = (value) => {
    setLabelValue(value);
    updateURL({ label: value, offset: 0 });
  };

  const handleSortByChange = (value) => {
    setSortBy(value);
    updateURL({ sort_by: value, offset: 0 });
  };

  const handleSortOrderChange = (value) => {
    setSortOrder(value);
    updateURL({ sort_order: value, offset: 0 });
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      const newOffset = offset - limit;
      setOffset(newOffset);
      updateURL({ offset: newOffset });
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      const newOffset = offset + limit;
      setOffset(newOffset);
      updateURL({ offset: newOffset });
    }
  };

  const handleReset = () => {
    setSearchValue("");
    setLabelValue("");
    setSortBy("title");
    setSortOrder("asc");
    setOffset(0);
    updateURL({
      search: "",
      label: "",
      sort_by: "title",
      sort_order: "asc",
      offset: 0,
    });
  };

  const totalPages = Math.ceil(total / limit);

  const renderTemplateCard = (template) => {
    return (
      <div className="template-card" key={template.uuid}>
        <div
          className="team-card"
          style={{ display: "flex", flexDirection: "column", height: "100%" }}
        >
          <div style={{ flex: 1 }}>
            <InlineStack gap="100">
              <div className="emoji">{template.emoji}</div>
              <Text variant="headingMd">{template.title}</Text>
            </InlineStack>

            <div className="top-space-10" />

            <Text as="p" variant="bodyMd" color="subdued">
              {template.description}
            </Text>
          </div>

          <div
            className="top-space-10"
            style={{ marginTop: "auto", marginBottom: "10px" }}
          >
            {template.tags?.length > 0 && (
              <div className="top-space-10">
                <InlineStack gap="200">
                  {template.tags.map((tag, index) => (
                    <Badge key={index} tone="info">
                      {tag}
                    </Badge>
                  ))}
                </InlineStack>
              </div>
            )}
            <div className="top-space-10">
              <InlineStack align="space-between">
                <Button
                  disabled={isLoading}
                  onClick={() => {
                    setIsLoading(true);
                    navigate(`/app/template/${template.uuid}`);
                  }}
                  variant="primary"
                >
                  {t("view_assigned_workers")}
                </Button>
                <Button disabled variant="primary">
                  {t("schedule")}
                </Button>
              </InlineStack>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Page
      title={t("templates_page_title")}
      subtitle={t("templates_page_desc")}
      primaryAction={
        <TextField
          label="Search"
          labelHidden
          value={searchValue}
          onChange={handleSearchChange}
          placeholder={t("search")}
          suffix={
            <div style={{ cursor: "pointer" }} onClick={handleSearchClick}>
              <Icon source={SearchIcon} />
            </div>
          }
        />
      }
    >
      {/* 
  <Card>
          <div style={{ display: "flex", gap: "12px" }}>
            <div style={{ flex: 1 }}>
              <Select
                label="Filter by capability"
                labelHidden
                options={[
                  { label: "Filter by capability", value: "" },
                  { label: "Content Creation", value: "content" },
                  { label: "SEO", value: "seo" },
                  { label: "Social Media", value: "social" },
                  { label: "Data Analysis", value: "data" },
                ]}
                value={filterValue}
                onChange={handleFilterChange}
              />
            </div>
            <div style={{ flex: 1 }}>
              <Select
                label="Sort by"
                labelHidden
                options={[
                  { label: "Sort by: Most used", value: "most_used" },
                  { label: "Sort by: Recently used", value: "recent" },
                  { label: "Sort by: Alphabetical", value: "alpha" },
                ]}
                value={sortValue}
                onChange={handleSortChange}
              />
            </div>
            <div style={{ flex: 1 }}>
              <TextField
                label="Search"
                labelHidden
                value={searchValue}
                onChange={handleSearchChange}
                placeholder="Search teams"
              />
            </div>
          </div>
        </Card>
*/}

      {isLoading && templatesList.length === 0 ? (
        <InlineGrid columns={3} gap="400">
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
        </InlineGrid>
      ) : templatesList.length === 0 ? (
        <Card>
          <EmptyState
            heading={t("no_templates_available")}
            image={imageUrls.emptyImage}
          />
        </Card>
      ) : (
        <div className="grid-container">
          {templatesList.map((template) => renderTemplateCard(template))}
        </div>
      )}

      <div style={{ marginTop: "16px" }}>
        {totalPages > 0 && (
          <InlineStack align="end">
            <Pagination
              hasPrevious={currentPage > 1}
              onPrevious={handlePreviousPage}
              hasNext={currentPage < totalPages}
              onNext={handleNextPage}
              label={`${t("page")} ${currentPage} ${t("of")} ${totalPages}`}
            />
          </InlineStack>
        )}
      </div>
      <div style={{ marginTop: "15px" }} />
    </Page>
  );
};

export default templatesManagement;
