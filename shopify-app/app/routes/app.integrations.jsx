import React, { useState, useCallback, useEffect } from "react";
import {
  Page,
  Card,
  Badge,
  Button,
  Text,
  EmptyState,
  Layout,
  BlockStack,
  InlineStack as PolarisInlineStack,
} from "@shopify/polaris";
import i18n from "i18next";
import { integrationsConfig } from "../config/integration_config.js";
import { json } from "@remix-run/node";
import { useActionData, useSubmit } from "@remix-run/react";
import { loggerError, sendResponse } from "../utilities/helper.js";
import { useTranslation } from "react-i18next";
import imageUrls from "../config/images.js";
import { authenticate } from "../config/shopify.js";
import {
  composioGetConnectedAccount,
  composioConnect,
} from "../models/composio.server.js";
import "../css/team.css";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";

export async function action({ request }) {
  const { session } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    const actions = {
      addConnect: () => composioConnect(data),
      initialFetch: async () => {
        const integrationData = await composioGetConnectedAccount(data);
        bodyData = {
          status: "ok",
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            integrationData:
              integrationData.status === 200 ? integrationData.data : null,
          },
        };
        return bodyData;
      },
    };
    const actionFn = actions[data.functionType];
    if (!actionFn) {
      throw new Error("Unknown functionType");
    }
    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: "ok",
        message: bodyData.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch integration data ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}

export default function IntegrationPage() {
  const { t } = useTranslation();
  const [integrations, setIntegrations] = useState(integrationsConfig);
  const [loading, setLoading] = useState(false);
  const submit = useSubmit();
  const actionData = useActionData();

  const handleConnect = useCallback((integrationId) => {
    setLoading(true);
    const result = {
      functionType: "addConnect",
      app_name: integrationId,
    };
    submit(result, { method: "post" });
  }, []);

  useEffect(() => {
    initialFetch();
    const handleVisibilityChange = async () => {
      if (document.visibilityState === "visible") {
        initialFetch();
      }
    };
    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  const initialFetch = useCallback(() => {
    setLoading(true);
    const result = {
      functionType: "initialFetch",
    };
    submit(result, { method: "post" });
  }, []);

  const handleConfirmation = async (url) => {
    window.open(url, "_blank");
    parent.parent.GB_hide();
  };

  useEffect(() => {
    if (actionData) {
      if (actionData.status === "ok") {
        switch (actionData.data?.functionType) {
          case "initialFetch":
            const data = actionData?.data?.data;
            // Update integration status based on connected accounts
            if (data?.integrationData?.accounts) {
              setIntegrations((prevIntegrations) =>
                prevIntegrations.map((integration) => {
                  const isConnected = data.integrationData.accounts.some(
                    (account) =>
                      account.appName.toLowerCase() ===
                      integration.id.toLowerCase(),
                  );
                  return {
                    ...integration,
                    status: isConnected ? "connected" : "disconnected",
                  };
                }),
              );
            }
            break;
          case "addConnect":
            const url =
              actionData?.data.status === 200
                ? actionData.data.data?.connection_request?.redirectUrl
                : "";
            if (url) {
              handleConfirmation(url);
            }

            trackButtonClick("Integration Screen", "redirect_url from api", {
              shop_name: "",
            });
            break;
        }
      } else {
        shopify.toast.show(actionData.message, { isError: true });
      }
      setLoading(false);
    }
  }, [actionData]);

  const handleDisconnect = useCallback((integrationId) => {
    setLoading(true);
    setTimeout(() => {
      setIntegrations((prev) =>
        prev.map((integration) =>
          integration.id === integrationId
            ? { ...integration, status: "disconnected" }
            : integration,
        ),
      );
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusBadge = (status) => {
    const statusConfig = {
      connected: { content: "connected", tone: "success" },
      disconnected: { content: "disconnected", tone: "critical" },
      pending: { content: "pending", tone: "warning" },
    };

    return (
      <Badge tone={statusConfig[status]?.tone || "info"}>
        {t(statusConfig[status]?.content || status)}
      </Badge>
    );
  };

  const renderIntegrationCard = (integration) => (
    <Layout.Section key={integration.id}>
      <Card>
        <BlockStack gap="400">
          <PolarisInlineStack align="space-between">
            <PolarisInlineStack gap="300" align="center">
              <Text variant="headingMd" as="h3">
                {integration.icon} {t(integration.name)}
              </Text>
              {getStatusBadge(integration.status)}
            </PolarisInlineStack>
          </PolarisInlineStack>

          <Text as="p" tone="subdued">
            {t(integration.description)}
          </Text>

          <PolarisInlineStack gap="200">
            {integration.status === "connected" ? (
              <Button
                //  onClick={() => handleDisconnect(integration.id)}
                loading={loading}
                tone="critical"
                variant="secondary"
                disabled={true}
              >
                {t("disconnect")}
              </Button>
            ) : (
              <Button
                onClick={() => handleConnect(integration.id)}
                loading={loading}
                variant="primary"
              >
                {t("connect")}
              </Button>
            )}
          </PolarisInlineStack>
        </BlockStack>
      </Card>
    </Layout.Section>
  );

  return (
    <Page
      title={t("integration_page_title")}
      subtitle={t("integration_page_desc")}
    >
      <Layout>
        {integrations.filter((integration) => integration.show).length > 0 ? (
          integrations
            .filter((integration) => integration.show)
            .map(renderIntegrationCard)
        ) : (
          <Layout.Section>
            <Card>
              <EmptyState
                heading="No integrations available"
                image={imageUrls.emptyImage}
              >
                <p>{t("tools_not_available")}</p>
              </EmptyState>
            </Card>
          </Layout.Section>
        )}
      </Layout>
    </Page>
  );
}
