import {
  loggerInfo,
  loggerError,
  parseStringToJSON,
  sendResponse,
} from "../utilities/helper.js";
import { unauthenticated } from "../config/shopify.js";
import { updateTemplateResult } from "../models/templates.server.js";
import { createUsageCharge } from "../models/charge.server.js";
import { sendSlackWebhook } from "../utilities/slack_webhook.js";
import { fetchSettings, sendEmail } from "../models/settings.server.js";
import { sendEmailFlag } from "../config/config.js";
import { fetchStore } from "../models/store.server.js";
import { fetchAppDetail } from "../graphql/get_app_details.js";
export async function action({ request }) {
  let store_name = null;
  try {
    const body = await request.json();
    // Check if request body is empty
    if (!body || Object.keys(body).length === 0) {
      const msg = "Request body cannot be empty";
      loggerError(msg, store_name, null, { body });
      return await sendResponse({
        status: 400,
        message: msg,
        data: { error: "Bad Request", job_status: "Failed" },
      });
    }
    loggerInfo(
      `Received from Run Job Callback: ${JSON.stringify(body)}`,
      body?.data?.store_name,
      { body },
    );
    store_name = body.data?.store_name;
    // Validate store name
    if (!store_name || store_name.trim() === "") {
      const msg = "Store name is required and cannot be empty";
      loggerError(msg, store_name, null, { body });
      return await sendResponse({
        status: 400,
        message: msg,
        data: { error: "Bad Request", job_status: "Failed" },
      });
    }
    let admin;
    try {
      const adminResult = await unauthenticated.admin(store_name);
      admin = adminResult.admin;
    } catch (adminError) {
      const msg = `Failed to initialize admin session for store: ${store_name}`;
      loggerError(msg, store_name, adminError.message, { body });
      return await sendResponse({
        status: 500,
        message: msg,
        data: { error: "Admin initialization failed", job_status: "Failed" },
      });
    }

    const mainData = body?.data ?? {};
    const job_metadata = mainData?.job_metadata ?? {};

    let result = [];
    const resultValue = mainData?.result;

    try {
      if (typeof resultValue === "string") {
        result = await parseStringToJSON(resultValue);
      } else if (Array.isArray(resultValue)) {
        result = resultValue;
      } else if (resultValue != null) {
        loggerError(
          `Unexpected result type: ${typeof resultValue}`,
          store_name,
          null,
          { resultValue },
        );
      }
    } catch (parseError) {
      const msg = `Failed to parse result data for store: ${store_name}`;
      loggerError(msg, store_name, parseError.message, { resultValue });
    }

    const teamResult = {
      team_id: mainData?.template_uuid ?? "",
      job_id: job_metadata?.job_id ?? "",
      store: store_name,
      type: mainData?.category ?? "",
      status: job_metadata?.job_status ?? "completed",
      title: mainData?.job_title || job_metadata?.job_title || "",
      description: job_metadata?.description || "",
      result: result || [],
    };

    // Check status_code from the request body
    const statusCode = body?.status_code;
    if (statusCode !== 200) {
      teamResult.status = "failed";
      const msg = `Job failed with status_code: ${statusCode}. Marking job as failed.`;
      loggerError(msg, store_name, null, { statusCode, body });
    } else {
      // Create usage charge only if status_code is 200
      try {
        const chargeRes = await createUsageCharge(admin, teamResult);
        if (chargeRes?.status !== 200 || result.length === 0) {
          teamResult.status = "failed";
          const msg =
            "Charge failed or result is empty. Marking job as failed.";
          loggerError(msg, store_name, null, {
            chargeStatus: chargeRes?.status,
            resultLength: result.length,
          });
        } else {
          loggerInfo(
            `Usage charge created successfully for store: ${store_name}`,
            store_name,
            { chargeRes },
          );
        }
      } catch (chargeError) {
        teamResult.status = "failed";
        const msg = `Failed to create usage charge for store: ${store_name}`;
        loggerError(msg, store_name, chargeError.message, { teamResult });
      }
    }

    // Save template result
    try {
      const saveRes = await updateTemplateResult(teamResult);
      if (saveRes.status !== 200) {
        const msg = `Failed to save template result for store: ${store_name}`;
        loggerError(msg, store_name, null, { saveRes, teamResult });
      } else {
        loggerInfo(
          `Template result saved successfully for store: ${store_name}`,
          store_name,
          { saveRes },
        );
      }
    } catch (saveError) {
      const msg = `Exception occurred while saving template result for store: ${store_name}`;
      loggerError(msg, store_name, saveError.message, { teamResult });
    }

    loggerInfo(
      `Job processed successfully for store: ${store_name}`,
      store_name,
      {
        finalStatus: teamResult.status,
        jobId: teamResult.job_id,
      },
    );
    const slackInput = {
      shop: store_name,
      ...job_metadata,
    };
    const settingsData = await fetchSettings(store_name);
    if (settingsData?.email_enable && sendEmailFlag) {
      const storeData = await fetchStore(store_name);
      if (
        storeData?.status === 200 &&
        storeData?.data?.email &&
        teamResult?.status === "completed"
      ) {
        const userEmail = storeData?.data?.email || "";
        const appHandle = await fetchAppDetail(admin);
        const userShop = store_name.replace(".myshopify.com", "");
        const appRedirectUrl = `https://admin.shopify.com/store/${userShop}/apps/${appHandle}/app/job-result/${teamResult.job_id}`;
        let mailInput = {
          email: userEmail,
          type: "runJob",
          store: store_name,
          TemplateModel: {
            job_url: appRedirectUrl,
            job_title: teamResult?.title || "",
          },
        };
        const mailRes = await sendEmail(mailInput);
      }
    }
     await sendSlackWebhook(slackInput, "runJob");
    return await sendResponse({
      status: 200,
      message: "Data received and processed successfully",
      data: {
        job_status: teamResult.status,
        store: store_name,
        job_id: teamResult.job_id,
      },
    });
  } catch (error) {
    const msg = `Unexpected error in Run Job action for store: ${store_name || "unknown"}`;
    loggerError(msg, store_name, error.message, {
      stack: error.stack,
      store_name,
    });

    return await sendResponse({
      status: 500,
      message: msg,
      functionType: "callback.runJob",
      data: {
        error: "Internal Server Error - Run Job",
        job_status: "Failed",
        store_name: store_name || "unknown",
        timestamp: new Date().toISOString(),
      },
    });
  }
}
