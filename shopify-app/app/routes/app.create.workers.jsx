import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import {
  Page,
  Button,
  Text,
  ProgressBar,
  ButtonGroup,
  InlineStack,
  Box,
} from "@shopify/polaris";
import "../css/workflow.css";
import {
  useLocation,
  useNavigate,
  useActionData,
  useSubmit,
} from "@remix-run/react";
import { WorkFlowStepOne } from "../components/workflow/step_1";
import { WorkFlowStepTwo } from "../components/workflow/step_2";
import { WorkFlowStepThree } from "../components/workflow/step_3";
import { WorkFlowStepFour } from "../components/workflow/step_4";
import { useAppBridge } from "@shopify/app-bridge-react";
import { loggerError, sendResponse } from "../utilities/helper";
import { useTranslation } from "react-i18next";
import { authenticate } from "../config/shopify";
import { json } from "@remix-run/node";
import i18n from "i18next";
import { trackButton<PERSON>lick, trackPageView } from "../utilities/mixpanel.js";
import {
  getTaskData,
  stepOneToStepTwo,
  stepTwoToThree,
  stepThreeToFour,
  saveWorkFlow,
} from "../models/workers.server";
import useWorkflowState from "../contexts/workflowState";
import { SpinnerComponent } from "../components/SpinnerComponent.jsx";
/**
 * Action function for handling form submissions from the workflow
 * Authenticates the user and processes different API requests based on functionType
 *
 * @param {Object} request - The incoming request
 * @param {Object} params - URL parameters
 * @returns {Object} JSON response with appropriate status and data
 */
export async function action({ request, params }) {
  try {
    // Authenticate the admin session
    const { session} = await authenticate.admin(request);

    // Parse form data and add params and store information
    const data = {
      ...Object.fromEntries(await request.formData()),
      params,
    };
    data.store = session.shop;

    // Process different API actions based on functionType
    let bodyData;
    switch (data.functionType) {
      case "initialFetch":
        // Initial data fetch, no specific action needed
        bodyData = {
          status: 200,
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
          },
        };
        return bodyData;
        break;
      case "getTask":
        // Fetch task data based on query
        bodyData = await getTaskData(data);
        break;
      case "stepOneToTwo":
        // Process step 1 to step 2 transition
        bodyData = await stepOneToStepTwo(data);
        break;
      case "stepTwoToThree":
        // Process step 2 to step 3 transition
        bodyData = await stepTwoToThree(data);
        break;
      case "stepThreeToFour":
        // Process step 3 to step 4 transition
        bodyData = await stepThreeToFour(data);
        break;
      case "createUsageCharge":
        // Create usage charge when workflow is completed
        const input = {
          store: session.shop,
          teamId: data.teamId,
          functionType: data.functionType,
        };
        const res = await saveWorkFlow(input);
        bodyData = {
          status: 200,
          functionType: "createUsageCharge",
          message: res.message,
        };
        break;
      default:
        throw new Error("Unknown functionType");
    }

    return json(
      await sendResponse({
        status: bodyData.status || 200,
        message: bodyData?.message || "",
        data: bodyData,
      }),
    );
  } catch (error) {
    console.error(error);
    loggerError(`Error in workflow action: ${error}`);

    // Return error response
    return json(
      await sendResponse({
        status: 500,
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}

/**
 * Header component for workflow steps
 * Displays the current step title, description and progress
 *
 * @param {Number} step - Current step number (1-4)
 * @param {String} title - Step title
 * @param {String} description - Step description
 */
function HeaderSection({ step, title, description }) {
  return (
    <Box paddingBlockEnd="4">
      <InlineStack align="space-between">
        <div>
          <Text variant="headingLg" as="h1">
            {title}
          </Text>
          <Text variant="bodyMd" as="p" color="subdued">
            {description}
          </Text>
        </div>
        <div className="progress-container">
          <Text variant="bodySm" as="span">
            Step {step} of 4
          </Text>
          <div className="progress-bar-container">
            <ProgressBar progress={step * 25} size="small" />
          </div>
        </div>
      </InlineStack>
    </Box>
  );
}

/**
 * Navigation buttons component for workflow steps
 * Handles validation, API calls, and navigation between steps
 *
 * @param {Number} step - Current step number
 * @param {Function} setStep - Function to update step
 * @param {Function} setErrors - Function to set validation errors
 * @param {Function} setConfigValidationErrors - Function to set config validation errors
 * @param {Function} submit - Function to submit form data
 * @param {Boolean} isLoading - Loading state flag
 */
function NavigationButtons({
  step,
  setStep,
  setErrors,
  setConfigValidationErrors,
  submit,
  isLoading,
  setIsLoading,
  shopName,
  t,
}) {
  // Determine if current step is first or last
  const isFirstStep = step === 1;
  const isLastStep = step === 4;

  // Extract required state for validation and API calls
  const workflow_name = useWorkflowState((state) => state.workflow_name);
  const query = useWorkflowState((state) => state.query);
  const selectedTask = useWorkflowState((state) => state.selectedTask);
  const selectedWorkers = useWorkflowState((state) => state.selectedWorkers);
  const configurationsValue = useWorkflowState(
    (state) => state.configurationsValue,
  );
  const teamId = useWorkflowState((state) => state.teamId);
  const initialStep1Data = useWorkflowState((state) => state.initialStep1Data);
  const initialStep2Data = useWorkflowState((state) => state.initialStep2Data);
  const initialStep3Data = useWorkflowState((state) => state.initialStep3Data);

  /**
   * Navigate to previous step
   */
  const goPrevious = () => {
    if (!isFirstStep) {
      trackButtonClick("Workflow Creation", `Previous Step ${step}`, {
        shop_name: shopName,
      });
      setStep((prev) => prev - 1);
    }
  };

  /**
   * Deep equality check for objects
   * Used to determine if state has changed and API calls are needed
   *
   * @param {Object} obj1 - First object to compare
   * @param {Object} obj2 - Second object to compare
   * @returns {Boolean} True if objects are equal
   */
  const isDeepEqual = (obj1, obj2) => {
    if (obj1 === obj2) return true;
    if (obj1 === null || obj2 === null) return obj1 === obj2;
    if (typeof obj1 !== "object" || typeof obj2 !== "object")
      return obj1 === obj2;

    // Convert to strings for comparison
    const obj1Str = JSON.stringify(obj1);
    const obj2Str = JSON.stringify(obj2);
    return obj1Str === obj2Str;
  };

  /**
   * Navigate to next step
   * Validates current step and makes API calls if needed
   */
  const goNext = async () => {
    // Validation logic for each step
    const error = {};
    trackButtonClick("Workflow Creation", `Next Step ${step}`, {
      shop_name: shopName,
    });
    // Step 1 validation
    if (step === 1) {
      if (!workflow_name) error.workflow_name = t("please_provide_a_job_name");
      if (!query)
        error.input_task_description = t(
          "please_do_let_us_know_your_requirement",
        );
      if (selectedTask.length === 0)
        error.selected_task = t("please_select_atleast_one_task");
    }

    // Step 3 validation - check configuration fields
    if (step === 3) {
      const configErrors = {};
      if (configurationsValue && Array.isArray(configurationsValue)) {
        configurationsValue.forEach((task) => {
          if (task && Array.isArray(task.values)) {
            task.values.forEach((valueEntry) => {
              const isEmpty =
                valueEntry.value === null ||
                valueEntry.value === "" ||
                (typeof valueEntry.value === "string" &&
                  valueEntry.value.trim() === "");
              if (valueEntry.required && isEmpty) {
                valueEntry.errorText = "This field is required.";
                configErrors[valueEntry.key] = "This field is required.";
              } else {
                valueEntry.errorText = "";
              }
            });
          }
        });
      }

      // If validation errors found, don't proceed
      if (Object.keys(configErrors).length > 0) {
        setConfigValidationErrors(configErrors);
        return;
      }
    }

    // If validation errors found, don't proceed
    if (Object.keys(error).length > 0) {
      setErrors(error);
      return;
    }

    // Check if data has changed before making API call
    let shouldCallApi = true;
    let functionType = "";
    let apiData = {};

    // Determine API call parameters based on current step
    switch (step) {
      case 1:
        functionType = "stepOneToTwo";
        apiData = {
          workflow_name,
          query,
          teamId: teamId,
          selectedTask: JSON.stringify(selectedTask),
        };
        // Check if step 1 data has changed
        shouldCallApi =
          workflow_name !== initialStep1Data.workflow_name ||
          query !== initialStep1Data.query ||
          !isDeepEqual(selectedTask, initialStep1Data.selectedTask) ||
          teamId !== initialStep1Data.teamId;

        // Reset configurations when step 1 data changes
        if (shouldCallApi) {
          const { setConfigurationsData, setConfigurationsValue } =
            useWorkflowState.getState();
          setConfigurationsData(null);
          setConfigurationsValue(null);
          shopify.toast.show(
            t(
              "hang_tight_we_are_preparing_our_workers_and_assigning_tasks_to_them",
            ),
          );
        }
        break;

      case 2:
        functionType = "stepTwoToThree";
        apiData = {
          teamId: teamId,
          selectedWorkers: JSON.stringify(selectedWorkers),
        };
        // Check if step 2 data has changed
        shouldCallApi = !isDeepEqual(
          selectedWorkers,
          initialStep2Data.selectedWorkers,
        );
        // Always call API if no configurations exist
        if (!configurationsValue) {
          shouldCallApi = true;
        }
        if (shouldCallApi) {
        }
        break;

      case 3:
        functionType = "stepThreeToFour";
        apiData = {
          teamId: teamId,
          configurations: JSON.stringify(configurationsValue),
          selectedWorkers: JSON.stringify(selectedWorkers),
        };
        // Check if step 3 data has changed
        shouldCallApi = !isDeepEqual(
          configurationsValue,
          initialStep3Data.configurationsValue,
        );
        if (shouldCallApi) {
        }
        break;
      case 4:
        functionType = "createUsageCharge";
        apiData = {
          teamId: teamId,
          teamName: workflow_name,
        };
        shouldCallApi = true;
        if (shouldCallApi) {
        }
        break;

      default:
        break;
    }

    // Make API call if data has changed
    if (shouldCallApi) {
      const result = {
        functionType,
        ...apiData,
      };
      console.log(`Submitting API call for step ${step}:`, result);
      setIsLoading(true);
      submit(result, { method: "post" });
    } else {
      // Skip API call if no changes detected
      console.log(`Skipping API call for step ${step}, no changes detected`);
      setStep((prev) => prev + 1);
    }
  };

  return (
    <Box paddingBlockStart="4">
      <div className="navigation-buttons">
        <ButtonGroup>
          <Button
            loading={isLoading}
            onClick={goPrevious}
            disabled={isFirstStep}
          >
            ← {t("back")}
          </Button>
          <Button loading={isLoading} variant="primary" onClick={goNext}>
            {isLastStep ? t("finish") : `${t("next")}→`}
          </Button>
        </ButtonGroup>
      </div>
    </Box>
  );
}

/**
 * Main workflow component
 * Handles state management, step navigation, and API interactions
 */
export default function NewWorkflow() {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const submit = useSubmit();
  const actionData = useActionData();
  const shopify = useAppBridge();
  const [shopName, setShopName] = useState("");
  // Get input from location state if available
  const { input } = location.state || {};

  // Local state for UI
  const [step, setStep] = useState(1);
  const [errors, setErrors] = useState({});
  const [configValidationErrors, setConfigValidationErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Global state access
  const setQuery = useWorkflowState((state) => state.setQuery);
  const reset = useWorkflowState((state) => state.reset);
  // Memoize step details to prevent re-renders
  const stepDetails = useMemo(
    () => ({
      1: {
        title: t("workflow_creation_step_1_title"),
        description: t("workflow_creation_step_1_desc"),
      },
      2: {
        title: t("workflow_creation_step_2_title"),
        description: t("workflow_creation_step_2_desc"),
      },
      3: {
        title: t("workflow_creation_step_3_title"),
        description: t("workflow_creation_step_3_desc"),
      },
      4: {
        title: t("workflow_creation_step_4_title"),
        description: t("workflow_creation_step_4_desc"),
      },
    }),
    [],
  );

  // Initialize state from location input if available
  useEffect(() => {
    if (input) {
      reset();
      setQuery(input);
    }
  }, [input, reset, setQuery]);

  useEffect(() => {
    if (shopName) {
      trackPageView("Workflow Creation", { shop_name: shopName });
    }
  }, [shopName]);
  /**
   * Handle task data retrieval
   * @param {String} value - Query value
   */
  const handleGetTaskData = useCallback(
    (value) => {
      setIsLoading(true);
      const result = {
        functionType: "getTask",
        query: value,
      };
      submit(result, { method: "post" });
    },
    [submit],
  );

  /**
   * Render the current workflow step component
   * @param {Number} step - Current step number
   * @param {Object} errors - Validation errors
   * @param {Object} configValidationErrors - Configuration validation errors
   */
  function WorkflowStepContent({ step, errors, configValidationErrors }) {
    const components = {
      1: (
        <WorkFlowStepOne
          errors={errors}
          isLoading={isLoading}
          handleGetTaskData={(value) => handleGetTaskData(value || "")}
        />
      ),
      2: <WorkFlowStepTwo />,
      3: <WorkFlowStepThree errors={configValidationErrors} />,
      4: <WorkFlowStepFour />,
    };

    return components[step] || null;
  }

  // Process API responses
  useEffect(() => {
    // Access Zustand state setters
    const {
      setTeamId,
      setTaskData,
      setWorkersData,
      setSelectedWorkers,
      setConfigurationsValue,
      updateInitialStep1Data,
      updateInitialStep2Data,
      updateInitialStep3Data,
      setSummary,
      setSuggestedQuery,
      setWorkflowName,
    } = useWorkflowState.getState();

    if (actionData) {
      if (actionData.status === 200) {
        // Process successful API responses based on function type
        switch (actionData.data?.functionType) {
          case "initialFetch":
            const data = actionData?.data.data;
            setShopName(data.shopName);
            // No specific action for initial fetch
            break;

          case "getTask":
            // Process task data
            const firstData = actionData?.data?.data[0];
            const taskData = firstData?.tasks ?? [];
            const teamId = firstData?.team_id ?? "";
            const workflowName = firstData?.workflow_name ?? "";
            const validVariations = firstData?.valid_variations ?? [];
            if (Array.isArray(taskData) && taskData.length > 0) {
              setTeamId(teamId);
              setTaskData(taskData);
              setWorkflowName(workflowName);
            } else {
              shopify.toast.show(t("job_retry_warn"), { isError: true });
              if (
                Array.isArray(validVariations) &&
                validVariations.length > 0
              ) {
                setSuggestedQuery(validVariations);
                //   console.log("Valid variations available:", validVariations);
              }
            }
            break;
          case "stepOneToTwo":
            // Process transition from step 1 to 2
            const workersTask = actionData.data.data.tasks || [];
            if (workersTask.length === 0) {
              setIsLoading(false);
              shopify.toast.show(t("job_retry_warn"), { isError: true });
            } else {
              setWorkersData(workersTask);
              setSelectedWorkers(workersTask);
              updateInitialStep1Data();
              setStep(2);
            }
            break;

          case "stepTwoToThree":
            // Process transition from step 2 to 3
            const config = actionData.data.data.tasks || [];
            if (config.length === 0) {
              setIsLoading(false);
              shopify.toast.show(t("job_retry_warn"), { isError: true });
              return;
            }
            const allowedTypes = [
              "text",
              "boolean",
              "number",
              "textarea",
              "dropdown",
            ];

            config.forEach((task) => {
              const values = [];
              task.configurations.forEach((configuration) => {
                configuration.fields = configuration.fields.filter((field) =>
                  allowedTypes.includes(field.type),
                ); // Remove fields with disallowed types

                configuration.fields.forEach((field) => {
                  values.push({
                    key: field.key,
                    display_name: field.display_name,
                    value: field.type === "boolean" ? false : null,
                    errorText: "",
                    required: field.required,
                  });
                });
              });
              task.values = values;
            });

            setConfigurationsValue(config);

            updateInitialStep2Data();
            setStep(3);
            break;

          case "stepThreeToFour":
            // Process transition from step 3 to 4
            updateInitialStep3Data();
            const summary = actionData.data.data.tasks || [];
            setSummary(summary);
            setStep(4);
            break;

          case "createUsageCharge":
            // Process workflow completion
            reset();
            const team_id = useWorkflowState((state) => state.teamId);
            shopify.toast.show(actionData.message);
            navigate(`/app/team/${team_id}`);
            break;

          default:
            break;
        }
      } else {
        // Handle API errors
        shopify.toast.show(actionData.message || "An error occurred", {
          isError: true,
        });
      }
    }
    setIsLoading(false);
  }, [actionData, navigate]);

  return (
    <Page>
      <HeaderSection
        step={step}
        title={stepDetails[step]?.title}
        description={stepDetails[step]?.description}
      />
      <br />
      <Box paddingBlockStart="4">
        <WorkflowStepContent
          step={step}
          errors={errors}
          configValidationErrors={configValidationErrors}
        />
      </Box>

      <NavigationButtons
        step={step}
        setStep={setStep}
        setErrors={setErrors}
        submit={submit}
        setConfigValidationErrors={setConfigValidationErrors}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        shopName={shopName}
        t={t}
      />
      {isLoading && (
        <SpinnerComponent
          message={
            step === 1
              ? "hang_tight_we_are_preparing_our_workers_and_assigning_tasks_to_them"
              : "hang_tight_we_are_preparing_our_workers_and_assigning_tasks_to_them"
          }
        />
      )}
    </Page>
  );
}
