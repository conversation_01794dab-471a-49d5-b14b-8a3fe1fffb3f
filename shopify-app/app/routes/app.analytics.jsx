import {
  Page,
  Card,
  Text,
  BlockStack,
  InlineStack,
  Box,
  ProgressBar,
  Select,
  Layout,
  Badge,
  Button,
  Divider,
  EmptyState,
  SkeletonBodyText,
} from "@shopify/polaris";
import React, { useState, useCallback, useEffect } from "react";

import { <PERSON><PERSON><PERSON> } from "../components/chart/main/barChart";
import { DonutChart } from "../components/chart/main/donutChart";
import { normalize } from "../utilities/dataNormalizer";
import "../css/analytics_dashboard.css";
import { useTranslation } from "react-i18next";
import imageUrls from "../config/images";
import { useActionData, useSubmit, useNavigate } from "@remix-run/react";
import i18n from "i18next";
import { json } from "@remix-run/node";
import { loggerError, sendResponse } from "../utilities/helper";
import { authenticate } from "../config/shopify.js";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";
import { getAnalyticsData } from "../models/analytics.server.js";

export async function action({ request }) {
  const { session } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    const actions = {
      initialFetch: async () => {
        const analyticsRes = await getAnalyticsData(data);
        bodyData = {
          status: "ok",
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            analyticsData:
              analyticsRes?.status === 200 ? analyticsRes?.data : null,
          },
        };
        return bodyData;
      },
    };
    const actionFn = actions[data.functionType];
    if (!actionFn) {
      throw new Error("Unknown functionType");
    }
    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: bodyData.status || "ok",
        message: bodyData.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch team ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}

export default function AnalyticsDashboard() {
  const { t } = useTranslation();
  const [selected, setSelected] = useState("Last 30 days");
  const [isLoading, setIsLoading] = useState(false);
  const actionData = useActionData();
  const [shopName, setShopName] = useState("");
  const [data, setData] = useState(null);
  const submit = useSubmit();

  const StatCard = ({ title, value, subtitle, tone, timeType }) => (
    <div style={{ width: "32%" }}>
      <Card>
        {isLoading ? (
          <SkeletonBodyText lines={4} />
        ) : (
          <Box>
            <Text variant="headingMd" as="h3">
              {title}
            </Text>
            <Text variant="headingXl" as="p" tone={tone}>
              {value}
            </Text>
            <Text variant="bodyMd" as="p" color="subdued">
              {subtitle}
            </Text>
          </Box>
        )}
      </Card>
    </div>
  );

  const options = [
    { label: "Last 30 days", value: "Last 30 days" },
    { label: "Last 60 days", value: "Last 60 days" },
    { label: "Last 90 days", value: "Last 90 days" },
  ];

  const dashboardData = {
    statistics: {
      tasksCompleted: 247,
      activeWorkflows: 4,
      totalWorkflows: 18,
      timeSaved: "63.5h",
      timePeriod: "This month",
    },
  };

  const dummySales = [
    {
      data: {
        name: "SEO optimized blog post creation",
        key: "41",
        value: 410,
      },
    },
    {
      data: {
        name: "Product SEO Enhancement Team",
        key: "7",
        value: 105,
      },
    },
    {
      data: {
        name: "Shopify Low Stock to Sheets Crew",
        key: "2",
        value: 60,
      },
    },
    {
      data: {
        name: "Shopify Daily Analytics Crew",
        key: "0",
        value: 0,
      },
    },
    {
      data: {
        name: "Shopify Return/Exchange Assistant Crew",
        key: "0",
        value: 0,
      },
    },
  ];

  const initialFetch = useCallback(() => {
    setIsLoading(true);
    const result = {
      functionType: "initialFetch",
    };
    submit(result, { method: "post" });
  }, []);

  useEffect(() => {
    initialFetch();
  }, []);

  useEffect(() => {
    if (shopName) {
      trackPageView("Analytics", { shop_name: shopName });
    }
  }, [shopName]);

  useEffect(() => {
    if (actionData) {
      if (actionData.status === "ok") {
        switch (actionData.data?.functionType) {
          case "initialFetch":
            const data = actionData?.data?.data;
            setShopName(data.shopName);
            console.log(data?.analyticsData);
            setData(data?.analyticsData || null);
            break;
        }
      } else {
        shopify.toast.show(actionData.message, { isError: true });
      }
      setIsLoading(false);
    }
  }, [actionData]);

  return (
    <Page
      title={t("analytics_page_title")}
      subtitle={t("analytics_page_desc")}

      /*  primaryAction={
        <Box paddingBlockEnd="400">
          <Select
            label=""
            labelHidden
            options={options}
            onChange={setSelected}
            value={selected}
          />
        </Box>
      } */
    >
      {/* Stat Cards */}
      <Box paddingBlockEnd="200">
        <Card>
          <BlockStack gap="400">
            <Layout>
              <Layout.Section>
                <InlineStack gap="400" wrap>
                  <StatCard
                    title={t("total_templates")}
                    value={data?.total_templates || 0}
                    subtitle={t("templates_ready_now")}
                  />
                  <StatCard
                    title={t("total_time_saved_minutes")}
                    value={data?.total_time_saved_minutes || 0}
                    subtitle={t("time_efficiency_gained")}
                    timeType="min"
                  />
                  <StatCard
                    title={t("total_time_saved_hours")}
                    value={Math.round(data?.total_time_saved_hours || 0)}
                    timeType="hour"
                    subtitle={t("time_efficiency_gained", {
                      count: 0,
                    })}
                  />
                </InlineStack>
              </Layout.Section>
            </Layout>
          </BlockStack>
        </Card>
      </Box>
      <Box paddingBlockEnd="400">
        <InlineStack wrap={false} gap="400" blockAlign="stretch">
          <Box width="50%">
            {isLoading ? (
              <Card>
                <SkeletonBodyText lines={10} />
              </Card>
            ) : (
              data?.most_used_templates ? (
                <DonutChart
                  title={t("most_used_templates")}
                  trendMetric="7.0"
                  data={normalize(data?.most_used_templates)}
                />
              ): <Card>
              <div style={{ height: "345px" }}>
                <Text variant="headingMd">
                  {t("most_used_templates")}
                </Text>
                <EmptyState
                  heading={t("no_data_found")}
                  image={imageUrls.emptyImage}
                />
              </div>
            </Card>
            )}
          </Box>
          <Box width="50%">
            {isLoading ? (
              <Card>
                <SkeletonBodyText lines={10} />
              </Card>
            ) : !data?.top_performing_templates ? (
              <Card>
                <div style={{ height: "345px" }}>
                  <Text variant="headingMd">
                    {t("top_performing_templates")}
                  </Text>
                  <EmptyState
                    heading={t("no_data_found")}
                    image={imageUrls.emptyImage}
                  />
                </div>
              </Card>
            ) : (
              <BarChart
              title={t("top_performing_templates")}
              trendMetric="7.0"
              data={normalize(data?.top_performing_templates)}
            />
            )}
          </Box>
        </InlineStack>
      </Box>
      {/* Charts
      
       <Box paddingBlockEnd="400">
        <InlineStack wrap={false} gap="400" blockAlign="stretch">
          <Box width="50%">
            <DonutChart
              title="Most Used Workers"
              trendMetric="7.0"
              data={normalize(dummySales)}
            />
          </Box>
          <Box width="50%">
            <BarChart
              title="Top Workflow Performance"
              data={normalize(dummySales)}
            />
          </Box>
        </InlineStack>
      </Box>
      
      */}

      {/* Business Impact Metrics */}
      <Box>
        <Card padding={0}>
          <Box padding="400">
            <BlockStack gap="400">
              <Text as="h2" variant="headingMd">
                {t("job_performance")}
              </Text>
              <InlineStack wrap={false} gap="400" blockAlign="stretch">
                <Box width="50%">
                  <Card>
                    {isLoading ? (
                      <SkeletonBodyText lines={4} />
                    ) : (
                      <Box>
                        <BlockStack gap="200">
                          <Text as="h3" variant="headingSm">
                            {t("success_rate")}
                          </Text>
                          <Text as="p" variant="headingXl">
                            {`${data?.success_rate || 0} %`}
                          </Text>
                          <ProgressBar
                            progress={data?.success_rate || 0}
                            tone="success"
                          />
                        </BlockStack>
                      </Box>
                    )}
                  </Card>

                  {/*  <Card>
                    <Box>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingSm">
                          Revenue Influenced
                        </Text>
                        <Text as="p" variant="headingXl">
                          $24,850
                        </Text>
                        <Text as="p" variant="bodyMd" color="success">
                          +15.3% from previous period
                        </Text>
                      </BlockStack>
                    </Box>
                  </Card> 
                  */}
                </Box>
                <Box width="50%">
                  <Card>
                    {isLoading ? (
                      <SkeletonBodyText lines={4} />
                    ) : (
                      <Box>
                        <BlockStack gap="200">
                          <Text as="h3" variant="headingSm">
                            {t("failure_rate")}
                          </Text>
                          <Text as="p" variant="headingXl">
                            {`${data?.failure_rate || 0} %`}
                          </Text>
                          <ProgressBar
                            progress={data?.failure_rate || 0}
                            tone="critical"
                          />
                        </BlockStack>
                      </Box>
                    )}
                  </Card>
                </Box>
              </InlineStack>
            </BlockStack>
          </Box>
        </Card>
        <br />
      </Box>
    </Page>
  );
}
