import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Tabs,
  Text,
  Layout,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ButtonGroup,
  Resource<PERSON><PERSON>,
  <PERSON>,
  InlineStack,
  Icon,
} from "@shopify/polaris";

const WorkflowDetailsPage = () => {
  // Sample data object
  const workflowData = {
    name: "Weekly Content Calendar",
    status: "Active",
    lastRun: "April 1, 2025",
    nextRun: "April 8, 2025",
    summary:
      "This workflow generates weekly content for your store, including blog posts, social media content, and email marketing. It runs every Monday at 9:00 AM to prepare content for the upcoming week.",
    workers: ["Content Writer", "SEO Specialist", "Social Media Specialist"],
    schedule: {
      frequency: "Weekly",
      day: "Monday",
      time: "9:00 AM EST",
    },
    performance: {
      timesRun: 14,
      successRate: "98%",
      avgDuration: "12 minutes",
    },
    tasks: [
      {
        id: 1,
        name: "Generate blog content",
        assignedTo: "Content Writer",
        estimatedTime: "~5 minutes",
        color: "#76C270",
      },
      {
        id: 2,
        name: "Create social media posts",
        assignedTo: "Social Media Specialist",
        estimatedTime: "~4 minutes",
        color: "#5C6AC4",
      },
      {
        id: 3,
        name: "Draft weekly newsletter",
        assignedTo: "Content Writer, SEO Specialist",
        estimatedTime: "~3 minutes",
        color: "#9C6ADE",
      },
    ],
  };

  // Tabs setup
  const tabs = [
    {
      id: "overview",
      content: "Overview",
      accessibilityLabel: "Overview",
      panelID: "overview-panel",
    },
    {
      id: "tasks",
      content: "Tasks",
      accessibilityLabel: "Tasks",
      panelID: "tasks-panel",
    },
    {
      id: "history",
      content: "History",
      accessibilityLabel: "History",
      panelID: "history-panel",
    },
    {
      id: "settings",
      content: "Settings",
      accessibilityLabel: "Settings",
      panelID: "settings-panel",
    },
  ];

  const breadcrumbs = [
    { content: "Home", url: "#" },
    { content: "Workflows", url: "#" },
  ];

  return (
    <AppProvider i18n={{}}>
      <Page
        title={workflowData.name}
        breadcrumbs={breadcrumbs}
        titleMetadata={<Badge status="success">{workflowData.status}</Badge>}
        secondaryActions={[
          {
            content: "Edit",
            accessibilityLabel: "Edit workflow",
            onAction: () => console.log("Edit clicked"),
          },
          {
            content: "Duplicate",
            accessibilityLabel: "Duplicate workflow",
            onAction: () => console.log("Duplicate clicked"),
          },
        ]}
      >
        <Text variant="bodySm" color="subdued">
          Last run: {workflowData.lastRun} • Next run: {workflowData.nextRun}
        </Text>

        <div style={{ marginTop: "16px", marginBottom: "24px" }}>
          <Tabs tabs={tabs} selected={0} />
        </div>

        <Layout>
          <Layout.Section>
            <Card>
              <Card>
                <Text>
                  <Text variant="headingMd">Workflow Summary</Text>
                  <Text>{workflowData.summary}</Text>
                  <Text>Workers: {workflowData.workers.join(", ")}</Text>
                </Text>
              </Card>
            </Card>
          </Layout.Section>

          <Layout.Section oneHalf>
            <Card>
              <Card>
                <Text variant="headingMd">Schedule</Text>
              </Card>
              <Card>
                <InlineStack vertical spacing="loose">
                  <InlineStack distribution="equalSpacing">
                    <Text>Frequency:</Text>
                    <Text>{workflowData.schedule.frequency}</Text>
                  </InlineStack>
                  <InlineStack distribution="equalSpacing">
                    <Text>Runs on:</Text>
                    <Text>{workflowData.schedule.day}</Text>
                  </InlineStack>
                  <InlineStack distribution="equalSpacing">
                    <Text>Time:</Text>
                    <Text>{workflowData.schedule.time}</Text>
                  </InlineStack>
                </InlineStack>
              </Card>
              <Card>
                <Button>Edit Schedule</Button>
              </Card>
            </Card>
          </Layout.Section>

          <Layout.Section oneHalf>
            <Card>
              <Card>
                <Text variant="headingMd">Performance</Text>
              </Card>
              <Card>
                <InlineStack vertical spacing="loose">
                  <InlineStack distribution="equalSpacing">
                    <Text>Times run:</Text>
                    <Text>{workflowData.performance.timesRun}</Text>
                  </InlineStack>
                  <InlineStack distribution="equalSpacing">
                    <Text>Success rate:</Text>
                    <Text>{workflowData.performance.successRate}</Text>
                  </InlineStack>
                  <InlineStack distribution="equalSpacing">
                    <Text>Avg. duration:</Text>
                    <Text>{workflowData.performance.avgDuration}</Text>
                  </InlineStack>
                </InlineStack>
              </Card>
              <Card>
                <Button>View Analytics</Button>
              </Card>
            </Card>
          </Layout.Section>

          <Layout.Section>
            <Card>
              <Card>
                <Text variant="headingMd">
                  Tasks ({workflowData.tasks.length})
                </Text>
              </Card>
              {workflowData.tasks.map((task) => (
                <Card key={task.id}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <span
                        style={{
                          width: "4px",
                          height: "20px",
                          backgroundColor: task.color,
                          marginRight: "16px",
                          borderRadius: "2px",
                        }}
                      ></span>
                      <div>
                        <Text variant="bodyMd">
                          <strong>
                            {task.id}. {task.name}
                          </strong>
                        </Text>
                        <Text variant="bodySm" color="subdued">
                          {task.assignedTo}
                        </Text>
                      </div>
                    </div>
                    <Text>{task.estimatedTime}</Text>
                  </div>
                </Card>
              ))}
              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  padding: "16px",
                }}
              >
                <Button primary>Run Now</Button>
              </div>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    </AppProvider>
  );
};

export default WorkflowDetailsPage;
