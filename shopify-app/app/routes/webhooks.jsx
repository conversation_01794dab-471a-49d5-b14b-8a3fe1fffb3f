import { authenticate } from "../config/shopify.js";
import db from "../config/db.js";
import { loggerInfo, loggerError } from "../utilities/helper";
//import { WebhookModel } from "../models/webhook.model.js";
import { sendSlackWebhook } from "../utilities/slack_webhook"

export const action = async ({ request }) => {
  const { topic, shop, session, admin, payload } =
    await authenticate.webhook(request);
  if (!admin) {
    // The admin context isn't returned if the webhook fired after a shop was uninstalled.
    throw new Response();
  }
  /**
   * Webhooks are stored to webhook table.
   * use as reference https://shopify.dev/docs/api/webhooks?reference=toml
   */
  // await WebhookModel.create({ shop, topic, payload });

  switch (topic) {
    case "APP_UNINSTALLED":
      if (session) {
        loggerInfo(
          `Webhook triggered for the topic ${topic} for the store ${shop}`,
        );
        loggerInfo(`Session deleted for the store ${shop}`);
        await db.session.deleteMany({ where: { shop } });
        const input = {
          shop: shop,
        }
        sendSlackWebhook(input, "uninstall");
      }
    case "CUSTOMERS_DATA_REQUEST":
      return new Response("Customers data request received", { status: 200 });
    case "CUSTOMERS_REDACT":
      return new Response("Customers data request received", { status: 200 });
    case "SHOP_REDACT":
      return new Response("Shop data request received", { status: 200 });
    default:
      throw new Response("Unhandled webhook topic", { status: 404 });
  }
  throw new Response({}, { status: 200 });
};
