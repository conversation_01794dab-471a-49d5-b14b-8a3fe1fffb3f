import React, { useState, useCallback, useEffect, useRef } from "react";
import {
  Page,
  Card,
  Tabs,
  Text,
  Layout,
  Badge,
  Button,
  InlineStack,
  Box,
  Divider,
  EmptyState,
  ResourceList,
  ResourceItem,
} from "@shopify/polaris";
import i18n from "i18next";
import { json } from "@remix-run/node";
import { useTranslation } from "react-i18next";
import { useActionData, useSubmit, useNavigate } from "@remix-run/react";
import { loggerError, sendResponse } from "../utilities/helper";
import { authenticate } from "../config/shopify.js";
import { getTeamDetailsData, runTeamFlow } from "../models/team.server.js";
import imageUrls from "../config/images";
import { FieldRenderer } from "../components/config.jsx";
import "../css/team.css";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";
import { SelectionSection } from "../components/selectionSection.jsx";
import { KeyValueInput } from "../components/KeyValueInput.jsx";
import useRunWorkflowState from "../contexts/state.js";

export async function action({ request, params }) {
  const { id } = params;
  const { session, admin, billing } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    data.id = id;
    const actions = {
      runTeam: () => runTeamFlow(data, admin, billing),
      initialFetch: async () => {
        const teamData = await getTeamDetailsData(data);
        bodyData = {
          status: 200,
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            teamData: teamData.status === 200 ? teamData.data : null,
          },
        };
        return bodyData;
      },
    };
    const actionFn = actions[data.functionType];
    if (!actionFn) {
      throw new Error("Unknown functionType");
    }
    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: bodyData?.status,
        message: bodyData?.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch team details ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}

const TeamDetailsPage = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const submit = useSubmit();
  const actionData = useActionData();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [teamData, setTeamData] = useState(null);
  const [shopName, setShopName] = useState("");
  const formValuesRef = useRef({});
  const formHasErrorsRef = useRef(false);
  const [itemsData, setItemsData] = useState([]);
  const [storeInput, setStoreInput] = useState(null);

  // Tabs setup
  const tabs = [
    {
      id: "overview",
      content: t("overview"),
      accessibilityLabel: "Overview",
      panelID: "overview-panel",
    },
    {
      id: "store_workers",
      content: t("store_workers"),
      accessibilityLabel: "store_workers",
      panelID: "store_workers-panel",
    },
    {
      id: "specifications",
      content: t("specifications"),
      accessibilityLabel: "specifications",
      panelID: "specifications-panel",
    },
  ];

  const initialFetch = useCallback(() => {
    setIsLoading(true);
    const result = {
      functionType: "initialFetch",
    };
    submit(result, { method: "post" });
  }, []);

  useEffect(() => {
    initialFetch();
  }, []);
  

  useEffect(() => {
    if (actionData) {
      if (actionData.status === 200 || actionData.status === "ok") {
        switch (actionData.data?.functionType) {
          case "initialFetch":
            const data = actionData?.data.data;
            setShopName(data.shopName);
            setTeamData(data?.teamData || null);
            setItemsData(data?.teamData?.itemData || []);
            setStoreInput(data?.teamData?.store_data || null);

            break;
          case "runTeam":
            shopify.toast.show(actionData.message);
            break;
          default:
            break;
        }
      } else {
        shopify.toast.show(actionData.message, { isError: true });
      }
      setIsLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    if (shopName) {
      trackPageView("Teams Detail", { shop_name: shopName });
    }
  }, [shopName]);

  // Validate form values and return if there are errors
  const validateFormValues = () => {
    if (!teamData?.current_configuration) return false;
    const updatedValues = { ...formValuesRef.current };
    let hasErrors = false;
    teamData.current_configuration.forEach((config) => {
      config.fields.forEach((field) => {
        const fieldState = updatedValues[field.key] || {
          value: "",
          errorText: "",
        };
        const value = fieldState.value;
        if (field.required && (value === "" || value === null)) {
          updatedValues[field.key] = {
            ...fieldState,
            errorText: t("this_field_is_required"),
          };
          hasErrors = true;
        } else {
          updatedValues[field.key] = {
            ...fieldState,
            errorText: "",
          };
        }
      });
    });

    formValuesRef.current = updatedValues;
    formHasErrorsRef.current = hasErrors;
    return !hasErrors;
  };

  // Handle Run Task action
  const handleRunTask = async () => {
    const additional_fields = useRunWorkflowState.getState().additional_fields;
    const selectedItems = useRunWorkflowState.getState().selectedItems;

    trackButtonClick("Run Task", "Team Detail", {
      shop_name: shopName,
    });

    const isValid = validateFormValues();
    if (!isValid) {
      if (selectedTab !== 2) {
        setSelectedTab(2);
      }
      const forceUpdateEvent = new CustomEvent("force-form-update");
      window.dispatchEvent(forceUpdateEvent);
      return;
    }
    if (!selectedItems || selectedItems.length === 0) {
      shopify.toast.show(t("please_select_any_store_input"), { isError: true });
      return;
    }

    const simplifiedValues = Object.fromEntries(
      Object.entries(formValuesRef.current).map(([key, { value }]) => [
        key,
        value,
      ]),
    );

    const value = {
      team_id: teamData?.team_id,
      configurations: {
        input_data: selectedItems,
      },
      output_structure: teamData.output_structure,
      ...simplifiedValues,
      additional_inputs: additional_fields,
    };

    const result = {
      functionType: "runTeam",
      value: JSON.stringify(value),
    };
    //console.log(result);
    submit(result, { method: "post" });
  };

  function OverviewTab() {
    return (
      <Layout>
        <Layout.Section>
          <Card>
            <Card>
              <Text>
                <Text variant="headingMd">{t("assigned_workers")}</Text>
                <div className="top-space-10" />
                {!teamData?.summary || teamData?.summary?.length === 0 ? (
                  <EmptyState
                    heading={t("no_workers_available")}
                    image={imageUrls.emptyImage}
                  />
                ) : (
                  teamData.summary.map((role, index) => (
                    <Box paddingBlockEnd="4" key={index}>
                      <div className="top-space-10" />
                      <Text variant="headingMd">{role.name}</Text>
                      <Text variant="bodyMd" color="subdued">
                        {role.description}
                      </Text>
                    </Box>
                  ))
                )}
              </Text>
            </Card>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <Text variant="headingMd">
              {t("team_history")}
              {teamData?.history?.length
                ? ` (${teamData.latest_job_runs.length} ${t("tasks")})`
                : ""}
            </Text>
            <br />
            {!teamData?.latest_job_runs ||
            teamData?.latest_job_runs?.length === 0 ? (
              <EmptyState
                heading={t("no_tasks_generated_yet")}
                image={imageUrls.emptyImage}
              />
            ) : (
              teamData?.latest_job_runs?.map((history) => (
                <Box className="margin-bottom-10" key={history.job_id}>
                  <Card key={history.job_id}>
                    <InlineStack align="space-between" gap={400}>
                      <Text variant="bodyMd">{history.job_title}</Text>
                      <Box>
                        <InlineStack align="end" gap={400}>
                          <Badge>
                            <Text>{t(history?.job_status)}</Text>
                          </Badge>
                          <Button
                            loading={isLoading}
                            onClick={() => {
                              setIsLoading(true);
                              navigate(`/app/job-result/${history.job_id}`);
                            }}
                            variant="primary"
                          >
                            {t("view")}
                          </Button>
                        </InlineStack>
                      </Box>
                    </InlineStack>
                  </Card>
                </Box>
              ))
            )}
          </Card>
        </Layout.Section>
      </Layout>
    );
  }

  function WorkersTab() {
    return (
      <Card>
        <Text variant="headingMd">{t("store_workers")}</Text>
        <br />
        {!teamData?.agents || teamData?.agents?.length === 0 ? (
          <EmptyState
            heading={t("no_workers_available")}
            image={imageUrls.emptyImage}
          />
        ) : (
          teamData?.agents?.map((worker) => (
            <div className="margin-bottom-10" key={worker.uuid}>
              <Card key={worker.uuid}>
                <InlineStack align="space-between">
                  <Text variant="headingMd">{worker.name}</Text>
                  <Badge>
                    <Text>{worker.role}</Text>
                  </Badge>
                </InlineStack>
                <Text>{worker.goal}</Text>
              </Card>
            </div>
          ))
        )}
      </Card>
    );
  }

  function ConfigurationTab() {
    const [localFormValues, setLocalFormValues] = useState({});
    // const allowedTypes = ["text", "boolean", "number", "textarea", "dropdown"];
    useEffect(() => {
      if (teamData?.current_configuration) {
        const initialValues = {};
        teamData.current_configuration.forEach((config) => {
          config.fields.forEach((field) => {
            initialValues[field.key] = {
              value: teamData.current_key_values?.[field.key] ?? "",
              errorText: "",
              required: field.required,
            };
          });
        });
        setLocalFormValues(initialValues);
        formValuesRef.current = initialValues;
      }
    }, []);

    const handleValueChange = (key, newValue) => {
      setLocalFormValues((prev) => {
        const updated = {
          ...prev,
          [key]: {
            ...prev[key],
            value: newValue,
            errorText: "",
          },
        };
        formValuesRef.current = updated;
        return updated;
      });
    };
    useEffect(() => {
      const handleForceUpdate = () => {
        setLocalFormValues({ ...formValuesRef.current });
      };
      window.addEventListener("force-form-update", handleForceUpdate);
      return () => {
        window.removeEventListener("force-form-update", handleForceUpdate);
      };
    }, []);

    useEffect(() => {
      if (formHasErrorsRef.current) {
        setLocalFormValues({ ...formValuesRef.current });
        formHasErrorsRef.current = false;
      }
    }, [selectedTab]);

    return (
      <>
        <Card>
          <Text variant="headingMd">{t("job_specifications")}</Text>
          <br />
          {isLoading || teamData?.current_configuration?.length === 0 ? (
            <EmptyState
              heading={t("no_specifications_available")}
              image={imageUrls.emptyImage}
            />
          ) : (
            teamData?.current_configuration?.map((config) => (
              <div key={config.name} className="margin-bottom-32">
                <Text variant="headingMd" as="h3">
                  {config.name}
                </Text>
                <div className="desc-text">{config.description}</div>
                {config.fields.map((field) => {
                  const fieldState = localFormValues?.[field.key] || {
                    value: "",
                    errorText: "",
                  };
                  return (
                    <div key={field.key} className="margin-bottom-16">
                      <FieldRenderer
                        field={field}
                        value={fieldState.value}
                        errorText={fieldState.errorText}
                        onChange={handleValueChange}
                      />
                    </div>
                  );
                })}
                <Divider />
              </div>
            ))
          )}
          {!isLoading && (
            <>
              <KeyValueInput title={t("additional_inputs")} t={t} />
              <div className="margin-bottom-10" />
              <br />
              <Divider />
            </>
          )}
        </Card>
        <>
          <br />
          {!isLoading && (
            <div className="product-selection-section">
              <SelectionSection
                itemsData={itemsData}
                itemType={storeInput?.store_data_type?.category}
                selectionType={storeInput?.store_data_type.type}
              />
            </div>
          )}
        </>
      </>
    );
  }
  const handleGoBack = () => {
    navigate("/app/teams", { replace: true });
  };

  return (
    <Page
      title={isLoading ? t("loading") : teamData?.name}
      titleMetadata={
        !isLoading ? <Badge tone="success">{t(teamData?.status)}</Badge> : null
      }
      backAction={{ content: "Products", onAction: handleGoBack }}
      primaryAction={{
        loading: isLoading,
        // disabled: teamData?.status !== "completed" || isLoading,
        content: t("start_job"),
        onAction: handleRunTask,
      }}
    >
      <div style={{ marginLeft: "10px" }}>
        <Text>
          {!isLoading
            ? teamData?.created_at
              ? `${t("created_at")}: ${new Date(teamData.created_at).toLocaleDateString("en-GB").replace(/\//g, "-")}`
              : ""
            : t("loading")}
        </Text>
      </div>
      <div className="margin-bottom-top-5">
        <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab} />
      </div>

      {selectedTab === 0 && <OverviewTab />}
      {selectedTab === 1 && <WorkersTab />}
      {selectedTab === 2 && <ConfigurationTab />}
      <br />
    </Page>
  );
};

export default TeamDetailsPage;
