import React, { useState } from "react";
import { <PERSON>, Tabs, Box, Badge, Banner, Icon } from "@shopify/polaris";
import { RefreshIcon } from "@shopify/polaris-icons";
import i18n from "i18next";
import { json } from "@remix-run/node";
import { useTranslation } from "react-i18next";
import { useNavigate } from "@remix-run/react";
import { loggerError, sendResponse } from "../utilities/helper";
import { authenticate } from "../config/shopify.js";
import {
  getTemplateDetailsData,
  runTemplateFlow,
  saveTemplateConfiguration,
  runAndSaveTemplate,
} from "../models/templates.server.js";
import "../css/team.css";
import { SpinnerComponent } from "../components/SpinnerComponent.jsx";
import { TemplateHeader } from "../components/template/TemplateHeader.jsx";
import { OverviewTab } from "../components/template/OverviewTab.jsx";
import { WorkersTab } from "../components/template/WorkersTab.jsx";
import { ConfigurationTab } from "../components/template/ConfigurationTab.jsx";
import { RecentJobsTab } from "../components/template/RecentJobs.jsx";
import { useTemplateData } from "../hooks/useTemplateData.js";
import { useTemplateActions } from "../hooks/useTemplateActions.js";
import { SaveRunModal } from "../components/saveRunModal.jsx";
import { EmailModal } from "../components/emailModal.jsx";
import { saveSettings } from "../models/settings.server.js";
import { sendEmailFlag, integrationTempId } from "../config/config.js";
export async function action({ request, params }) {
  const { id } = params;
  const { session } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    if (data.functionType !== "updateEmailEnable") {
      data.id = id;
    }
    const actions = {
      runTemplate: () => runTemplateFlow(data, session),
      saveRunTemplate: () => runAndSaveTemplate(data, session),
      saveTemplateConfiguration: () => saveTemplateConfiguration(data),
      updateEmailEnable: () => saveSettings(data),
      initialFetch: async () => {
        const templateData = await getTemplateDetailsData(data);
        bodyData = {
          status: 200,
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            templateData:
              templateData.status === 200 ? templateData.data : null,
          },
        };
        return bodyData;
      },
    };
    const actionFn = actions[data.functionType];
    if (!actionFn) {
      throw new Error("Unknown functionType");
    }
    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: bodyData?.status,
        message: bodyData?.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch Template details ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}

const TemplateDetailsPage = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState(0);

  // Use custom hooks for data and actions
  const {
    isLoading,
    setIsLoading,
    templateData,
    shopName,
    itemsData,
    storeInput,
    runLoader,
    setRunLoader,
    initialFetch,
  } = useTemplateData();

  const { handleRunTask, handleSave, handleEmailEnable } = useTemplateActions(
    templateData,
    shopName,
    setSelectedTab,
    setRunLoader,
    setIsLoading,
    storeInput,
  );

  // Tabs setup
  const tabs = [
    {
      id: "overview",
      content: t("overview"),
      accessibilityLabel: "Overview",
      panelID: "overview-panel",
    },
    {
      id: "workers",
      content: t("workers"),
      accessibilityLabel: "workers",
      panelID: "workers-panel",
    },
    {
      id: "specifications",
      content: t("specifications"),
      accessibilityLabel: "specifications",
      panelID: "specifications-panel",
    },
    {
      id: "recent_jobs",
      content: t("recent_jobs"),
      accessibilityLabel: "recent_jobs",
      panelID: "recent_jobs-panel",
    },
  ];

  const handleGoBack = () => {
    navigate("/app/templates", { replace: true });
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    initialFetch();
  };

  return (
    <Page
      title={isLoading ? t("loading") : templateData?.title}
      titleMetadata={
        !isLoading || templateData?.category ? (
          <Badge tone="success">{t(templateData?.category)}</Badge>
        ) : null
      }
      backAction={{ content: "Products", onAction: handleGoBack }}
      primaryAction={{
        loading: isLoading,
        content: t("start_job"),
        disabled:
          integrationTempId.includes(templateData?.uuid) &&
          templateData?.integrationData?.length < 2,
        onAction: () => {
          setSelectedTab(2);
          if (sendEmailFlag) {
            if (templateData?.settingsData?.email_enable) {
              shopify.modal.show("save-run-modal");
            } else {
              shopify.modal.show("email-modal");
            }
          } else {
            shopify.modal.show("save-run-modal");
          }
        },
      }}
      secondaryActions={[
        {
          icon: <Icon source={RefreshIcon} />,
          onAction: handleRefresh,
          disabled: isLoading,
        },
      ]}
    >
      <TemplateHeader
        isLoading={isLoading}
        templateData={templateData}
        shopName={shopName}
      />
      {integrationTempId.includes(templateData?.uuid) &&
        templateData?.integrationData?.length < 2 && (
          <div className="margin-bottom-top-5">
            <br />
            <Banner
              title={t(
                "to_use_this_template_please_integrate_the_required_tool_first",
              )}
              tone="warning"
              action={{
                content: t("integrate"),
                onAction: () => navigate("/app/integrations"),
              }}
            >
              <p>{t("run_temp_warning_desc")}</p>
            </Banner>
          </div>
        )}
      <div className="margin-bottom-top-5">
        <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab} />
      </div>

      {selectedTab === 0 && (
        <OverviewTab
          templateData={templateData}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
        />
      )}
      {selectedTab === 1 && <WorkersTab templateData={templateData} />}
      {selectedTab === 2 && (
        <ConfigurationTab
          templateData={templateData}
          isLoading={isLoading}
          itemsData={itemsData}
          onSave={handleSave}
        />
      )}
      {selectedTab === 3 && (
        <RecentJobsTab
          templateData={templateData}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
        />
      )}
      <Box paddingBlockStart="4" />
      {runLoader || (isLoading && <SpinnerComponent />)}
      <br />
      <SaveRunModal
        id="save-run-modal"
        title={t("save_and_run")}
        desc={t("save_and_run_desc")}
        onClose={() => shopify.modal.hide("save-run-modal")}
        onConfirm={(runType) => {
          handleRunTask(runType);
          shopify.modal.hide("save-run-modal");
        }}
      />
      <EmailModal
        id="email-modal"
        title={t("enable_email")}
        desc={t("save_and_run_desc")}
        onClose={() => shopify.modal.hide("email-modal")}
        onConfirm={(value) => {
          handleEmailEnable(value);
          shopify.modal.hide("email-modal");
        }}
      />
    </Page>
  );
};

export default TemplateDetailsPage;
