import React, { useState, useCallback, useEffect } from "react";
import {
  Page,
  Card,
  Badge,
  Button,
  Select,
  TextField,
  ButtonGroup,
  Text,
  Grid,
  EmptyState,
  Pagination,
  SkeletonBodyText,
  InlineStack,
  InlineGrid,
} from "@shopify/polaris";
import i18n from "i18next";
import { json } from "@remix-run/node";
import { useActionData, useSubmit, useNavigate } from "@remix-run/react";
import { loggerError, sendResponse } from "../utilities/helper.js";
import { useTranslation } from "react-i18next";
import imageUrls from "../config/images.js";
import { authenticate } from "../config/shopify.js";
import { fetchTeamResult } from "../models/job_result.server.js";
import "../css/team.css";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";
import { showPromptFlow } from "../config/config.js";

export async function action({ request }) {
  const { session, admin } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    const actions = {
      initialFetch: async () => {
        const teamList = await fetchTeamResult(data);
        bodyData = {
          status: "ok",
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            teamList: teamList.status === 200 ? teamList.data : [],
          },
        };
        return bodyData;
      },
    };
    const actionFn = actions[data.functionType];
    if (!actionFn) {
      throw new Error("Unknown functionType");
    }
    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: bodyData.status || "ok",
        message: bodyData.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch team ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}
const teamDonePage = () => {
  const SORT_OPTIONS = [
    { label: "Latest", value: "latest" },
    { label: "Oldest", value: "oldest" },
    { label: "Ascending", value: "asc" },
    { label: "Descending", value: "desc" },
  ];

  const TYPE_OPTIONS = [
    { label: "All Types", value: "all" },
    { label: "Article", value: "article" },
    { label: "Collection", value: "collection" },
    { label: "Blog", value: "blog" },
  ];

  const STATUS_OPTIONS = [
    { label: "All Status", value: "all" },
    { label: "Pending", value: "pending" },
    { label: "Failed", value: "failed" },
    { label: "Completed", value: "completed" },
    { label: "Active", value: "active" },
  ];

  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchValue, setSearchValue] = useState("");
  const [sortValue, setSortValue] = useState(SORT_OPTIONS[0].value);
  const [typeValue, setTypeValue] = useState(TYPE_OPTIONS[0].value);
  const [statusValue, setStatusValue] = useState(STATUS_OPTIONS[0].value);
  const [filterValue, setFilterValue] = useState("");
  const navigate = useNavigate();
  const submit = useSubmit();
  const actionData = useActionData();
  const [isLoading, setIsLoading] = useState(true);
  const [shopName, setShopName] = useState("");
  const [teamList, setTeamList] = useState([]);

  const initialFetch = useCallback(() => {
    setIsLoading(true);
    setTeamList([]);
    const result = {
      functionType: "initialFetch",
      statusValue,
      typeValue,
      sortValue,
    };
    submit(result, { method: "post" });
  }, [statusValue, typeValue, sortValue]);

  useEffect(() => {
    if (actionData) {
      if (actionData.status === "ok") {
        switch (actionData.data?.functionType) {
          case "initialFetch":
            const data = actionData?.data?.data;
            setShopName(data.shopName);
            setTeamList(data?.teamList || []);
            break;
        }
      } else {
        shopify.toast.show(actionData.message, { isError: true });
      }
      setIsLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    initialFetch();
  }, []);

  useEffect(() => {
    if (shopName) {
      trackPageView("Teams list", { shop_name: shopName });
    }
  }, [shopName]);
  // Group teams by category
  const groupedTeams = teamList.reduce((acc, team) => {
    if (!acc[team.category]) {
      acc[team.category] = [];
    }
    acc[team.category].push(team);
    return acc;
  }, {});

  const handleSortChange = (value) => setSortValue(value);
  const handleFilterChange = (value) => setFilterValue(value);
  const handleSearchChange = (value) => setSearchValue(value);

  const handleNextPage = () => {
    if (currentPage < 2) setCurrentPage(currentPage + 1);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const renderTeamCard = (team) => {
    return (
      <Grid.Cell
        columnSpan={{ xs: 6, sm: 6, md: 6, lg: 6, xl: 6 }}
        key={team.job_id}
      >
        <Card>
          <div className="team-card">
            <InlineStack align="space-between">
              <Text variant="headingMd">{team.title}</Text>
              {team.status && (
                <Badge
                  tone={
                    team.status === "success" || team.status === "active"
                      ? "success"
                      : null
                  }
                >
                  {t(team.status)}
                </Badge>
              )}
            </InlineStack>
            <div className="top-space-10" />
            <Text as="p" variant="bodyMd" color="subdued">
              {team.description}
            </Text>
            <div className="top-margin-auto">
              <InlineStack align="space-between">
                {/* <Button onClick={() => {}}>Assign Task</Button> */}
                <Button
                  disabled={isLoading}
                  onClick={() => {
                    setIsLoading(true);
                    navigate(
                      `/app/job-result/${team?.result_id || team?.job_id}`,
                    );
                  }}
                  variant="primary"
                >
                  {t("view")}
                </Button>
              </InlineStack>
            </div>
          </div>
        </Card>
      </Grid.Cell>
    );
  };

  return (
    <Page
      title={t("teams_page_title")}
      subtitle={t("teams_page_desc")}
      primaryAction={
        showPromptFlow && (
          <Button
            onClick={() => {
              trackButtonClick("Create Workers flow", "Teams list", {
                shop_name: shopName,
              });
              navigate(`/app/create/workers`);
            }}
            variant="primary"
          >
            {t("create_new_store_job")}
          </Button>
        )
      }
    >
      <Card>
        <InlineGrid columns={{ xs: 1, sm: 2, md: 4 }} gap="400">
          <Select
            label="Sort by"
            labelHidden
            options={SORT_OPTIONS}
            value={sortValue}
            onChange={(value) => setSortValue(value)}
          />

          <Select
            label="Type"
            labelHidden
            options={TYPE_OPTIONS}
            value={typeValue}
            onChange={(value) => setTypeValue(value)}
          />

          <Select
            label="Status"
            labelHidden
            options={STATUS_OPTIONS}
            value={statusValue}
            onChange={(value) => setStatusValue(value)}
          />
          <InlineStack align="end" blockAlign="center" gap={400}>
            <Button
              onClick={() => {
                setIsLoading(true);
                setSortValue(SORT_OPTIONS[0].value);
                setTypeValue(TYPE_OPTIONS[0].value);
                setStatusValue(STATUS_OPTIONS[0].value);
                setTeamList([]);
                setTimeout(() => {
                  initialFetch();
                }, 300);
              }}
            >
              {t("reset")}
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                setIsLoading(true);
                setTeamList([]);
                initialFetch();
              }}
            >
              {t("search")}
            </Button>
          </InlineStack>
        </InlineGrid>
      </Card>

      <br />
      {/* 
  <Card>
          <div style={{ display: "flex", gap: "12px" }}>
            <div style={{ flex: 1 }}>
              <Select
                label="Filter by capability"
                labelHidden
                options={[
                  { label: "Filter by capability", value: "" },
                  { label: "Content Creation", value: "content" },
                  { label: "SEO", value: "seo" },
                  { label: "Social Media", value: "social" },
                  { label: "Data Analysis", value: "data" },
                ]}
                value={filterValue}
                onChange={handleFilterChange}
              />
            </div>
            <div style={{ flex: 1 }}>
              <Select
                label="Sort by"
                labelHidden
                options={[
                  { label: "Sort by: Most used", value: "most_used" },
                  { label: "Sort by: Recently used", value: "recent" },
                  { label: "Sort by: Alphabetical", value: "alpha" },
                ]}
                value={sortValue}
                onChange={handleSortChange}
              />
            </div>
            <div style={{ flex: 1 }}>
              <TextField
                label="Search"
                labelHidden
                value={searchValue}
                onChange={handleSearchChange}
                placeholder="Search teams"
              />
            </div>
          </div>
        </Card>
*/}
      {isLoading && teamList.length === 0 ? (
        <InlineGrid columns={3} gap="400">
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
        </InlineGrid>
      ) : teamList.length === 0 ? (
        <Card>
          <EmptyState
            heading={t("no_teams_available")}
            image={imageUrls.emptyImage}
          />
        </Card>
      ) : (
        <Grid>{teamList.map((team) => renderTeamCard(team))}</Grid>
      )}
      <div>
        {/*<Pagination
          label={`Page ${currentPage} of 2`}
          hasPrevious={currentPage > 1}
          hasNext={currentPage < 2}
          onPrevious={handlePrevPage}
          onNext={handleNextPage}
        />
        */}
      </div>
      <div style={{ marginTop: "15px" }} />
    </Page>
  );
};

export default teamDonePage;
