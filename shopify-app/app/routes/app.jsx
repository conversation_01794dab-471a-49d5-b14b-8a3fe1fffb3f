import { json } from "@remix-run/node";
import { Outlet, useLoaderData, useRouteError } from "@remix-run/react";
import { boundary } from "@shopify/shopify-app-remix/server";
import { AppProvider } from "@shopify/shopify-app-remix/react";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";
import { authenticate } from "../config/shopify.js";
import { useNavigation } from "@remix-run/react";
import Nav from "../components/nav/index.jsx";
import ChartProvider from "../components/shared/chartProvider.jsx";
import polarisVizStyles from "@shopify/polaris-viz/build/esm/styles.css?url";
import { authenticateExtra } from "../config/shopify";
import "../assets/custom-styles.css";
import { useAppBridge } from "@shopify/app-bridge-react";
import { useEffect } from "react";
import { updateCharge } from "../models/charge.server.js";
import { fetchCharge } from "../models/charge.server.js";
import { initializeIntercom } from "../utilities/intercom.js";

export const links = () => [
  { rel: "stylesheet", href: polarisStyles },
  { rel: "stylesheet", href: polarisVizStyles },
];

export const loader = async ({ request }) => {
  await authenticate.admin(request);
  const { billing, session } = await authenticateExtra(request);
  const chargeData = await fetchCharge(session.shop);
  const showAllFeature = chargeData?.data?.show_all_feature || false;
  // Check whether the store has an active subscription
  const { hasActivePayment, appSubscriptions } = await billing.check();
  let activeDataRes = appSubscriptions;
  if (activeDataRes.length > 0) {
    const data = activeDataRes[0];
    data.store = session.shop;
    const res = await updateCharge(data);
  } else {
    if (!hasActivePayment) {
      const input = {
        store: session.shop,
        status: "pending",
        name: null,
        is_onboarding: true,
      };
      const res = await updateCharge(input);
    }
  }
  const getFirstSegment = (s) => s?.split?.(".")[0] || "";
  return json({
    hasActivePayment:
      showAllFeature === true ? showAllFeature : hasActivePayment,
    apiKey: process.env.SHOPIFY_API_KEY || "",
    shop: getFirstSegment(session.shop) || session.shop,
  });
};

export default function App() {
  const { apiKey, hasActivePayment, shop } = useLoaderData();
  const shopify = useAppBridge();
  // Navigation state and loader
  const nav = useNavigation();

  useEffect(() => {
    initializeIntercom({
      name: shop,
      email: shop,
    });
  }, [shop]);

  useEffect(() => {
    if (nav.state === "loading" || nav.state === "submitting") {
      shopify.loading(true);
    }
    if (typeof shopify !== "undefined" && nav.state === "idle" && shopify) {
      shopify.loading(false);
    }
  }, [nav]);

  return (
    <AppProvider isEmbeddedApp apiKey={apiKey}>
      <Nav isActive={hasActivePayment} />
      <ChartProvider>
        <Outlet />
      </ChartProvider>
    </AppProvider>
  );
}

// Shopify needs Remix to catch some thrown responses, so that their headers are included in the response.
export function ErrorBoundary() {
  return boundary.error(useRouteError());
}

export const headers = (headersArgs) => {
  return boundary.headers(headersArgs);
};
