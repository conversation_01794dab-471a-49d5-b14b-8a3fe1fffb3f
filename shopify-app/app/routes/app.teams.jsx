import React, { useState, useCallback, useEffect } from "react";
import {
  Page,
  Card,
  Badge,
  Button,
  Select,
  TextField,
  ButtonGroup,
  Text,
  Grid,
  EmptyState,
  Pagination,
  SkeletonBodyText,
  InlineStack,
  InlineGrid,
} from "@shopify/polaris";
import i18n from "i18next";
import { json } from "@remix-run/node";
import { useActionData, useSubmit, useNavigate } from "@remix-run/react";
import { loggerError, sendResponse } from "../utilities/helper";
import { useTranslation } from "react-i18next";
import imageUrls from "../config/images";
import { authenticate } from "../config/shopify.js";
import { getTeamsData } from "../models/team.server.js";
import "../css/team.css";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";
import { RefreshIcon } from "@shopify/polaris-icons";
import { showPromptFlow } from "../config/config.js";
export async function action({ request }) {
  const { session, admin } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    const actions = {
      initialFetch: async () => {
        const teamList = await getTeamsData(data);
        bodyData = {
          status: "ok",
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            teamList: teamList.status === 200 ? teamList.data : [],
          },
        };
        return bodyData;
      },
    };
    const actionFn = actions[data.functionType];
    if (!actionFn) {
      throw new Error("Unknown functionType");
    }
    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: bodyData.status || "ok",
        message: bodyData.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch team ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}
const teamsManagement = () => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchValue, setSearchValue] = useState("");
  const [sortValue, setSortValue] = useState("most_used");
  const [filterValue, setFilterValue] = useState("");
  const navigate = useNavigate();
  const submit = useSubmit();
  const actionData = useActionData();
  const [isLoading, setIsLoading] = useState(true);
  const [shopName, setShopName] = useState("");
  const [teamList, setTeamList] = useState([]);

  const initialFetch = useCallback(() => {
    setIsLoading(true);
    setTeamList([]);
    const result = {
      functionType: "initialFetch",
      status: filterValue,
    };
    submit(result, { method: "post" });
  }, [filterValue, searchValue]);

  useEffect(() => {
    if (actionData) {
      if (actionData.status === "ok") {
        switch (actionData.data?.functionType) {
          case "initialFetch":
            const data = actionData?.data?.data;
            setShopName(data.shopName);
            setTeamList(data?.teamList || []);
            break;
        }
      } else {
        shopify.toast.show(actionData.message, { isError: true });
      }
      setIsLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    initialFetch();
  }, []);

  useEffect(() => {
    if (shopName) {
      trackPageView("Teams list", { shop_name: shopName });
    }
  }, [shopName]);
  // Group teams by category
  const groupedTeams = teamList.reduce((acc, team) => {
    if (!acc[team.category]) {
      acc[team.category] = [];
    }
    acc[team.category].push(team);
    return acc;
  }, {});

  const handleSortChange = (value) => setSortValue(value);
  const handleFilterChange = (value) => setFilterValue(value);
  const handleSearchChange = (value) => setSearchValue(value);

  const handleNextPage = () => {
    if (currentPage < 2) setCurrentPage(currentPage + 1);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const renderTeamCard = (team) => {
    return (
      <Grid.Cell
        columnSpan={{ xs: 6, sm: 6, md: 6, lg: 6, xl: 6 }}
        key={team.team_id}
      >
        <Card>
          <div className="team-card">
            <InlineStack align="space-between">
              <Text variant="headingMd">{team.name}</Text>
              {team.status && (
                <Badge
                  tone={
                    team.stats === "success" || team.stats === "active"
                      ? "success"
                      : null
                  }
                >
                  {t(team.status)}
                </Badge>
              )}
            </InlineStack>
            <div className="top-space-10" />
            <Text as="p" variant="bodyMd" color="subdued">
              {team.description}
            </Text>
            <div className="top-margin-auto">
              <InlineStack align="space-between">
                {/* <Button onClick={() => {}}>Assign Task</Button> */}
                <Button
                  disabled={isLoading}
                  onClick={() => {
                    setIsLoading(true);
                    navigate(`/app/team/${team.team_id}`);
                  }}
                  variant="primary"
                >
                  {t("view_assigned_workers")}
                </Button>
                {team?.status === "pending" && (
                  <Button
                    icon={RefreshIcon}
                    onClick={() => {
                      initialFetch();
                    }}
                  />
                )}
              </InlineStack>
            </div>
          </div>
        </Card>
      </Grid.Cell>
    );
  };

  return (
    <Page
      title={t("teams_page_title")}
      subtitle={t("teams_page_desc")}
      primaryAction={
        showPromptFlow && (
          <Button
            onClick={() => {
              trackButtonClick("Create Workers flow", "Teams list", {
                shop_name: shopName,
              });
              navigate(`/app/create/workers`);
            }}
            variant="primary"
          >
            {t("create_new_store_job")}
          </Button>
        )
      }
    >
      {/* 
  <Card>
          <div style={{ display: "flex", gap: "12px" }}>
            <div style={{ flex: 1 }}>
              <Select
                label="Filter by capability"
                labelHidden
                options={[
                  { label: "Filter by capability", value: "" },
                  { label: "Content Creation", value: "content" },
                  { label: "SEO", value: "seo" },
                  { label: "Social Media", value: "social" },
                  { label: "Data Analysis", value: "data" },
                ]}
                value={filterValue}
                onChange={handleFilterChange}
              />
            </div>
            <div style={{ flex: 1 }}>
              <Select
                label="Sort by"
                labelHidden
                options={[
                  { label: "Sort by: Most used", value: "most_used" },
                  { label: "Sort by: Recently used", value: "recent" },
                  { label: "Sort by: Alphabetical", value: "alpha" },
                ]}
                value={sortValue}
                onChange={handleSortChange}
              />
            </div>
            <div style={{ flex: 1 }}>
              <TextField
                label="Search"
                labelHidden
                value={searchValue}
                onChange={handleSearchChange}
                placeholder="Search teams"
              />
            </div>
          </div>
        </Card>
*/}

      {isLoading && teamList.length === 0 ? (
        <InlineGrid columns={3} gap="400">
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
        </InlineGrid>
      ) : teamList.length === 0 ? (
        <Card>
          <EmptyState
            heading={t("no_teams_available")}
            image={imageUrls.emptyImage}
          />
        </Card>
      ) : (
        <Grid>{teamList.map((team) => renderTeamCard(team))}</Grid>
      )}

      <div>
        {/*  <Pagination
          label={`Page ${currentPage} of 2`}
          hasPrevious={currentPage > 1}
          hasNext={currentPage < 2}
          onPrevious={handlePrevPage}
          onNext={handleNextPage}
        />

        */}
      </div>
      <div style={{ marginTop: "15px" }} />
    </Page>
  );
};

export default teamsManagement;
