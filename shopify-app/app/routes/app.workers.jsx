import React, { useState, useCallback, useEffect } from "react";
import {
  Page,
  Card,
  Button,
  Select,
  TextField,
  ButtonGroup,
  Text,
  Grid,
  EmptyState,
  Pagination,
  InlineStack,
  Badge,
  InlineGrid,
  SkeletonBodyText,
} from "@shopify/polaris";
import i18n from "i18next";
import { json } from "@remix-run/node";
import { useActionData, useSubmit, useNavigate } from "@remix-run/react";
import { loggerError, sendResponse } from "../utilities/helper";
import { useTranslation } from "react-i18next";
import imageUrls from "../config/images";
import { authenticate } from "../config/shopify.js";
import { getAllWorkersData } from "../models/workers.server";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";
export async function action({ request }) {
  const { session } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    const actions = {
      initialFetch: async () => {
        const workersList = await getAllWorkersData(data);
        bodyData = {
          status: "ok",
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            workersList: workersList?.status === 200 ? workersList?.data : [],
          },
        };
        return bodyData;
      },
    };
    const actionFn = actions[data.functionType];
    if (!actionFn) {
      throw new Error("Unknown functionType");
    }
    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: bodyData.status || "ok",
        message: bodyData.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch worker ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}

const WorkersManagement = () => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchValue, setSearchValue] = useState("");
  const [sortValue, setSortValue] = useState("most_used");
  const [filterValue, setFilterValue] = useState("");
  const navigate = useNavigate();
  const submit = useSubmit();
  const actionData = useActionData();
  const [isLoading, setIsLoading] = useState(false);
  const [workersList, setWorkersList] = useState([]);
  const [shopName, setShopName] = useState("");

  const initialFetch = useCallback(() => {
    setIsLoading(true);
    const result = {
      functionType: "initialFetch",
      status: filterValue,
    };
    submit(result, { method: "post" });
  }, [filterValue, searchValue]);

  useEffect(() => {
    initialFetch();
  }, [filterValue]);

  useEffect(() => {
    if (shopName) {
      trackPageView("Workers list", { shop_name: shopName });
    }
  }, [shopName]);

  useEffect(() => {
    if (actionData) {
      if (actionData.status === "ok") {
        switch (actionData.data?.functionType) {
          case "initialFetch":
            const data = actionData?.data?.data;
            setShopName(data.shopName);
            setWorkersList(data?.workersList || []);
            break;
        }
      } else {
        shopify.toast.show(actionData.message, { isError: true });
      }
      setIsLoading(false);
    }
  }, [actionData]);

  const handleSortChange = (value) => setSortValue(value);
  const handleFilterChange = (value) => setFilterValue(value);
  const handleSearchChange = (value) => setSearchValue(value);

  const handleNextPage = () => {
    if (currentPage < 2) setCurrentPage(currentPage + 1);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const renderWorkerCard = (worker) => {
    return (
      <Grid.Cell
        columnSpan={{ xs: 6, sm: 6, md: 6, lg: 6, xl: 6 }}
        key={worker.uuid}
      >
        <Card>
          <div style={{ marginTop: "10px", minHeight: "100px" }}>
            <InlineStack>
              {worker.emoji && (
                <div style={{ fontSize: "24px", marginRight: "12px" }}>
                  {worker.emoji}
                </div>
              )}
              <Text variant="headingMd">{worker.name}</Text>
            </InlineStack>
            <Text as="p" variant="bodyMd" color="subdued">
              {worker.goal}
            </Text>
            <br />
            <InlineStack align="space-between">
              <Badge>
                <Text as="p" variant="bodyMd" color="subdued">
                  {worker.role}
                </Text>
              </Badge>
              {/*   <Button
                onClick={() => {
                  //navigate(`/app/worker/${worker.uuid}`);
                }}
                variant="primary"
              >
                View Performance
              </Button>
              */}
            </InlineStack>
          </div>
        </Card>
      </Grid.Cell>
    );
  };

  return (
    <Page
      title={t("workers_page_title")}
      subtitle={t("workers_page_desc")}
      primaryAction={
        <Button
          onClick={() => {
            trackButtonClick("Create Workers flow", "Workers list", {
              shop_name: shopName,
            });
            navigate(`/app/create/workers`);
          }}
          variant="primary"
        >
          {t("create_new_store_job")}
        </Button>
      }
    >
      {/* 
  <Card>
          <div style={{ display: "flex", gap: "12px" }}>
            <div style={{ flex: 1 }}>
              <Select
                label="Filter by capability"
                labelHidden
                options={[
                  { label: "Filter by capability", value: "" },
                  { label: "Content Creation", value: "content" },
                  { label: "SEO", value: "seo" },
                  { label: "Social Media", value: "social" },
                  { label: "Data Analysis", value: "data" },
                ]}
                value={filterValue}
                onChange={handleFilterChange}
              />
            </div>
            <div style={{ flex: 1 }}>
              <Select
                label="Sort by"
                labelHidden
                options={[
                  { label: "Sort by: Most used", value: "most_used" },
                  { label: "Sort by: Recently used", value: "recent" },
                  { label: "Sort by: Alphabetical", value: "alpha" },
                ]}
                value={sortValue}
                onChange={handleSortChange}
              />
            </div>
            <div style={{ flex: 1 }}>
              <TextField
                label="Search"
                labelHidden
                value={searchValue}
                onChange={handleSearchChange}
                placeholder="Search workers"
              />
            </div>
          </div>
        </Card>
*/}

      {isLoading && workersList.length === 0 ? (
        <InlineGrid columns={3} gap="400">
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
          <Card>
            <SkeletonBodyText lines={4} />
          </Card>
        </InlineGrid>
      ) : workersList.length === 0 ? (
        <Card>
          <EmptyState
            heading={t("no_workers_available")}
            image={imageUrls.emptyImage}
          />
        </Card>
      ) : (
        <Grid>{workersList.map((worker) => renderWorkerCard(worker))}</Grid>
      )}

      <div
        style={{ marginTop: "20px", display: "flex", justifyContent: "center" }}
      >
        {/*  <Pagination
          label={`Page ${currentPage} of 2`}
          hasPrevious={currentPage > 1}
          hasNext={currentPage < 2}
          onPrevious={handlePrevPage}
          onNext={handleNextPage}
        />

        */}
      </div>
    </Page>
  );
};

export default WorkersManagement;
