import React, { useState, useCallback, useEffect } from "react";
import {
  <PERSON>,
  Card,
  Badge,
  Button,
  Text,
  SkeletonBodyText,
  Divider,
  Layout,
  Banner,
  EmptyState,
  BlockStack,
  InlineStack,
  Box,
  DescriptionList,
  Collapsible,
} from "@shopify/polaris";
import { ChevronDownIcon, ChevronUpIcon } from "@shopify/polaris-icons";
import i18n from "i18next";
import { json } from "@remix-run/node";
import { useActionData, useSubmit, useNavigate } from "@remix-run/react";
import { loggerError, sendResponse } from "../utilities/helper.js";
import { useTranslation } from "react-i18next";
import { authenticate } from "../config/shopify.js";
import {
  fetchTeamResultById,
  updateResultItemStatus,
} from "../models/job_result.server.js";
import "../css/team.css";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";
import imageUrls from "../config/images";

export async function action({ request, params }) {
  const { id } = params;
  const { session } = await authenticate.admin(request);
  let bodyData;
  try {
    const data = { ...Object.fromEntries(await request.formData()) };
    data.store = session.shop;
    data.id = id;
    data.jobId = id;
    const actions = {
      initialFetch: async () => {
        const resultData = await fetchTeamResultById(data);
        bodyData = {
          status: "ok",
          functionType: "initialFetch",
          data: {
            shopName: session.shop,
            resultData: resultData.status === 200 ? resultData.data : null,
          },
        };
        return bodyData;
      },
      publishItem: () => updateResultItemStatus(data, session),
    };

    const actionFn = actions[data.functionType];
    if (!actionFn) throw new Error("Unknown functionType");

    bodyData = await actionFn();
    return json(
      await sendResponse({
        status: bodyData.status || "ok",
        message: bodyData.message,
        data: bodyData,
      }),
    );
  } catch (error) {
    loggerError(`Error from fetch team result ${error.message}`);
    return json(
      await sendResponse({
        status: "error",
        message: i18n.t("something_went_wrong"),
      }),
    );
  }
}

const capitalizeFirstLetter = (string) => {
  if (!string || typeof string !== "string") return "";
  // First replace both - and _ with spaces
  const stringWithSpaces = string.replace(/[-_]/g, " ");
  // Then capitalize first letter
  return stringWithSpaces.charAt(0).toUpperCase() + stringWithSpaces.slice(1);
};

const ValueDisplay = ({ value, fieldKey }) => {
  if (value === null || value === undefined)
    return <Text color="subdued">-</Text>;

  if (typeof value === "object") {
    if (Array.isArray(value)) {
      return (
        <BlockStack gap="2">
          {value.map((item, idx) => (
            <Box key={idx} paddingBlock="1">
              {typeof item === "object" ? (
                <NestedObjectDisplay object={item} />
              ) : (
                <Text>{String(item)}</Text>
              )}
            </Box>
          ))}
        </BlockStack>
      );
    }
    return <NestedObjectDisplay object={value} />;
  }

  if (typeof value === "boolean") {
    return (
      <Badge status={value ? "success" : "critical"}>
        {value ? "Yes" : "No"}
      </Badge>
    );
  }

  if (typeof value === "number") return <Text>{value.toString()}</Text>;

  if (typeof value === "string") {
    if (fieldKey.toLowerCase().includes("status")) {
      return <Badge>{value}</Badge>;
    }
    const isHtml = /<[a-z][\s\S]*>/i.test(value);
    if (isHtml) {
      const cleanedValue = value.replace(/\\n|\/n/g, ""); // remove \n or /n
      return <div dangerouslySetInnerHTML={{ __html: cleanedValue }} />;
    }
  
    return <Text>{value}</Text>;
  }
  

  return <Text>{String(value)}</Text>;
};

const NestedObjectDisplay = ({ object }) => {
  if (!object || Object.keys(object).length === 0) {
    return <Text color="subdued">-</Text>;
  }

  return (
    <Box paddingBlockStart="2">
      {Object.entries(object).map(([key, val], index, arr) => (
        <Box key={key} paddingBlockEnd="3">
          <Text variant="bodyMd" fontWeight="bold">
            {capitalizeFirstLetter(key)}
          </Text>
          <Box paddingBlockStart="2" paddingInlineStart="4">
            <ValueDisplay value={val} fieldKey={key} />
          </Box>
          {index < arr.length - 1 && (
            <Box paddingBlockStart="3">
              <Divider />
            </Box>
          )}
        </Box>
      ))}
    </Box>
  );
};

const TeamDoneResultPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const submit = useSubmit();
  const actionData = useActionData();

  const [isLoading, setIsLoading] = useState(true);
  const [contentLoading, setContentLoading] = useState(false);
  const [shopName, setShopName] = useState("");
  const [resultData, setResultData] = useState(null);
  const [errorMessage, setErrorMessage] = useState("");

  const ResultItem = ({ item, index }) => {
    const [isExpanded, setIsExpanded] = useState(true);

    const handleToggle = useCallback(() => {
      setIsExpanded((expanded) => !expanded);
    }, []);

    const handlePublish = useCallback(() => {
      setContentLoading(true);
      submit(
        {
          functionType: "publishItem",
          resultData: JSON.stringify(resultData),
          itemId: item?.display_data?.id,
        },
        { method: "post" },
      );
    }, [item.graphql_string, item.input]);

    const displayData = item.display_data || {};
    const filteredDisplayData = Object.fromEntries(
      Object.entries(displayData).filter(
        ([key]) =>
          !key.toLowerCase().includes("status") &&
          !key.toLowerCase().includes("id") &&
          !key.toLowerCase().includes("title"),
      ),
    );

    return (
      <div style={{ marginBottom: "10px" }} key={index}>
        <Card>
          <BlockStack gap="4">
            <InlineStack wrap={false} align="space-between">
              <BlockStack gap="1">
                <Text variant="headingLg" as="h3">
                  {displayData.title || `Result ${index + 1}`}
                </Text>
                {displayData.description && (
                  <Text variant="bodyMd" color="subdued">
                    {displayData.description}
                  </Text>
                )}
              </BlockStack>
              <InlineStack gap="400">
                {item?.graphql_string && (
                  <Button
                    disabled={displayData?.status === true}
                    loading={contentLoading}
                    onClick={handlePublish}
                    variant="primary"
                  >
                    {t("publish")}
                  </Button>
                )}
                <Button
                  plain
                  icon={isExpanded ? ChevronUpIcon : ChevronDownIcon}
                  onClick={handleToggle}
                />
              </InlineStack>
            </InlineStack>
            <Collapsible open={isExpanded}>
              <br />
              <DescriptionList
                items={Object.entries(filteredDisplayData).map(
                  ([key, value]) => ({
                    term: capitalizeFirstLetter(key),
                    description: <ValueDisplay value={value} fieldKey={key} />,
                  }),
                )}
              />
            </Collapsible>
          </BlockStack>
        </Card>
      </div>
    );
  };

  const initialFetch = useCallback(() => {
    setIsLoading(true);
    setResultData(null);
    setErrorMessage("");
    submit({ functionType: "initialFetch" }, { method: "post" });
  }, [submit]);

  useEffect(() => {
    if (actionData) {
      if (actionData.status === "ok" || actionData.status === 200) {
        const data = actionData.data?.data;
        switch (actionData.data.functionType) {
          case "initialFetch":
            setShopName(data?.shopName);
            setResultData(data?.resultData || null);
            break;
          case "publishItem":
            const updatedResult = resultData.result.map((item) => {
              if (
                item.display_data &&
                item.display_data.id === actionData.data.data.itemId
              ) {
                return {
                  ...item,
                  display_data: {
                    ...item.display_data,
                    status: true,
                  },
                };
              }
              return item;
            });
            setResultData({
              ...resultData,
              result: updatedResult,
            });
            shopify.toast.show(t("item_published_successfully"));
            break;

          default:
            shopify.toast.show(actionData.message, { isError: true });
            break;
        }
      } else {
        //setErrorMessage(t("something_went_wrong"));
        shopify.toast.show(actionData.message, { isError: true });
      }
    }
    setContentLoading(false);
    setIsLoading(false);
  }, [actionData, t, initialFetch]);

  useEffect(() => {
    initialFetch();
  }, [initialFetch]);

  useEffect(() => {
    if (shopName) {
      trackPageView("Teams list", { shop_name: shopName });
    }
  }, [shopName]);

  const handlePublish = useCallback(() => {
    trackButtonClick("Create Workers flow", "Teams list", {
      shop_name: shopName,
    });
  }, [shopName]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <Card>
          <BlockStack gap="4">
            <InlineStack alignment="center"></InlineStack>
            <SkeletonBodyText lines={8} />
          </BlockStack>
        </Card>
      );
    }

    if (errorMessage) {
      return (
        <Banner
          title={t("error_occurred")}
          status="critical"
          onDismiss={() => setErrorMessage("")}
        >
          <p>{errorMessage}</p>
        </Banner>
      );
    }

    if (
      !resultData?.result ||
      (Array.isArray(resultData.result) && resultData.result.length === 0)
    ) {
      return (
        <Card>
          <EmptyState
            heading={t("no_results_available")}
            image={imageUrls.emptyImage}
          />
        </Card>
      );
    }

    const results = Array.isArray(resultData.result)
      ? resultData.result
      : [resultData.result];

    return (
      <div style={{ marginBottom: "30px" }}>
        {results.map((item, index) => (
          <ResultItem key={index} item={item} index={index} />
        ))}
      </div>
    );
  };
  const handleGoBack = () => {
    // navigate("/app/team/done", { replace: true });
    navigate(-1);
  };

  return (
    <Page
      title={resultData?.title || t("team_results")}
      subtitle={resultData?.description || ""}
      backAction={{ content: "Products", onAction: handleGoBack }}
      primaryAction={
        <InlineStack gap={400} align="center">
          <Badge tone="success">{t(resultData?.type || "")}</Badge>
          {/*  <Button onClick={handlePublish} variant="primary">
          {t("publish")}
          </Button> */}
        </InlineStack>
      }
    >
      <Layout>
        <Layout.Section>{renderContent()}</Layout.Section>
      </Layout>
    </Page>
  );
};

export default TeamDoneResultPage;
