import { authenticateExtra } from "../config/shopify.js";
import { json } from "@remix-run/node";
import Pricing from "../components/pricing/index.jsx";
import appConfig from "../config/app.js";
import { authenticate } from "../config/shopify.js";
import { createCharge } from "../models/charge.server.js";
const METAFIELD_NAMESPACE = "shipready";
const METAFIELD_KEY = "paid";
const METAFIELD_TYPE = "boolean";
import { useActionData, useLoaderData } from "@remix-run/react";
const { pricingPlans } = appConfig;
import { usagePricingPlans } from "../config/price.js";
import { sendResponse } from "../utilities/helper";
import React, { useEffect, useState } from "react";
import { trackButtonClick, trackPageView } from "../utilities/mixpanel.js";

export async function loader({ request }) {
  await authenticate.admin(request);
  const { billing ,session} = await authenticateExtra(request);

  const plans = pricingPlans.map((plan) => plan.id.toUpperCase());
  // Check whether the store has an active subscription
  const { hasActivePayment, appSubscriptions } = await billing.check({});
  return json({ hasActivePayment, appSubscriptions, shopName: session.shop });
}

export async function action({ request }) {
  const { billing, session, metafield, admin } =
    await authenticateExtra(request);
  const { plan, cancel } = Object.fromEntries(await request.formData());
  const userShop = session.shop.replace(".myshopify.com", "");
  const currentAppOwnerID = await metafield.getCurrentAppOwnerId();
  if (plan) {
    const planData = usagePricingPlans.find((p) => p.id === plan);
    const value = {
      plan: planData,
      store: session.shop,
      functionType: "createCharge",
    };
    const createRes = await createCharge(admin, value);
    const url = createRes?.data?.confirmationUrl;
    
    if (!url) {
      return json(
        await sendResponse({
          status: "error",
          message: "Something went wrong.",
          data: null,
        }),
      );
    }

    return json(
      await sendResponse({
        status: "ok",
        message: "confirmation_charge_url_generated_successfully",
        data: url,
      }),
    );
    //  await updateMetafieldAndPlan(metafield, currentAppOwnerID, plan !== "free", billing, plan, userShop);
  } else if (cancel) {
    await updateMetafieldAndCancelPlan(
      metafield,
      currentAppOwnerID,
      billing,
      cancel,
    );
  }

  return null;
}

async function updateMetafieldAndPlan(
  metafield,
  ownerId,
  isPaid,
  billing,
  plan,
  userShop,
) {
  await updateMetafield(metafield, ownerId, isPaid);
  await handlePlanSelection(billing, plan, userShop);
}

async function updateMetafieldAndCancelPlan(
  metafield,
  ownerId,
  billing,
  subscriptionId,
) {
  await updateMetafield(metafield, ownerId, false);
  await handlePlanCancellation(billing, subscriptionId);
}

async function updateMetafield(metafield, ownerId, isPaid) {
  const metafieldData = {
    namespace: METAFIELD_NAMESPACE,
    key: METAFIELD_KEY,
    value: isPaid.toString(),
    type: METAFIELD_TYPE,
    ownerId,
  };

  await metafield.create(metafieldData);
}

async function handlePlanSelection(billing, plan, userShop) {
  const planUpperCase = plan.toUpperCase();
  await billing.require({
    plans: [planUpperCase],
    isTest: isTestMode(),
    onFailure: async () => requestBilling(billing, planUpperCase, userShop),
  });
}

async function handlePlanCancellation(billing, subscriptionId) {
  await billing.cancel({
    subscriptionId,
    isTest: isTestMode(),
    prorate: true,
  });
}

async function requestBilling(billing, plan, userShop) {
  return billing.request({
    plan,
    isTest: isTestMode(),
    returnUrl: `https://admin.shopify.com/store/${userShop}/apps/${process.env.APP_HANDLE}/app/pricing`,
  });
}

function isTestMode() {
  return process.env.TESTMODE === "true";
}
export default function PricingPage() {
  const actionData = useActionData();
  const { shopName } = useLoaderData();
  // Initialize loading as an array of false values, one for each plan
  const [loading, setLoading] = useState(Array(usagePricingPlans.length).fill(false));
  
  useEffect(() => {
    if (shopName) {
      trackPageView("Price Plan", { shop_name: shopName });
    }
    if (actionData) {
      if (actionData.status === "ok") {
        handleConfirmation(actionData.data);
        trackButtonClick("Price Plan", "Plan Subscription", {
          shop_name: shopName,
        });
      } else {
        // Reset loading state when there's an error
        setLoading(Array(usagePricingPlans.length).fill(false));
        shopify.toast.show(actionData.message, { isError: true });
      }
    }
  }, [actionData]);
  const handleConfirmation = async (url) => {
    parent.parent.window.location.replace(url);
    parent.parent.GB_hide();
  };

  return <Pricing loading={loading} setLoading={setLoading} />;
}
