import { json } from "@remix-run/node";
import { useEffect } from "react";
import { authenticateExtra } from "../config/shopify";
import { InlineStack, Spinner } from "@shopify/polaris";
import { fetchCharge } from "../models/charge.server";
import { useLoaderData, useNavigate } from "@remix-run/react";

export const loader = async ({ request }) => {
  const { session, billing } = await authenticateExtra(request);
  const chargeData = await fetchCharge(session.shop);
  // Check whether the store has an active subscription
  const { hasActivePayment } = await billing.check();
  // shop here is passed empty string since the seed data is store with shop as ''
 // const verifyAppEmbed = await shipReady.verifyAppEmbed();
  return json({
    shop: session.shop,
    //verifyAppEmbed,
    uuid: process.env.SHOPIFY_THEME_APP_EXTENSION_ID,
    hasActivePayment: hasActivePayment,
    showAllFeature: chargeData?.data?.show_all_feature || false,
  });
};

export default function Index() {
  const { hasActivePayment, showAllFeature } = useLoaderData();
  const navigate = useNavigate();
  useEffect(() => {
    if (!hasActivePayment) {
      if (showAllFeature) {
        navigate("/app/home");
      } else {
        navigate("/app/pricing");
      }
    } else {
      navigate("/app/home");
    }
  }, [hasActivePayment, navigate]);

  return (
    <InlineStack align="center">
      <Spinner accessibilityLabel="Spinner example" size="large" />
    </InlineStack>
  );
}
