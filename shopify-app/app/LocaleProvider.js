// app/LocaleProvider.js
import { initReactI18next } from 'react-i18next';
import i18n from 'i18next';
import translationEN from '../app/locales/en.json';
i18n.use(initReactI18next).init({
  resources: {
    en: { translation: translationEN },
  },
  lng: 'en', // Default language
  interpolation: {
    escapeValue: false
  }
}, (err, t) => { // Ensure a callback is provided
  if (err) {
    console.error('Error initializing i18next:', err);
  }
});

export default i18n;
