// create_tables.js
import pool from "./pool_config.js"; //importing config pool to create table
import { storesTable, chargeTable,jobOutputTable,settingsTable } from "./schemas.js"; //importing schema for the table
import { loggerInfo, loggerError } from "../utilities/helper.js"

// Function to create all tables
export async function createTables() {
  loggerInfo("Connecting to the database...");
  const client = await pool.connect();
  loggerInfo("Connected to the database.");
  try {
    // Execute queries to create tables
    await client.query(storesTable);
    loggerInfo("Store Table created successfully");
    await client.query(chargeTable);
    loggerInfo("Charge Table created successfully");
    await client.query(jobOutputTable);
    loggerInfo("Job output Table created successfully");
    await client.query(settingsTable);
    loggerInfo("Settings Table created successfully");
  } catch (error) {
    console.log(error);
    loggerError(`Error creating tables:${error.message}`);
  } finally {
    client.release();
  }
}
// Close pool when script exits
process.on("exit", () => {
  pool.end();
  loggerInfo("Pool has been closed.");
});
