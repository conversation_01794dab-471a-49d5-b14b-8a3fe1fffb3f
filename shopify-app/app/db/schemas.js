// schemas.js
import { STORES_TABLE, CHARGE_TABLE, JOB_OUTPUT_TABLE, SETTINGS_TABLE } from "../config/config";
export const storesTable = `
    CREATE TABLE IF NOT EXISTS public.${STORES_TABLE}  (
      id SERIAL PRIMARY KEY,
      uuid VARCHAR(100), 
      store TEXT UNIQUE NOT NULL,
      session_id TEXT,
      shop_id bigint,
      email VARCHAR(100),
      name Text,
      phone Text,
      customer_email VARCHAR(100),
      timezone TEXT ,
      store_domain VARCHAR(100),
      app_count INT,
      country_code TEXT,
      proactive_ai_api_token TEXT,
      created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP

    );
  `;

export const chargeTable = `
  CREATE TABLE IF NOT EXISTS public.${CHARGE_TABLE}  (
    id SERIAL PRIMARY KEY,
    charge_id TEXT,
    store TEXT UNIQUE NOT NULL,
    name VARCHAR(50),
    price VARCHAR(50),
    currency VARCHAR(50),
    test BOOLEAN,
    status VARCHAR(100),
    is_onboarding BOOLEAN,
    current_period_end TEXT,
    charge_created_at TEXT,
    trial_day TEXT,
    lineitems_id TEXT,
    lineitems_capped_amount TEXT,
    lineitem_terms TEXT,
    lineitem_balance_used TEXT,
    show_all_feature BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
  );
`;

export const jobOutputTable = `
  CREATE TABLE IF NOT EXISTS public.${JOB_OUTPUT_TABLE}  (
    id SERIAL PRIMARY KEY,
    team_id TEXT,
    job_id TEXT UNIQUE,  
    type TEXT,
    status TEXT,
    title TEXT,
    description TEXT,
    store TEXT NOT NULL,
    result JSON,
    result_created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
  );
`;


export const settingsTable = `
  CREATE TABLE IF NOT EXISTS public.${SETTINGS_TABLE}  (
    id SERIAL PRIMARY KEY,
      store TEXT UNIQUE NOT NULL,
    email_enable BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
  );
`;
