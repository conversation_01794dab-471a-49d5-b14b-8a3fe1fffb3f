import { proactiveAiInstall } from "../proactive_ai/install";
import { fetchShopifyProducts } from "../graphql/get_products";
import { proactiveAiProductSync } from "../proactive_ai/sync_products";
import { proactiveAiArticleSync } from "../proactive_ai/sync_articles";
import { proactiveAiCollectionSync } from "../proactive_ai/sync_collections";
import { proactiveAiBlogSync } from "../proactive_ai/sync_blogs";
import { generateProductUrl } from "../utilities/helper";
import { fetchShopifyArticles } from "../graphql/get_articles";
import { fetchShopifyBlogs } from "../graphql/get_blogs";
import { fetchShopifyCollections } from "../graphql/get_collections";
import { proactiveAiStartup } from "../proactive_ai/startup";
import { sendSlackWebhook } from "../utilities/slack_webhook";

export async function generateProActiveAIToken(data, inputStoreData) {
  const res = await proactiveAiInstall(data);
  const store_token = res?.data.data?.api_token || "";
  data.store_token = store_token;
  const startupRes = await proactiveAiStartup(data);
  if (startupRes.status_code !== 200) {
    inputStoreData.status_code =
    startupRes.status_code || startupRes.status || 500;
    inputStoreData.message = startupRes.message;
    await sendSlackWebhook(inputStoreData, "startup");
    delete inputStoreData.status_code;
    delete inputStoreData.message;
  }
  return res.data;
}

export async function syncProductsProactive(
  admin,
  shopName,
  store_domain,
  store_token,
) {
  const productsList = await fetchShopifyProducts(admin, true);
  const updatedProducts = await Promise.all(
    productsList.map(async (product) => {
      const product_url = await generateProductUrl(
        store_domain || "",
        product.handle,
      );
      return {
        ...product,
        product_url,
      };
    }),
  );
  const value = {
    products: updatedProducts,
    shopName: shopName,
    store_token: store_token,
  };
  const sync = await proactiveAiProductSync(value);
  return sync?.data || null;
}

export async function syncArticlesProactive(admin, shopName, store_token) {
  const articleList = await fetchShopifyArticles(admin);
  const value = {
    articles:
      articleList.status === 200 ? JSON.stringify(articleList.data) : [],
    shopName: shopName,
    store_token: store_token,
  };
  const sync = await proactiveAiArticleSync(value);
  return sync?.data || null;
}

export async function syncBlogsProactive(admin, shopName, store_token) {
  const blogList = await fetchShopifyBlogs(admin);
  const value = {
    blogs: blogList.status === 200 ? blogList.data : [],
    shopName: shopName,
    store_token: store_token,
  };
  const sync = await proactiveAiBlogSync(value);
  return sync?.data || null;
}

export async function syncCollectionProactive(admin, shopName, store_token) {
  const collectionsRes = await fetchShopifyCollections(admin);
  const value = {
    collections:
      collectionsRes.status === 200 ? JSON.stringify(collectionsRes.data) : [],
    shopName: shopName,
    store_token: store_token,
  };
  const sync = await proactiveAiCollectionSync(value);
  return sync?.data || null;
}

export async function syncAll(data) {
  const { admin, store, domain, appToken } = data;
  syncProductsProactive(admin, store, domain, appToken);
  syncCollectionProactive(admin, store, appToken);
  syncArticlesProactive(admin, store, appToken);
  syncBlogsProactive(admin, store, appToken);
}
