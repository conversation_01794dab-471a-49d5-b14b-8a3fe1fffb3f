import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { supabase } from "../db/db_config";
import { CHARGE_TABLE, usagePricingRate } from "../config/config";
const chargeTable = CHARGE_TABLE;
import { createAppSubscriptionWithUsage } from "../graphql/create_app_subscription_and_usage";
import { createAppUsageRecord } from "../graphql/create_usage_pricing";
import { fetchAppDetail } from "../graphql/get_app_details";
import { getSubscriptionStatus } from "../graphql/get_app_charge";

export async function createCharge(admin, data) {
  const { plan, store, functionType } = data;
  try {
    loggerInfo(`Creating Charge data started`, store || "");
    const appHandle = await fetchAppDetail(admin);
    const userShop = store.replace(".myshopify.com", "");
    const chargeRes = await createAppSubscriptionWithUsage(
      admin,
      plan,
      appHandle,
      userShop,
    );
    loggerInfo(`Creating Charge data completed`, store || "");
    return sendResponse({
      status: 200,
      message: "Store data fetched successfully",
      functionType,
      data: chargeRes || null,
    });
  } catch (error) {
    loggerError(`Error Creating Charge data`, "", error.message);
    return sendResponse({
      status: 500,
      message: `Failed Creating Charge data: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

export async function updateCharge(newData) {
  const functionType = "updateCharge";
  const { store } = newData;
  const formattedData = await formatSubscriptionData(newData);
  try {
    loggerInfo(`updating charge data in db started`, store || "");
    const { data, error } = await supabase
      .from(chargeTable)
      .upsert(formattedData, {
        onConflict: ["store"],
      })
      .eq("store", store)
      .select();
    if (error) {
      loggerError(`Error updating charge data in db`, "", error.message);
      return sendResponse({
        status: 400,
        message: `Failed to updating charge data: ${error.message}`,
        functionType,
        data: null,
      });
    }
    loggerInfo(`updating charge data in db completed`, store || "");
    return sendResponse({
      status: 200,
      message: "Charge data updated successfully",
      functionType,
      data: data[0] || null,
    });
  } catch (error) {
    loggerError(`Error updating charge data in db`, store, error.message);
    return sendResponse({
      status: 500,
      message: `Failed to update charge data: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

async function formatSubscriptionData(data) {
  const getSafe = (path, defaultValue = "") =>
    path?.toString?.() || defaultValue;
  const lineItem = data?.lineItems?.[0] || {};
  const pricingDetails = lineItem?.plan?.pricingDetails || {};
  return {
    charge_id: getSafe(data?.id),
    store: data.store,
    name: getSafe(data?.name),
    status: getSafe(data?.status),
    trial_day: getSafe(data?.trialDays),
    is_onboarding: true,
    charge_created_at: getSafe(data?.createdAt),
    current_period_end: getSafe(data?.currentPeriodEnd),
    lineitems_id: getSafe(lineItem?.id),
    lineitems_capped_amount: getSafe(pricingDetails?.cappedAmount?.amount),
    currency: getSafe(pricingDetails?.cappedAmount?.currencyCode),
    lineitem_terms: getSafe(pricingDetails?.terms),
    lineitem_balance_used: getSafe(pricingDetails?.balanceUsed?.amount),
  };
}

export async function createUsageCharge(admin, data) {
  const { store, functionType } = data;
  try {
    loggerInfo(`Creating Usage based Charge data started`, store || "");
    const chargeData = await fetchCharge(store);
    const input = {
      currencyCode: chargeData?.data?.currency || null,
      subscriptionLineItemId: chargeData?.data?.lineitems_id || null,
      description: "workflow",
      amount: usagePricingRate,
    };
    const showAllFeature = chargeData?.data?.show_all_feature || false;
    if (!showAllFeature) {
      const chargeRes = await createAppUsageRecord(admin, input);
      loggerInfo(`Creating Usage based charge data completed`, store || "");
      if (chargeRes.status === 200) {
        // const { appSubscriptions } = await billing.check();
        const shopifyChargeData = await getSubscriptionStatus(admin);
        // let activeDataRes = appSubscriptions;
        // const data = activeDataRes[0];
        if (shopifyChargeData) {
          shopifyChargeData.store = store;
          const res = await updateCharge(shopifyChargeData);
        }
      }
    } else {
      loggerInfo(
        `Skipped Usage based charge for show all feature flag is enabled`,
        store || "",
      );
    }
    return sendResponse({
      status: 200,
      message: "Usage based charge flow created successfully",
      functionType,
      //  data: chargeRes.data || null,
    });
  } catch (error) {
    loggerError(`Error Creating usage based Charge data`, "", error.message);
    return sendResponse({
      status: 500,
      message: `Failed Creating usage based Charge data: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

export async function fetchCharge(store) {
  const functionType = "fetchCharge";
  try {
    loggerInfo(`Fetching charge data from db started`, store || "");
    const { data, error } = await supabase
      .from(chargeTable)
      .select("*")
      .eq("store", store);

    if (error) {
      loggerError(`Error fetching charge data from db`, "", error.message);
      return sendResponse({
        status: 400,
        message: `Failed to fetch charge data: ${error.message}`,
        functionType,
        data: null,
      });
    }

    loggerInfo(`Fetching charge data from db completed`, store || "");
    return sendResponse({
      status: 200,
      message: "Charge data fetched successfully",
      functionType,
      data: data[0] || null,
    });
  } catch (error) {
    loggerError(`Error fetching charge data from db`, store, error.message);
    return sendResponse({
      status: 500,
      message: `Failed to fetch charge data: ${error.message}`,
      functionType,
      data: null,
    });
  }
}
