import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { prepareStoreInput } from "./store.server";
import { proactiveAiGetAnalytics } from "../proactive_ai/get_analytics";

export async function getAnalyticsData(data) {
  const { store, functionType } = data;
  loggerInfo("Get Workers data list backend - Started", store);
  const storeData = await prepareStoreInput(store);
  try {
    let dataRes = await proactiveAiGetAnalytics(storeData);
    loggerInfo(
      "Get analytics data backend Completed",
      store,
      JSON.stringify(dataRes),
    );
    const most_used_templates = dataRes?.data?.data?.most_used_templates || [];
    const top_performing_templates =
      dataRes?.data?.data?.top_performing_templates || [];
    const chartDataMostUsed = most_used_templates.map((template) => ({
      data: {
        name: template.title,
        key: String(template.usage_count),
        value: template.time_saved_minutes,
      },
    }));
    const chartDataTopPerform = top_performing_templates.map((template) => ({
      data: {
        name: template.title,
        key: String(template.usage_count),
        value: template.time_saved_minutes,
      },
    }));
    if (dataRes?.data?.data) {
      dataRes.data.data.most_used_templates = chartDataMostUsed;
      dataRes.data.data.top_performing_templates = chartDataTopPerform;
    }
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: dataRes?.status === 200 ? dataRes?.data.data : null,
    });
  } catch (error) {
    loggerError("Failed to Get analytics data  backend", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to Get analytics data  backend: ${error.message}`,
      functionType,
    });
  }
}
