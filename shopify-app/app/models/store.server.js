import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { supabase } from "../db/db_config";
import { STORES_TABLE } from "../config/config";
const storesTable = STORES_TABLE;

export async function fetchStore(store) {
  const functionType = "fetchStore";
  try {
    loggerInfo(`Fetching store data from db started`, store || "");
    const { data, error } = await supabase
      .from(storesTable)
      .select("*")
      .eq("store", store);
    if (error) {
      return sendResponse({
        status: 400,
        message: `Failed to fetch store data: ${error.message}`,
        functionType,
        data: null,
      });
    }
    loggerInfo(`Fetching store data from db completed`, store || "");
    return sendResponse({
      status: 200,
      message: "Store data fetched successfully",
      functionType,
      data: data[0] || null,
    });
  } catch (error) {
    loggerError(`Error fetching store data from db`, "", error.message);
    return sendResponse({
      status: 500,
      message: `Failed to fetch store data: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

export async function isExistStore(shop) {
  const { data } = await supabase
    .from(storesTable)
    .select("*")
    .eq("store", shop)
    .limit(1);
  return data?.length > 0;
}

export async function updateStore(store, newData) {
  const functionType = "updateStore";
  try {
    loggerInfo(`Upserting store data in db started`, store || "");
    const { data, error } = await supabase
      .from(storesTable)
      .upsert(newData, {
        onConflict: ["store"],
      })
      .eq("store", store)
      .select();
    if (error) {
      loggerError(`Error upserting store data in db`, "", error.message);
      return sendResponse({
        status: 400,
        message: `Failed to upsert store data: ${error.message}`,
        functionType,
        data: null,
      });
    }
    loggerInfo(`Upserting store data in db completed`, store || "");
    return sendResponse({
      status: 200,
      message: "Store data upserted successfully",
      functionType,
      data: data[0] || null,
    });
  } catch (error) {
    loggerError(`Error upserting store data in db`, store, error.message);
    return sendResponse({
      status: 500,
      message: `Failed to upsert store data: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

export async function prepareStoreInput(store, additionalData = {}) {
  const storeData = await fetchStore(store);
  if (storeData.status !== 200 || !storeData.data?.proactive_ai_api_token) {
    loggerError(
      "Store Validation Failed",
      store,
      "Invalid or missing store token",
    );
    throw sendResponse({
      status: 400,
      message: "Invalid or missing store token",
      functionType: additionalData.functionType || "",
      data: null,
    });
  }
  return {
    ...additionalData,
    store_token: storeData.data.proactive_ai_api_token,
    store_domain: storeData.data.store_domain,
    store: store,
  };
}
