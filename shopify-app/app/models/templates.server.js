import { proactiveAiGetTemplate } from "../proactive_ai/get_templates";
import { prepareStoreInput } from "./store.server";
import {
  loggerInfo,
  loggerError,
  sendResponse,
  appendAndEncode,
} from "../utilities/helper";
import { proactiveAiGetTemplateDetails } from "../proactive_ai/get_template_details";
import { proactiveAiRunTemplateAsync } from "../proactive_ai/run_template_async";
import { getAllProductsData } from "./products.server";
import { getAllBlogsData } from "./article.server";
import { proactiveAiSaveTemplateConfiguration } from "../proactive_ai/save_template_config";
import fs from "fs";
import path from "path";
import i18n from "i18next";
import { supabase } from "../db/db_config";
import { JOB_OUTPUT_TABLE, integrationTempId } from "../config/config";
import { composioGetConnectedAccount } from "./composio.server";
import {fetchSettings} from "./settings.server"
const productFilePath = path.join(
  process.cwd(),
  "app/config/graphql_structure/product_update.json",
);
const articleFilePath = path.join(
  process.cwd(),
  "app/config/graphql_structure/article_update.json",
);

const generalFilePath = path.join(
  process.cwd(),
  "app/config/graphql_structure/general_structure.json",
);

export async function getTemplatesData(data) {
  const {
    store,
    functionType,
    search,
    label,
    sort_by,
    sort_order,
    limit,
    offset,
  } = data;
  loggerInfo("Get Templates data list backend - Started", store);
  const storeData = await prepareStoreInput(store);

  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (search) queryParams.append("search", search);
    if (label) queryParams.append("label", label);
    if (sort_by) queryParams.append("sort_by", sort_by);
    if (sort_order) queryParams.append("sort_order", sort_order);
    if (limit) queryParams.append("limit", limit);
    if (offset) queryParams.append("offset", offset);

    const dataRes = await proactiveAiGetTemplate(
      storeData,
      queryParams.toString(),
    );
    loggerInfo(
      "Get Templates data list backend Completed",
      store,
      JSON.stringify(dataRes),
    );
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: dataRes.data?.data?.templates || [],
      total: dataRes.data?.data?.total || 0,
      limit: dataRes.data?.data?.limit || 15,
      offset: dataRes.data?.data?.offset || 0,
    });
  } catch (error) {
    loggerError(
      "Failed to Get Templates data list backend",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Get Templates data list backend: ${error.message}`,
      functionType,
    });
  }
}

export async function getTemplateDetailsData(data) {
  const { store, functionType, id } = data;
  let itemData;
  let queryData;
  let integrationData = [];
  loggerInfo("Get Template detail data  backend - Started", store);
  try {
    const storeData = await prepareStoreInput(store);
    const dataRes = await proactiveAiGetTemplateDetails(storeData, id);
    const settingsData = await fetchSettings(store);
    if (integrationTempId.includes(id)) {
      const integrationRes = await composioGetConnectedAccount(data);
      integrationData =
        integrationRes.status === 200 ? integrationRes?.data?.accounts : [];
    }

    const category = dataRes?.data?.data?.store_data_type?.category;
    if (category === "article" || category === "blog") {
      const fileContent = fs.readFileSync(articleFilePath, "utf8");
      queryData = JSON.parse(fileContent);
    } else if (category === "products") {
      const fileContent = fs.readFileSync(productFilePath, "utf8");
      queryData = JSON.parse(fileContent);
    } else {
      const fileContent = fs.readFileSync(generalFilePath, "utf8");
      queryData = JSON.parse(fileContent);
    }

    let storeInputData = dataRes?.data?.data?.store_data;
    // Ensure storeInputData is always an array
    storeInputData = Array.isArray(storeInputData) ? storeInputData : [storeInputData];

    // Loop over each store_data object and apply the existing logic
    const processedStoreData = await Promise.all(
      storeInputData.map(async (storeInput) => {
        let itemData;
        if (
          storeInput?.store_data_type?.category === "article" ||
          storeInput?.store_data_type?.category === "blog"
        ) {
          itemData = await getAllBlogsData(data, storeData);
        } else if (storeInput?.store_data_type?.category === "products") {
          itemData = await getAllProductsData(data, storeData);
        } else {
        }
        return {
          ...storeInput,
          itemData:
            itemData?.status === 200 && Array.isArray(itemData.data)
              ? itemData.data.map((item) => ({
                  ...item,
                  category: storeInput?.store_data_type?.category,
                }))
              : [],
        };
      }),
    );
    itemData = processedStoreData[0].itemData ||[];
    const firstStoreData =  processedStoreData[0];
    delete firstStoreData.itemData
    // Update the final data with processed store data
    let finalData = dataRes.data?.data || [];
    finalData.store_data = firstStoreData;
    finalData.integrationData = integrationData;
    finalData.settingsData = settingsData || null;
    finalData.itemData = itemData;
    (finalData.output_structure = queryData || null),
      loggerInfo(
        "Get Template detail data backend Completed",
        store,
        JSON.stringify(dataRes),
      );
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: finalData || [],
    });
  } catch (error) {
    loggerError(
      "Failed to Get  Template detail data backend",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Get Template detail data backend: ${error.message}`,
      functionType,
    });
  }
}

export async function runTemplateFlow(data, session) {
  const { store, functionType, value } = data;
  loggerInfo("Run Template Flow & usage pricing - Started", store, value);
  const parsedValue = JSON.parse(value);
  try {
    let storeData = await prepareStoreInput(store);
    if (integrationTempId.includes(parsedValue?.template_uuid)) {
      const accessToken = (await appendAndEncode(session?.accessToken)) || "";
      //const accessToken = session?.accessToken || "";
      parsedValue.configurations.shop_url = storeData.store_domain || "";
      parsedValue.configurations.access_token = accessToken || "";
    }
    const dataRes = await proactiveAiRunTemplateAsync(storeData, parsedValue);
    const isSuccess = dataRes?.status_code === 200 || dataRes?.status === 200;
    if (!isSuccess) {
      loggerError(
        "proactiveAiRunTemplate returned non-200 status_code",
        store,
        JSON.stringify(dataRes),
      );
      throw sendResponse({
        status: dataRes?.status || 500,
        message: `proactiveAiRunTemplate failed: ${dataRes?.message || "Unknown error"}`,
        functionType,
      });
    }
    loggerInfo(
      "Run Template Flow - proactiveAiRunTemplate Completed",
      store,
      JSON.stringify(dataRes),
    );
    const resultValue = dataRes.data?.data;
    const teamResult = {
      team_id: resultValue?.template_uuid ?? "",
      job_id: resultValue?.job_id ?? "",
      store,
      type: resultValue?.category ?? "",
      status: resultValue?.job_status ?? "completed",
    };
    const saveRes = await updateTemplateResult(teamResult);
    loggerInfo(
      "Template result saved successfully",
      store,
      JSON.stringify(saveRes),
    );

    return sendResponse({
      status: dataRes.status,
      message: i18n.t("run_job_started_successfully"),
      functionType,
      data: dataRes.data?.data ?? [],
    });
  } catch (error) {
    loggerError(
      "Failed to Run Template Flow & usage pricing",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Run Template Flow & usage pricing: ${error.message}`,
      functionType,
    });
  }
}

export async function saveTemplateConfiguration(data) {
  const { store, functionType, value, id, saveData } = data;
  let inputData;
  if (functionType === "saveRunTemplate") {
    inputData = saveData;
  } else {
    inputData = value;
  }
  loggerInfo("Save Template Configuration - Started", store, inputData);
  try {
    const storeData = await prepareStoreInput(store);
    const parsedValue = JSON.parse(inputData);
    parsedValue.template_uuid = id;
    const dataRes = await proactiveAiSaveTemplateConfiguration(
      storeData,
      parsedValue,
    );
    if (dataRes?.status !== 200) {
      loggerError(
        "Failed to Save Template Configuration",
        store,
        dataRes.message,
      );
      throw sendResponse({
        status: 400,
        message: `Failed to Save Template Configuration: ${dataRes.message}`,
        functionType,
      });
    }
    loggerInfo("Save Template Configuration - Completed", store, dataRes);
    return sendResponse({
      status: dataRes?.status,
      message:
        i18n.t("job_specifications_saved_successfully") || dataRes.message,
      functionType,
      data: dataRes.data?.data ?? [],
    });
  } catch (error) {
    loggerError("Failed to Save Template Configuration", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to Save Template Configuration: ${error.message}`,
      functionType,
    });
  }
}

export async function updateTemplateResult(value) {
  const { store } = value;
  const functionType = "";
  try {
    loggerInfo(`Update or Insert job data into db started`, store || "", value);
    const { data, error } = await supabase
      .from(JOB_OUTPUT_TABLE)
      .upsert(value, {
        onConflict: ["job_id"],
      })
      .select()
      .single();
    if (error) {
      loggerError(
        `Error Update or Insert job data into db`,
        store || "",
        error.message,
      );
      return sendResponse({
        status: 400,
        message: `Failed to Update or Insert job data: ${error.message}`,
        functionType,
        data: null,
      });
    }
    loggerInfo(
      `Update or Insert job data into db completed`,
      store || "",
      data,
    );
    return sendResponse({
      status: 200,
      message: "Job data Update or Insert successfully",
      functionType,
      data,
    });
  } catch (error) {
    loggerError(
      `Error Update or Insert job data into db`,
      store || "",
      error.message,
    );
    return sendResponse({
      status: 500,
      message: `Failed to Update or Insert job data: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

export async function runAndSaveTemplate(data, session) {
  const { store, functionType, value } = data;
  loggerInfo("Run Template Flow & usage pricing - Started", store, value);
  try {
    const saveRes = await saveTemplateConfiguration(data);
    if (saveRes?.status !== 200) {
      loggerError(
        "Failed to Save Template Configuration",
        store,
        saveRes?.message || "Unknown error",
      );
      throw sendResponse({
        status: 400,
        message: `Failed to Save Template Configuration: ${saveRes?.message || "Unknown error"}`,
        functionType,
      });
    }
    const runRes = await runTemplateFlow(data, session);
    if (runRes?.status !== 200) {
      loggerError(
        "Failed to Run Template Flow",
        store,
        runRes?.message || "Unknown error",
      );
      throw sendResponse({
        status: 400,
        message: `Failed to Run Template Flow: ${runRes?.message || "Unknown error"}`,
        functionType,
      });
    }
    loggerInfo("Run Template Flow Completed", store, runRes);
    return sendResponse({
      status: runRes?.status,
      message: runRes.message,
      functionType,
      data: runRes.data || null,
    });
  } catch (error) {
    loggerError(
      "Failed to Run Template Flow & usage pricing",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Run Template Flow & usage pricing: ${error.message}`,
      functionType,
    });
  }
}
