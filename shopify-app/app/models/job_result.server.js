import { supabase } from "../db/db_config";
import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { JOB_OUTPUT_TABLE } from "../config/config";
import { updateQuery } from "../graphql/update_query";
export async function fetchTeamResult(value) {
  const { store, functionType, statusValue, typeValue, sortValue } = value;
  try {
    loggerInfo(`Fetching Job done data from db started`, store || "", value);
    // Start building query
    let query = supabase.from(JOB_OUTPUT_TABLE).select("*").eq("store", store);
    // Apply filters only if not 'all'
    if (statusValue !== "all") {
      query = query.eq("status", statusValue);
    }
    if (typeValue !== "all") {
      query = query.eq("type", typeValue);
    }
    // Sorting
    if (sortValue === "latest") {
      query = query.order("created_at", { ascending: false });
    } else if (sortValue === "oldest") {
      query = query.order("created_at", { ascending: true });
    }
    const { data, error } = await query;
    if (error) {
      return sendResponse({
        status: 400,
        message: `Failed to fetch store data: ${error.message}`,
        functionType,
        data: [],
      });
    }

    loggerInfo(`Fetching Job done data from db completed`, store || "", data);
    return sendResponse({
      status: 200,
      message: "Job done data fetched successfully",
      functionType,
      data: data || [],
    });
  } catch (error) {
    loggerError(`Error fetching Job done data from db`, "", error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to fetch job done data: ${error.message}`,
      functionType,
      data: [],
    });
  }
}

export async function fetchTeamResultById(value) {
  const { id, store, functionType } = value;
  try {
    loggerInfo(
      `Fetching job data by ID and store from db started`,
      store,
      value,
    );
    const { data, error } = await supabase
      .from(JOB_OUTPUT_TABLE)
      .select("*")
      .eq("job_id", id)
      .eq("store", store)
      .single();
    if (error) {
      throw sendResponse({
        status: 400,
        message: `Failed to fetch job data: ${error.message}`,
        functionType,
        data: null,
      });
    }
    loggerInfo(`Fetching job data by ID and store completed`, store, data);
    return sendResponse({
      status: 200,
      message: "Job data fetched successfully",
      functionType,
      data,
    });
  } catch (error) {
    loggerError(
      `Error fetching job data by ID and store`,
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to fetch job data: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

export async function updateTeamResult(value) {
  const { store } = value;
  const functionType = "";
  try {
    loggerInfo(`Update or Insert job data into db started`, store || "", value);
    const { data, error } = await supabase
      .from(JOB_OUTPUT_TABLE)
      .upsert(value, {
        onConflict: ["job_id"],
      })
      .select()
      .single();
    if (error) {
      loggerError(
        `Error Update or Insert job data into db`,
        store || "",
        error.message,
      );
      return sendResponse({
        status: 400,
        message: `Failed to Update or Insert job data: ${error.message}`,
        functionType,
        data: null,
      });
    }
    loggerInfo(
      `Update or Insert job data into db completed`,
      store || "",
      data,
    );
    return sendResponse({
      status: 200,
      message: "Job data Update or Insert successfully",
      functionType,
      data,
    });
  } catch (error) {
    loggerError(
      `Error Update or Insert job data into db`,
      store || "",
      error.message,
    );
    return sendResponse({
      status: 500,
      message: `Failed to Update or Insert job data: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

export async function updateResultItemStatus(value, session) {
  const { itemId, store, functionType, resultData, jobId } = value;
  try {
    loggerInfo(`Updating result item status started`, store || "", itemId);
    const parsedResultData = JSON.parse(resultData);
    const resultArray = parsedResultData.result;
    const updatedResult = resultArray.map((item) => {
      if (item.display_data && item.display_data.id === itemId) {
        return {
          ...item,
          display_data: {
            ...item.display_data,
            status: true,
          },
        };
      }
      return item;
    });
    const itemToUpdate = resultArray.find(
      (item) => item.display_data && item.display_data.id === itemId,
    );
    const publishRes = await updateQuery({
      query: itemToUpdate.graphql_string,
      variables: itemToUpdate.input,
      accessToken: session.accessToken,
      store: store,
    });

    if (publishRes.status !== 200) {
      loggerError(
        `Error publishing update to Shopify`,
        store || "",
        publishRes.message || "Failed to publish update",
      );
      throw sendResponse({
        status: 400,
        message: `Failed to publish update to Shopify: ${publishRes.message || "Unknown error"}`,
        functionType,
        data: null,
      });
    }

    const { data, error: updateError } = await supabase
      .from(JOB_OUTPUT_TABLE)
      .update({ result: updatedResult })
      .eq("job_id", jobId)
      .eq("store", store)
      .select()
      .single();

    if (updateError) {
      loggerError(
        `Error updating result item status`,
        store || "",
        updateError.message,
      );
      throw sendResponse({
        status: 400,
        message: `Failed to update result item status: ${updateError.message}`,
        functionType,
        data: null,
      });
    }
    loggerInfo(`Updating result item status completed`, store || "", data);
    data.itemId = itemId;
    return sendResponse({
      status: 200,
      message: "Result item status updated successfully",
      functionType,
      data,
    });
  } catch (error) {
    loggerError(
      `Error updating result item status`,
      store || "",
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to update result item status: ${error.message}`,
      functionType,
      data: null,
    });
  }
}
