import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveAiGetTaskValidateNlq } from "../proactive_ai/get_task";
import { proactiveAiSaveApprovedNlqTask } from "../proactive_ai/save_approved_nlq_task";
import { proactiveAiGetTaskWorkers } from "../proactive_ai/get_workers_for_task";
import { proactiveAiGetTaskConfigurations } from "../proactive_ai/get_identify_configurations";
import { proactiveAiSaveApprovedConfig } from "../proactive_ai/save_ approved_configuration";
import { proactiveAiGetTaskSummarize } from "../proactive_ai/get_ summarize_tasks";
import { prepareStoreInput } from "./store.server";
import { proactiveAiCreateWorkflow } from "../proactive_ai/create_workflow";
import { proactiveAiGetWorkers } from "../proactive_ai/get_workers";
import { getAllProductsData } from "./products.server";
import { getAllBlogsData } from "./article.server";
// Prepares input and handles store validation

// Get task from Proactive AI
export async function getTaskData({
  functionType,
  store,
  query,
  workflow_name = "",
}) {
  const input = await prepareStoreInput(store, {
    query,
    workflow_name,
    functionType,
  });
  try {
    loggerInfo("Task Creation Started", store);
    const resData = await proactiveAiGetTaskValidateNlq(input);
    loggerInfo("Task Creation Completed", store);
    return sendResponse({
      status: resData?.status,
      message: resData?.message,
      functionType,
      data: resData?.data.data || null,
    });
  } catch (error) {
    loggerError("Failed to Create Task", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to Create Task: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

export async function stepOneToStepTwo(data) {
  const { store, functionType, teamId, selectedTask } = data;
  const storeData = await prepareStoreInput(store);
  try {
    loggerInfo("Task Saving & get workers list Started", store);
    const inputSaveWorkers = {
      team_id: teamId,
      selected_tasks: JSON.parse(selectedTask),
    };
    const saveRes = await proactiveAiSaveApprovedNlqTask(
      storeData,
      inputSaveWorkers,
    );
    if (saveRes?.status !== 200) {
      loggerError(
        "Task Saving & get workers list Error",
        store,
        saveRes?.message || "Task Saving & get workers list error",
      );
      throw sendResponse({
        status: 400,
        message: `Failed to Task Saving & get workers list: ${saveRes?.message || "Unknown error"}`,
        functionType,
      });
    }
    const inputGetWorkers = {
      team_id: teamId,
      tasks: JSON.parse(selectedTask),
    };
    const resData = await proactiveAiGetTaskWorkers(storeData, inputGetWorkers);
    if (resData?.status !== 200) {
      loggerError(
        "Fetching Task Workers Failed - Non 200 Status",
        store,
        resData?.message || "Unknown error",
      );
      throw sendResponse({
        status: 400,
        message: `Failed to Fetch Task Workers: ${resData?.message || "Unknown error"}`,
        functionType,
      });
    }

    loggerInfo("Task Saving & get workers list Completed", store);
    return sendResponse({
      status: resData?.status,
      message: resData.message,
      functionType,
      data: resData.data?.data || null,
    });
  } catch (error) {
    loggerError(
      "Failed to Task Saving & get workers list",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Task Saving & get workers list: ${error.message}`,
      functionType,
    });
  }
}

export async function stepTwoToThree(data) {
  const { store, functionType, teamId, selectedWorkers } = data;
  try {
    loggerInfo("Workers Saving & Get Configurations Started", store);

    const storeData = await prepareStoreInput(store);
    const parsedWorkers = JSON.parse(selectedWorkers);

    const newSelectedWorkers = parsedWorkers.map((worker) => ({
      task_id: worker.task_id,
      task: worker.name,
      Description: worker.Description,
      agents: worker.formatted_agents,
    }));

    // Get Task Configurations
    const inputGetConfig = {
      team_id: teamId,
      tasks: newSelectedWorkers,
    };
    const resData = await proactiveAiGetTaskConfigurations(
      storeData,
      inputGetConfig,
    );

    loggerInfo("Get Configurations Completed", store);
    let finalRes;
    let itemData = [];
    finalRes = resData.data?.data || null;
    const typeData = finalRes.tasks[0];
    if (finalRes) {
      if (typeData.store_data?.category === "article") {
        itemData = await getAllBlogsData(data, storeData);
      } else if (typeData?.store_data?.category === "product") {
        itemData = await getAllProductsData(data, storeData);
      } else if (typeData?.store_data?.category === "collection") {
      }
    }
    finalRes.itemData = itemData.status === 200 ? itemData.data : [];
    return sendResponse({
      status: resData?.status,
      message: resData?.message,
      functionType,
      data: finalRes || null,
    });
  } catch (error) {
    loggerError("Failed to Get Configurations", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to get configurations: ${error.message}`,
      functionType,
      data: null,
    });
  }
}

export async function stepThreeToFour(data) {
  const { store, functionType, teamId, configurations, selectedWorkers } = data;
  try {
    const storeData = await prepareStoreInput(store);
    loggerInfo("Configurations Saving and Get Summary Started", store, data);

    const parsedConfigurations = JSON.parse(configurations);

    const formattedTasks = parsedConfigurations.map((task) => {
      const newValues = {};
      task.values.forEach((item) => {
        newValues[item.key] = item.value;
      });
      return {
        ...task,
        values: newValues,
      };
    });

    const inputSaveSummary = {
      team_id: teamId,
      tasks: formattedTasks,
    };

    const saveRes = await proactiveAiSaveApprovedConfig(
      storeData,
      inputSaveSummary,
    );

    if (saveRes?.status !== 200) {
      loggerError(
        "Configurations Saving Failed - Non 200 Status",
        store,
        saveRes?.message || "Unknown error",
      );
      throw sendResponse({
        status: 400,
        message: `Failed to Save Configurations: ${saveRes?.message || "Unknown error"}`,
        functionType,
      });
    }

    const newSelectedWorkers = JSON.parse(selectedWorkers).map((worker) => ({
      task_id: worker.task_id,
      task: worker.name,
      agents: worker.formatted_agents,
    }));

    const inputGetSummary = {
      team_id: teamId,
      tasks: newSelectedWorkers,
    };

    const resData = await proactiveAiGetTaskSummarize(
      storeData,
      inputGetSummary,
    );
    if (resData?.status !== 200) {
      loggerError(
        "Fetching Task Summary Failed - Non 200 Status",
        store,
        resData?.message || "Unknown error",
      );
      throw sendResponse({
        status: 400,
        message: `Failed to Fetch Task Summary: ${resData?.message || "Unknown error"}`,
        functionType,
      });
    }

    loggerInfo("Configurations Saving and Get Summary Completed", store);

    return sendResponse({
      status: resData?.status,
      message: resData.message,
      functionType,
      data: resData.data?.data || null,
    });
  } catch (error) {
    loggerError(
      "Failed to Save Configurations and Get Summary",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Save Configurations and Get Summary: ${error.message}`,
      functionType,
    });
  }
}

export async function saveWorkFlow(data) {
  const { store, functionType, teamId, teamName } = data;
  try {
    const storeData = await prepareStoreInput(store);
    loggerInfo("Workflow creation Started", store, data);
    const inputSaveWorkflow = {
      team_id: teamId,
      team_name: teamName || "",
    };
    const saveRes = await proactiveAiCreateWorkflow(
      storeData,
      inputSaveWorkflow,
    );
    if (saveRes?.status !== 200) {
      loggerError(
        "Workflow creation Saving Failed - Non 200 Status",
        store,
        saveRes?.message || "Unknown error",
      );
      throw sendResponse({
        status: 400,
        message: `Failed to Workflow creation: ${saveRes?.message || "Unknown error"}`,
        functionType,
      });
    }
    loggerInfo("Workflow creation Completed", store);
    return sendResponse({
      status: 200,
      message: saveRes.message,
      functionType,
      data: saveRes.data?.data || null,
    });
  } catch (error) {
    loggerError("Failed to Workflow creation", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to Workflow creation: ${error.message}`,
      functionType,
    });
  }
}

export async function getAllWorkersData(data) {
  const { store, functionType } = data;
  loggerInfo("Get Workers data list backend - Started", store);
  const storeData = await prepareStoreInput(store);
  try {
    const dataRes = await proactiveAiGetWorkers(storeData);
    loggerInfo(
      "Get Workers data list backend Completed",
      store,
      JSON.stringify(dataRes),
    );
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: dataRes.data?.data || [],
    });
  } catch (error) {
    loggerError(
      "Failed to Get Workers data list backend",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Get Workers data list backend: ${error.message}`,
      functionType,
    });
  }
}
