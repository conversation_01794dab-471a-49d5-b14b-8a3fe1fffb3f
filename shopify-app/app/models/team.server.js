import { proactiveAiGetTeams } from "../proactive_ai/get_teams";
import { prepareStoreInput } from "./store.server";
import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveAiGetTeamDetails } from "../proactive_ai/get_team_details";
import { proactiveAiRunTeam } from "../proactive_ai/run_teams";
import { createUsageCharge } from "./charge.server";
import { getAllProductsData } from "./products.server";
import { getAllBlogsData } from "./article.server";
import fs from "fs";
import path from "path";
const productFilePath = path.join(
  process.cwd(),
  "app/config/graphql_structure/product_update.json",
);
const articleFilePath = path.join(
  process.cwd(),
  "app/config/graphql_structure/article_update.json",
);
import { updateTeamResult } from "./job_result.server";
export async function getTeamsData(data,storeData=null) {
  const { store, functionType } = data;
  loggerInfo("Get Team data list backend - Started", store);
  if(!storeData){
    storeData = await prepareStoreInput(store);
  }
  try {
    const dataRes = await proactiveAiGetTeams(storeData);
    loggerInfo(
      "Get Team data list backend Completed",
      store,
      JSON.stringify(dataRes),
    );
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: dataRes.data?.data || [],
    });
  } catch (error) {
    loggerError("Failed to Get Team data list backend", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to Get Team data list backend: ${error.message}`,
      functionType,
    });
  }
}

export async function getTeamDetailsData(data) {
  const { store, functionType, id } = data;
  let itemData;
  let queryData;
  loggerInfo("Get Team detail data  backend - Started", store);
  try {
    const storeData = await prepareStoreInput(store);
    const dataRes = await proactiveAiGetTeamDetails(storeData, id);
    const storeInputData = dataRes?.data?.data?.store_data?.store_data_type;
    if (storeInputData?.category === "article") {
      itemData = await getAllBlogsData(data, storeData);
      const fileContent = fs.readFileSync(articleFilePath, "utf8");
      queryData = JSON.parse(fileContent);
    } else if (storeInputData?.category === "product") {
      const fileContent = fs.readFileSync(productFilePath, "utf8");
      queryData = JSON.parse(fileContent);
      itemData = await getAllProductsData(data, storeData);
    }
    loggerInfo(
      "Get Team detail data backend Completed",
      store,
      JSON.stringify(dataRes),
    );
    let finalData = dataRes.data?.data || [];
    finalData.itemData = itemData?.status === 200 ? itemData.data : [];
    finalData.output_structure = queryData || null;
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: finalData || [],
    });
  } catch (error) {
    loggerError(
      "Failed to Get  Team detail data backend",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Get Team detail data backend: ${error.message}`,
      functionType,
    });
  }
}

export async function runTeamFlow(data, admin, billing) {
  const { store, functionType, value } = data;

  loggerInfo("Run Team Flow & usage pricing - Started", store, value);

  try {
    const storeData = await prepareStoreInput(store);
    const dataRes = await proactiveAiRunTeam(storeData, value);
    const isSuccess = dataRes?.status_code === 200 || dataRes?.status === 200;
    if (!isSuccess) {
      loggerError(
        "proactiveAiRunTeam returned non-200 status_code",
        store,
        JSON.stringify(dataRes),
      );
      throw sendResponse({
        status: dataRes?.status || 500,
        message: `proactiveAiRunTeam failed: ${dataRes?.message || "Unknown error"}`,
        functionType,
      });
    }
    loggerInfo("Run Team Flow - proactiveAiRunTeam Completed", store, dataRes);
    try {
      const resultValue = dataRes.data?.data;
      const teamResult = {
        result: resultValue?.result ?? [],
        team_id: resultValue?.team_id ?? "",
        job_id: resultValue?.job_id ?? "",
        store,
        title: resultValue?.title ?? "",
        description: resultValue?.desc ?? "",
        type: resultValue?.type ?? "",
        status: resultValue?.status ?? "",
      };
      const saveRes = await updateTeamResult(teamResult);
      loggerInfo(
        "Team result saved successfully",
        store,
        JSON.stringify(saveRes),
      );
    } catch (saveError) {
      loggerError(
        "Failed to save team result, continuing with pricing",
        store,
        saveError.message,
      );
    }
    // Handle usage charge
    const resData = await createUsageCharge(admin, data, billing);
    loggerInfo("Usage charge created", store, JSON.stringify(resData));
    if (resData?.status !== 200) {
      loggerError(
        "Failed to create usage charge",
        store,
        JSON.stringify(resData),
      );
      throw sendResponse({
        status: 400,
        message: `Usage charge creation failed: ${error.message}`,
        functionType,
      });
    }

    return sendResponse({
      status: dataRes.status,
      message: dataRes.message,
      functionType,
      data: dataRes.data?.data ?? [],
    });
  } catch (error) {
    loggerError(
      "Failed to Run Team Flow & usage pricing",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Run Team Flow & usage pricing: ${error.message}`,
      functionType,
    });
  }
}
