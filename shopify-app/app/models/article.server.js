import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { prepareStoreInput } from "./store.server";
import { proactiveAiGetBlogs } from "../proactive_ai/get_blogs";
export async function getAllBlogsData(data, storeData) {
  const { store, functionType } = data;
  loggerInfo("Get Blogs data list backend - Started", store);
  try {
    if (!storeData) {
      storeData = await prepareStoreInput(store);
    }
    const dataRes = await proactiveAiGetBlogs(storeData);
    loggerInfo(
      "Get Blogs data list backend Completed",
      store,
      JSON.stringify(dataRes),
    );
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: dataRes.data?.data || [],
    });
  } catch (error) {
    loggerError("Failed to Get Blogs data list backend", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to Get Blogs data list backend: ${error.message}`,
      functionType,
    });
  }
}
