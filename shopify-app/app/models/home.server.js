import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { proactiveAiGetHome } from "../proactive_ai/get_home";
import { prepareStoreInput } from "./store.server";

export async function getHomeData(data) {
  const { store, functionType } = data;
  loggerInfo("Get Home data backend - Started", store);
  try {
    const storeData = await prepareStoreInput(store);
    const templateRes = await proactiveAiGetHome(storeData);
    const templateList =
      templateRes.status === 200 ? templateRes?.data?.data?.templates : [];
    const recentJob =
      templateRes.status === 200
        ? templateRes?.data?.data?.recent_activity
        : [];
    const value = {
      templateList: templateList,
      workersList: recentJob,
    };
    loggerInfo("Get Home data backend Completed", store);
    return sendResponse({
      status: 200,
      message: "fetched",
      functionType,
      data: value || {},
    });
  } catch (error) {
    loggerError("Failed to Get Home data backend", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to Get Home data backend: ${error.message}`,
      functionType,
    });
  }
}
