import { loggerInfo, loggerError } from "../utilities/helper.js";
import { supabase } from "../db/db_config.js";
import { SETTINGS_TABLE } from "../config/config.js";
import i18n from "i18next";
const settingsTable = SETTINGS_TABLE;
import dotenv from "dotenv";
import postmark from "postmark";
import {
  runJobTemplateId,
  welcomeTemplateId,
} from "../config/mail_temp_config.js";
dotenv.config();

export async function saveSettings(InputData) {
  let value;
  const { store, functionType, ...rest } = InputData;
  const settingsData = {
    ...rest,
    store,
    updated_at: new Date().toISOString(),
  };
  try {
    loggerInfo("Inserting / Updating settings data in db started", store);
    const { data, error } = await supabase
      .from(settingsTable)
      .upsert([settingsData], {
        onConflict: "store",
      });
    if (error) {
      console.log(error);
      throw new Error(error.message);
    }
    loggerInfo("Inserting / Updating settings data in db completed", store);
    value = {
      status: "ok",
      message: i18n.t("updated_successfully"),
      functionType,
      data: data,
    };
    return value;
  } catch (error) {
    loggerError(
      `Error Inserting / Updating settings data in db`,
      store,
      error.message,
    );
    throw new Error("Inserting / Updating settings");
  }
}

export async function fetchSettings(store) {
  try {
    loggerInfo(`Fetching settings data from db started`, store);
    const { data, error } = await supabase
      .from(settingsTable)
      .select("*")
      .eq("store", store);
    if (error) {
      throw new Error(error.message);
    }
    loggerInfo(`Fetching settings data from db completed`, store, data);
    return data?.[0] || null;
  } catch (error) {
    loggerError(`Error Fetching settings data from db`, store, error.message);
    throw error;
  }
}

async function handleTypeEmail(type) {
  let templateId;
  switch (type) {
    case "runJob":
      templateId = runJobTemplateId;
      break;
    case "welcome":
      templateId = welcomeTemplateId;
      break;
    default:
      // templateId = generalUpdatedTemplateId;
      return;
  }
  return templateId;
}
export async function sendEmail(data) {
  const { type, email, store, TemplateModel } = data;
  try {
    loggerInfo(`Mailer function started`, store);
    const accessKey = process.env.EMAIl_ACCESS_key;
    const client = new postmark.ServerClient(accessKey);
    // let TemplateModel = {
    // title: title,
    //};
    const tempId = await handleTypeEmail(type);
    const message = {
      From: process.env.EMAIL_USER,
      To: email,
      TemplateId: tempId,
      //  TemplateModel: TemplateModel,
      ...(TemplateModel && { TemplateModel }),
    };

    const response = await client.sendEmailWithTemplate(message);
    loggerInfo(`Message sent: ${response.MessageID}`, store);
    loggerInfo(`Mailer function completed`, store);
  } catch (error) {
    loggerError(`Error sending email: ${error.message}`, store, error.message);
  }
}
