import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { prepareStoreInput } from "./store.server";
import { proactiveAiComposioConnect } from "../proactive_ai/composio/create_connection";
import { proactiveAiComposioTestConnection } from "../proactive_ai/composio/test_connection";
import { proactiveAiGetConnectedAccount } from "../proactive_ai/composio/get_connected_account";
export async function composioConnect(data) {
  const { store, functionType, app_name } = data;
  let inputValue;
  loggerInfo("Composio Connect - Started", store, app_name);
  try {
    inputValue = {
      app_name: app_name,
      timeout: 120,
    };
    const storeData = await prepareStoreInput(store);
    const dataRes = await proactiveAiComposioConnect(storeData, inputValue);
    loggerInfo("Composio Connect - Completed", store, JSON.stringify(dataRes));
    return sendResponse({
      status: dataRes?.status,
      message: "connection success",
      functionType,
      data: dataRes.data?.data || null,
    });
  } catch (error) {
    loggerError("Failed to Composio Connect", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to Composio Connect: ${error.message}`,
      functionType,
    });
  }
}

export async function composioTestConnection(data) {
  const { store, functionType, value } = data;
  loggerInfo("Composio Test Connection - Started", store, value);
  try {
    const storeData = await prepareStoreInput(store);
    const dataRes = await proactiveAiComposioTestConnection(storeData, value);
    loggerInfo(
      "Composio Test Connection - Completed",
      store,
      JSON.stringify(dataRes),
    );
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: dataRes.data?.data || null,
    });
  } catch (error) {
    loggerError("Failed to Composio Test Connection", store, error.message);
    throw sendResponse({
      status: 500,
      message: `Failed to Composio Test Connection: ${error.message}`,
      functionType,
    });
  }
}

export async function composioGetConnectedAccount(data) {
  const { store, functionType } = data;
  loggerInfo("Composio Get Connected Account - Started", store);
  try {
    const storeData = await prepareStoreInput(store);
    const dataRes = await proactiveAiGetConnectedAccount(storeData);
    loggerInfo(
      "Composio Get Connected Account - Completed",
      store,
      JSON.stringify(dataRes),
    );
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: dataRes.data?.data || null,
    });
  } catch (error) {
    loggerError(
      "Failed to Composio Get Connected Account",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Composio Get Connected Account: ${error.message}`,
      functionType,
    });
  }
}
