import { proactiveAiGetProducts } from "../proactive_ai/get_products";
import { loggerInfo, loggerError, sendResponse } from "../utilities/helper";
import { prepareStoreInput } from "./store.server";
import { proactiveAiGetCollections } from "../proactive_ai/get_collections";
export async function getAllProductsData(data, storeData) {
    // Remove the redeclaration since storeData is already available as a parameter
    const { store, functionType } = data;
    loggerInfo("Get Products data list backend - Started", store);
    try {
      if (!storeData) {
        storeData = await prepareStoreInput(store);
      }
      const dataRes = await proactiveAiGetProducts(storeData);
      loggerInfo(
        "Get Products data list backend Completed",
        store,
        JSON.stringify(dataRes),
      );
      return sendResponse({
        status: dataRes?.status,
        message: dataRes.message,
        functionType,
        data: dataRes.data?.data || [],
      });
    } catch (error) {
      loggerError(
        "Failed to Get Products data list backend",
        store,
        error.message,
      );
      throw sendResponse({
        status: 500,
        message: `Failed to Get Products data list backend: ${error.message}`,
        functionType,
      });
    }
  }
export async function getAllCollectionsData(data) {
  const { store, functionType } = data;
  loggerInfo("Get Collections data list backend - Started", store);
  const storeData = await prepareStoreInput(store);
  try {
    const dataRes = await proactiveAiGetCollections(storeData);
    loggerInfo(
      "Get Collections data list backend Completed",
      store,
      JSON.stringify(dataRes),
    );
    return sendResponse({
      status: dataRes?.status,
      message: dataRes.message,
      functionType,
      data: dataRes.data?.data || [],
    });
  } catch (error) {
    loggerError(
      "Failed to Get Collections data list backend",
      store,
      error.message,
    );
    throw sendResponse({
      status: 500,
      message: `Failed to Get Collections data list backend: ${error.message}`,
      functionType,
    });
  }
}
