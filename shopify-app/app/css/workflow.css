.progress-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  width: 200px;
}

.progress-bar-container {
  width: 100%;
}

.help-button-container {
  margin-left: auto;
}

.tasks-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-item {
  display: flex;
  border: 1px solid #e1e3e5;
  border-radius: 8px;
  padding: 16px;
  gap: 12px;
  align-items: flex-start;
}

.task-content {
  flex: 1;
}

.suggested-tasks {
  border: 1px dashed #c9cccf;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.navigation-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  margin-bottom: 24px;
}

.subtitle {
  font-weight: normal;
  color: var(--p-text-subdued);
  font-size: 0.9rem;
}

.task-indicator {
  padding-top: 2px;
  position: relative;
}

.task-indicator::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 28px;
  bottom: -14px;
  width: 2px;
  background-color: #000;
  transform: translateX(-50%);
}

.task-indicator:last-child::after {
  display: none;
}

.task-content {
  flex-grow: 1;
  padding: 4px 0;
}

.suggestion-box {
  border: 1px dashed var(--p-border-subdued);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.light-grey-box {
  background-color: #f0f0f0;
  padding: 0.5rem;
  border-radius: 8px;
  width: 100%;
}

.worker-card {
  border: 2px solid transparent;
  width: 200px;
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition:
    border-color 0.2s ease,
    box-shadow 0.2s ease;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.worker-card.selected {
  border-color: black;
  background-color: white;
}

/* Task selection buttons */
.task-selection {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 8px;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  
  &::-webkit-scrollbar {
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.task-button {
  flex: 0 0 auto;
  min-width: 200px;
  height: 48px;
  border: 1px solid #e3e5e7;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 12px 16px;
  text-align: center;
  cursor: pointer;
  font-size: 16px;
  color: #637381;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-button.active {
  border-left: 4px solid #008060;
  color: #212b36;
  font-weight: 500;
}
/* Settings sections */
.settings-section {
  margin-bottom: 10px;
}

.settings-section h4 {
  color: #637381;
  margin-bottom: 10px;
}

/* Individual field styling */
.field-label {
  flex: 0 0 180px;
  color: #212b36;
  font-size: 14px;
}

.field-input {
  flex: 1;
  max-width: 450px;
}
