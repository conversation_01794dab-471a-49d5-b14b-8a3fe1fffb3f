/* Toggle switch container */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  /* Hide the default checkbox */
  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  /* Slider */
  .toggle-slider {
    position: relative;
    cursor: pointer;
    width: 40px; /* Adjusted width */
    height: 20px; /* Adjusted height */
    background-color: #ccc;
    border-radius: 20px; /* Adjusted for rounded corners */
    transition: background-color 0.4s;
  }
  
  /* Slider handle */
  .toggle-slider:before {
    content: "";
    position: absolute;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    left: 2px;
    bottom: 2px;
    background-color: black;
    transition: transform 0.4s;
  }
  
  /* Checked state */
  input:checked + label.toggle-slider {
    background-color: lightgray;
  }
  
  input:checked + label.toggle-slider:before {
    transform: translateX(20px);
  }
  