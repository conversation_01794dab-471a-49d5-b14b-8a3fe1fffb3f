.team-card {
  margin-top: 10px;
  min-height: 150px;
  display: flex;
  flex-direction: column;
}

.top-space-10 {
  margin-top: 10px;
}

.top-margin-auto {
  margin-top: auto;
}

.margin-bottom-32 {
  margin-bottom: 32px;
}

.desc-text {
  color: gray;
  font-size: 12px;
  margin-bottom: 16px;
}
.margin-bottom-10 {
  margin-bottom: 10px;
}
.margin-bottom-16 {
  margin-bottom: 16px;
}
.margin-bottom-top-5 {
  margin-top: 5px;
  margin-bottom: 5px;
}

.margin-left-10 {
  margin-left: 10px;
}

.margin-top-negative-10 {
  margin-top: -10px;
}

.margin-top-16 {
  margin-top: 16px;
}

.product-selection-section {
  margin-top: 16px;
}

.custom-box {
  margin-top: -18px;
  margin-left: 34px;
}

@media (max-width: 768px) {
  .custom-box {
    margin-top: -10px;
    margin-left: 8px;
  }
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 20px;
}

.grid-container:has(.template-card:only-child) {
  justify-content: center;
}

.template-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
  padding: 16px;
  max-width: 500px;
}
