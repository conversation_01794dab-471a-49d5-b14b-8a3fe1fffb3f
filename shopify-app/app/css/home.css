.main-content {
    margin-top: 20px;
  }
  
  .submit-button-container {
    display: flex;
    justify-content: flex-end;
  }
  
  .templates-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 8px;
  }
  

  .emoji {
    font-size: 24px;
  }
  
  .badge-container {
    margin-top: 16px;
  }
  
  .task-selection {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 8px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    align-items: stretch;
    
    &::-webkit-scrollbar {
      height: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
  
.blurred-container {
  position: relative;
  filter: blur(4px);
  opacity: 0.5;
  z-index: 1;
  width: 100%;
}

.full-width-container{
  width: 100%;
}

.task-selection {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding: 10px 0;
}


.truncate-2-lines {
  white-space: pre-wrap;
  overflow-wrap: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-height: 1.5em;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 3em;
}

.clickable-box {
  cursor: pointer;
  min-width: 300px;
  max-width: 300px;
}
