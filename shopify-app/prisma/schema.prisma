// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:dev.db"
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Bo<PERSON>an   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  <PERSON>olean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified <PERSON>ole<PERSON>?  @default(false)
}

model Event {
  id        Int      @id @default(autoincrement())
  shop      String
  name      String
  data      Json?  @default("{}")
  createdAt DateTime @default(now())
}

model Webhook {
  id        Int      @id @default(autoincrement())
  shop      String
  topic     String
  payload   String?  @default("{}")
  createdAt DateTime @default(now())
}
